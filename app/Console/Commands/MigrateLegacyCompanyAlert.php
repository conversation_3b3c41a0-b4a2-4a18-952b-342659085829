<?php

namespace App\Console\Commands;

use App\Enums\CompanyNotificationType;
use App\Models\CompanyNotificationSetting;
use App\Models\Legacy\EloquentAlert;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class MigrateLegacyCompanyAlert extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:company-alert';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migration Legacy company alert (tblalert)';

    protected array $unprocessedAlerts = [];

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $query = EloquentAlert::query()
            ->whereHas('eloquentContact', fn(Builder $query) => $query->where('status', 1))
            ->where(EloquentAlert::FIELD_TYPE, '!=', CompanyNotificationType::LEADS);
        $total = $query->count();
        $bar = $this->output->createProgressBar($total);

        $this->info("Migrating $total alerts.");
        $bar->start();

        $query->oldest(EloquentAlert::FIELD_ALERT_ID)->chunk(100, function(Collection $alerts) use($bar)  {
            $this->processChunk($alerts);
            $bar->advance($alerts->count());
        });

        $bar->finish();

        $this->newLine();
        $this->warn('Alerts that failed migration due to missing contact and/or company in A2: ' . collect($this->unprocessedAlerts)->join(', '));
    }

    protected function processChunk(Collection $alerts): void
    {
        $insertData = collect();
        $companyUsers = CompanyUser::query()
            ->select([CompanyUser::FIELD_COMPANY_ID, CompanyUser::FIELD_LEGACY_ID, CompanyUser::FIELD_ID])
            ->whereIntegerInRaw(CompanyUser::FIELD_LEGACY_ID, $alerts->pluck(EloquentAlert::FIELD_CONTACT_ID)->toArray())
            ->get()
            ->keyBy(CompanyUser::FIELD_LEGACY_ID);
        $companies = Company::query()
            ->select(Company::FIELD_ID)
            ->whereIntegerInRaw(Company::FIELD_ID, $companyUsers->pluck(CompanyUser::FIELD_COMPANY_ID)->toArray())
            ->get()
            ->keyBy(Company::FIELD_ID);

        $alerts->each(function (EloquentAlert $alert) use ($insertData, $companyUsers, $companies) {
            /** @var CompanyUser|null $companyUser */
            $companyUser = $companyUsers[$alert->{EloquentAlert::FIELD_CONTACT_ID}] ?? null;
            $company = $companyUser ? ($companies[$companyUser->company_id] ?? null) : null;

            if ($companyUser && $company) {
                $insertData->push([
                    CompanyNotificationSetting::FIELD_COMPANY_ID => $companyUser->company_id,
                    CompanyNotificationSetting::FIELD_NOTIFIABLE_TYPE => CompanyUser::class,
                    CompanyNotificationSetting::FIELD_NOTIFIABLE_ID => $companyUser->id,
                    CompanyNotificationSetting::FIELD_NOTIFICATION_TYPE => $alert->{EloquentAlert::FIELD_TYPE},
                    CompanyNotificationSetting::FIELD_NOTIFICATION_METHOD => $alert->{EloquentAlert::FIELD_METHOD},
                    CompanyNotificationSetting::CREATED_AT => now(),
                    CompanyNotificationSetting::UPDATED_AT => now(),
                ]);
            } else {
                $this->unprocessedAlerts[] = $alert->{EloquentAlert::FIELD_ALERT_ID};
            }
        });

        CompanyNotificationSetting::query()->insert($insertData->toArray());
    }
}
