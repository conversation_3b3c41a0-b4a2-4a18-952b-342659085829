<?php

namespace App\Repositories;

use App\Enums\CompanyNotificationType;
use App\Enums\NotificationMethod;
use App\Models\CompanyNotificationSetting;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;

class CompanyNotificationSettingsRepository
{
    public function updateCompanyNotificationSettings(array $settings, Company $company): void
    {
        foreach ($settings as $setting) {
            $companyUserId = $setting['id'];

            foreach ($setting['alerts'] as $type => $alert) {
                foreach ($alert as $method => $enabled) {
                    $notificationType   = CompanyNotificationType::from($type);
                    $notificationMethod = NotificationMethod::from($method);

                    if ($enabled) {
                        CompanyNotificationSetting::query()->updateOrCreate([
                            CompanyNotificationSetting::FIELD_COMPANY_ID          => $company->id,
                            CompanyNotificationSetting::FIELD_NOTIFIABLE_TYPE     => CompanyUser::class,
                            CompanyNotificationSetting::FIELD_NOTIFIABLE_ID       => $companyUserId,
                            CompanyNotificationSetting::FIELD_NOTIFICATION_TYPE   => $notificationType,
                            CompanyNotificationSetting::FIELD_NOTIFICATION_METHOD => $notificationMethod
                        ]);
                    } else {
                        CompanyNotificationSetting::query()
                            ->where(CompanyNotificationSetting::FIELD_COMPANY_ID, $company->id)
                            ->where(CompanyNotificationSetting::FIELD_NOTIFIABLE_TYPE, CompanyUser::class)
                            ->where(CompanyNotificationSetting::FIELD_NOTIFIABLE_ID, $companyUserId)
                            ->where(CompanyNotificationSetting::FIELD_NOTIFICATION_TYPE, $notificationType)
                            ->where(CompanyNotificationSetting::FIELD_NOTIFICATION_METHOD, $notificationMethod)
                            ->first()?->delete();
                    }
                }
            }
        }
    }
}
