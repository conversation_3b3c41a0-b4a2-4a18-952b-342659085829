<?php

namespace App\Repositories;

use App\Enums\CompanyNotificationType;
use App\Enums\NotificationMethod;
use App\Models\CompanyNotificationSetting;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Support\Collection;

class CompanyNotificationRepository
{
    /**
     * @param Company $company
     * @param CompanyNotificationType $type
     * @param string $notifiableType
     *
     * @return Collection<string>
     */
    public function getNotifiableCompanyEmails(
        Company                 $company,
        CompanyNotificationType $type,
        string $notifiableType = CompanyUser::class
    ): Collection
    {
        return $company->companyNotificationSettings()
            ->with(CompanyNotificationSetting::RELATION_NOTIFIABLE)
            ->has(CompanyNotificationSetting::RELATION_NOTIFIABLE)
            ->where(CompanyNotificationSetting::FIELD_NOTIFIABLE_TYPE, $notifiableType)
            ->where(CompanyNotificationSetting::FIELD_NOTIFICATION_TYPE, $type)
            ->where(CompanyNotificationSetting::FIELD_NOTIFICATION_METHOD, NotificationMethod::EMAIL)
            ->get()
            ->map(fn(CompanyNotificationSetting $notificationSetting) => $notificationSetting->notifiable->email)
            ->filter()
            ->unique();
    }
}
