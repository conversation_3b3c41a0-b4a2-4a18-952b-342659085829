<?php

namespace App\BillingWorkflows\Activities;

use App\DTO\BillingWorkflow\ActivityArgument;
use App\Enums\Billing\BillingLogLevel;
use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\CompanyNotificationType;
use App\Enums\EmailTemplateAttachmentType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceTemplate;
use App\Models\EmailTemplate;
use App\Models\Odin\Company;
use App\Repositories\CompanyNotificationRepository;
use App\Services\Billing\InvoicePdfService\InvoicePdfTemplateService;
use App\Services\ConfigurableEmailSenderService;
use App\Services\EmailTemplates\EmailTemplateService;
use App\Services\EmailTemplates\TemplateAttachment\EmailAttachmentFactory;
use App\Services\Shortcode\ShortcodeImplementation\InvoiceEmailShortcodeUseCase;
use App\Services\Shortcode\ShortcodeReplacerService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Throwable;
use Workflow\Models\StoredWorkflow;

class SendEmailNotification extends BaseActivity
{
    protected string $logNamespace = 'send_email_notification';

    protected ConfigurableEmailSenderService $configurableEmailSenderService;
    protected InvoicePdfTemplateService      $invoicePdfTemplateService;
    protected ShortcodeReplacerService       $shortcodeReplacerService;

    public function __construct(int $index, string $now, StoredWorkflow $storedWorkflow, ...$arguments)
    {
        $this->configurableEmailSenderService = app()->make(ConfigurableEmailSenderService::class);
        $this->invoicePdfTemplateService = app()->make(InvoicePdfTemplateService::class);
        $this->shortcodeReplacerService = app()->make(ShortcodeReplacerService::class);
        parent::__construct($index, $now, $storedWorkflow, $arguments);
    }

    /**
     * @return string
     */
    static function getId(): string
    {
        return BillingPolicyActionType::SEND_EMAIL_NOTIFICATION->value;
    }

    /**
     * @return string
     */
    static function getName(): string
    {
        return 'Send email notification';
    }

    /**
     * @param ActivityArgument $activityArgument
     * @return array
     * @throws Exception
     */
    public function run(ActivityArgument $activityArgument): array
    {
        [$invoiceUuid] = getValueCaseInsensitive('invoice_uuid', $activityArgument->getContext());
        [$emailTemplateId] = getValueCaseInsensitive('email_template_id', $activityArgument->getActionData());
        [$targetTypes] = getValueCaseInsensitive('target_types', $activityArgument->getActionData());

        if (empty($targetTypes)) {
            $this->log(
                message: "No target types found",
                level  : BillingLogLevel::ERROR,
                context: [
                    'context'     => $activityArgument->getContext(),
                    'action_data' => $activityArgument->getActionData(),
                ]
            );

            return [];
        }

        $emailTemplate = EmailTemplate::query()
            ->where(EmailTemplate::FIELD_ID, $emailTemplateId)
            ->firstOrFail();

        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($invoiceUuid);

        foreach ($targetTypes as $targetType) {
            try {
                $this->getEmailAddressesAndSendEmails(
                    invoice      : $invoice,
                    emailTemplate: $emailTemplate,
                    targetType   : $targetType,
                );
            } catch (Throwable $exception) {
                $this->log(
                    message: "Failed to send email to target type $targetType",
                    context: [
                        'exception' => $exception->getMessage(),
                    ]
                );
            }
        }

        return [];
    }

    /**
     * @param string $type
     * @param Invoice $invoice
     * @return Collection
     * @throws BindingResolutionException
     */
    public function getEmailAddressesByType(string $type, Invoice $invoice): Collection
    {
        return match ($type) {
            'company_billing_contacts' => $this->getTargetCompanyUserEmails($invoice->company),
            'business-development-manager',
            'customer-success-manager',
            'account-manager',
            'account_manager',
            'business_development_manager',
            'customer_success_manager'
            => $this->getTargetCompanyManagerEmails($invoice->company, $type),
            default => throw new Exception("Type not supported $type")
        };
    }

    /**
     * @param Invoice $invoice
     * @param EmailTemplate $emailTemplate
     * @param string $targetType
     * @return array|void
     * @throws BindingResolutionException
     */
    public function getEmailAddressesAndSendEmails(
        Invoice $invoice,
        EmailTemplate $emailTemplate,
        string $targetType
    )
    {
        $targetCompanyEmails = $this->getEmailAddressesByType(
            type   : $targetType,
            invoice: $invoice
        )->filter()->unique();

        if ($targetCompanyEmails->isEmpty()) {
            $this->log(
                message: "No emails found for " . $invoice->company->id,
                level  : BillingLogLevel::WARNING,
                context: [
                    'type'       => $targetType,
                    'company_id' => $invoice->company->id,
                    'invoice_id' => $invoice->id,
                ]
            );

            return [];
        }

        /** @var EmailTemplateService $emailTemplateService */
        $emailTemplateService = app()->make(EmailTemplateService::class);

        ['body' => $emailBody, 'subject' => $emailSubject] = $this->compileEmailContent(
            invoice      : $invoice,
            emailTemplate: $emailTemplate
        );

        $attachments = $this->getEmailAttachments(
            emailTemplate: $emailTemplate,
            invoice      : $invoice,
        );

        $invoiceTemplate = $this->invoicePdfTemplateService->getInvoiceTemplate($invoice);

        $this->sendEmails(
            body               : $emailTemplateService->convertTemplateMarkdownToHtml($emailBody),
            subject            : $emailSubject,
            fromEmail          : Arr::get($invoiceTemplate->{InvoiceTemplate::FIELD_PROPS}, 'support_email'),
            targetCompanyEmails: $targetCompanyEmails,
            context            : [
                'target_type' => $targetType,
                'company_id'  => $invoice->company->id,
                'invoice_id'  => $invoice->id,
            ],
            attachments        : $attachments
        );
    }

    /**
     * @param EmailTemplate $emailTemplate
     * @param Invoice $invoice
     * @return Collection
     */
    public function getEmailAttachments(
        EmailTemplate $emailTemplate,
        Invoice $invoice,
    ): Collection
    {
        $templateAttachments = $emailTemplate->payload->getAttachments();

        $attachments = collect();

        // One day move this guy to a class
        foreach ($templateAttachments as $attachment) {
            $type = EmailTemplateAttachmentType::tryFrom($attachment);

            if (!$type) {
                continue;
            }

            $class = EmailAttachmentFactory::make($type, collect([
                'invoice' => $invoice
            ]));

            if (!$class) {
                continue;
            }

            $attachments->add($class->getEmailAttachment());
        }

        return $attachments;
    }

    /**
     * @param string $body
     * @param string $subject
     * @param string $fromEmail
     * @param Collection $targetCompanyEmails
     * @param array|null $context
     * @param Collection $attachments
     * @return void
     */
    public function sendEmails(
        string $body,
        string $subject,
        string $fromEmail,
        Collection $targetCompanyEmails,
        ?array $context = [],
        Collection $attachments = new Collection()
    ): void
    {
        foreach ($targetCompanyEmails as $targetCompanyEmail) {
            try {
                $this->log(
                    message: "Sending email to $targetCompanyEmail subject $subject",
                    context: [
                        ...$context,
                        "to_email"   => $targetCompanyEmail,
                        "from_email" => $fromEmail,
                        "subject"    => $subject,
                    ]
                );

                $this->configurableEmailSenderService->send(
                    toEmail     : $targetCompanyEmail,
                    fromEmail   : $fromEmail,
                    subject     : $subject,
                    body        : $body,
                    fromUserName: 'Solarreviews',
                    attachments : $attachments
                );
            } catch (Exception $exception) {
                $this->log(
                    message: $exception->getMessage(),
                    level  : BillingLogLevel::ERROR,
                    context: [
                        'target_company_email' => $targetCompanyEmail,
                        'subject'              => $subject,
                    ]
                );
            }
        }
    }

    /**
     * @param Invoice $invoice
     * @param EmailTemplate $emailTemplate
     * @return array
     * @throws Exception
     */
    protected function compileEmailContent(Invoice $invoice, EmailTemplate $emailTemplate): array
    {
        $useCase = new InvoiceEmailShortcodeUseCase(
            invoice: $invoice,
        );

        $compiled = $useCase->compile()->toArray();

        return [
            'body'    => $this->shortcodeReplacerService->process($emailTemplate->content, $compiled),
            'subject' => $this->shortcodeReplacerService->process($emailTemplate->subject, $compiled),
        ];
    }

    /**
     * @param Company $company
     * @param string $type
     * @return Collection
     * @throws Exception
     */
    protected function getTargetCompanyManagerEmails(Company $company, string $type): Collection
    {
        $type = match ($type) {
            'account_manager', 'account-manager' => 'account-manager',
            'business_development_manager', 'business-development-manager' => 'business-development-manager',
            'customer_success_manager', 'customer-success-manager' => 'customer-success-manager',
            default => throw new Exception("Type not supported $type"),
        };

        return collect($company->currentlyAssigned($type)?->email)->filter();
    }

    /**
     * @param Company $company
     * @return Collection
     * @throws BindingResolutionException
     */
    protected function getTargetCompanyUserEmails(Company $company): Collection
    {
        /** @var CompanyNotificationRepository $companyNotificationRepository */
        $companyNotificationRepository = app()->make(CompanyNotificationRepository::class);

        return $companyNotificationRepository->getNotifiableCompanyEmails(
            company: $company,
            type   : CompanyNotificationType::INVOICES
        );
    }
}
