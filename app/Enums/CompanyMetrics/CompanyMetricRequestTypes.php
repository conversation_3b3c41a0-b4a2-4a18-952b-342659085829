<?php

namespace App\Enums\CompanyMetrics;

use App\Http\Resources\CompanyMetrics\CompanyPaidPerClickMetricResource;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use phpDocumentor\Reflection\Types\Self_;

enum CompanyMetricRequestTypes: string
{
    case PPC_SPEND = 'ppc-spend';
    case TRAFFIC_SUMMARY = 'traffic-summary';
    case PERMIT_METRICS = 'permit-metrics';
    case CLASSIFICATION_METRICS = 'classification-metrics';

    /**
     * @return string
     */
    public function getResource(): string
    {
        return match ($this) {
            self::PPC_SPEND => CompanyPaidPerClickMetricResource::class,
        };
    }

    /**
     * @param Collection $collection
     * @return AnonymousResourceCollection
     */
    public function getResourceCollection(Collection $collection): AnonymousResourceCollection
    {
        return match ($this) {
            self::PPC_SPEND => CompanyPaidPerClickMetricResource::collection($collection)
        };
    }
}
