<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Http\Requests\Dashboard\StoreNotificationSettingsRequest;
use App\Http\Resources\Dashboard\CompanyNotificationSettingsResource;
use App\Http\Resources\Dashboard\LegacyTransformers\NotificationSettingLegacyTransformer;
use App\Models\CompanyNotificationSetting;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\CompanyNotificationSettingsRepository;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Legacy\APIConsumer;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Exception;

class NotificationSettingsController extends BaseDashboardApiController
{
    const RESPONSE_STATUS   = 'status';
    const RESPONSE_MESSAGE  = 'message';
    const RESPONSE_NOTIFICATION_SETTINGS = 'notification_settings';

    const ROUTE_INTEGRATION_PREFIX  = '/fixr-dashboard';
    const ROUTE_INTEGRATION_SUFFIX  = '/notification-settings';

    public function __construct(
        Request              $request,
        DashboardAuthService $authService,
        DashboardJWTService  $jwtService,
        protected APIConsumer $integrationApiConsumer,
        protected NotificationSettingLegacyTransformer $legacyTransformer,
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    private function getIntegrationRoute(Company $company, ?string $manualSuffix = null): string
    {
        $suffix = $manualSuffix ?? self::ROUTE_INTEGRATION_SUFFIX;
        return self::ROUTE_INTEGRATION_PREFIX."/$company->reference".$suffix;
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function getNotificationSettings(): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        return $this->formatResponse([
            self::RESPONSE_STATUS                => true,
            self::RESPONSE_NOTIFICATION_SETTINGS => CompanyNotificationSettingsResource::collection(
                $company->companyNotificationSettings()
                    ->where(CompanyNotificationSetting::FIELD_NOTIFIABLE_TYPE, CompanyUser::class)
                    ->get()
            ),
        ]);
    }

    /**
     * @param StoreNotificationSettingsRequest $request
     * @param CompanyNotificationSettingsRepository $repository
     *
     * @return JsonResponse
     */
    public function updateNotificationSettings(StoreNotificationSettingsRequest $request, CompanyNotificationSettingsRepository $repository): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $repository->updateCompanyNotificationSettings($request->safe()->toArray(), $company);

        return $this->formatResponse([
            self::RESPONSE_STATUS => true
        ]);
    }

}
