<?php

namespace App\Http\Resources\Dashboard;

use App\Models\CompanyNotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

/**
 * @mixin CompanyNotificationSetting
 */
class CompanyNotificationSettingsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'company_id' => $this->company->id,
            'company_user_id' => $this->notifiable->id,
            'name' => $this->notifiable->completeName(),
            'email' => $this->notifiable->email,
            'cell_phone' => $this->notifiable->cell_phone,
            'office_phone' => $this->notifiable->office_phone,
            'alerts' => $this->alerts,
        ];
    }

    /**
     * @param Collection $resource
     *
     * @return AnonymousResourceCollection
     */
    public static function collection($resource): AnonymousResourceCollection
    {
        $resource = $resource->groupBy(CompanyNotificationSetting::FIELD_NOTIFIABLE_ID)
            ->map(function (Collection $settings) {
                $first = $settings->first();

                $first->alerts = $settings->map(fn(CompanyNotificationSetting $notificationSetting) => [
                    'type' => $notificationSetting->notification_type->value,
                    'method' => $notificationSetting->notification_method->value,
                ]);

                return $first;
            });

        return parent::collection($resource);
    }
}
