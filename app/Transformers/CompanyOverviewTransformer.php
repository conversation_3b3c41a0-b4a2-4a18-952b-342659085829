<?php

namespace App\Transformers;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanySalesStatus;
use App\Models\Legacy\EloquentReview;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\CompanyAccountManagerRepository;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\Legacy\CompanyRepository;
use App\Repositories\Legacy\ReviewRepository;
use App\Services\UserAuthorizationService;
use App\Transformers\Odin\IndustryTransformer;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Repositories\Legacy\QuoteCompanyRepository;

class CompanyOverviewTransformer
{

    /** @var ComputedRejectionStatisticRepository $computedRejectionStatisticRepository */
    protected ComputedRejectionStatisticRepository $computedRejectionStatisticRepository;

    /** @var ReviewRepository $reviewRepository */
    protected ReviewRepository $reviewRepository;

    /** @var QuoteCompanyRepository $companyQuoteRepository */
    protected QuoteCompanyRepository $companyQuoteRepository;

    /** @var CompanyRepository $companyRepository */
    protected CompanyRepository $companyRepository;

    /**
     * @param ComputedRejectionStatisticRepository $computedRejectionStatisticRepository
     * @param ReviewRepository $reviewRepository
     * @param QuoteCompanyRepository $companyQuoteRepository
     * @param CompanyRepository $companyRepository
     * @param IndustryTransformer $industryTransformer
     * @param CompanyAccountManagerRepository $accountManagerRepository
     * @param UserAuthorizationService $userAuthorizationService
     */
    public function __construct(
        ComputedRejectionStatisticRepository $computedRejectionStatisticRepository,
        ReviewRepository $reviewRepository,
        QuoteCompanyRepository $companyQuoteRepository,
        CompanyRepository $companyRepository,
        protected IndustryTransformer $industryTransformer,
        protected CompanyAccountManagerRepository $accountManagerRepository,
        protected UserAuthorizationService $userAuthorizationService
    )
    {
        $this->computedRejectionStatisticRepository  = $computedRejectionStatisticRepository;
        $this->reviewRepository = $reviewRepository;
        $this->companyQuoteRepository = $companyQuoteRepository;
        $this->companyRepository = $companyRepository;
    }

    /**
     * @param EloquentCompany $company
     * @return array
     */
    public function transformCompanyOverview(EloquentCompany $company): array
    {
        $latestQuote = $this->companyQuoteRepository->getMostRecentQuoteCompanyByCompanyId($company->{EloquentCompany::ID});
        $latestReview = $this->reviewRepository->getLatestApprovedReviewForCompany($company->{EloquentCompany::ID});

        /** @var Company|null $miCompany */
        $miCompany = Company::query()->where(Company::FIELD_LEGACY_ID, $company->companyid)->firstOrFail();

        $accountManager = $miCompany->accountManager;

        $fistLocation = $miCompany->locations->first();

        return [
            'id'                   => $company->{EloquentCompany::ID},
            'legacy_id'            => $miCompany->legacy_id,
            'mi_id'                => $miCompany->id,
            'name'                 => $company->{EloquentCompany::COMPANY_NAME},
            'website'              => $company->{EloquentCompany::WEBSITE},
            'phone'                => $fistLocation?->phone,
            'phone_location_id'    => $fistLocation?->id,
            'prescreened'          => $company->isPrescreeed(),
            'status'               => CompanyConsolidatedStatus::label($miCompany->consolidated_status),
            'sales_status'         => CompanySalesStatus::label($miCompany->sales_status),
            'type'                 => $company->{EloquentCompany::TYPE},
            'buying_leads'         => $company->{EloquentCompany::BUYING_SOLAR_ESTIMATE_LEADS} === $company->{EloquentCompany::BUYING_LEADS_STATUS_ACTIVE},
            'lead_rejection_percentage' => number_format($this->computedRejectionStatisticRepository->getCompanyLeadRejectionPercentage($miCompany), 2),
            'appointment_rejection_percentage' => '0%', // todo: remove from frontend
            'date_registered'      => Carbon::parse($company->{EloquentCompany::TIMESTAMP_ADDED})->format('Y-m-d'),
            'total_spend'          => '$' . number_format(round($this->companyRepository->getCompanyTotalSpend($company->{EloquentCompany::ID}), 2), 2),
            'last_quote'           => $latestQuote ? Carbon::parse($latestQuote->{EloquentQuoteCompany::TIMESTAMP_DELIVERED})->format('Y-m-d') : "Never",
            'last_review'          => $latestReview ? Carbon::parse($latestReview->{EloquentReview::FIELD_TIMESTAMP_ADDED})->format('Y-m-d') : "Never",
            'image'                => $miCompany->link_to_logo,
            'industries'           => $this->industryTransformer->transform($miCompany->industries),
            'success_manager'      => [
                "id"   => $miCompany->successManagerClient()?->success_manager_id,
                "name" => $miCompany->successManagerClient()?->successManager?->user?->name
            ],
            'business_development_manager' => [
                "id"   => $miCompany->businessDevelopmentManager?->id,
                "name" => $miCompany->businessDevelopmentManager?->name
            ],
            'onboarding_manager' => [
                "id"   => $miCompany->onboardingManager?->id,
                "name" => $miCompany->onboardingManager?->name
            ],
            'sales_development_representative' => [
                "id"   => $miCompany->salesDevelopmentRepresentative?->id,
                "name" => $miCompany->salesDevelopmentRepresentative?->name
            ],
            'account_manager' => [
                "id"   => $miCompany->accountManager?->id,
                "name" => $miCompany->accountManager?->name
            ],
            'main_office_location' => $miCompany->locations->first()?->address?->full_address ?? '',
            'classification' => $miCompany->latestCompanyClassificationMetrics?->rating ?? null,
        ];
    }
}
