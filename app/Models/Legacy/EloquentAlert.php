<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @deprecated
 */
class EloquentAlert extends LegacyModel
{
    const string TABLE = 'tblalert';

    protected $table = self::TABLE;

    const string FIELD_ALERT_ID        = 'alertid';
    const string FIELD_COMPANY_ID      = 'companyid';
    const string FIELD_CONTACT_ID      = 'contactid';
    const string FIELD_TYPE            = 'type';
    const string FIELD_METHOD          = 'method';

    protected $guarded = [
        self::FIELD_ALERT_ID
    ];

    public $timestamps = false;

    /**
     * @return BelongsTo
     */
    public function eloquentInvoice(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, EloquentCompany::ID, self::FIELD_COMPANY_ID);
    }

    /**
     * @return BelongsTo
     */
    public function eloquentContact(): BelongsTo
    {
        return $this->belongsTo(EloquentCompanyContact::class, EloquentCompanyContact::FIELD_CONTACT_ID, self::FIELD_CONTACT_ID);
    }
}
