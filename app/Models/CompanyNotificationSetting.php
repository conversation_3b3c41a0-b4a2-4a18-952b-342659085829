<?php

namespace App\Models;

use App\Enums\NotificationMethod;
use App\Enums\CompanyNotificationType;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property int $company_id
 * @property string $notifiable_type
 * @property int $notifiable_id
 * @property CompanyNotificationType $notification_type
 * @property NotificationMethod $notification_method
 *
 * @property-read Company $company
 * @property-read CompanyUser|mixed $notifiable
 */
class CompanyNotificationSetting extends Model
{
    use LogsActivity;

    const string TABLE = 'company_notification_settings';

    const string FIELD_ID = 'id';
    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_NOTIFICATION_TYPE = 'notification_type';
    const string FIELD_NOTIFICATION_METHOD = 'notification_method';
    const string FIELD_NOTIFIABLE_TYPE = 'notifiable_type';
    const string FIELD_NOTIFIABLE_ID = 'notifiable_id';

    const string RELATION_NOTIFIABLE = 'notifiable';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts =[
        self::FIELD_NOTIFICATION_TYPE => CompanyNotificationType::class,
        self::FIELD_NOTIFICATION_METHOD => NotificationMethod::class
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                self::FIELD_COMPANY_ID,
                self::FIELD_NOTIFIABLE_TYPE,
                self::FIELD_NOTIFIABLE_ID,
                self::FIELD_NOTIFICATION_TYPE,
                self::FIELD_NOTIFICATION_METHOD
            ])
            ->useLogName('company_notification_settings')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
