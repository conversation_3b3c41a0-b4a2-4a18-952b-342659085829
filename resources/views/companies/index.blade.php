<x-app-layout title="{{ data_get($company, 'name') ? 'Company: ' . $company['name'] : 'Company' }}">
    @push('head_scripts')
        <link href="https://assets.calendly.com/assets/external/widget.css" rel="stylesheet">
        <script src="https://assets.calendly.com/assets/external/widget.js" type="text/javascript" async></script>
    @endpush
        @if (session('status'))
            <div id="flash-message" class="fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-100 text-green-800 px-4 py-2 rounded shadow z-50">
                {{ session('status') }}
            </div>
            <script>
                setTimeout(() => {
                    const flash = document.getElementById('flash-message');
                    if (flash) flash.style.display = 'none';
                }, 4000);
            </script>
        @endif
    <company-page
        :dark-mode="darkMode"
        :init-company="{{ json_encode($company) }}"
        :account-managers="{{ isset($accountManagers) ? json_encode($accountManagers): 'null' }}"
        :success-managers="{{  isset($accountManagers) ? json_encode($successManagers): 'null' }}"
        :business-development-managers="{{ isset($businessDevelopmentManagers) ? json_encode($businessDevelopmentManagers): 'null' }}"
        :onboarding-managers="{{ isset($onboardingManagers) ? json_encode($onboardingManagers): 'null' }}"
        :sales-development-representatives="{{ isset($salesDevelopmentRepresentatives) ? json_encode($salesDevelopmentRepresentatives): 'null' }}"
        :tab="{{ isset($tab) ? json_encode($tab) : 'null' }}"
        :industries="{{ json_encode($industries) }}"
        :current-user-id="{{\Illuminate\Support\Facades\Auth::user()->id}}"
        :campaign-activity-log-relation-class="'{{str_replace( '\\', '\\\\', \App\Models\Campaigns\CompanyCampaign::class)}}'"
        :action-categories="{{ json_encode($actionCategories) }}"
        :admin-statuses="{{ json_encode($adminStatuses) }}"
    >
    </company-page>
</x-app-layout>
