import {defineStore} from "pinia";
import {PERMISSIONS, useRolesPermissions} from "./roles-permissions.store.js";
import {useCookies} from "../composables/useCookies.js";
import {ref} from "vue";


export const usePiiDataStore = defineStore('pii-data',
    () => {
        const PII_DATA_TOKEN = 'pii-token';
        const DEFAULT_PII_DATA_EXPIRY_IN_MILLI = 60 * 1000; // 60s

        const loading = ref(false)

        const permissionStore = useRolesPermissions()

        const cookies = useCookies()

        const canViewPiiData = () => {
            return permissionStore.hasPermission(PERMISSIONS.PERMISSION_VIEW_LEAD_PII) || cookies.getCookie(PII_DATA_TOKEN)
        }

        const addPiiTokenToCookiesAndReloadPage = () => {
            cookies.setCookie(PII_DATA_TOKEN, true, DEFAULT_PII_DATA_EXPIRY_IN_MILLI)
            loading.value = true
            window.location.reload()
        }

        return {
            canViewPiiData,
            addPiiTokenToCookiesAndReloadPage,
            loading
        }
    });
