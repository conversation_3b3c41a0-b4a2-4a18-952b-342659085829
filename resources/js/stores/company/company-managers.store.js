import {defineStore} from "pinia";
import {ref} from "vue";

export const useCompanyManagersStore = defineStore('company-managers', () => {
    const accountManagers = ref([]);
    const businessDevelopmentManagers = ref([]);
    const onboardingManagers = ref([]);
    const customerSuccessManagers = ref([]);

    const initialize = (ams, bdms, oms, csms) => {
        accountManagers.value = ams;
        businessDevelopmentManagers.value = bdms;
        onboardingManagers.value = oms;
        customerSuccessManagers.value = csms;
    }

    return {
        accountManagers,
        businessDevelopmentManagers,
        onboardingManagers,
        customerSuccessManagers,
        initialize
    }
});
