import {define<PERSON><PERSON>} from "pinia";
import {computed, ref} from "vue";
import {filterContacts as searchContacts} from "../modules/contacts/filter/multi-search";
import {filterContacts} from "../modules/contacts/filter/filter";
import {isObject} from "../modules/helpers";

const contactsFilterVar = ref(
    {
        phone: {
            presence: {
                options: [
                    {
                        id: null,
                        name: "Select presence",
                    },
                    {
                        id: "exists",
                        name: "Exists",
                    },
                    {
                        id: "doesNotExist",
                        name: "Does Not Exist",
                    }
                ],
                selected: null,
            }
        },
        email: {
            presence: {
                options: [
                    {
                        id: null,
                        name: "Select presence",
                    },
                    {
                        id: "exists",
                        name: "Exists",
                    },
                    {
                        id: "doesNotExist",
                        name: "Does Not Exist",
                    }
                ],
                selected: null,
            }
        },
        lastContactedAt: {
            active: false,
            /**
             * @type {number|string}
             */
            value: 0,
            operator: {
                options: [

                    {
                        id: "within",
                        name: "Within",
                    },
                    {
                        id: "longerThan",
                        name: "Longer Than",
                    },
                ],
                selected: "within"
            },
            cadence: {
                selected: "days"
            }
        },
    }
);

const contactVar = ref({
    first_name: undefined,
    last_name: undefined,
    title: undefined,
    email: undefined,
    office_phone: undefined,
    cell_phone: undefined,
    total_calls_count: undefined,
    total_calls_over_one_minute_count: undefined,
    last_called_timestamp: undefined,
    user_fields: undefined,
});

export const useContactsStore = defineStore('contacts',
    () => {
        const contacts = ref([]);

        const recordings = ref([]);

        const clonedContacts = ref([]);

        const contact = ref(contactVar);

        const contactMultiSearch = ref("");

        const contactsFilter = ref(
            contactsFilterVar
        );

        const activeFilters = ref(0);

        const calculateActiveFilters = () => {
            const phone = phonePresence.value;
            const email = emailPresence.value;

            const phonePresenceIsActive = isObject(phone) ? (phone?.id ?? null) !== null : phone !== null;
            const emailPresenceIsActive = isObject(email) ? (email?.id ?? null) !== null : email !== null;

            const activeArray = [phonePresenceIsActive, emailPresenceIsActive, lastContactedAtIsActive.value];

            activeFilters.value = activeArray.filter(x => x).length;
        }

        const setRecordings = (newRecordings) => {
            recordings.value = newRecordings;
        };


        const phonePresence = computed(() => {
            return contactsFilter?.value?.phone?.presence?.selected ?? null;
        });

        const emailPresence = computed(() => {
            return contactsFilter?.value?.email?.presence?.selected ?? null;
        });

        const lastContactedAtIsActive = computed(() => {
            return contactsFilter.value.lastContactedAt.active;
        })

        const applyFilterToContacts = () => {
            let result = contacts.value;

            if (contactMultiSearch.value) {
                result = searchContacts(result, contactMultiSearch.value);
            }

            if (phonePresence.value || emailPresence.value || lastContactedAtIsActive.value) {
                const lastContactedAtOperator = contactsFilter.value?.lastContactedAt?.operator?.selected ?? null;
                const lastContactedAtCadence = contactsFilter.value?.lastContactedAt?.cadence?.selected ?? null;
                const lastContactedAtInput = contactsFilter.value?.lastContactedAt?.value ?? null;

                const lastContactedAt = {
                    operator: lastContactedAtOperator,
                    cadence: lastContactedAtCadence,
                    number: lastContactedAtInput,
                    active: lastContactedAtIsActive.value
                }

                /**
                 * @type {number}
                 */
                const lastContactedAtNumber = typeof lastContactedAt.number === 'string' ? Math.abs(parseInt(lastContactedAtInput)) : lastContactedAtInput;

                if (lastContactedAtNumber === undefined || isNaN(lastContactedAtNumber)) {
                    lastContactedAt.number = 0;
                    contactsFilter.value.lastContactedAt.value = lastContactedAtNumber;
                } else {
                    lastContactedAt.number = lastContactedAtNumber;
                    contactsFilter.value.lastContactedAt.value = lastContactedAtNumber;
                }

                const phone = isObject(phonePresence.value) ? (phonePresence.value?.id ?? null) : phonePresence.value;
                const email = isObject(emailPresence.value) ? (emailPresence.value?.id ?? null) : emailPresence.value;

                result = filterContacts(result, phone, email, lastContactedAt)
            }

            clonedContacts.value = result;

            calculateActiveFilters();
        }

        const lastContactedAtCadenceOptions = computed(() => {
            const value = contactsFilter.value.lastContactedAt.value;
            const parsedValue = parseInt(value);
            const valueEqualsOne = parsedValue === 1;
            return [
                {
                    id: "days",
                    name: valueEqualsOne ? "Day" : "Days",
                },
                {
                    id: "weeks",
                    name: valueEqualsOne ? "Week" : "Weeks",
                },
                {
                    id: "months",
                    name: valueEqualsOne ? "Month" : "Months",
                },
                {
                    id: "years",
                    name: valueEqualsOne ? "Year" : "Years",
                },
            ]
        });

        return {
            contacts,
            recordings,
            clonedContacts,
            contact,
            contactMultiSearch,
            contactsFilter,
            applyFilterToContacts,
            setRecordings,
            activeFilters,
            lastContactedAtCadenceOptions,
        }
    },
    {
        persist: true,
    }
)

