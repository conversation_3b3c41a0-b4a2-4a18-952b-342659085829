import {defineStore} from "pinia";
import {ref} from "vue";
import {ApiFactory} from "../../vue/components/Mailbox/services/api/factory";
import {useMailboxComposeEmailStore} from "./composeEmail";
import useSimpleIcon from "../../composables/useSimpleIcon";
import useQueryParams from "../../composables/useQueryParams";

const queryParams = useQueryParams();

export const useMailboxStore = defineStore('mailbox',
    () => {
        const isMailboxSyncing = ref(false);
        const paginatedEmailsResponse = ref(null);
        const selectedCompanyId = ref(null);
        const errorMessage = ref('');
        const api = ApiFactory.makeApiService('api');
        const loadingEmailsPaginated = ref(false)
        const selectedEmail = ref(null)
        const selectedEmailIdx= ref(null)
        const selectedTab = ref('inbox')
        const userEmailSignature = ref(null)
        const compactList = ref(false)
        const deleteWholeThread = ref(false)
        const bulkSelect = ref([])
        const composeEmailStore = useMailboxComposeEmailStore();
        const simpleIcon = useSimpleIcon();
        const emailModificationActions = {
            READ       : 'read',
            UNREAD     : 'unread',
            STAR       : 'star',
            UNSTAR     : 'unstar',
            ARCHIVE    : 'archive',
            UNARCHIVE  : 'unarchive',
            IMPORTANT  : 'important',
            UNIMPORTANT: 'unimportant',
            DELETE     : 'delete',
        }

        const listFilters = ref({
            tab: selectedTab,
            query: null,
            uuid: null,
        })

        const showCreateTaskModal = ref(false)
        const showAddNoteModal = ref(false)
        const showEmailDeletionModal = ref(false)

        const noteRelationId = ref(null)
        const taskNote = ref(null)

        const listEmails = async (page = 1, perPage = 25) => {
            loadingEmailsPaginated.value = true
            try {
                const response = await api.listEmails({...listFilters.value, page, perPage, companyId: selectedCompanyId.value})
                paginatedEmailsResponse.value = response.data.data.emails

                if (isMailboxSyncing.value) {
                    isMailboxSyncing.value = !(paginatedEmailsResponse.value.data.length > 0)
                }
            } catch (error) {
                errorMessage.value = error.response.data.error
                paginatedEmailsResponse.value = []
            }

            loadingEmailsPaginated.value = false
        }

        const setSelectedEmail = (email) => {
            selectedEmail.value = email
        }

        const deleteEmail = async (emailId) => {
            await api.deleteEmails([emailId], deleteWholeThread.value)
            setCompactListFalse();
            await listEmails(1);
            setSelectedEmail(null)
        }

        const resetSearch = async () => {
            listFilters.value = {
                tab: 'inbox',
                page: 1,
                perPage: 50,
                query: null,
                uuid: null,
            }

            await listEmails(1)
        }

        const selectAllToggle = () => {
            if (bulkSelect.value.length === paginatedEmailsResponse.value.data.length) {
                bulkSelect.value = [];
            } else {
                bulkSelect.value = [];
                paginatedEmailsResponse.value.data.forEach((email) => {
                    bulkSelect.value.push(email.uuid)
                })
            }
        }

        const setCompactListTrue = () => {
            compactList.value = true
        }

        const setCompactListFalse = () => {
            compactList.value = false
        }

        const getUserEmailSignature = async () => {
            const res = await api.getUserEmailSignature()
            userEmailSignature.value = res.data.data.signature
        }

        const getUrlToUserGrantAccess = async ()  => {
            const response = await api.getPermission();
            if (!response.data.data.has_token && response.data.data.url) {
                return response.data.data.url;
            }
        }

        const modifyEmails = async (emailIds, action) => {
            if (!Object.values(emailModificationActions).includes(action))
                throw new Error(`Email modification action ${action} not implemented`)

            await api.modifyEmails(emailIds, action)

            paginatedEmailsResponse.value.data.forEach((email) => {
                if (emailIds.includes(email.uuid)) {
                    if (action === emailModificationActions.READ) email.is_read = true
                    if (action === emailModificationActions.UNREAD) email.is_read = false

                    if (action === emailModificationActions.ARCHIVE) {
                        email.is_archived = true
                        email.is_inbox = false
                    }

                    if (action === emailModificationActions.UNARCHIVE) {
                        email.is_archived = false
                        email.is_inbox = true
                    }

                    if (action === emailModificationActions.IMPORTANT) email.is_important = true
                    if (action === emailModificationActions.UNIMPORTANT) email.is_important = false

                    if (action === emailModificationActions.STAR) email.is_starred = true
                    if (action === emailModificationActions.UNSTAR) email.is_starred = false
                }
            })
        }

        const toggleRead = (email, value) => {
            return modifyEmails([email.uuid], !value ? emailModificationActions.READ : emailModificationActions.UNREAD)
        }

        const toggleArchive = (email, value) => {
            return modifyEmails([email.uuid], !value ? emailModificationActions.ARCHIVE : emailModificationActions.UNARCHIVE)
        }

        const toggleImportant = (email, value) => {
            return modifyEmails([email.uuid], !value ? emailModificationActions.IMPORTANT : emailModificationActions.UNIMPORTANT)
        }

        const toggleStar = (email, value) => {
            return modifyEmails([email.uuid], !value ? emailModificationActions.STAR : emailModificationActions.UNSTAR)
        }

        const toggleBatchArchive = async (anyArchived) => {
            await modifyEmails(bulkSelect.value, anyArchived ? emailModificationActions.UNARCHIVE : emailModificationActions.ARCHIVE)
            listEmails()
            bulkSelect.value = []
        }

        const toggleBatchRead = (anyUnread) => {
            modifyEmails(bulkSelect.value, anyUnread ? emailModificationActions.READ : emailModificationActions.UNREAD)
            bulkSelect.value = []
        }

        const toggleBatchDelete = async () => {
            await api.deleteEmails(bulkSelect.value);
            listEmails();
            bulkSelect.value = []
        }



        const getEmail = (uuid) => paginatedEmailsResponse.value.data.find(e => e.uuid === uuid)

        const getBatchEmailActions = () => {


            const anyUnread = bulkSelect.value.some((uuid) => !getEmail(uuid).is_read)
            const anyArchived = bulkSelect.value.some((uuid) => getEmail(uuid).is_archived)

            return [
                {
                    callback: () => toggleBatchArchive(anyArchived),
                    icon: {
                        icon: anyArchived ? simpleIcon.icons.ARCHIVE_BOX_X_MARK : simpleIcon.icons.ARCHIVE_BOX_ARROW_DOWN,
                    },
                    name: anyArchived ? 'Unarchive All' : 'Archive All',
                },
                {
                    callback: () => toggleBatchDelete(),
                    icon: {
                        icon: simpleIcon.icons.BIN,
                    },
                    name: 'Delete All'
                },
                {
                    callback: () => toggleBatchRead(anyUnread),
                    icon: {
                        icon: anyUnread ? simpleIcon.icons.ENVELOPE : simpleIcon.icons.ENVELOPE_OPEN
                    },
                    name: anyUnread ? 'Mark all read' : 'Mark all unread'
                }

            ]
        }

        const getEmailActions = (userEmail, deleteThread = false, fromEmailData = false) => {
            return [
                {
                    callback: () => composeEmailStore.reply(userEmail, 'fixed'),
                    icon: {
                        icon: simpleIcon.icons.ARROW_UTURN_LEFT
                    },
                    name: 'Reply',
                },
                {
                    callback: () => composeEmailStore.replyAll(userEmail, 'fixed'),
                    icon: {
                        icon: simpleIcon.icons.CURVED_DOUBLE_ARROW_LEFT
                    },
                    name: 'Reply All',
                },
                {
                    callback: () => composeEmailStore.forward(userEmail, 'fixed'),
                    icon: {
                        icon: simpleIcon.icons.ARROW_UTURN_RIGHT
                    },
                    name: 'Forward',
                },
                {
                    callback: () => toggleArchive(userEmail, userEmail.is_archived),
                    icon: {
                        icon: userEmail.is_archived ? simpleIcon.icons.ARCHIVE_BOX_X_MARK : simpleIcon.icons.ARCHIVE_BOX_ARROW_DOWN,
                    },
                    name: userEmail.is_archived ? 'Unarchive' : 'Archive',
                },
                {
                    callback: () => {
                        deleteWholeThread.value = deleteThread;
                        openDeleteEmailModal(userEmail);
                    },
                    icon: {
                        icon: simpleIcon.icons.BIN,
                    },
                    name: 'Delete'
                },
                {
                    callback: () => {
                        if (fromEmailData) {
                            toggleRead(selectedEmail.value, selectedEmail.value.is_read)
                        } else {
                            toggleRead(userEmail, userEmail.is_read)
                        }
                    },
                    icon: {
                        icon: fromEmailData ? selectedEmail.value.is_read ? simpleIcon.icons.ENVELOPE_OPEN : simpleIcon.icons.ENVELOPE : userEmail.is_read ? simpleIcon.icons.ENVELOPE_OPEN : simpleIcon.icons.ENVELOPE,
                    },
                    name: fromEmailData ? selectedEmail.value.is_read ? 'Mark as unread' : 'Mark as read' : userEmail.is_read ? 'Mark as unread' : 'Mark as read'
                },
                {
                    callback: () => openLeaveNoteModal(userEmail),
                    icon: {
                        icon: simpleIcon.icons.DOCUMENT_TEXT,
                    },
                    name: 'Leave note'
                },
            ]
        }

        const openTaskModal = (userEmail) =>{
            taskNote.value = '/mailbox' + queryParams.mountUrlWithSearchParams({uuid: userEmail.uuid})
            showCreateTaskModal.value = true
        }

        const handleCreateTaskModalClose = () =>{
            taskNote.value = null
            showCreateTaskModal.value = false
        }

        const openLeaveNoteModal = (userEmail) =>{
            noteRelationId.value = userEmail.email_id
            showAddNoteModal.value = true;
        }

        const handleAddNoteModalClose = () =>{
            noteRelationId.value = null
            showAddNoteModal.value = false;
        }

        const openDeleteEmailModal = (userEmail) => {
            selectedEmail.value = userEmail
            showEmailDeletionModal.value = true
        }

        const handleDeleteEmailConfirm = async () =>{
            await deleteEmail(selectedEmail.value.uuid);

            showEmailDeletionModal.value = false
        }

        const handleEmailSelected = (email, idx) => {
            compactList.value = true;
            selectedEmailIdx.value = idx
            setSelectedEmail(email)
            toggleRead(email, false)
        }


        return {
            selectedCompanyId,
            paginatedEmailsResponse,
            api,
            loadingEmailsPaginated,
            selectedEmail,
            selectedTab,
            userEmailSignature,
            listFilters,
            compactList,
            selectedEmailIdx,
            showCreateTaskModal,
            showAddNoteModal,
            showEmailDeletionModal,
            bulkSelect,
            errorMessage,
            isMailboxSyncing,

            noteRelationId,
            taskNote,

            resetSearch,
            listEmails,
            setSelectedEmail,
            deleteEmail,
            getUserEmailSignature,
            setCompactListFalse,
            setCompactListTrue,
            getEmailActions,
            getBatchEmailActions,
            selectAllToggle,
            openTaskModal,
            toggleRead,
            toggleArchive,
            toggleImportant,
            toggleStar,
            getUrlToUserGrantAccess,

            handleCreateTaskModalClose,
            handleAddNoteModalClose,
            handleDeleteEmailConfirm,
            handleEmailSelected,
        }
    });
