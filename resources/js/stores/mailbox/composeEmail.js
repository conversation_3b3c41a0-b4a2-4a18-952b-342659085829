import {defineS<PERSON>} from "pinia";
import {ref} from "vue";
import {PERMISSIONS, useRolesPermissions} from "../roles-permissions.store";

export const useMailboxComposeEmailStore = defineStore('mailbox-compose-email',
    () => {
        const position = ref('closed');
        const to        = ref([]);
        const content   = ref('');
        const subject   = ref('');
        const cc        = ref([]);
        const bcc       = ref([]);
        const type      = ref(null);
        const replyTo   = ref(null);
        const uniqueIdx = ref(null);
        const possibleContacts      = ref([]);

        const permissionStore = useRolesPermissions();

        const resetComposeEmailData = () => {
            to.value = [];
            content.value = '';
            subject.value = '';
            cc.value = [];
            bcc.value = [];
            type.value = null;
            replyTo.value = null;
            uniqueIdx.value = null;
        }

        const forward = (email, composePosition = 'embedded', idx = null) => {
            resetComposeEmailData();
            type.value = 'forward';
            updatePosition(composePosition);
            replyTo.value = email;
            uniqueIdx.value = idx;
            let toEmails = [];

            email.to.forEach((toContacts) => {
                toEmails.push(toContacts.identifier_value)
            })

            const forwardPrefixMessage = buildForwardPrefixMessage({
                from: email.from.identifier_value,
                sentAt: email.sent_at,
                subject: email.subject,
                to: toEmails.join(', ')
            })

            content.value += forwardPrefixMessage + email.content
        }

        const updatePosition = (composePosition) => {
            if (permissionStore.hasPermission(PERMISSIONS.PERMISSION_MAILBOX_SEND_EMAILS)) {
                position.value = composePosition
            } else {
                console.error('User does not have permission to compose email.');
            }
        }

        const getPosition = () => {
            return position.value;
        }

        const buildForwardPrefixMessage = ({from, sentAt, subject, to}) => {
            return `<div dir="ltr">
                    	<div className="gmail_quote">
                    		<div dir="ltr" className="gmail_attr">
                    			---------- Forwarded message ---------
                    			<br />
                    			From:
                    			<span dir="auto"><a href="mailto:${from}">${from}</a></span>
                    			<br />
                    			Date: ${formatDate(sentAt)}<br />
                    			Subject: ${subject}
                    			<br />To: <a href="mailto:${to}">${to}</a>
                    			<br />
                    		</div>
                    		<br />
                    		<br />
                    		<div><u></u></div>
                    	</div>
                    </div>`;
        }

        const formatDate = (inputDate) => {
            try {
                const dateObj = new Date(inputDate);

                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                const dayIndex = dateObj.getDay();
                const dayName = daysOfWeek[dayIndex];

                // Get the day, month, and year components
                const day = dateObj.getDate();
                const month = months[dateObj.getMonth()];
                const year = dateObj.getFullYear();

                // Get the hours and minutes components
                const hours = ('0' + dateObj.getHours()).slice(-2);
                const minutes = ('0' + dateObj.getMinutes()).slice(-2);

                // Format the date in the desired format
                return `${dayName}, ${day} ${month} ${year} at ${hours}:${minutes}`;
            } catch {
                return inputDate;
            }
        }

        const reply = (email, composePosition = 'embedded', idx = null) => {
            resetComposeEmailData();
            type.value = 'reply';
            updatePosition(composePosition);
            replyTo.value = email;
            to.value = [email.from];
            subject.value = email.subject;
            uniqueIdx.value = idx
        }

        const replyAll = (email, composePosition = 'embedded', idx) => {
            resetComposeEmailData();
            // TODO - Remove the logged-in user email
            let recipients = [email.from]
            recipients.push(...email.to)
            type.value = 'reply';
            updatePosition(composePosition);
            replyTo.value = email;
            to.value = recipients;
            cc.value = email.cc;
            bcc.value = email.bcc;
            subject.value = email.subject;
            uniqueIdx.value = idx
        }

        const positions = {
            EMBEDDED: 'embedded',
            FIXED: 'fixed',
            FULL: 'full',
            MINIMISED: 'minimised',
            CLOSED: 'closed'
        }

        const positionStyle = {
            [positions.FULL]: {
                AUTO_WIDTH: '100%',
                EDITOR_HEIGHT: `100%`
            },
            [positions.FIXED]: {
                AUTO_WIDTH: '100%',
                EDITOR_HEIGHT: 350
            },
            [positions.EMBEDDED]: {
                AUTO_WIDTH: '100%',
                EDITOR_HEIGHT: 350
            },
            [positions.MINIMISED]: {
                AUTO_WIDTH: '100%',
                EDITOR_HEIGHT:  5350
            },
            [positions.CLOSED]: {
                AUTO_WIDTH: '100%',
                EDITOR_HEIGHT:  5350
            },
        }

        const getPositionStyle = (position) => {
            return positionStyle[position]
        }

        const showComposeFixed = () => {
            return [positions.FIXED, positions.FULL, positions.MINIMISED].includes(position.value)
        };

        return {
            to,
            content,
            subject,
            cc,
            bcc,
            type,
            replyTo,
            position,
            uniqueIdx,
            positions,
            positionStyle,
            possibleContacts,

            resetComposeEmailData,
            reply,
            replyAll,
            forward,
            getPositionStyle,
            showComposeFixed,
            updatePosition,
            getPosition,
        }
    });
