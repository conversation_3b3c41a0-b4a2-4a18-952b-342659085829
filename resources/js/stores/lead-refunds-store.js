import {defineStore} from "pinia";
import {ROLES, useRolesPermissions} from "./roles-permissions.store.js";

export const useLeadRefundsStore = defineStore('lead-refunds-store',
    () => {
        const permissionStore = useRolesPermissions()

        const canReviewRefund = () =>{
            return permissionStore.hasRole(ROLES.LEAD_REFUNDS_REVIEWER)
        }

        const canViewRefund = () =>{
            return permissionStore.hasAnyRole([
                ROLES.LEAD_REFUNDS_REQUESTER,
                ROLES.LEAD_REFUNDS_REVIEWER,
                ROLES.LEAD_REFUNDS_VIEWER
            ])
        }

        const canRequestRefund = () =>{
            return permissionStore.hasAnyRole([ROLES.LEAD_REFUNDS_REVIEWER, ROLES.LEAD_REFUNDS_REQUESTER])
        }

        return {
            canReviewRefund,
            canRequestRefund,
            canViewRefund
        }
    });
