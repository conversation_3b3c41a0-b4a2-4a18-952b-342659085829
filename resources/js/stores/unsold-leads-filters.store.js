import {defineStore} from "pinia";

export const useUnsoldLeadsFilters = defineStore('unsold-leads-filters', {
    state: () => {
        return {
            filters: {
                "industry": {
                    "name": "Industry",
                    "active": false,
                    "firstOperator": "Solar",
                },
                "status": {
                    "name": "Status",
                    "active": false,
                    "firstOperator": "Unsold"
                }
            },
        }
    },
    persist: true,
})
