import {defineStore} from "pinia";
import Api from "../../vue/components/Shared/services/company-credit-api";
import {computed, ref} from "vue";
import useErrorHandler from "../../composables/useErrorHandler";
export const useCompanyCreditManagementStore
    = defineStore('company-credit-management',
    () => {

        const api = Api.make();
        const balances = ref([]);
        const credits = ref([]);
        const companyId = ref({});
        const newCredit = ref({
            value: 100,
        });

        const formErrorHandler = useErrorHandler();

        const $reset = () => {
            balances.value = []
            credits.value = []
            companyId.value = {}
            newCredit.value = {
                value: 100,
            }
            formErrorHandler.resetError()
        }

        const getBalances = async (companyId, billingProfileId) => {
            try{
                const response = await api.getCreditBalances(companyId, billingProfileId);
                if (response.data.data.status) {
                    balances.value = response.data.data.balances;
                }
            } catch (error) {
                console.error(error)
            }
        }

        const getCompanyCredits = async (companyId) => {
            try{
                const response = await api.getCredits(companyId);
                if (response.data.data.status) {
                    credits.value = response.data.data.credits;
                }
            } catch (error) {
                console.error(error)
            }
        }

        const expireCredit = async (companyId, creditId) => {
            formErrorHandler.resetError();

            try{
                const response = await api.expireCredit(companyId, creditId);
                if (response.data.data.status === true) {
                    credits.value = response.data.data.data;
                }
            } catch (error) {
                formErrorHandler.handleError(error)
                console.error(error)
            }
        }

        const extendCredit = async (companyId, creditId, newDate) => {
            formErrorHandler.resetError();

            try{
                const response = await api.extendCredit(
                    companyId,
                    creditId,
                    newDate
                );
                if (response.data.data.status === true) {
                    credits.value = response.data.data.data;
                }
            } catch (error) {
                formErrorHandler.handleError(error)
                console.error(error)
            }
        }

        const applyCredit = async () => {
            formErrorHandler.resetError();
            try {
                const response = await api.applyCredit(companyId.value, newCredit.value);

                getBalances(companyId.value)

                if (response.data.data.status === true) {
                    credits.value = response.data.data.data;
                    newCredit.value = { value: 100 }
                }

                return response.data.data.message;
            } catch (error) {
                formErrorHandler.handleError(error)
                console.error(error)
                return error.response.data.message;
            }
        }

        const totalCreditsAvailable = computed(() => {
            return balances.value.reduce((prev, next) => prev + next.balance,0)
        })

        return {
            balances,
            credits,
            api,
            getBalances,
            getCompanyCredits,
            expireCredit,
            extendCredit,
            applyCredit,
            companyId,
            newCredit,
            formErrorHandler,
            totalCreditsAvailable,
            $reset
        }
});
