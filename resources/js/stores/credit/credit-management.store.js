import {defineStore} from "pinia";
import {ref} from "vue";
import Api from "../../vue/components/BillingManagement/services/credit-api";
import useEventHandler from "../../composables/useEventHandler";
import useArrayHelper from "../../composables/useArrayHelper";

export const useCreditManagementStore
    = defineStore('credit-management',
    () => {
        const eventHandler = useEventHandler();
        const creditTypes = ref([]);
        const defaultCredit = {
            active: true,
            cash: false,
            expiry: false,
        }
        const newCreditType = ref({...defaultCredit});
        const {paginateArray} = useArrayHelper()

        const api = Api.make();

        const paginatedCreditTypes = (page, perPage) => {
            return paginateArray(creditTypes.value, page, perPage)
        }

        const resetNewCreditType = () => {
            newCreditType.value = {...defaultCredit}
        }

        const changePriority = (rowIndex, direction) => {
            if (rowIndex === 0 && direction === 'increase') return
            if (rowIndex === creditTypes.value.length -1 && direction === 'decrease') return

            const credit = creditTypes.value[rowIndex]
            const newIndex = rowIndex + (direction === 'increase' ? -1 : 1);
            const otherCredit = creditTypes.value[newIndex]

            creditTypes.value[newIndex] = credit
            creditTypes.value[rowIndex] = otherCredit
        }
        const increasePriority = (rowIndex) => {
            changePriority(rowIndex, 'increase')
        }

        const decreasePriority = (rowIndex) => {
            changePriority(rowIndex, 'decrease')
        }

        const addCreditTypeAddedEventListener = (listenerId, callback) => {
            eventHandler.addListener('credit-management', listenerId, callback)
        }

        const addCreditType = async () => {
            const resp = await api.createCreditType(newCreditType.value);
            eventHandler.dispatchEvent('credit-management');
            return resp;
        }

        return {
            creditTypes,
            newCreditType,
            paginatedCreditTypes,
            resetNewCreditType,
            increasePriority,
            decreasePriority,
            addCreditTypeAddedEventListener,
            addCreditType,
            api,
        }
});
