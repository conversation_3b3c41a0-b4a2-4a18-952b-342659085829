import {defineStore} from "pinia";
import Api from "../../vue/components/Billing/services/invoice-chargeback-api.js";
import {computed, ref} from "vue";
import useErrorHandler from "../../composables/useErrorHandler.js";

export const useInvoiceChargebackStore = defineStore('invoice-chargeback',
    () => {
        const api = Api.make()

        const loading = ref(false)
        const invoiceChargebacks = ref([])
        const invoiceChargebacksAggregate = ref({
            total_chargeback: 0
        })
        const errorHandler = useErrorHandler();

        const getInvoiceChargebacks = async (invoiceId) => {
            loading.value = true

            try {
                const response = await api.getInvoiceChargebacks(invoiceId)
                invoiceChargebacks.value = response.data.data.chargebacks
                invoiceChargebacksAggregate.value = response.data.data.aggregate
            } catch (err) {
                errorHandler.handleError(err)
            }

            loading.value = false
        }

        const getChargebacks = async (tableFilter) => {
            const response = await api.getChargebacks(tableFilter)
            return response.data
        }

        return {
            invoiceChargebacksAggregate,
            invoiceChargebacks,
            errorHandler,
            loading,
            getInvoiceChargebacks,
            getChargebacks
        }
    });
