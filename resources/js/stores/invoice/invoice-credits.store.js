import {defineStore} from "pinia";
import {computed, ref} from "vue";
import Api from "../../vue/components/Billing/services/invoice-credits-api.js";

export const useInvoiceCreditsStore = defineStore('invoice-credits', () => {
    const creditsApplied = ref([])
    const loadingCreditsApplied = ref(false)
    const loadingApplyCredit = ref(false)

    const api = Api.make()

    const initStoreValues = () => {
        creditsApplied.value = []

        loadingCreditsApplied.value = false
        loadingApplyCredit.value = false
    }

    const applyCredit = async (invoiceId, data) => {
        loadingApplyCredit.value = true
        try {
            await api.applyCredit(invoiceId, data)
        } catch (err) {
            console.error(err)
        }
        loadingApplyCredit.value = false
    }

    const getCreditsApplied = async (invoiceId) => {
        loadingCreditsApplied.value = true
        try {
            const response = await api.getInvoiceCredits(invoiceId);

            creditsApplied.value = response.data.data
        } catch (err) {
            console.error(err)
        }
        loadingCreditsApplied.value = false
    }

    const resetStore = () => {
        initStoreValues()
    }

    const totalCreditsApplied = computed(() => {
        return creditsApplied.value.reduce((prev, curr) => prev + curr.amount_applied, 0)
    })

    return {
        applyCredit,
        getCreditsApplied,
        resetStore,
        creditsApplied,
        loadingCreditsApplied,
        loadingApplyCredit,
        totalCreditsApplied
    }
})
