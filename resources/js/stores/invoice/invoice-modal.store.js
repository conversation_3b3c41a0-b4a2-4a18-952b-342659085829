import {defineStore} from "pinia";
import {computed, ref} from "vue";
import ApiService from "../../vue/components/Billing/services/api";
import {DateTime} from 'luxon'
import useInvoiceHelper from "../../composables/useInvoiceHelper";
import {useCompanyCreditManagementStore} from "../credit/company-credit-management.store.js";

export const useInvoiceModalStore = defineStore('invoice-modal',
    () => {
        const locked = ref(false)
        const api = ApiService.make();
        const readonly = ref(false)
        const invoiceItems = ref([]);
        const uninvoicedItems = ref([]);
        const uninvoicedAdded = ref(false);
        const company = ref();
        const companyName = ref('');
        const showInvoiceModal = ref(false)
        const processAuto = ref(false);
        const prepayment = ref(false);
        const isProcessingPayment = ref(false)
        const billingProfile = ref({})
        const billingAccounts = ref([
            { id: 'default', name: 'Default'},
        ]);
        const loadingInvoicePdf = ref(false)

        const issueDate = ref(DateTime.now().setZone('America/Denver').plus({days: 0}).toJSDate());
        const dueDate = ref(DateTime.now().setZone('America/Denver').plus({days: 0}).toJSDate());
        const invoiceNote = ref("");
        const invoiceTags = ref([]);
        const invoiceId = ref(null);
        const invoiceUuid = ref(null);
        const invoiceTotals = ref(null);
        const invoiceCollections = ref(null);
        const invoiceWriteOffs = ref(null);
        const invoiceHelper = useInvoiceHelper();
        const loadingInvoiceRetrieve = ref(false)
        const invoiceTransactionStatuses = ref([])
        const creditsApplied = ref()
        const itemsTotal = ref()
        const totalIssuable = ref()
        const hasPendingAction = ref(false)

        const manualInvoiceItem = ref({//todo: remove
            description: null,
            quantity: null,
            unit_price: null,
            billable_type: 'manual',
            billable_id: 1,
        })

        const creditInvoiceItem = ref({//todo: remove
            description: "Credit Item",
            quantity: null,
            unit_price: null,
            billable_type: 'credit',
            billable_id: null,
        })

        const itemTypes = {
            MANUAL: {
                id: 'manual',
                name: 'Manual'
            },
            CREDIT:  {
                id: 'credit',
                name: 'Credit'
            },
        }

        const NEW_INVOICE_STATUS = {
            id: 'draft',
            title: "Draft",
            actions: [
                {
                    id: 'draft',
                    title: 'Draft'
                },
                {
                    id: 'issued',
                    title: 'Issued'
                },
            ]
        }

        const status = ref(NEW_INVOICE_STATUS);

        const selectedBillingAccount = ref(billingAccounts.value[0])

        const addItem = (value) => {
            const clone = { ...value}
            invoiceItems.value.push(clone);
        }

        const invoiceItemAdded = (itemType) => {
            if (itemType === itemTypes.CREDIT.id) {
                addItem(creditInvoiceItem.value)
            }

            if (itemType === itemTypes.MANUAL.id) {
                addItem(manualInvoiceItem.value)
            }
        }

        const removeItem = (index) => {
            invoiceItems.value.splice(index,1);
        }

        const calculateTotal = () => {
            return invoiceItems.value.reduce((prev, current) => {
                return current.unit_price * current.quantity + prev
            }, 0)
        }

        const setCompanyDetails = (id, name) => {
            company.value = id;
            companyName.value = name;
        }

        const getInvoice = () => {
            issueDate.value = issueDate.value instanceof Date
                ? issueDate.value
                : new Date(issueDate.value);

            dueDate.value = dueDate.value instanceof Date
                ? dueDate.value
                : new Date(dueDate.value);

            let invoice = {
                company_id: company.value,
                issue_date: DateTime.fromJSDate(issueDate.value).setZone('America/Denver').toISO(),
                due_date: DateTime.fromJSDate(dueDate.value).setZone('America/Denver').toISO(),
                billing_account: selectedBillingAccount.value,
                process_auto: processAuto.value,
                prepayment: prepayment.value,
                items: invoiceItems.value,
                note: invoiceNote.value,
                tags: invoiceTags.value.map((tag) => tag.name),
                status: status.value
            }

            if (invoiceUuid.value !== null) {
                invoice = {...invoice, id: invoiceId.value, uuid: invoiceUuid.value}
            }

            return invoice
        }

        const retrieveInvoiceData = async (id) => {
            try {
                loadingInvoiceRetrieve.value = true
                const response = await api.getInvoice(id);
                invoiceItems.value = response.data.data.invoice_items;
                status.value = response.data.data.status_data;
                dueDate.value = response.data.data.due_at;
                issueDate.value = response.data.data.issue_at;
                invoiceNote.value = response.data.data.notes;
                invoiceTags.value = response.data.data.tags;
                invoiceId.value = response.data.data.id;
                invoiceUuid.value = response.data.data.uuid;
                invoiceTotals.value = response.data.data.totals;
                invoiceCollections.value = response.data.data.collections
                invoiceWriteOffs.value = response.data.data.write_offs
                invoiceTransactionStatuses.value = response.data.data.transaction_statuses
                creditsApplied.value = response.data.data.credits_applied
                itemsTotal.value = response.data.data.items_total
                totalIssuable.value = response.data.data.total_issuable
                hasPendingAction.value = response.data.data.has_pending_action
                locked.value = response.data.data.locked
                isProcessingPayment.value = response.data.data.is_processing_payment
                billingProfile.value = response.data.data.billing_profile

                setCompanyDetails(response.data.data.company_id, response.data.data.company_name);
            } catch (error) {
                console.error(error)
            }

            loadingInvoiceRetrieve.value = false
        }

        function $reset() {
            invoiceItems.value = [];
            company.value = null;
            companyName.value = "";
            status.value = {...NEW_INVOICE_STATUS};
            issueDate.value = DateTime.now().setZone('America/Denver').plus({days: 0}).toJSDate()
            dueDate.value = DateTime.now().setZone('America/Denver').plus({days: 0}).toJSDate()
            processAuto.value = false
            prepayment.value = false
            invoiceNote.value = ""
            invoiceTags.value = []
            invoiceId.value = null;
            invoiceUuid.value = null;
            invoiceTotals.value = null;
            invoiceCollections.value = null;
            invoiceWriteOffs.value = null;
            invoiceTransactionStatuses.value = [];
            uninvoicedAdded.value = false;
            readonly.value = false;
            creditsApplied.value = null;
            itemsTotal.value = null;
            totalIssuable.value = null;
            hasPendingAction.value = false;
            locked.value = false;
            isProcessingPayment.value = false;
            billingProfile.value = {};
        }

        const getUninvoicedProducts = async () => {
            try {
                const response = await api.getUninvoicedProducts(company.value);
                uninvoicedItems.value = response.data.data.data;

            } catch (e) {
                console.error(e);
            }
        }


        const getCompanyAvailableCredits = async () => {
            try {
                const response = await api.getCompanyAvailableCredits(company.value);
                creditsApplied.value = response.data.data.total_available

            } catch (e) {
                console.error(e);
            }
        }

        const addRemoveUninvoiced = () => {
            if (uninvoicedAdded.value) {
                invoiceItems.value = invoiceItems.value.filter(item => {
                    return !uninvoicedItems.value.some(uninvoicedItem => {
                        return uninvoicedItem.billable_id === item.billable_id && item.billable_type === 'product'
                    })
                })
                uninvoicedAdded.value = false;
            } else {
                invoiceItems.value.push(...uninvoicedItems.value);
                uninvoicedAdded.value = true;
            }
        }

        const updateInvoiceStatus = async (newStatus) => {
            status.value = newStatus;
            try {
                return await api.createUpdateInvoice(getInvoice());
            } catch (err) {
                console.error(err);
            }
        }

        const isEditable = computed(() => {
            return status.value?.id === 'draft' && readonly.value === false && !hasPendingAction.value;
        });

        const companyChanged = () => {
            invoiceItems.value = [];
            uninvoicedAdded.value = false;
        }

        const nonEmptyInvoiceTotals = computed(() => {
            return Object.entries(invoiceTotals.value)
                .filter(([_,val]) => val > 0)
                .reduce((prev, [key, val]) => Object.assign({[key]: val}, prev), {})
        })

        const refreshInvoice = async () => {
            await retrieveInvoiceData(invoiceId.value)
        }

        const generatePdfSignedUrl = async (invoiceId) => {
            if (loadingInvoicePdf.value) {
                return
            }

            loadingInvoicePdf.value = true

            let url;

            try {
                const response = await api.generateSignedUrl(invoiceId)

                url = response.data.data.url
            } catch (err) {
                console.error(err)
            }

            loadingInvoicePdf.value = false

            return url;
        }

        const subTotal = computed(() => {
            return itemsTotal.value ?? calculateTotal() ?? 0
        })

        const creditStore = useCompanyCreditManagementStore()

        const creditsAvailableForInvoice = computed(() => {
            return creditStore.totalCreditsAvailable;
        })

        const creditsAppliedToInvoice = computed(() => {
            if (status.value?.id !== 'draft') {
                return creditsApplied.value
            }

            if (creditsApplied.value > 0) {
                return creditsApplied.value;
            }

            if (subTotal.value === null) {
                return 0;
            }

            return subTotal.value >= creditsAvailableForInvoice.value
                ? creditsAvailableForInvoice.value
                : subTotal.value
        })

        const setInvoiceItems = (invoiceItemsData) => {
            invoiceItems.value = invoiceItemsData
        }

        const setShowInvoiceModal = (show) => {
            showInvoiceModal.value = show
        }

        return {
            creditsAppliedToInvoice,
            subTotal,
            api,
            invoiceItems,
            setInvoiceItems,
            company,
            status,
            issueDate,
            dueDate,
            processAuto,
            prepayment,
            billingAccounts,
            invoiceId,
            selectedBillingAccount,
            invoiceItem: manualInvoiceItem,
            invoiceNote,
            invoiceTags,
            uninvoicedItems,
            itemTypes,
            uninvoicedAdded,
            isEditable,
            nonEmptyInvoiceTotals,
            readonly,
            addItem,
            calculateTotal,
            getInvoice,
            removeItem,
            $reset,
            addRemoveUninvoiced,
            getUninvoicedProducts,
            companyChanged,
            invoiceItemAdded,
            retrieveInvoiceData,
            setCompanyDetails,
            updateInvoiceStatus,
            getCompanyAvailableCredits,
            refreshInvoice,
            generatePdfSignedUrl,
            companyName,
            loadingInvoiceRetrieve,
            invoiceTotals,
            invoiceCollections,
            invoiceWriteOffs,
            invoiceTransactionStatuses,
            creditsApplied,
            itemsTotal,
            totalIssuable,
            hasPendingAction,
            locked,
            loadingInvoicePdf,
            isProcessingPayment,
            billingProfile,
            showInvoiceModal,
            setShowInvoiceModal
        }
});
