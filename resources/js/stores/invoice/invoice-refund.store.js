import {defineStore} from "pinia";
import ApiService from "../../vue/components/Billing/services/invoice-refund-api.js";
import {ref} from "vue";
import {cloneDeep} from "lodash";

const INITIAL_INVOICE_REFUND_PAYLOAD = {
    items: [],
    custom_amount_refunded: 0.00,
    refund_reason: null,
}

const REFUND_AGGREGATES = {
    paid: null,
    refunded: null,
}

export const useInvoiceRefundStore = defineStore('invoice-refund', () => {
    const api = ApiService.make();
    const newRefundPayload = ref(cloneDeep(INITIAL_INVOICE_REFUND_PAYLOAD))
    const invoiceRefunds = ref([])
    const invoiceAggregates = ref(cloneDeep(REFUND_AGGREGATES))
    const refundableItems = ref([]);

    const addItemToRefund = (item) => {
        newRefundPayload.value.items.push(item)
    }

    const removeItemFromRefund = (itemId) => {
        newRefundPayload.value.items = newRefundPayload.value.items.filter(item => item.invoice_item_id !== itemId);
    }

    const getRefunds = async (invoiceId) => {
        const response = await api.getInvoiceRefunds(invoiceId);
        invoiceRefunds.value = response.data.data.refunds;
        invoiceAggregates.value = response.data.data.aggregates;
        refundableItems.value = response.data.data.refundable;
    }

    const $reset = () => {
        newRefundPayload.value = cloneDeep(INITIAL_INVOICE_REFUND_PAYLOAD);
        invoiceRefunds.value = []
        invoiceAggregates.value = cloneDeep(REFUND_AGGREGATES)
        refundableItems.value = []
    }

    const isSelectedForRefund = (itemId) => {
        return newRefundPayload.value.items.some(item => item.invoice_item_id === itemId)
    }

    const toggleItemRefunded = (item) => {
        const refunded = isSelectedForRefund(item.invoice_item_id);

        if (!refunded) {
            addItemToRefund(item)
        } else {
            removeItemFromRefund(item.invoice_item_id)
        }
    }

    const issueRefund = async (invoiceId) => {
        return await api.issueRefund(invoiceId, newRefundPayload.value);
    }

    return {
        isSelectedForRefund,
        toggleItemRefunded,
        getRefunds,
        invoiceRefunds,
        refundableItems,
        newRefundPayload,
        invoiceAggregates,
        issueRefund,
        $reset,
    }
})
