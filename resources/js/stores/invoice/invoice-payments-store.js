import {defineStore} from "pinia";
import Api from "../../vue/components/Billing/services/invoice-chargeback-api.js";
import {ref} from "vue";
import useErrorHandler from "../../composables/useErrorHandler.js";

export const useInvoicePaymentsStore = defineStore('invoice-payments',
    () => {
        const api = Api.make()

        const loading = ref(false)
        const invoicePayments = ref([])
        const errorHandler = useErrorHandler();
        const totalPaid = ref(null)

        const getInvoicePayments = async (invoiceId) => {
            loading.value = true

            try {
                const response = await api.getInvoicePayments(invoiceId)
                invoicePayments.value = response.data.data.payments
                totalPaid.value = response.data.data.total
            } catch (err) {
                errorHandler.handleError(err)
            }

            loading.value = false
        }

        const $reset = () => {
            loading.value = false
            invoicePayments.value = []
            errorHandler.resetError()
            totalPaid.value = null
        }

        return {
            invoicePayments,
            errorHandler,
            loading,
            totalPaid,
            getInvoicePayments,
            $reset,
        }
    });
