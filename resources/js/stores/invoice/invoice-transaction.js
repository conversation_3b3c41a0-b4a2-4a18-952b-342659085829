import {defineStore} from "pinia";
import Api from "../../vue/components/Billing/services/invoice-transaction.js";
import {ref} from "vue";
import useErrorHandler from "../../composables/useErrorHandler.js";

export const useInvoiceTransactionStore = defineStore('invoice-transaction',
    () => {
        const api = Api.make();
        const loading = ref(true)
        const transactions = ref([])
        const errorHandler = useErrorHandler();

        const getInvoiceTransactions = async (invoiceId) => {
            loading.value = true;

            try {
                const response = await api.getInvoiceTransactions(invoiceId)
                transactions.value = response.data.data
            } catch (err) {
                errorHandler.handleError(err)
            }

            loading.value = false;
        }

        return {
            getInvoiceTransactions,
            loading,
            transactions
        }
    });
