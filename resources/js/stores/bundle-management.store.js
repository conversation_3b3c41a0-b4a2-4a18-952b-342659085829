import {defineStore} from "pinia";
import {onMounted, ref} from "vue";
import SharedApiService from "../vue/components/Shared/services/api";

export const invoiceTransitions = {
    issue: 'issue',
    paid: 'paid',
    cancel: 'cancel',
    approve: 'approve',
    deny: 'deny',
    fail: 'fail',
}

export const invoiceStatusMap = {
    New: {
        transitions: [invoiceTransitions.issue, invoiceTransitions.cancel]
    },
    Issued: {
        transitions: [invoiceTransitions.cancel, invoiceTransitions.fail, invoiceTransitions.paid]
    },
    Paid: {
        transitions: [invoiceTransitions.approve, invoiceTransitions.deny]
    },
    Complete: {
        transitions: []
    },
    Cancelled: {
        transitions: []
    },
};


export const useBundlesStore = defineStore('bundles',
    () => {
        const bundles = ref([]);
        const bundlePaginationData = ref(null);
        const bundleDropdownOptions = ref([]);
        const bundleFilters = ref({
            name: '',
            cost_from: null,
            cost_to: null,
            credit_from: null,
            credit_to: null,
            active: null,
            sort_col: 'name',
            sort_dir: 'asc',
            per_page: 25
        });
        const invoices = ref([]);
        const invoicePaginationData = ref(null);
        const invoiceFilters = ref({
            bundle_name: '',
            company_name: '',
            cost_from: null,
            cost_to: null,
            credit_from: null,
            credit_to: null,
            status: null,
            issued_at: null,
            sort_col: 'created_at',
            sort_dir: 'desc',
            per_page: 25
        });
        const loadingInvoices = ref(false);
        const loadingBundles = ref(false);
        const searchLoading = ref(false);
        const companyNameResults = ref([]);
        const currentUserRoles = ref({});
        const bundleError = ref(null);
        const bundleSuccess = ref(null);
        const invoiceError = ref(null);
        const invoiceSuccess = ref(null);
        const searchError = ref(null);

        const versionedApiClients = {
            'v1': SharedApiService.make(),
            'v2': SharedApiService.make(2)
        }

        const getVersionedApi = (version) => {
            return versionedApiClients[version] ?? versionedApiClients.v1
        }

        function canEditBundles() {
            return userHasRole(['bundle-admin']);
        }

        function canEditInvoices() {
            return userHasRole(['bundle-admin', 'bundle-issuer']);
        }

        function getBundles() {
            return getVersionedApi().getBundles();
        }

        function getInvoices(bundleId= null) {
            return getVersionedApi().getBundleInvoices(bundleId);
        }

        async function getInvoiceHistory(invoiceId) {
            return getVersionedApi().getBundleInvoiceHistory(invoiceId);
        }

        async function getIndustries() {
            return getVersionedApi().getOdinIndustries();
        }

        function createBundle(bundle) {
            bundleError.value = null;
            loadingBundles.value = true;

            getVersionedApi().createBundle(bundle)
                .then(resp => {
                    bundleSuccess.value = 'Bundle created.';
                    setTimeout(() => {
                        bundleSuccess.value = null;
                    },5000)
                })
                .catch((resp) => {
                    console.error("Error fetching bundles: ",resp);
                    bundleError.value = resp.response.data.message;
                    setTimeout(() => {
                        bundleError.value = null;
                    }, 5000)
                })
                .finally(() => {
                    loadingBundles.value = false;
                    searchBundles();
                });
        }

        function updateBundle(bundle) {
            bundleError.value = null;
            loadingBundles.value = true;

            getVersionedApi().updateBundle(bundle, bundle.id)
                .then(resp => {
                    bundleSuccess.value = 'Bundle updated.';
                    setTimeout(() => {
                        bundleSuccess.value = null;
                    },5000)
                })
                .catch((resp) => {
                    console.error("Error fetching bundles: ",resp);
                    bundleError.value = resp.response.data.message;
                    setTimeout(() => {
                        bundleError.value = null;
                    }, 5000)
                })
                .finally(() => {
                    loadingBundles.value = false;
                    searchBundles();
                });
        }


        function createInvoice(invoice) {
            invoiceError.value = null;
            loadingInvoices.value = true;

            getVersionedApi().createBundleInvoice(invoice)
                .then(resp => {
                    invoiceSuccess.value = 'Invoice created.';
                    setTimeout(() => {
                        invoiceSuccess.value = null;
                    },5000)
                })
                .catch((resp) => {
                    console.error("Error fetching invoices: ",resp);
                    invoiceError.value = resp.response.data.message;
                })
                .finally(() => {
                    loadingInvoices.value = false;
                    searchInvoices();
                });
        }

        function updateInvoice(invoice) {
            invoiceError.value = null;
            loadingInvoices.value = true;

            getVersionedApi().updateBundleInvoice(invoice, invoice.id)
                .then(resp => {
                    invoiceSuccess.value = 'Invoice updated.';
                    setTimeout(() => {
                        invoiceSuccess.value = null;
                    },5000)
                })
                .catch((resp) => {
                    console.error("Error fetching invoices: ",resp);
                    invoiceError.value = resp.response.data.message;
                })
                .finally(() => {
                    loadingInvoices.value = false;
                    searchInvoices();
                });
        }

        function executeInvoiceTransition(transitionName, invoiceId, options = {}, v = 'v1') {
            invoiceError.value = null;
            loadingInvoices.value = true;

            // searchInvoices() will refresh the list and on success the user will see an updated status.
            getVersionedApi(v).executeBundleInvoiceTransition(transitionName, invoiceId, options)
                .catch((resp) => {
                    console.error("Error executing invoice transition: ",resp);
                    invoiceError.value = resp.response.data.message;
                })
                .finally(() => {
                    loadingInvoices.value = false;
                    searchInvoices();
                });
        }

        function searchCompanyNames(nameType, query) {
            getVersionedApi().searchCompanyNames(nameType, query).then(
                (res) => {
                    if(res.data.data.status === true) {
                        if (searchLoading.value) return;

                        companyNameResults.value = res.data.data.companies;
                    }
                },
                () => {
                    searchError.value = "Error searching company names";

                    setTimeout(() => {
                        searchError.value = null;
                    }, 5000);
                }
            )
        }

        function bundleSelected(bundle = null) {
            invoiceFilters.value.bundle_name = bundle ? bundle.name : null;
            searchInvoices();
        }

        function userHasRole(roleNames) {
            for (const role of roleNames) {
                for (const userRole of currentUserRoles.value) {
                    if(userRole.name === role) {
                        return true;
                    }
                }
            }
            return false;
        }

        function formatPrice(value) {
            let val = (value/1).toFixed(2).replace(',', '.');
            return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        function searchBundles(page = 1) {
            loadingBundles.value = true;

            getVersionedApi().searchBundles(page, bundleFilters.value).then((res) => {
                if(res.data.data.status === true) {
                    let {data, ...paginationData} = res.data.data.bundles_paginated;
                    bundles.value = data;
                    bundlePaginationData.value = paginationData;
                }
            }).catch((err) => {
                bundleError.value = err.data.message || "Error retrieving bundles";

                setTimeout(() => {
                    bundleError.value = null;
                }, 5000);
            }).finally(() => {
                loadingBundles.value = false;
            });
        }


        function searchInvoices(page = 1) {
            loadingInvoices.value = true;

            getVersionedApi().searchBundleInvoices(page, invoiceFilters.value).then((res) => {
                if(res.data.data.status === true) {
                    let {data, ...paginationData} = res.data.data.invoices_paginated;
                    invoices.value = data;
                    invoicePaginationData.value = paginationData;
                }
            }).catch((err) => {
                invoiceError.value = err.data.message || "Error retrieving invoices";

                setTimeout(() => {
                    invoiceError.value = null;
                }, 5000);
            }).finally(() => {
                loadingInvoices.value = false;
            });
        }

        async function handleBundlePaginationEvent (newPageUrl) {
            const page = (new URL(newPageUrl.link)).searchParams.get('page');
            searchBundles(page);
        }

        async function handleInvoicePaginationEvent (newPageUrl) {
            const page = (new URL(newPageUrl.link)).searchParams.get('page');
            searchInvoices(page);
        }

        function resetBundleFilters() {
            bundleFilters.value = {
                name: '',
                cost_from: null,
                cost_to: null,
                credit_from: null,
                credit_to: null,
                active: null,
                sort_col: 'name',
                sort_dir: 'asc',
                per_page: 25
            };
            searchBundles();
        }

        function resetInvoiceFilters() {
            invoiceFilters.value = {
                bundle_name: '',
                company_name: '',
                cost_from: null,
                cost_to: null,
                credit_from: null,
                credit_to: null,
                status: null,
                issued_at: null,
                sort_col: 'created_at',
                sort_dir: 'desc',
                per_page: 25
            };
            searchInvoices();
        }

        function sortBundles(column = 'name') {
            if(bundleFilters.value.sort_col === column) {
                bundleFilters.value.sort_dir = bundleFilters.value.sort_dir === 'asc' ? 'desc' : 'asc';
            }

            bundleFilters.value.sort_col = column;

            searchBundles();
        }

        function sortInvoices(column = 'id') {
            if(invoiceFilters.value.sort_col === column) {
                invoiceFilters.value.sort_dir = invoiceFilters.value.sort_dir === 'asc' ? 'desc' : 'asc';
            }

            invoiceFilters.value.sort_col = column;

            searchInvoices();
        }

        onMounted(() => {
            searchBundles();
            searchInvoices();
        });

        return {
            bundles,
            bundlePaginationData,
            bundleFilters,
            invoices,
            invoicePaginationData,
            invoiceFilters,
            currentUserRoles,
            loadingBundles,
            loadingInvoices,
            bundleDropdownOptions,
            companyNameResults,
            bundleError,
            invoiceError,
            searchError,
            bundleSuccess,
            invoiceSuccess,
            getBundles,
            getInvoices,
            getInvoiceHistory,
            getIndustries,
            createBundle,
            updateBundle,
            createInvoice,
            updateInvoice,
            bundleSelected,
            userHasRole,
            canEditBundles,
            canEditInvoices,
            searchCompanyNames,
            searchBundles,
            resetBundleFilters,
            sortBundles,
            handleBundlePaginationEvent,
            searchInvoices,
            resetInvoiceFilters,
            sortInvoices,
            handleInvoicePaginationEvent,
            executeInvoiceTransition,
            formatPrice
        }
    },
    {
        persist: true,
    }
)

