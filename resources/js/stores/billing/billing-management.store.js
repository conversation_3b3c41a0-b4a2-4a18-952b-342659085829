import {defineStore} from "pinia";
import {ref} from "vue";
import ApiService from "../../vue/components/BillingManagement/services/api";
import Api from "../../vue/components/Billing/services/api"
import useEventHandler from "../../composables/useEventHandler";
import {DateTime} from 'luxon'

export const useBillingManagementStore = defineStore('billing-management',
    () => {
        const eventHandler = useEventHandler();
        const targetTimeSpan = ref(1);
        const targetTimeSpanString = ref("Today");

        const targetTimeSpanLabels = {
            TODAY: 'Today',
            DAY: 'Day',
            FORTNIGHT: 'Fortnight',
            WEEK: 'Week',
            MONTH: 'Month',
            CUSTOM: 'Custom'
        }

        const currentPeriod = ref({
            startDate: DateTime.now(),
            endDate: DateTime.now(),
        })

        const resetPeriod = () => {
            targetTimeSpan.value = 1;
            targetTimeSpanString.value = targetTimeSpanLabels.TODAY;
            currentPeriod.value.startDate = DateTime.now().setZone('America/Denver').toISO();
            currentPeriod.value.endDate = DateTime.now().setZone('America/Denver').toISO();
            eventHandler.dispatchEvent('update-period');
        }

        const setDateTime = (startDate, endDate) => {
            targetTimeSpan.value = 1;
            currentPeriod.value.startDate = DateTime.fromJSDate(startDate).setZone('America/Denver').toISO()
            currentPeriod.value.endDate = DateTime.fromJSDate(endDate).setZone('America/Denver').toISO()
            eventHandler.dispatchEvent('update-period');
        }


        const move = (direction) => {
            let method = direction === 'forward' ? 'plus' : 'minus';

            currentPeriod.value.startDate = currentPeriod.value.startDate[method]({days: targetTimeSpan.value})
            currentPeriod.value.endDate = currentPeriod.value.endDate[method]({days: targetTimeSpan.value})

            eventHandler.dispatchEvent('update-period')
        }

        const updateTargetTimeSpan = (days) => {
            targetTimeSpan.value = days

            currentPeriod.value.startDate = DateTime.now().minus({days: targetTimeSpan.value}).setZone('America/Denver').toISO()
            currentPeriod.value.endDate = DateTime.now().setZone('America/Denver').toISO()

            eventHandler.dispatchEvent('update-period')
        }

        const getBillingPeriods = () => {
            return [
                {
                    callback: () => {
                        targetTimeSpanString.value = targetTimeSpanLabels.TODAY;
                        resetPeriod()
                    },
                    name: targetTimeSpanLabels.TODAY,
                },
                {
                    callback: () => {
                        targetTimeSpanString.value = targetTimeSpanLabels.WEEK;
                        updateTargetTimeSpan(7)
                    },
                    name: 'Last 7 Days',
                },
                {
                    callback: () => {
                        targetTimeSpanString.value = targetTimeSpanLabels.FORTNIGHT;
                        updateTargetTimeSpan(14)
                    },
                    name: 'Last 14 Days',
                },
                {
                    callback: () => {
                        targetTimeSpanString.value = targetTimeSpanLabels.MONTH;
                        updateTargetTimeSpan(28)
                    },
                    name: 'Last 28 Days',
                },
            ]
        }

        const addPeriodUpdateEventListener = (listenerId, callback) => {
            eventHandler.addListener('update-period', listenerId, callback)
        }

        const api = ApiService.make(currentPeriod)
        const invoiceApi = Api.make()

        return {
            currentPeriod,
            getBillingPeriods,
            api,
            invoiceApi,
            resetPeriod,
            move,
            setDateTime,
            eventHandler,
            targetTimeSpanString,
            addPeriodUpdateEventListener,
        }
});
