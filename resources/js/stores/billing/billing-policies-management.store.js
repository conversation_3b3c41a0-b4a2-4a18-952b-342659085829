import {defineStore} from "pinia";
import {ref} from "vue";
import Api from "../../vue/components/BillingManagement/services/billing-profile-policies-api";
import useErrorHandler from "../../composables/useErrorHandler";

export const useBillingPoliciesManagementStore = defineStore('billing-policies-management', () => {
    const errorHandler = useErrorHandler();
    const loading = ref(false);
    const billingPolicies = ref([]);
    const api = Api.make();

    const selectedEventSlug = ref(null);
    const selectedActionSlug = ref(null);

    const paginatedCreditTypes = (page, perPage) => {
        const startingPoint = page - 1;
        const chunk = startingPoint * perPage;
        return billingPolicies.value.slice(chunk, chunk + perPage)
    }

    const getBillingProfilePolicies = async () => {
        loading.value = true
        try {
            const response = await api.getBillingProfilePolicies()
            billingPolicies.value = response?.data?.data?.billing_profile_policies ?? []
        } catch (err) {
            errorHandler.handleError(err, 'Error trying to list policies')
        }
        loading.value = false
    }

    const createBillingProfilePolicy = async ({event, action}) => {
        loading.value = true

        try {
            await api.createBillingProfilePolicy({event, action})
        } catch (err) {
            errorHandler.handleError(err, 'Error trying to create policy')
            loading.value = false

            return
        }

        await getBillingProfilePolicies()
        loading.value = false
    }

    const deleteBillingProfilePolicy = async (id) => {
        // todo - add loading
        loading.value = true
        try {
            await api.deleteBillingProfilePolicy(id)
        } catch (err) {
            errorHandler.handleError(err, 'Error trying to delete policy')
            loading.value = false
            return
        }

        await getBillingProfilePolicies()
        loading.value = false
    }

    const setSelectedEventSlug = (eventSlug = null) => {
        selectedEventSlug.value = eventSlug
    }

    const setSelectedActionSlug = (actionSlug = null) => {
        selectedActionSlug.value = actionSlug
    }

    const batchUpdate = async (billingPolicies) => {
        loading.value = true

        try {
            await api.batchUpdate(billingPolicies)
        } catch (err) {
            errorHandler.handleError(err, 'Error trying to update policies')
            loading.value = false

            return
        }

        await getBillingProfilePolicies()
        loading.value = false
    }

    return {
        loading,
        api,
        errorHandler,
        billingPolicies,
        selectedEventSlug,
        selectedActionSlug,
        batchUpdate,
        deleteBillingProfilePolicy,
        paginatedCreditTypes,
        getBillingProfilePolicies,
        createBillingProfilePolicy,
        setSelectedEventSlug,
        setSelectedActionSlug,
    }
});
