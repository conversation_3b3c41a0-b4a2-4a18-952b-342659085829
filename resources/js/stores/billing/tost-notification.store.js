import {defineStore} from 'pinia'
import {ref} from 'vue'
import useSimpleIcon from "../../composables/useSimpleIcon.js";

export const useToastNotificationStore = defineStore('toast-notifications', () => {
    const notifications = ref([])

    const simpleIcon = useSimpleIcon()

    const variants = {
        INFO: 'info',
        ERROR: 'error',
        WARNING: 'warning',
        SUCCESS: 'success',
    }

    const parseVariant = (notification, variant) => {
        const variantStyles = {
            [variants.INFO]: {
                leftIcon: {icon: simpleIcon.icons.EXCLAMATION_CIRCLE},
                color: 'blue'
            },
            [variants.ERROR]: {
                leftIcon: {icon: simpleIcon.icons.EXCLAMATION_CIRCLE},
                color: 'red'
            },
            [variants.WARNING]: {
                leftIcon: {icon: simpleIcon.icons.EXCLAMATION_CIRCLE},
                color: 'yellow'
            },
            [variants.SUCCESS]: {
                leftIcon: {icon: simpleIcon.icons.CHECK_CIRCLE},
                color: 'green'
            }
        };

        return {
            ...notification,
            ...variantStyles[variant] || {}
        };
    };

    const parseNotification = (notification) => {
        if (typeof notification === 'object') return notification;

        return {
            message: notification
        }
    }

    const add = (notification, variant = variants.INFO, timeout = 3500) => {
        notification = parseNotification(notification)

        notification = {
            ...parseVariant(notification, variant),
            _id: crypto.randomUUID(),
            rightIcon: notification.rightIcon ?? {
                icon: simpleIcon.icons.X_MARK,
                callback: () => dismiss(notification)
            }
        }

        if (timeout > 0) {
            setTimeout(() => dismiss(notification), timeout)
        }

        notifications.value.push(notification)
    }

    const notifySuccess = (notification, timeout = 3500) => {
        add(notification, variants.SUCCESS, timeout)
    }

    const notifyError = (notification, timeout = 3500) => {
        add(notification, variants.ERROR, timeout)
    }


    const dismiss = (notification) => {
        notifications.value = notifications.value.filter(n => n._id !== notification._id)
    }

    return {
        notifications,
        variants,

        add,
        notifySuccess,
        notifyError,
        dismiss
    }
})
