import {defineStore} from "pinia";
import {ref} from "vue";
import use<PERSON>rror<PERSON>and<PERSON> from "../../composables/useErrorHandler";
import Api from "../../vue/components/BillingManagement/services/invoice-templates-api";

export const useInvoiceTemplatesManagementStore = defineStore('invoice-templates-management', () => {
    const errorHandler = useErrorHandler();
    const loadingList = ref(false);
    const loadingModelTypes = ref(false);
    const loadingSave = ref(false);
    const templates = ref([]);
    const paginationData = ref({
        page: 1,
        total: 0,
        perPage: 5,
    });
    const api = Api.make();

    const notificationMessage = ref({
        message: null,
        variant: null,
    })

    const clearMessage = () => {
        notificationMessage.value = {
            message: null,
            variant: null,
        }
    }

    const selectedInvoiceTemplate = ref({})

    const modelOptions = ref([]);

    const getTemplates = async (page = 1, perPage = 5) => {
        loadingList.value = true
        try {
            const response = await api.getInvoiceTemplates({page, perPage})

            const {invoice_templates = {}} = response?.data?.data ?? {}

            templates.value = invoice_templates?.data ?? []
            paginationData.value = {
                page: invoice_templates?.meta?.current_page ?? 1,
                total: invoice_templates?.meta?.total ?? 0,
                perPage: invoice_templates?.meta?.per_page ?? 0,
            }
        } catch (err) {
            notificationMessage.value = {
                variant: 'light-red',
                message: 'Error trying to list invoice templates'
            }
            errorHandler.handleError(err)
        }
        loadingList.value = false
    }

    const setSelectedInvoiceTemplate = (invoiceTemplate) => {
        selectedInvoiceTemplate.value = invoiceTemplate ?? {
            props: {
                currency: null,
                invoice_logo: null,
                support_email: null,
                billing_account: null,
                invoice_contact_address: {
                    city: null,
                    state: null,
                    country: null,
                    line_one: null,
                    line_two: null,
                    post_code: null,
                }
            }
        }
    }

    const saveInvoiceTemplate = async () => {
        loadingSave.value = true

        errorHandler.resetError()

        try {
            const response = selectedInvoiceTemplate.value.id
                ? await api.update(selectedInvoiceTemplate.value.id, selectedInvoiceTemplate.value)
                : await api.create(selectedInvoiceTemplate.value)

            selectedInvoiceTemplate.value = response.data.data.invoice_template
            notificationMessage.value = {
                message: 'Template saved',
                variant: 'light-green',
            }
        } catch (err) {
            notificationMessage.value = {
                message: err?.response?.data?.message ?? 'Error saving invoice template',
                variant: 'light-red',
            }
            errorHandler.handleError(err)
        }

        loadingSave.value = false
    }


    const searchModels = async (query) => {
        loadingModelTypes.value = true
        try {
            const response = await api.searchModels(
                selectedInvoiceTemplate.value.model_type,
                query
            )

            modelOptions.value = response.data.data?.model_options ?? []

        } catch (err) {
            notificationMessage.value = {
                message: 'Error trying to search models',
                variant: 'light-red',
            }
            errorHandler.handleError(err)
        }
        loadingModelTypes.value = false
    }

    const deleteInvoice = async () => {
        await api.delete(selectedInvoiceTemplate.value.id)
        selectedInvoiceTemplate.value = null
        getTemplates()
    }

    return {
        modelOptions,
        selectedInvoiceTemplate,
        loadingList,
        loadingModelTypes,
        api,
        errorHandler,
        templates,
        paginationData,
        loadingSave,
        notificationMessage,
        clearMessage,

        getTemplates,
        setSelectedInvoiceTemplate,
        saveInvoiceTemplate,
        searchModels,
        deleteInvoice
    }
});
