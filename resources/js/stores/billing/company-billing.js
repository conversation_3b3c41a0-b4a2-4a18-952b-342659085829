import {defineStore} from "pinia";
import {ref} from "vue";
import ApiService from "../../vue/components/BillingManagement/services/api";
import Api from "../../vue/components/Billing/services/api"
import useEventHandler from "../../composables/useEventHandler";
import {DateTime} from 'luxon'
import SharedApiService from "../../vue/components/Shared/services/api.js";
import useErrorHandler from "../../composables/useErrorHandler.js";

export const useCompanyBillingStore = defineStore('billing-management',
    () => {
        const api = SharedApiService.make()

        const loading = ref(false)
        const version = ref(null)
        const errorHandler = useErrorHandler()

        const VERSIONS = {
            V1: 'v1',
            V2: 'v2',
        }

        const getBillingVersion = async (companyId) => {
            if (loading.value) return

            loading.value = true

            try {
                const response = await api.getCompanyBillingVersion(companyId)

                version.value = response.data.data.version
            } catch (err) {
                errorHandler.handleError(err)
                version.value = VERSIONS.V1
            }

            loading.value = false
        }

        const isV2 = () => {
            return version.value === VERSIONS.V2
        }

        return {
            isV2,
            getBillingVersion,
            version,
            VERSIONS,
            loading
        }
    });
