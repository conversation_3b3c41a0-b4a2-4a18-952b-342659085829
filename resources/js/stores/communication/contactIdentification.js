import { defineStore } from 'pinia'


const STORE_NAME = 'communicationIdentificationStore'

const INIT_STATE = {
    isContactIdentificationModalVisible: false,

    possibleContacts: [],
    createNewRecordComponentShown: {},

    isCallActive: false,

    currentCallInfo: {
        sid: null,
        logId: null,
        otherNumber: null,
        relationId: null,
        relationType: null,
        identified: null,
        callLogType: 'call',
    }
}

export const CONTACT_IDENTIFICATION_STORE_METHODS = {
    SET_CREATE_NEW_RECORD_COMPONENT_SHOWN: 'setCreateNewRecordComponentShown',
    RESET_STORE_STATE: 'resetStoreState',
}

export const useContactIdentificationStore = defineStore(STORE_NAME, {
    state: () => {
        return {
            ...INIT_STATE
        }
    },

    actions: {
        [CONTACT_IDENTIFICATION_STORE_METHODS.RESET_STORE_STATE](){
          Object.assign(this, INIT_STATE)
        },

        [CONTACT_IDENTIFICATION_STORE_METHODS.SET_CREATE_NEW_RECORD_COMPONENT_SHOWN](component){
            this.createNewRecordComponentShown = component
        },
    },
})
