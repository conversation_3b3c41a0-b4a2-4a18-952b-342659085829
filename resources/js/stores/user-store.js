import {defineStore} from "pinia";
import {ref} from "vue";
import ApiService from '../../js/services/api/user/user-api'
export const useUserStore = defineStore('user-store', () => {
    const user = ref(null)
    const api = ApiService.make()
    const getLoggedUser = async () => {
        try {
            const response = await api.getLoggedUser();

            user.value = {
                name: response.data.data.name,
                id: response.data.data.id,
                email: response.data.data.email,
                impersonating: response.data.data.impersonating,
                meeting_url: response.data.data.meeting_url,
                has_enabled_mailbox: response.data.data.has_enabled_mailbox,
                roles: response.data.data.roles || []
            };

        } catch (error) {
            console.error(error)
        }

    }

    return {
        user,
        getLoggedUser
    }
});
