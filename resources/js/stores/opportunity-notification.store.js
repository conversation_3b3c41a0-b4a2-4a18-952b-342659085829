import {defineStore} from "pinia";
import {onMounted, ref} from "vue";
import SharedApiService from "../vue/components/Shared/services/api";
import {UserPresetApiServiceFactory} from "../vue/components/UserPreset/services/api/factory";

export const useOpportunityNotificationStore = defineStore('opportunity-notifications',
    () => {
        const notifications = ref([]);
        const notificationPaginationData = ref(null);
        const notificationFilters = ref({
            recipients: null,
            delivery_method: null,
            sent_at: null,
            company_name: null,
            configuration_id: null,
            sort_col: 'created_at',
            sort_dir: 'desc',
            per_page: 25
        });
        const notificationConfigs = ref([]);
        const notificationConfigPaginationData = ref(null);
        const notificationConfigFilters = ref({
            name: '',
            send_day: '',
            send_time: null,
            frequency: '',
            content: null,
            active: '',
            last_sent_at: null,
            lead_threshold: null,
            sort_col: 'name',
            sort_dir: 'asc',
            per_page: 25
        });
        const loadingNotifications = ref(false);
        const loadingNotificationConfigs = ref(false);
        const api = SharedApiService.make();
        const userPresetApiService = UserPresetApiServiceFactory.make('api');
        const filterCompanyPresets = ref(null);
        const productTypeOptions = ref(null);
        const emailTemplateOptions = ref([]);
        const searchLoading = ref(false);
        const searchError = ref(null);
        const companyNameResults = ref([]);
        const configurationOptions = ref([]);
        const notificationError = ref(null);
        const notificationSuccess = ref(null);
        const notificationConfigError = ref('');
        const notificationConfigSuccess = ref(null);
        const generalErrorMessage = ref('An unknown error occurred.');
        const emailTemplatePreview = ref(null);
        const testEmailRecipients = ref(null);

        const notificationConfigTypes = ref({});

        async function createOpportunityNotificationConfig(config){
            return await api.createOpportunityNotificationConfig(config)
        }

        async function getNotificationConfigTypes() {
            const resp = await api.getOpportunityNotificationConfigTypes().catch(e => e);
            if (resp.data?.data?.status) {
                notificationConfigTypes.value = resp.data?.data?.config;
            }
        }

        function bdmConfigExists() {
            return notificationConfigs.value.some(config => config.type === 1);
        }

        function createNotificationConfig(config) {
            notificationConfigError.value = null;
            loadingNotificationConfigs.value = true;

            return new Promise(res => {
                let success;
                createOpportunityNotificationConfig(config).then(() => {
                        notificationConfigSuccess.value = 'Notification config created.';
                        setTimeout(() => {
                            notificationConfigSuccess.value = null;
                        }, 5000);
                        success = true;
                    }).catch((resp) => {
                        console.error("Error creating notification config: ", resp);
                        notificationConfigError.value = resp.response.data.message;
                        setTimeout(() => {
                            notificationConfigError.value = null;
                        }, 5000);
                        success = false;
                    }).finally(() => {
                        loadingNotificationConfigs.value = false;
                        searchNotificationConfigs();
                        res(success);
                    });
            });
        }

        async function updateNotificationConfig(config) {
            notificationConfigError.value = null;
            loadingNotificationConfigs.value = true;

            return new Promise(res => {
                let success;
                api.updateOpportunityNotificationConfig(config, config.id).then(() => {
                        notificationConfigSuccess.value = 'Notification config updated.';
                        setTimeout(() => {
                            notificationConfigSuccess.value = null;
                        },5000);
                        success = true;
                    }).catch((resp) => {
                        console.error("Error updating notification config: ",resp);
                        notificationConfigError.value = resp.response.data.message;
                        setTimeout(() => {
                            notificationConfigError.value = null;
                        }, 5000);
                        success = false;
                    }).finally(() => {
                        loadingNotificationConfigs.value = false;
                        searchNotificationConfigs();
                        res(success);
                    });
            });
        }

        function deleteNotificationConfig(configId) {
            notificationConfigError.value = null;
            loadingNotificationConfigs.value = true;

            api.deleteOpportunityNotificationConfig(configId)
                .then(() => {
                    notificationConfigSuccess.value = 'Notification config deleted.';
                    setTimeout(() => {
                        notificationConfigSuccess.value = null;
                    },5000)
                })
                .catch((resp) => {
                    console.error("Error deleting notification config: ",resp);
                    notificationConfigError.value = resp.response.data.message;
                    setTimeout(() => {
                        notificationConfigError.value = null;
                    }, 5000);
                })
                .finally(() => {
                    loadingNotificationConfigs.value = false;
                    searchNotificationConfigs();
                });
        }

        function searchCompanyNames(nameType, query) {
            api.searchCompanyNames(nameType, query).then(
                (res) => {
                    if(res.data.data.status === true) {
                        if (searchLoading.value) return;

                        companyNameResults.value = res.data.data.companies;
                    }
                },
                () => {
                    searchError.value = "Error searching company names";

                    setTimeout(() => {
                        searchError.value = null;
                    }, 5000);
                }
            )
        }

        function searchConfigurations(name) {
            api.searchConfigurationsOptions(name).then(
                (res) => {
                    if(res.data.data.status === true) {
                        if (searchLoading.value) return;

                        configurationOptions.value = res.data.data.configurations;
                    }
                },
                () => {
                    searchError.value = "Error searching configurations";

                    setTimeout(() => {
                        searchError.value = null;
                    }, 5000);
                }
            )
        }

        function searchNotifications(page = 1) {
            if (loadingNotifications.value)
                return;

            loadingNotifications.value = true;

            api.searchOpportunityNotifications(page, notificationFilters.value).then((res) => {
                if(res.data.data.status === true) {
                    let {data, ...paginationData} = res.data.data.notifications_paginated;
                    notifications.value = data;
                    notificationPaginationData.value = paginationData;
                }
            }).catch((err) => {
                notificationError.value = err.data.message || "Error retrieving opportunity notifications";

                setTimeout(() => {
                    notificationError.value = null;
                }, 5000);
            }).finally(() => {
                loadingNotifications.value = false;
            });
        }

        function searchNotificationConfigs(page = 1) {
            if (loadingNotificationConfigs.value)
                return;

            loadingNotificationConfigs.value = true;

            api.searchOpportunityNotificationConfigs(page, notificationConfigFilters.value).then((res) => {
                if(res.data.data.status === true) {
                    let {data, ...paginationData} = res.data.data.configs_paginated;
                    notificationConfigs.value = data;
                    notificationConfigPaginationData.value = paginationData;
                }
            }).catch((err) => {
                notificationConfigError.value = err.data.message || "Error retrieving opportunity notification configs";

                setTimeout(() => {
                    notificationConfigError.value = null;
                }, 5000);
            }).finally(() => {
                loadingNotificationConfigs.value = false;
            });
        }

        async function handleNotificationPaginationEvent (newPageUrl) {
            const page = (new URL(newPageUrl.link)).searchParams.get('page');
            searchNotifications(page);
        }

        async function handleNotificationConfigPaginationEvent (newPageUrl) {
            const page = (new URL(newPageUrl.link)).searchParams.get('page');
            searchNotificationConfigs(page);
        }

        function resetNotificationFilters() {
            notificationFilters.value = {
                recipients: '',
                delivery_method: null,
                sent_at: null,
                company_name: null,
                consumer_product_id: null,
                sort_col: 'created_at',
                sort_dir: 'desc',
                per_page: 25
            };
            searchNotifications();
        }

        function resetNotificationConfigFilters() {
            notificationConfigFilters.value = {
                name: '',
                send_day: '',
                send_time: null,
                frequency: '',
                content: null,
                active: '',
                last_sent_at: null,
                lead_threshold: null,
                sort_col: 'name',
                sort_dir: 'asc',
                per_page: 25
            };
            searchNotificationConfigs();
        }

        function sortNotifications(column = 'created_at') {
            if(notificationFilters.value.sort_col === column) {
                notificationFilters.value.sort_dir = notificationFilters.value.sort_dir === 'asc' ? 'desc' : 'asc';
            }

            notificationFilters.value.sort_col = column;

            searchNotifications();
        }

        function sortNotificationConfigs(column = 'id') {
            if(notificationConfigFilters.value.sort_col === column) {
                notificationConfigFilters.value.sort_dir = notificationConfigFilters.value.sort_dir === 'asc' ? 'desc' : 'asc';
            }

            notificationConfigFilters.value.sort_col = column;

            searchNotificationConfigs();
        }

        function getCompanyFilterPresets() {
            if (loadingNotificationConfigs.value)
                return;

            loadingNotificationConfigs.value = true;

            userPresetApiService.getFilterPresets()
                .then(resp => {
                    if (resp.data?.data?.status) {
                        this.filterCompanyPresets = resp.data?.data?.presets ?? [];
                    } else {
                        console.error('error', resp.message || resp.data?.message || generalErrorMessage.value);
                    }
                })
                .catch(e => console.error('error', e.response?.data?.message || generalErrorMessage.value))
                .finally(() => loadingNotificationConfigs.value = false);
        }

        function getEmailTemplateOptions() {
            api.getEmailTemplateOptions()
                .then(resp => {
                    if (resp.data?.data?.status) {
                        this.emailTemplateOptions = resp.data?.data?.options ?? [];
                    } else {
                        console.error('error', resp.message || resp.data?.message || generalErrorMessage.value);
                    }
                })
                .catch(e => console.error('error', e.response?.data?.message || generalErrorMessage.value))
                .finally(() => loadingNotificationConfigs.value = false);
        }

        async function getEmailTemplatePreview(companyUserId, opportunityConfigurationId) {
            try {
                const resp = await api.getEmailTemplatePreview(companyUserId, opportunityConfigurationId)

                if (resp.data?.data?.status) {
                    this.emailTemplatePreview = resp.data?.data?.preview_content ?? [];
                } else {
                    console.error('error', resp.message || resp.data?.message || generalErrorMessage.value);
                }
            } catch(e) {
                console.error('error', e.response?.data?.message || generalErrorMessage.value)
            }
        }

        function getTestEmailRecipients() {
            api.getTestEmailRecipients()
                .then(resp => {
                    if (resp.data?.data?.status) {
                        this.testEmailRecipients = resp.data?.data?.recipients ?? [];
                    } else {
                        console.error('error', resp.message || resp.data?.message || generalErrorMessage.value);
                    }
                })
                .catch(e => console.error('error', e.response?.data?.message || generalErrorMessage.value))
        }


        function sendOpNotificationEmails(configId, companyId) {
            api.dispatchOpNotificationEmails(configId, companyId)
                .then(() => {
                    notificationConfigSuccess.value = 'Dispatched emails.';
                    setTimeout(() => {
                        notificationConfigSuccess.value = null;
                    },5000)
                })
                .catch((resp) => {
                    console.error("Error dispatching emails to send ",resp);
                    notificationConfigError.value = resp.response.data.message;
                    setTimeout(() => {
                        notificationConfigError.value = null;
                    }, 5000);
                });
        }

        function resetTemplate(){
            this.emailTemplatePreview = null
            this.testEmailRecipients = null
        }

        const getPreviewCompanyCount = async (filterPresetOd) => {
            return api.getCompanyCountPreview(filterPresetOd)
        }

        const getConfigTypeById = (id) => {
            return Object.values(notificationConfigTypes.value).find(config => config.id === id);
        }

        onMounted(async () => {
            searchNotifications();
            searchNotificationConfigs();
            await getNotificationConfigTypes();
        });

        return {
            notifications,
            notificationPaginationData,
            notificationFilters,
            notificationConfigs,
            notificationConfigPaginationData,
            notificationConfigFilters,
            loadingNotifications,
            loadingNotificationConfigs,
            searchLoading,
            searchError,
            companyNameResults,
            configurationOptions,
            notificationError,
            notificationSuccess,
            notificationConfigError,
            notificationConfigSuccess,
            generalErrorMessage,
            filterCompanyPresets,
            productTypeOptions,
            emailTemplateOptions,
            emailTemplatePreview,
            testEmailRecipients,
            notificationConfigTypes,
            getCompanyFilterPresets,
            getEmailTemplateOptions,
            sortNotifications,
            sortNotificationConfigs,
            searchNotifications,
            searchNotificationConfigs,
            createNotificationConfig,
            updateNotificationConfig,
            deleteNotificationConfig,
            handleNotificationPaginationEvent,
            handleNotificationConfigPaginationEvent,
            resetNotificationFilters,
            resetNotificationConfigFilters,
            searchCompanyNames,
            sendOpNotificationEmails,
            getPreviewCompanyCount,
            getEmailTemplatePreview,
            getTestEmailRecipients,
            resetTemplate,
            createOpportunityNotificationConfig,
            searchConfigurations,
            getConfigTypeById,
            bdmConfigExists,
        }
    },
    {
        persist: true,
    }
)

