/**
 * This is to map the API services keys on frontend with the backend (API endpoints) request/response.
 */
export const REQUEST = {
    ID                          : 'id',
    NAME                        : 'name',
    SUBJECT                     : 'subject',
    RULESET                     : 'ruleset',
    RULES                       : 'rules',
    TYPE                        : 'type',
    SOURCE                      : 'source',
    FILTER                      : 'filter',
    RULE                        : 'rule',
    RULESETS                    : 'rulesets',
    STATUS                      : 'status',
    MESSAGE                     : 'message',
    INDUSTRY_ID                 : 'industry_id',
    INDUSTRY                    : 'industry',
    TEMPLATE                    : 'template',
    COMPANY_QUALITY_SCORE_RULES : 'company_quality_score_rules',
    COMPANY_QUALITY_SCORE_RULE  : 'company_quality_score_rule',
    INDUSTRIES                  : 'industries',
    TOTAL_POINTS                : 'total_points',
    IS_PRODUCTION               : 'is_production',
    RULE_ID                     : 'rule_id',
    RESULT                      : 'result',
    COMPANY_IDS                 : 'company_ids',
    TEST_COMPANY_IDS            : 'test_company_ids',
    GLOBAL                      : 'global',
    PERSONAL                    : 'personal',
    HEADER                      : 'header',
    FOOTER                      : 'footer',
    BACKGROUND_ID               : 'background_id',
    TEMPLATE_ID                 : 'template_id',
    CONTENT                     : 'content',
    IS_OWNER                    : 'is_owner',
    OWNER_NAME                  : 'owner_name',
    LAST_UPDATED                : 'last_updated',
    CONTENT_IMAGES              : 'content_images',
    HEADER_IMAGES               : 'header_images',
    FOOTER_IMAGES               : 'footer_images',
    OPTIONS                     : 'options',
    LOGO                        : 'logo',
    MEDIA_ASSETS                : 'media_assets',
    YOUTUBE_ASSET               : 'youtube_asset',
    ASSIGNMENTS                 : 'assignments',
    ENGINE                      : 'engine',
    ACTIVE                      : 'active',
    PAYLOAD                     : 'payload',
}

/**
 * Contains the driver types to be used for the API factories.
 */
export const DRIVER = {
    API    : 'api',
    DUMMY  : 'dummy',
}
