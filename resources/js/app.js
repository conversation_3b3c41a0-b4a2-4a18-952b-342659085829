import './bootstrap';

import {
    initializeApp,
    initializeGuestApp,
    initializePdfApp,
    PDF_MOUNT_SELECTOR,
    ADMIN_MOUNT_SELECTOR,
    GUEST_MOUNT_SELECTOR
} from './vue/vue';

if (document.querySelector(ADMIN_MOUNT_SELECTOR)) {
    initializeApp();
} else if (document.querySelector(GUEST_MOUNT_SELECTOR)) {
    initializeGuestApp();
} else if (document.querySelector(PDF_MOUNT_SELECTOR)) {
    initializePdfApp();
}
