const cadenceToSecondsIndex = {
    days: 86400,
    weeks: 604800,
    months: 2592000,
    years: 31536000,
}

/**
 * Returns true if the contact meets the filter requirements.
 * @param contact
 * @param {?"exists"|"doesNotExist"} phonePresence
 * @param {?"exists"|"doesNotExist"} emailPresence
 * @param {{operator: "longerThan"|"within", number: number, cadence: string, active: boolean}} lastContactedAt
 * @return {boolean}
 */
export const getContactMeetsFilterRequirements = (contact, phonePresence, emailPresence, lastContactedAt) => {
    let result = true;

    if (phonePresence === "exists") {
        result &&= (contact?.office_phone !== null && contact?.office_phone !== "") || (contact?.cell_phone !== null && contact?.cell_phone !== "")
    } else if (phonePresence === "doesNotExist") {
        result &&= !contact?.office_phone && !contact?.cell_phone;
    } else {
        result &&= true;
    }

    if (emailPresence === "exists") {
        result &&= (contact?.email !== null && contact?.email !== "")
    } else if (emailPresence === "doesNotExist") {
        result &&= !contact?.email;
    } else {
        result &&= true;
    }

    if (lastContactedAt?.active && typeof contact?.latest_call_timestamp === "number") {
        if (lastContactedAt?.operator === "within") {
            result &&= contact?.latest_call_timestamp > (lastContactedAt?.number * cadenceToSecondsIndex[lastContactedAt?.cadence]);
        } else if (lastContactedAt?.operator === "longerThan") {
            result &&= contact?.latest_call_timestamp <= (lastContactedAt?.number * cadenceToSecondsIndex[lastContactedAt?.cadence]);
        } else {
            result &&= true;
        }
    }

    return result;
}

export const filterContacts = (contacts, phonePresence, emailPresence, lastContactedAt) => {
    return contacts?.filter(contact => {
        return getContactMeetsFilterRequirements(contact, phonePresence, emailPresence, lastContactedAt)
    }) ?? [];
};
