import { getOperatorResult } from './helpers'

export const companyContactConditionOptions = ['withPhone', 'withEmail', 'withBoth', 'withNeither']

const testTwoOperators = (comparedAgainst, firstOperator, firstComparer, secondOperator, secondComparer, logical) => {
    let operatorOneResult = true
    let operatorTwoResult = true

    try {
        operatorOneResult = getOperatorResult(comparedAgainst, firstOperator, firstComparer)
    } catch (e) {
        throw e
    }

    if (logical === 'and' || logical === 'or') {
        try {
            operatorTwoResult = getOperatorResult(comparedAgainst, secondOperator, secondComparer)
        } catch (e) {
            throw e
        }
    }

    if (logical === 'and') {
        return operatorOneResult && operatorTwoResult
    } else if (logical === 'or') {
        return operatorOneResult || operatorTwoResult
    } else {
        return operatorOneResult
    }
}

/**
 * Applies filter for each company.
 * @param {object} company
 * @param {object} filters
 * @return boolean
 * @throws Error
 */
export const companyMatchesFilter = (company, filters) => {
    // returns the company if theres no filter applied
    if (Object.keys(filters).length === 0) return company

    const evaluatedFilters = { ...filters }

    const evaluateCompanyContactsFilter = (evaluatedFilters) => {
        /**
         * @type {CompanyContactsCompanyServicingAreaFilterJsDocType}
         */
        const companyContactsFilter = evaluatedFilters.companyContacts

        if (companyContactsFilter?.active) {

            let operatorResult = false

            let companyField

            switch (companyContactsFilter.condition) {
                case 'withPhone':
                    operatorResult = testTwoOperators(company.contacts_with_phone_count, companyContactsFilter.firstOperator, companyContactsFilter.firstValue, companyContactsFilter.secondOperator, companyContactsFilter.secondValue, companyContactsFilter.logical)
                    break
                case 'withEmail':
                    operatorResult = testTwoOperators(company.contacts_with_email_count, companyContactsFilter.firstOperator, companyContactsFilter.firstValue, companyContactsFilter.secondOperator, companyContactsFilter.secondValue, companyContactsFilter.logical)
                    break
                case 'withBoth':
                    if (company.hasOwnProperty('contacts_with_both_count')) {
                        companyField = company.contacts_with_both_count
                    } else if (company.hasOwnProperty('contacts_with_phone_and_email_count')) {
                        companyField = company.contacts_with_phone_and_email_count
                    } else if (company.hasOwnProperty('contacts_with_both_phone_and_email_count')) {
                        companyField = company.contacts_with_both_phone_and_email_count
                    }

                    operatorResult = testTwoOperators(companyField, companyContactsFilter.firstOperator, companyContactsFilter.firstValue, companyContactsFilter.secondOperator, companyContactsFilter.secondValue, companyContactsFilter.logical)
                    break
                case 'withNeither':
                    if (company.hasOwnProperty('contacts_with_neither_count')) {
                        companyField = company.contacts_with_neither_count
                    } else if (company.hasOwnProperty('contacts_with_neither_phone_or_email_count')) {
                        companyField = company.contacts_with_neither_phone_or_email_count
                    }

                    operatorResult = testTwoOperators(companyField, companyContactsFilter.firstOperator, companyContactsFilter.firstValue, companyContactsFilter.secondOperator, companyContactsFilter.secondValue, companyContactsFilter.logical)
                    break
                default:
                    throw new TypeError(`"condition" must be one of ${companyContactConditionOptions}. ${companyContactsFilter.condition} passed.`)
            }

            return operatorResult
        }

        return true
    }

    const evaluateEstimatedRevenueFilter = (evaluatedFilters) => {
        const estimatedRevenueFilter = evaluatedFilters.estimatedRevenue

        if (estimatedRevenueFilter?.active) {

            let operatorResult = false

            operatorResult = testTwoOperators(company.estimated_revenue, estimatedRevenueFilter.firstOperator, estimatedRevenueFilter.firstValue, estimatedRevenueFilter.secondOperator, estimatedRevenueFilter.secondValue, estimatedRevenueFilter.logical)

            return operatorResult
        }

        return true
    }

    const evaluateEmployeeCountFilter = (evaluatedFilters) => {
        const employeeCountFilter = evaluatedFilters.employeeCount

        if (employeeCountFilter?.active) {
            let operatorResult = false

            operatorResult = testTwoOperators(company?.employee_count, employeeCountFilter.firstOperator, employeeCountFilter.firstValue, employeeCountFilter.secondOperator, employeeCountFilter.secondValue, employeeCountFilter.logical)

            return operatorResult
        }

        return true
    }

    const evaluateGoogleReviewCountFilter = (evaluatedFilters) => {
        const googleReviewCountFilter = evaluatedFilters.googleReviews

        if (googleReviewCountFilter?.active) {
            let operatorResult = false

            operatorResult = testTwoOperators(company.google_review_count, googleReviewCountFilter.firstOperator, googleReviewCountFilter.firstValue, googleReviewCountFilter.secondOperator, googleReviewCountFilter.secondValue, googleReviewCountFilter.logical)

            return operatorResult
        }

        return true
    }

    const evaluateGoogleRatingFilter = (evaluatedFilters) => {
        const googleRating = evaluatedFilters.googleRating

        if (googleRating?.active) {
            let operatorResult = false

            /**
             * @param rating
             * @throws TypeError
             */
            const checkRating = (rating) => {
                const ratingCheck = [1, 2, 3, 4, 5].includes(rating)

                if (!ratingCheck) {
                    throw new TypeError(`Rating must be a number from 1 to 5. ${rating} passed.`)
                }
            }

            checkRating(company.google_rating)

            operatorResult = testTwoOperators(company.google_rating, googleRating.firstOperator, googleRating.firstValue, googleRating.secondOperator, googleRating.secondValue, googleRating.logical)

            return operatorResult
        }

        return true
    }

    const evaluateSalesStatusFilter = (evaluatedFilters) => {
        const filter = evaluatedFilters.salesStatus

        if (filter?.active) {
            const isOrIsNot = filter.isOrIsNot

            if (isOrIsNot === 'is') {
                return filter.selectedOptions.filter(x => x === company?.sales_status_id).length > 0
            } else {
                const result = [];

                filter.selectedOptions.forEach(x => {
                    if (x !== company?.sales_status_id) {
                        result.push(x)
                    }
                })

                return result.length === filter.selectedOptions.length
            }
        }

        return true
    }

    const evaluateStatusFilter = (evaluatedFilters) => {
        const filter = evaluatedFilters.status

        if (filter?.active) {
            const isOrIsNot = filter.isOrIsNot

            if (isOrIsNot === 'is') {
                return filter.selectedOptions.filter(x => x === company?.mi_company?.consolidated_status).length > 0
            } else {
                const result = []

                filter.selectedOptions.forEach(x => {
                    if (x !== company?.mi_company?.consolidated_status) {
                        result.push(x)
                    }
                })

                return result.length === filter.selectedOptions.length
            }
        }

        return true
    }

    return evaluateCompanyContactsFilter(evaluatedFilters) &&
        evaluateEstimatedRevenueFilter(evaluatedFilters) &&
        evaluateEmployeeCountFilter(evaluatedFilters) &&
        evaluateGoogleReviewCountFilter(evaluatedFilters) &&
        evaluateGoogleRatingFilter(evaluatedFilters) &&
        evaluateSalesStatusFilter(evaluatedFilters) &&
        evaluateStatusFilter(evaluatedFilters)
}

/**
 * Filters companies based on the filters passed.
 * @param filters
 * @param companies
 * @return {*}
 */
export const filterCompanies = (filters, companies) => {
    return companies.filter(x => companyMatchesFilter(x, filters))
}
