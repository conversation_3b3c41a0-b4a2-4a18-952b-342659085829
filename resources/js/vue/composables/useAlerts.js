import {ref} from 'vue';

export function useAlerts() {
    const alertType = ref('');
    const alertText = ref('');
    const alertActive = ref(false);

    const displayAlert = (text, type, timeout = 5000) => {
        const alertTypes = ['warning', 'error', 'success'];

        if (!alertTypes.includes(type)) {
            throw new TypeError(`"type" must be one of the following: ${JSON.stringify(alertTypes)}. "${type}" passed`)
        }

        if (typeof text !== 'string') {
            throw new TypeError(`"text" must be a string. ${text} passed.`)
        }

        alertText.value = text;
        alertType.value = type;
        alertActive.value = true;

        setTimeout(() => {
            alertActive.value = false;
            alertType.value = '';
            alertText.value = '';
        }, timeout);
    };

    return {
        alertType,
        alertText,
        alertActive,
        displayAlert
    }
}
