<template>
    <div :class="{'pb-5': rootNode, 'pt-3': !rootNode}">
        <div class="flex items-center gap-1">
            <div class="border-b border-dashed w-4 absolute -ml-[19px]" v-if="!rootNode" :class="[borderColor]"></div>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="h-6 w-6 text-cyan-700">
                <path fill-rule="evenodd" d="M8.25 6.75a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0ZM15.75 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM2.25 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM6.31 15.117A6.745 6.745 0 0 1 12 12a6.745 6.745 0 0 1 6.709 7.498.75.75 0 0 1-.372.568A12.696 12.696 0 0 1 12 21.75c-2.305 0-4.47-.612-6.337-1.684a.75.75 0 0 1-.372-.568 6.787 6.787 0 0 1 1.019-4.38Z" clip-rule="evenodd" />
                <path d="M5.082 14.254a8.287 8.287 0 0 0-1.308 5.135 9.687 9.687 0 0 1-1.764-.44l-.115-.04a.563.563 0 0 1-.373-.487l-.01-.121a3.75 3.75 0 0 1 3.57-4.047ZM20.226 19.389a8.287 8.287 0 0 0-1.308-5.135 3.75 3.75 0 0 1 3.57 4.047l-.01.121a.563.563 0 0 1-.373.486l-.115.04c-.567.2-1.156.349-1.764.441Z" />
            </svg>
            <p class="font-semibold">{{ team.name }}</p>
            <p class="text-sm ml-1" v-if="team.leader"> (Leader: {{ team.leader.user_name }})</p>
        </div>

        <div v-if="team.members && team.members.length" class="ml-3 border-l border-dashed" :class="[borderColor]">
            <div v-for="member in team.members" :key="member.id" class="flex items-center my-1">
                <div class="border-b border-dashed w-6" :class="[borderColor]"></div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="h-5 w-5 text-cyan-700">
                    <path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z" clip-rule="evenodd" />
                </svg>
                <p class="text-sm ml-1">{{ member.user_name }}</p>
            </div>
        </div>

        <div v-if="team.children && team.children.length" class="ml-3 pl-5 border-l border-dashed relative" :class="[borderColor]">
            <TreeNode v-for="child in team.children"
                      :key="child.id"
                      :team="child"
                      :dark-mode="darkMode"
            />
        </div>
    </div>
</template>

<script>
export default {
    name: "TreeNode",
    props: {
        team: {
            type: Object,
            required: true
        },
        darkMode: {
            type: Boolean,
            default: false
        },
        rootNode: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        borderColor() {
            return this.darkMode ? 'border-blue-400' : 'border-gray-800'
        }
    }
}
</script>
