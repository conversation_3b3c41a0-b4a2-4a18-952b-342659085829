<template>
    <Modal :dark-mode="darkMode" no-buttons @close="$emit('close')">
        <template v-slot:header>Team Hierarchy</template>
        <template v-slot:content>
            <div v-if="teamTree.length">
                <div class="px-4">
                    <TreeNode v-for="team in teamTree"
                              :key="team.id"
                              :team="team"
                              root-node
                              :dark-mode="darkMode"
                    />
                </div>
            </div>
            <div v-else>
                <p>No teams available</p>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import TreeNode from "./TreeNode.vue";

export default {
    name: "TeamTree",
    components: {TreeNode, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        teams: {
            type: Array,
            required: true
        }
    },
    data () {
        return {
            teamTree: []
        }
    },
    emits: ['close'],
    beforeMount() {
        this.prepareTreeData()
    },
    methods: {
        prepareTreeData() {
            const teams = JSON.parse(JSON.stringify(this.teams));
            const map = {};

            teams.forEach(team => {
                map[team.id] = { ...team, children: [] };
            });

            teams.forEach(team => {
                if (team.parent_team_id) {
                    if (map[team.parent_team_id]) {
                        map[team.parent_team_id].children.push(map[team.id]);
                    }
                } else {
                    this.teamTree.push(map[team.id]);
                }
            });
        }
    }
}
</script>

