<template>
    <div class="border rounded-lg h-auto"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="p-5 flex items-center justify-between">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Teams List</h5>
            <CustomButton :dark-mode="darkMode" @click="showTeamTree=true" :disabled="loading || initialLoad">View Hierarchy</CustomButton>
        </div>
        <div class="grid grid-cols-12 gap-x-3 mb-2 px-5 font-medium-400 uppercase text-xs" :class="[{'text-slate-100': darkMode}, {'text-slate': !darkMode}]">
            <p class="col-span-2">Name</p>
            <p class="col-span-2">Description</p>
            <p class="col-span-2">Parent Team</p>
            <p class="col-span-1">Type</p>
            <p class="col-span-2">Leader</p>
            <p class="col-span-2">Members</p>
        </div>
        <div class="border-t border-b h-[calc(100vh-20rem)] overflow-y-auto"
             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
            <div class="h-full">
                <div class="flex items-center h-full justify-center"
                     v-if="!showTeamModal && (loading || saving || initialLoad)"
                >
                    <loading-spinner></loading-spinner>
                </div>
                <div class="grid grid-cols-12 gap-x-2 gap-y-4 border-b px-5 py-3"
                     v-for="team in teams" :key="team.id"
                     :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                    <p class="text-sm col-span-2 truncate">
                        {{ team.name }}
                    </p>
                    <p class="text-sm col-span-2 truncate">
                        {{ team.description }}
                    </p>
                    <p class="text-sm col-span-2 pl-1 truncate">
                        {{ getParentTeam(team.parent_team_id)?.name }}
                    </p>
                    <p class="text-sm col-span-1 pl-1 truncate">
                        {{ team.teamType.name }}
                    </p>
                    <p class="text-sm col-span-2 pl-1 truncate">
                        {{ team.leader?.user_name }}
                    </p>
                    <div class="text-sm pl-1 truncate col-span-2">
                        <p v-for="member in team.members">{{ member.user_name }}</p>
                    </div>
                    <actions-handle :dark-mode="darkMode"
                        class="col-span-1"
                        @edit="editTeam(team.id)"
                        no-delete-button
                    />
                </div>
            </div>
        </div>
        <div class="p-3"></div>
    <!-- Main Edit modal -->
        <modal :dark-mode="darkMode"
           @close="closeTeamModal"
           close-text="Cancel"
           @confirm="saveTeam"
           :confirm-text="editingTeam.id > 0 ? 'Update Team' : 'Save New Team'"
           v-if="showTeamModal"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium">{{ editingTeam.id > 0 ? 'Edit' : 'Add' }} Team</h4>
            </template>
            <template v-slot:content>
                <div class="max-height-[40rem] overflow-y-auto pb-11">
                    <h3 class="block pb-3 font-medium text-lg">Team Details: {{ editingTeam.name }}</h3>
                    <div class="grid grid-cols-2 gap-8">
                        <div class="grid gap-3">
                            <div class="grid grid-cols-2 gap-3">
                                <div class="col-span-2">
                                    <label class="block pb-1 font-medium text-sm">Team Type</label>
                                    <dropdown :dark-mode="darkMode"
                                              :options="teamTypes"
                                              v-model="editingTeam.teamType.id"
                                              :selected="editingTeam.teamType?.id ?? 0"
                                              placeholder="Select Type"
                                              @change="updateSelectedTeamType($event)"
                                    />
                                </div>
                                <div class="col-span-2">
                                    <label class="block pb-1 font-medium text-sm">Parent Team</label>
                                    <dropdown :dark-mode="darkMode"
                                              :options="parentTeamOptions"
                                              v-model="editingTeam.parent_team_id"
                                              :selected="editingTeam.parent_team_id"
                                              placeholder="Select Parent Team"
                                    />
                                </div>
                               <div class="col-span-2">
                                   <label class="block pb-1 font-medium text-sm">Description</label>
                                   <CustomInput :dark-mode="darkMode" v-model="editingTeam.description"
                                          placeholder="Team description..."
                                   />
                               </div>
                            </div>
                            <div :class="[ this.editingTeam.teamType.id ? '' : 'grayscale-[70%] pointer-events-none opacity-50' ]">
                                <div>
                                    <div class="flex items-center pb-2">
                                        <label class="block font-medium text-sm mr-4">Team Leader:</label>
                                        <p class="font-semibold text-sm text-primary-500">{{ editingTeam.leader.user_name || 'None' }}</p>
                                        <button @click="unAssignLeader" v-if="editingTeam.leader.user_name" class="ml-6">
                                            <svg width="12" viewBox="0 0 9 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M1.5 2.75H1V9.9C1 10.1917 1.10536 10.4715 1.29289 10.6778C1.48043 10.8841 1.73478 11 2 11H7C7.26522 11 7.51957 10.8841 7.70711 10.6778C7.89464 10.4715 8 10.1917 8 9.9V2.75H1.5ZM6.809 1.1L6 0H3L2.191 1.1H0V2.2H9V1.1H6.809Z" fill="#339AFF"/>
                                            </svg>
                                        </button>

                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <autocomplete
                                            :dark-mode="darkMode"
                                            :input-class="['w-full border border-grey-350 rounded px-4 py-3', {'border-grey-350 bg-light-module': !darkMode, 'border-blue-400 bg-dark-background text-blue-400': darkMode}]"
                                            :list-class="['absolute top-0 w-full border rounded', {'border-grey-200 bg-light-module': !darkMode, 'border-dark-40 bg-dark-module': darkMode}]"
                                            :option-class="['cursor-pointer px-5 py-2 my-1', {'hover:bg-grey-100': !darkMode, 'hover:bg-blue-800': darkMode}]"
                                            v-model="editingTeam.addLeader"
                                            :value="editingTeam.addLeader"
                                            :options="searchFilteredUsers.leader"
                                            @keyup.enter="assignLeader"
                                            @clear-search="filterUsersBySearch('leader', '')"
                                            @search="(searchInput) => filterUsersBySearch('leader', searchInput)"
                                            :placeholder="'Enter Name'"
                                            :disable-input="!this.editingTeam.teamType.id"
                                            class="relative flex-grow"
                                        >
                                        </autocomplete>
                                        <CustomButton class="flex-shrink-0" :dark-mode="darkMode" @click="assignLeader"
                                                      :class="[ !editingTeam.addLeader ? 'grayscale-50' : '']"
                                                      :disabled="!editingTeam.addLeader"
                                        >
                                            Assign Leader
                                        </CustomButton>
                                    </div>
                                </div>
                            </div>
<!--                            <div :class="[ this.editingTeam.teamType.id ? '' : 'grayscale-[70%] pointer-events-none opacity-50' ]">-->
<!--                                <div>-->
<!--                                    <div class="flex items-center pb-1">-->
<!--                                        <label class="block font-medium text-sm mr-4">Team Manager (Team reports to):</label>-->
<!--                                        <p class="font-semibold text-sm text-primary-500">{{ editingTeam.leader.reports_to?.user_name|| 'None' }}</p>-->
<!--                                    </div>-->
<!--                                    <div class="flex items-center space-x-2">-->
<!--                                        <autocomplete-->
<!--                                            :dark-mode="darkMode"-->
<!--                                            :input-class="['w-full border border-grey-350 rounded px-4 py-3', {'border-grey-350 bg-light-module': !darkMode, 'border-blue-400 bg-dark-background text-blue-400': darkMode}]"-->
<!--                                            :list-class="['absolute top-0 w-full border rounded', {'border-grey-200 bg-light-module': !darkMode, 'border-dark-40 bg-dark-module': darkMode}]"-->
<!--                                            :option-class="['cursor-pointer px-5 py-2 my-1', {'hover:bg-grey-100': !darkMode, 'hover:bg-blue-800': darkMode}]"-->
<!--                                            v-model="editingTeam.addManager"-->
<!--                                            :value="editingTeam.addManager"-->
<!--                                            :options="searchFilteredUsers.manager"-->
<!--                                            @keyup.enter="assignManager"-->
<!--                                            @clear-search="filterUsersBySearch('manager', '')"-->
<!--                                            @search="(searchInput) => filterUsersBySearch('manager', searchInput)"-->
<!--                                            :placeholder="'Enter Name'"-->
<!--                                            :disable-input="!this.editingTeam.teamType.id"-->
<!--                                            class="relative flex-grow"-->
<!--                                        >-->
<!--                                        </autocomplete>-->
<!--                                        <CustomButton class="flex-shrink-0" :dark-mode="darkMode" @click="assignManager"-->
<!--                                                      :class="[ !editingTeam.addManager ? 'grayscale-50' : '' ]"-->
<!--                                                      :disabled="!editingTeam.addManager"-->
<!--                                        >-->
<!--                                            Assign Manager-->
<!--                                        </CustomButton>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                        </div>
                        <div :class="[ this.editingTeam.teamType.id ? '' : 'grayscale-[70%] pointer-events-none opacity-50' ]">
                            <label class="block pb-1 font-medium text-sm">Team Members</label>
                            <div class="flex">
                                <div class="grid gap-y-4 w-full">
                                    <div class="flex gap-2 items-center">
                                        <autocomplete
                                            :dark-mode="darkMode"
                                            :input-class="['w-full border border-grey-350 rounded px-4 py-3 rounded-md', {'border-grey-350 bg-light-module': !darkMode, 'border-blue-400 bg-dark-background text-blue-400': darkMode}]"
                                            :list-class="['absolute top-0 w-full border rounded', {'border-grey-200 bg-light-module': !darkMode, 'border-dark-40 bg-dark-module': darkMode}]"
                                            :option-class="['cursor-pointer px-5 py-2 my-1', {'hover:bg-grey-100': !darkMode, 'hover:bg-blue-800': darkMode}]"
                                            v-model="editingTeam.addMember"
                                            :value="editingTeam.addMember"
                                            :options="searchFilteredUsers.members"
                                            @keyup.enter="addMember"
                                            @clear-search="filterUsersBySearch('members', '')"
                                            @search="(searchInput) => filterUsersBySearch('members', searchInput)"
                                            :placeholder="'Enter Name'"
                                            :disable-input="!this.editingTeam.teamType.id"
                                            class="relative min-w-36"
                                        >
                                        </autocomplete>
                                        <CustomButton :dark-mode="darkMode" @click="addMember"
                                                      :class="[ !editingTeam.addLeader ? 'grayscale-50' : '' ]"
                                                      :disabled="!editingTeam.addMember"
                                        >
                                            Add Member
                                        </CustomButton>
                                    </div>
                                    <div class="rounded p-2 columns-1 border h-[18rem] overflow-y-auto"
                                         :class="[darkMode ? 'text-slate-300 bg-dark-background border-dark-border' : 'text-slate-700 bg-light-background border-light-border']"
                                         v-if="editingTeam.members.length">
                                        <div v-for="member in editingTeam.members"
                                             class="flex justify-between p-2 rounded-md text-sm cursor-default"
                                             :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']"
                                             :key="member.user_id"
                                        >
                                            <p class="">{{ member.user_name || 'No name' }}</p>
                                            <button @click="removeMember(member.user_id)"
                                            >
                                                <svg width="12" viewBox="0 0 9 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M1.5 2.75H1V9.9C1 10.1917 1.10536 10.4715 1.29289 10.6778C1.48043 10.8841 1.73478 11 2 11H7C7.26522 11 7.51957 10.8841 7.70711 10.6778C7.89464 10.4715 8 10.1917 8 9.9V2.75H1.5ZM6.809 1.1L6 0H3L2.191 1.1H0V2.2H9V1.1H6.809Z" fill="#339AFF"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="rounded p-3 columns-1 border text-sm h-[18rem]"
                                         :class="[darkMode ? 'text-slate-300 bg-dark-background border-dark-border' : 'text-slate-700 bg-light-background border-light-border']" v-else>None</div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
                <alert v-if="alertActive && showTeamModal"
                       :alert-type="alertType"
                       :text="alertText"
                       :dark-mode="darkMode"
                />
                <loading-spinner v-if="showTeamModal && (loading || saving || initialLoad)" />
            </template>
        </modal>

    <!-- Delete confirm modal -->
        <modal :dark-mode="darkMode"
               :small="true"
               @close="cancelDelete"
               close-text="Cancel"
               @confirm="deleteTeam"
               confirm-text="Delete Team"
               v-if="showDeleteModal"
               :restrict-width="true"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium">Delete Team</h4>
            </template>
            <template v-slot:content>
                Are you sure you wish to delete Team {{ deletingTeam.name }}?<p class="italic text-sm">Please note: this will destroy all Team structure data, but the Users will not be deleted.</p>
            </template>
        </modal>
        <alert v-if="alertActive && !showTeamModal"
               :alert-type="alertType"
               :text="alertText"
               :dark-mode="darkMode"
        />
        <TeamTree :dark-mode="darkMode" :teams="teams" @close="showTeamTree=false" v-if="showTeamTree"/>
    </div>
</template>

<script>
import TeamManagementApi from "../services/teamManagementApi.js";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import Modal from "../../Shared/components/Modal.vue";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Alert from "../../Shared/components/Alert.vue";
import AlertsMixin from "../../../mixins/alerts-mixin.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import TeamTree from "./TeamTree.vue";

export default {
    name: 'TeamManagementModule',
    components: {
        TeamTree,
        CustomButton,
        CustomInput,
        LoadingSpinner,
        Alert,
        Dropdown,
        Autocomplete,
        Modal,
        ActionsHandle
    },
    mixins: [
        AlertsMixin
    ],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            api: TeamManagementApi.make(),
            teams: [],
            users: [],
            teamTypes: [ ],
            defaultTeamTypeFilter: null,
            showTeamModal: false,
            leaderOptions: [],
            memberOptions: [],
            loading: false,
            saving: false,
            initialLoad: false,
            editingTeam: {
                id: null,
                name: '',
                description: '',
                teamType: {
                    name: '',
                    id: 0,
                    leader_roles: [],
                    member_roles: [],
                },
                leader: {
                    id: null,
                    user_name: '',
                    user_id: null,
                    reports_to: {
                        user_name: '',
                        user_id: null
                    },
                },
                members: [],
                addMember: '',
                addLeader: '',
                addManager: '',
                parent_team_id: null,
            },
            availableUsers: {
                leader: [],
                members: [],
                manager: [],
            },
            searchFilteredUsers: {
                leader: [],
                members: [],
                manager: []
            },
            showDeleteModal: false,
            deletingTeam: {
                id: null,
                name: '',
            },
            messages: {
                errorGet: 'There was an error fetching Team data.',
                errorPost: `An error occurred saving this Team.`,
            },
            showTeamTree: false,
        }
    },
    mounted() {
        this.getInitialData();
    },
    computed: {
        parentTeamOptions () {
            const teams = this.teams.filter(team => team.id !== this.editingTeam.id && team.parent_team_id !== this.editingTeam.id)
                .map(team => ({id: team.id, name: team.name}));

            return [
                {id: 0, name: 'No Parent Team'},
                ... teams
            ]
        }
    },
    methods: {
        async getInitialData() {
            this.initialLoad = true;
            await Promise.all([
                this.getTeams(),
                this.getTeamTypes(),
                this.getUsers(),
            ]).catch(err => {
                this.showAlert('error', err.message || this.messages.errorGet);
            });
            this.initialLoad = false;
        },
        async getTeams() {
            this.loading = true;
            this.api.getAllTeams().then(response => {
                if (response.data.data.teams) {
                    this.teams = response.data.data.teams;
                }
                else {
                    this.showAlert('error', this.messages.errorGet);
                }
            }).catch(err => {
                this.showAlert('error', err.message || this.messages.errorGet);
            });
            this.loading = false;
        },
        async getTeamTypes() {
            this.loading = true;
            await this.api.getTeamTypes().then(response => {
                if (response.data.data.team_types) {
                    this.teamTypes.push(...response.data.data.team_types);
                } else {
                    this.showAlert('error', this.messages.errorGet);
                }
            }).catch(err => {
                this.showAlert('error', err.message || this.messages.errorGet);
            });
            this.loading = false;
        },
        async getUsers() {
            this.loading = true;
            await this.api.getAllUsers().then(response => {
                if (response.data.data.users) {
                    this.users = response.data.data.users.filter(user => user.roles.indexOf('deactivated') === -1);
                }
            else {
                this.showAlert('error', this.messages.errorGet);
            }
            }).catch(err => {
                this.showAlert('error', err.messages || this.messages.errorGet);
            });
            this.loading = false;
        },
        openTeamModal(teamId = 0) {
            this.loading = true;
            if (teamId) {
                const currentTeam = this.teams.find(team => team.id === teamId);
                if (!currentTeam) {
                    this.showAlert('error', `Could not find team with id ${teamId}.`);
                    return;
                }
                Object.assign(this.editingTeam, {
                    id: currentTeam.id,
                    name: currentTeam.name,
                    description: currentTeam.description,
                    teamType: {
                        name: currentTeam.teamType.name,
                        id: currentTeam.teamType.id,
                        leader_roles: [ ...currentTeam.teamType.leader_roles ],
                        member_roles: [ ...currentTeam.teamType.member_roles ],
                    },
                    leader: {
                        id: currentTeam.leader?.id,
                        user_name: currentTeam.leader?.user_name,
                        user_id: currentTeam.leader?.user_id,
                        reports_to: {
                            user_name: currentTeam.leader?.reports_to?.user_name || '',
                            user_id: currentTeam.leader?.reports_to?.user_id || null,
                        }
                    },
                    members: [ ...currentTeam.members ],
                    addMember: '',
                    addLeader: '',
                    addManager: '',
                    parent_team_id: currentTeam.parent_team_id,
                });
                this.updateAvailableUsers();
            }
            else {
                this.clearEditValues();
            }
            this.showTeamModal = true;
            this.loading = false;
        },
        createTeam() {
            this.openTeamModal(0);
        },
        editTeam(teamId) {
            if (teamId) this.openTeamModal(teamId);
        },
        async saveTeam() {
            if (this.saving) return;
            this.editingTeam.update = this.editingTeam.id > 0;
            this.saving = true;
            let response = null, errorMessage = this.messages.errorPost;

            if (this.editingTeam.parent_team_id === 0) {
                this.editingTeam.parent_team_id = null; //remove parent
            }

            if (!this.editingTeam.update) {
                response = await this.api.createTeam(this.editingTeam)
                    .catch(err => {
                        errorMessage = err.response?.data?.message ?? err.message ?? errorMessage;
                    });
            }
            else {
                response = await this.api.updateTeam(this.editingTeam)
                    .catch(err => {
                        console.log(err, err.response);
                        errorMessage = err.response?.data?.message ?? err.message ?? errorMessage;
                    });
            }
            if (!response || !response.data.data.status) {
                this.showAlert('error', errorMessage);
                this.saving = false;
                return;
            }
            await this.getTeams();
            this.clearEditValues();
            this.saving = false;
            this.showTeamModal = false;
        },
        confirmDelete(teamId) {
            const deleteTeam = this.teams.find(team => team.id === teamId);
            if (deleteTeam) {
                Object.assign(this.deletingTeam, {
                    id: deleteTeam.id,
                    name: deleteTeam.name,
                });
                this.showDeleteModal = true;
            }
            else {
                this.showAlert('error', `Error: Could not find team with id "${teamId}"`)
            }
        },
        async deleteTeam() {
            this.showDeleteModal = false;
            if (this.deletingTeam.id) {
                this.saving = true;
                const response = await this.api.deleteTeam(this.deletingTeam.id).catch(err => {
                    this.showAlert('error', err.message || 'There was an error deleting the team.');
                });
                if (response.data.data.status) {
                    await this.getTeams();
                }
            }
            this.saving = false;
        },
        cancelDelete() {
            Object.assign(this.deletingTeam, {
                id: null,
                name: '',
            });
            this.showDeleteModal = false;
        },
        clearEditValues() {
            Object.assign(this.editingTeam, {
                id: null,
                name: '',
                description: '',
                teamType: {
                    name: '',
                    id: 0,
                    leader_roles: [],
                    member_roles: [],
                },
                leader: {
                    id: null,
                    user_name: '',
                    user_id: null,
                    reports_to: {
                        user_name: '',
                        user_id: null
                    }
                },
                members: [],
                addMember: '',
                addLeader: '',
                addManager: '',
            });
        },
        closeTeamModal() {
            this.showTeamModal = false;
            this.clearEditValues();
        },
        formatRolesForDisplay(roleArray) {
            if (!Array.isArray(roleArray)) return;
            return roleArray.reduce((output, role) => {
                return role.name
                    ? [ ...output, this.$filters.toProperCase(role.name) ]
                    : output;
            }, []).join(', ');
        },
        updateSelectedTeamType({ id }) {
            const selectedType = this.teamTypes.find(teamType => teamType.id === id);
            if (selectedType) {
                Object.assign(this.editingTeam.teamType, selectedType);
                this.updateAvailableUsers();
            }
        },
        updateAvailableUsers() {
            // const { leader_roles, member_roles } = this.editingTeam.teamType;
            // [ leader_roles, member_roles ].forEach((roleType, index) => {
            //     const roleNames = roleType.map(role => role.name);
            //     const targetArray = index === 0
            //         ? 'leader'
            //         : 'members';
            //     if (Array.isArray(roleType) && roleType.length) {
            //         this.availableUsers[targetArray] = this.users.filter(user => this.arrayContainsAllValues(user.roles, roleNames));
            //     }
            //     else {
            //         this.availableUsers[targetArray] = this.users;
            //     }
            //     this.searchFilteredUsers[targetArray] = this.availableUsers[targetArray];
            //     this.availableUsers.manager = this.users;
            //     this.searchFilteredUsers.manager = this.users;
            // });

            //TODO: leader, manager and member grouping
            this.searchFilteredUsers.leader = this.users;
            this.searchFilteredUsers.manager = this.users;
            this.searchFilteredUsers.members = this.users;
            this.availableUsers.leader = this.users;
            this.availableUsers.manager = this.users;
            this.availableUsers.members = this.users;
        },
        arrayContainsAllValues(targetArray, validationArray) {
            for (let i = 0; i < validationArray.length; i++) {
                if (!targetArray.includes(validationArray[i])) return false;
            }
            return true;
        },
        filterUsersBySearch(searchType, searchValue) {
            if (this.searchFilteredUsers[searchType]) {
                this.searchFilteredUsers[searchType] = searchValue
                    ? this.availableUsers[searchType].filter(user => user.name.toLowerCase().indexOf(searchValue) > -1)
                    : this.availableUsers[searchType];
            }
        },
        assignLeader() {
            if (this.editingTeam.addLeader) {
                const targetUser = this.users.find(user => user.id === this.editingTeam.addLeader);
                Object.assign(this.editingTeam.leader, {
                    user_id: targetUser.id,
                    user_name: targetUser.name,
                });
                this.editingTeam.addLeader = null;
            }
        },
        unAssignLeader() {
            Object.assign(this.editingTeam.leader, {
                user_id: null,
                user_name: '',
            });
        },
        assignManager() {
            if (this.editingTeam.addManager) {
                const targetUser = this.users.find(user => user.id === this.editingTeam.addManager);
                Object.assign(this.editingTeam.leader.reports_to, {
                    user_id: targetUser.id,
                    user_name: targetUser.name,
                });
                this.editingTeam.addManager = null;
            }
        },
        addMember() {
            if (this.editingTeam.addMember) {
                const targetUser = this.users.find(user => user.id === this.editingTeam.addMember);
                if (this.editingTeam.leader.user_id && this.editingTeam.leader.user_id === targetUser.id) {
                    this.showAlert('error', 'Cannot assign Team Leader as a Team Member');
                    return;
                }
                if (this.editingTeam.members.find(member => member.user_id === targetUser.id)) {
                    this.showAlert('error', 'User is already a Team Member');
                    return;
                }
                this.editingTeam.members.push({
                    user_id: targetUser.id,
                    user_name: targetUser.name,
                });
                this.editingTeam.addMember = null;
            }
        },
        removeMember(userId) {
            if (userId) {
                this.editingTeam.members = this.editingTeam.members.filter(member => member.user_id !== userId);
            }
        },
        getParentTeam(parentTeamId) {
            if (!parentTeamId) {
                return null;
            }

            return this.teams.find(team => team.id === parentTeamId);
        }
    }
}

</script>
