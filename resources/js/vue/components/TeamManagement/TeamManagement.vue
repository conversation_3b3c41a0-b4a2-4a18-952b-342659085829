<template>
    <div class="px-10">
        <div class="mt-7 mb-4 text-xl font-medium"  :class="{'text-slate-900': !darkMode, 'text-slate-100': darkMode}">Team Management</div>
        <TeamManagementModule :dark-mode="darkMode"/>
    </div>
</template>

<script>
import TeamManagementModule from "./components/TeamManagementModule.vue";

export default {
    name: "TeamManagement",
    components: {TeamManagementModule},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    }
}
</script>
