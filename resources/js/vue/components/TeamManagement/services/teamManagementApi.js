import axios from 'axios';

export default class TeamManagementApi {

    ROUTE_MANAGEMENT = '/management';
    ROUTE_INFO       = '/info';

    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios(overrideBaseUrl = false) {
        const axiosConfig = {
            baseURL: overrideBaseUrl
                ? ''
                : `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`,
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new TeamManagementApi('internal-api', 'team', 1);
    }

    getAllTeams() {
        return this.axios().get(`${this.ROUTE_INFO}/get-teams`);
    }

    getAllUsers() {
        return this.axios(true).get(`/internal-api/v1/users`);
    }

    getTeamTypes() {
        return this.axios().get(`${this.ROUTE_INFO}/get-team-types`);
    }

    createTeam(teamData = {}) {
        return this.axios().post(`${this.ROUTE_MANAGEMENT}/create-new-team`, {
            ...teamData,
        });
    }

    updateTeam(teamData = {}) {
        return this.axios().patch(`${this.ROUTE_MANAGEMENT}/update-team`, {
            ...teamData,
        });
    }

    deleteTeam(teamId) {
        return this.axios().delete(`${this.ROUTE_MANAGEMENT}/delete-team/${teamId}`);
    }

    getLeaderSalesTeams() {
        return this.axios().get(`${this.ROUTE_INFO}/get-leader-teams`, {
            params: {
                'team_type': 'Sales Team',
            }
        });
    }

    /**
     * Fetches a list of all teams related to the sales.
     *
     * @return {Promise<AxiosResponse<any>>}
     */
    getSalesTeams() {
        return this.axios().get(`${this.ROUTE_INFO}/sales-teams`);
    }

}
