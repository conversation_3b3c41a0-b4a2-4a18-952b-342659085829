<template>
    <div class="mx-10">
        <CampaignCrmDeliveryLogs :darkMode="darkMode" />
    </div>
</template>

<script>
import CampaignCrmDeliveryLogs from "../CampaignCrmDeliveryLogs/CampaignCrmDeliveryLogs.vue";
export default {
    name: "CrmDeliveryLogsTab",
    components: {CampaignCrmDeliveryLogs},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    }
}
</script>

<style scoped>

</style>
