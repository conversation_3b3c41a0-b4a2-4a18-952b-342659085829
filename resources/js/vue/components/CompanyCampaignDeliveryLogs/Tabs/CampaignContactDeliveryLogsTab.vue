<template>
    <div class="mx-10">
        <CampaignContactDeliveryLogs :dark-mode="darkMode"/>
    </div>
</template>

<script>
import CampaignContactDeliveryLogs from "../CampaignContactDeliveryLogs/CampaignContactDeliveryLogs.vue";
export default {
    name: "CampaignContactDeliveryLogsTab",
    components: {CampaignContactDeliveryLogs},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    }
}
</script>
<style scoped>

</style>
