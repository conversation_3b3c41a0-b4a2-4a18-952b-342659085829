import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig)
    }

    static make() {
        return new ApiService('internal-api', 'contact-delivery-logs', 1)
    }

    getContactDeliveryLogs(params = {}) {
        return this.axios().get('/', {
            params
        })
    }

    exportDeliveryLog(params = {})
    {
        return this.axios().get('/export', {
            params
        });
    }

}
