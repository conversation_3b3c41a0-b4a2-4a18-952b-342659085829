<template>
    <div class="flex flex-col">
        <loading-spinner v-if="loading" :dark-mode="darkMode"/>
        <div v-else>
            <Tab
                v-if="isEditMode"
                :dark-mode="darkMode"
                :tab-type="'Normal'"
                :tabs="tabs"
                :tabs-classes="'w-full'"
                @selected="processTabFilter"
            />
            <div v-if="selectedTab === 'Request Details'" :class="requestId !== null ? `grid-cols-3` : `grid-cols-2`" class="grid gap-x-4 gap-y-4">
                <div class="col-span-2 font-semibold text-lg flex items-center">
                    Request Details
                    <button v-if="isEditMode && !isEditing" @click="toggleEditing" class="ml-2">
                        <svg class="w-4 mr-2" viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z" fill="#0081FF"/>
                        </svg>
                    </button>
                </div>
                <div v-if="requestId !== null" class="flex justify-end font-semibold text-lg">Timeline </div>
                <div class="col-span-2 grid grid-cols-2 gap-4 uppercase text-slate-500 text-xs">
                    <div v-for="field in privacyRequestFields" :class=" field?.col ? `col-span-${field.col}` : `col-span-1`" class="flex flex-col gap-2">
                        <div class="text-sm font-semibold"> {{ field.title }}</div>
                        <div v-if="requestId !== null && !isEditing" :class="darkMode ? 'text-white' : 'text-black'" class="font-semibold">
                            {{ data?.[field.id] || 'Not available' }}
                        </div>
                        <custom-input
                            v-else-if="field.id !== 'description'"
                            v-model="data[field.id]"
                            :dark-mode="darkMode"
                            class="focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                        <textarea
                            v-else
                            v-model="data[field.id]"
                            :class="[darkMode ? 'border-blue-700 bg-dark-background text-blue-400' : 'border-grey-200 bg-grey-50']"
                            class="w-full border rounded pl-4 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                            type="text"
                        />
                        <span v-if="errorHandler.errors?.[field.id]" class="text-red-400 text-sm">{{ errorHandler.errors?.[field.id] }}</span>
                    </div>
                    <div class="col-span-2 flex justify-end gap-2 mt-4">
                        <custom-button
                            v-if="isEditing"
                            :dark-mode="darkMode"
                            class="transition-none"
                            @click="closeEditing"
                        >
                            Close
                        </custom-button>
                        <custom-button
                            v-if="isEditing"
                            :dark-mode="darkMode"
                            class="transition-none"
                            @click="updateRequestDetails"
                        >
                            Update
                        </custom-button>
                    </div>

                </div>
                <div v-if="requestId !== null" class="overflow-hidden rounded-lg shadow-md" :class="{ 'text-gray-700': !darkMode, 'text-gray-400 bg-gray-800': darkMode }">
                    <div class="px-4 py-5 sm:px-6">
                        <div class="grid grid-cols-1 gap-y-4">
                            <div class="flex justify-between text-sm font-medium" :class="{ 'text-gray-700': !darkMode, 'text-gray-400': darkMode }">
                                <span class="text-sm font-semibold">Created At</span>
                                <span :class="darkMode ? 'text-white' : 'text-gray-900'" class="font-semibold">{{ data?.created_at ? $filters.dateFromTimestamp(data.created_at) : 'N/A' }}</span>
                            </div>

                            <div class="flex justify-between text-sm font-medium" :class="{ 'text-gray-700': !darkMode, 'text-gray-400': darkMode }">
                                <span class="text-sm font-semibold">Last Updated At</span>
                                <span :class="darkMode ? 'text-white' : 'text-gray-900'" class="font-semibold">{{ data?.updated_at ? $filters.dateFromTimestamp(data.updated_at) : 'N/A' }}</span>
                            </div>

                            <div class="flex justify-between text-sm font-medium" :class="{ 'text-gray-700': !darkMode, 'text-gray-400': darkMode }">
                                <span class="text-sm font-semibold">Deleted At</span>
                                <span :class="darkMode ? 'text-white' : 'text-gray-900'" class="font-semibold">{{ data?.deleted_at ? $filters.dateFromTimestamp(data.deleted_at) : 'N/A' }}</span>
                            </div>

                            <div class="flex justify-between text-sm font-medium" :class="{ 'text-gray-700': !darkMode, 'text-gray-400': darkMode }">
                                <span class="text-sm font-semibold">Status</span>
                                <badge :color="privacyRequestHelper.STATUS_STYLES[data?.status?.id]?.color" :dark-mode="darkMode">
                                    {{ data?.status?.title }}
                                </badge>
                            </div>

                            <div v-if="data.approved_by !== null" class="flex justify-between text-sm font-medium text-gray-900 dark:text-gray-100">
                                <span>Approved By</span>
                                <span v-if="data.approved_by !== null" :class="darkMode ? 'text-white' : 'text-gray-900'" class="font-semibold">{{ data?.approved_by?.name + ' (' + data?.approved_by?.id + ')' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else-if="selectedTab === 'Redaction Records'">
                <simple-table
                    :dark-mode="darkMode"
                    :data="data?.redaction_records"
                    :headers="headers"
                    no-pagination
                    :loading="loading"
                    />

            </div>
            <div v-else class="space-y-6">
                <!-- Sticky Header -->
                <div
                    :class="{'shadow-none': !data?.phone && !data?.email, 'bg-light-module text-gray-700' : !darkMode, 'bg-dark-module text-gray-400': darkMode}"
                    class="grid grid-cols-2 uppercase text-sm p-4 sticky top-0 z-10 shadow-md"
                >
                    <p>{{ data?.phone }}</p>
                    <p>{{ data?.email }}</p>
                </div>

                <div v-if="!data?.scan_response" class="text-center text-red-500 py-4">
                    No Scan Response
                </div>

                <div v-else>
                    <Accordion v-for="(website_response, key) in data?.scan_response" :key="key" :darkMode="darkMode" :title="key" :total="`${data?.totals[key]} total`">
                        <div>
                            <Accordion
                                v-for="model in website_response"
                                :key="model.model"
                                :darkMode="darkMode"
                                :title="model.model"
                                :total="model.records?.length ? `${model.records.length} total` : null"
                                class="mx-auto max-w-screen-md"
                            >
                                <div v-for="values in model.records" :key="values.key"
                                     :class="{'border-light-border hover:bg-light-module text-dark-175': !darkMode, 'border-dark-border hover:bg-dark-module text-gray-300': darkMode }"
                                     class="border py-4 px-6 relative transition-colors duration-150 cursor-pointer"
                                     @click="confirmRedact(key, model.model, values)"
                                >
                                    <div class="px-2 sm:px-0">
                                        <h3 class="text-base font-semibold leading-7"
                                            :class="{ 'text-gray-900': !darkMode, 'text-gray-300': darkMode }">
                                            {{ values.first_name ?? values.firstname ?? 'Not found' }} {{ values.last_name ?? values.lastname ?? 'Not found' }}
                                        </h3>
                                    </div>
                                    <div class="mt-6">
                                        <dl class="grid grid-cols-1 sm:grid-cols-2">
                                            <div class="border-t px-2 py-2 sm:col-span-1 sm:px-0"
                                                 :class="{ 'border-gray-100': !darkMode, 'border-gray-700': darkMode }">
                                                <dt class="text-sm font-medium leading-6"
                                                    :class="{ 'text-gray-900': !darkMode, 'text-gray-300': darkMode }">
                                                    ID
                                                </dt>
                                                <dd class="mt-1 text-sm leading-6"
                                                    :class="{ 'text-gray-700': !darkMode, 'text-gray-400': darkMode }">
                                                    {{ values.key }}
                                                </dd>
                                            </div>

                                            <div class="border-t px-2 py-2 sm:col-span-1 sm:px-0"
                                                 :class="{ 'border-gray-100': !darkMode, 'border-gray-700': darkMode }">
                                                <dt class="text-sm font-medium leading-6"
                                                    :class="{ 'text-gray-900': !darkMode, 'text-gray-300': darkMode }">
                                                    Email address
                                                </dt>
                                                <dd class="mt-1 text-sm leading-6"
                                                    :class="{ 'text-gray-700': !darkMode, 'text-gray-400': darkMode }">
                                                    {{ values.email ?? values.useremail ?? 'Not found' }}
                                                </dd>
                                            </div>

                                            <div class="px-2 py-2 sm:col-span-1 sm:px-0"
                                                 :class="{ 'border-gray-100': !darkMode, 'border-gray-700': darkMode }">
                                                <dt class="text-sm font-medium leading-6"
                                                    :class="{ 'text-gray-900': !darkMode, 'text-gray-300': darkMode }">
                                                    Phone
                                                </dt>
                                                <dd class="mt-1 text-sm leading-6"
                                                    :class="{ 'text-gray-700': !darkMode, 'text-gray-400': darkMode }">
                                                    {{ values.phone ?? values.formatted_phone ?? values.cell_phone ?? values.office_phone ?? values.address?.phone ?? 'Not found' }}
                                                </dd>
                                            </div>
                                        </dl>
                                    </div>
                                </div>
                            </Accordion>
                        </div>
                    </Accordion>
                </div>
            </div>

            <div :class="{'bg-light-module': !darkMode, 'bg-dark-module': darkMode}" class="rounded-b-lg flex justify-end pt-6 gap-4 sticky bottom-0">
                <custom-button
                    v-for="action in privacyRequestActions"
                    v-show="action.permission"
                    :dark-mode="darkMode"
                    class="transition-none"
                    @click="action.callback"
                >
                    {{ action.title }}
                </custom-button>
            </div>
        </div>
        <modal
            v-if="this.showModel"
            :confirm-text="'Redact'"
            :dark-mode="darkMode"
            no-min-height
            small
            @close="closeModal"
            @confirm="redact"
        >
            <template v-slot:header class="font-semibold">
                Are you sure you want to redact this model?
            </template>
            <template v-slot:content>
                <div class="overflow-hidden rounded-lg shadow-md" :class="{'bg-white text-gray-900': !darkMode, 'bg-gray-800 text-gray-100': darkMode}">
                    <div class="px-4 py-5 sm:px-6">
                        <div class="space-y-4">
                            <div class="text-lg font-semibold">
                                <span class="font-normal">Website: </span>
                                <span>{{ this.redactWebsite }}</span>
                            </div>

                            <div class="text-lg font-semibold">
                                <span class="font-normal">Model: </span>
                                <span>{{ this.redactModel }}</span>
                            </div>

                            <div class="p-4 rounded-md border border-gray-300" :class="{'bg-gray-50': !darkMode, 'bg-gray-900 border-gray-700': darkMode}">
                                <div class="grid grid-cols-1 gap-y-2">
                                    <div v-for="(value, key) in redactPayload" :key="key" class="flex justify-between text-sm border-b pb-2" :class="{'border-gray-200 text-gray-900': !darkMode, 'bg-gray-9000 border-gray-600 text-gray-100 border-gray-700': darkMode}">
                                        <span class="font-semibold">{{ key }}:</span>
                                        <span>{{ value }}</span>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </template>
        </modal>
    </div>
</template>
<script>
import Api from '../services/api'
import usePrivacyRequestHelper from "../../../../composables/usePrivacyRequestHelper";
import Badge from "../../Shared/components/Badge.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import useErrorHandler from "../../../../composables/useErrorHandler";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Tab from "../../Shared/components/Tab.vue";
import ActivityComments from "../../Companies/components/activity/comments/ActivityComments.vue";
import Modal from "../../Shared/components/Modal.vue";
import Accordion from "../../Shared/components/Accordion.vue";
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";

const privacyRequestHelper = usePrivacyRequestHelper()
const privacyRequestFields = [
    {title: "First Name", id: 'first_name'},
    {title: "Last Name", id: 'last_name'},
    {title: "Email", id: 'email'},
    {title: "Phone", id: 'phone'},
    {title: "Address", id: 'address'},
    {title: "Source", id: 'source'},
    {title: "Description", id: 'description', col: 2},
]
const tabs = [
    {name: 'Request Details', current: true},
    {name: 'Scan Results', current: false},
    {name: 'Redaction Records', current: false}
]
export default {
    name: "PrivacyRequestModalContent",
    components: {SimpleTable, Accordion, Modal, ActivityComments, Tab, LoadingSpinner, CustomInput, CustomButton, Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        requestId: {
            type: Number,
            default: null,
        }
    },
    emits: ['close', 'create', 'actioned'],
    data() {
        return {
            permissionsStore: useRolesPermissions(),
            errorHandler: useErrorHandler(),
            privacyRequestHelper,
            privacyRequestFields,
            api: Api.make(),
            data: {},
            loading: false,
            selectedTab: 'Request Details',
            tabs,
            showModel: false,
            redactWebsite: null,
            redactModel: null,
            redactPayload: null,
            isEditing: false,
            originalData: {},
            headers: [
                {title: "Status", field: "status"},
                {title: "Initiator", field: 'user_name'},
                {title: "Model Id", field: 'model_id'},
                {title: "Model", field: 'model_type', cols: 3},
                {title: "Last Updated At", field: 'updated_at'},
            ],
            backdoor: 0,
        }
    },
    created() {
        if (this.requestId !== null) {
            this.handlePrivacyAction(this.getPrivacyRequestData)
        }
    },
    computed: {
        privacyRequestActions() {
            const condition = this.requestId === null ? 'new' : 'view'
            this.backdoor;
            const actions = [
                {
                    useCase: 'view',
                    permission: this.permissionsStore.hasPermission(PERMISSIONS.PRIVACY_MANAGEMENT_REQUEST_SEARCH),
                    title: "Scan for PPI",
                    callback: () => this.handlePrivacyAction(this.scanSystem)
                },
                {
                    useCase: this.data?.totals ? 'view' : 'hidden',
                    permission: this.permissionsStore.hasPermission(PERMISSIONS.PRIVACY_MANAGEMENT_REQUEST_REDACT),
                    title: "Finish Redacting",
                    callback: () => this.handlePrivacyAction(this.finishRedaction)
                },
                {
                    useCase: 'new',
                    permission: this.permissionsStore.hasPermission(PERMISSIONS.PRIVACY_MANAGEMENT_REQUEST_CREATE),
                    title: "+ Privacy Request",
                    callback: () => this.handlePrivacyAction(this.createPrivacyRequest)
                },
            ]

            return actions.filter(action => action.useCase === condition)
        },
        isEditMode() {
            return this.requestId !== null;
        }
    },
    watch: {
        isEditMode(newVal) {
            this.isEditing = !newVal;
            if (newVal) {
                this.handlePrivacyAction(this.getPrivacyRequestData)
            }
        }
    },
    methods: {
        async handlePrivacyAction(callback) {
            this.loading = true
            try {
                await callback()
            } catch (err) {
                this.errorHandler.handleError(err)
                console.error(err)
            }
            this.loading = false
        },

        async getPrivacyRequestData() {
            const response = await this.api.getPrivacyRequest(this.requestId);
            this.data = response.data.data
            this.originalData = { ...this.data };
            this.changeTab('Request Details')
            this.backdoor ++;
        },
        async scanSystem() {
            const response = await this.api.scanSystem(this.requestId);
            this.$emit('actioned', response.data.data.message);
        },
        confirmRedact(website, model, dataPayload) {
            this.redactWebsite = website;
            this.redactModel = model;
            this.redactPayload = dataPayload;
            this.showModel = true;
        },
        async redact() {
            const response = await this.api.redact(this.requestId, this.redactWebsite, this.redactModel, this.redactPayload);
            this.$emit('actioned', response.data.data.message);
        },
        async createPrivacyRequest() {
            try {
                const response = await this.api.createPrivacyRequest(this.data);
                this.$emit('create', response.data.data.message);
                this.errorHandler.resetError();
            } catch (error) {
                console.error(error);
                this.errorHandler.handleError(error);
            }
        },
        async finishRedaction() {
            const response = await this.api.finishRedaction(this.requestId);
            this.$emit('actioned', response.data.data.message);
        },
        processTabFilter(filter) {
            this.selectedTab = filter;
        },
        changeTab(tab) {
            for (let t of tabs) {
                t.current = t.name === tab;
            }
        },
        closeModal() {
            this.redactWebsite = null;
            this.redactModel = null;
            this.redactPayload = null;
            this.isEditing = false;
            this.showModel = false;
        },
        toggleEditing() {
            this.isEditing = !this.isEditing;
            this.data = this.isEditing ? { ...this.originalData } : this.originalData;
        },
        closeEditing() {
            this.data = { ...this.originalData }; // Revert to original data
            this.isEditing = false;
        },
        async updateRequestDetails() {
            this.loading = true;
            try {
                await this.api.updatePrivacyRequest(this.requestId, this.data);
                this.isEditing = false;
                this.errorHandler.resetError();
            } catch (error) {
                console.error(error);
                this.errorHandler.handleError(error);
            }
            this.loading = false;
        },
    }
}
</script>
