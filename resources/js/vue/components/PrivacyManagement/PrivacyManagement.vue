<template>
    <div>
        <alerts-container
            v-if="alertActive"
            :dark-mode="darkMode"
            :alert-type="alertType"
            :text="alertText"/>
        <simple-table
            title="Privacy Requests"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :pagination-data="paginationData"
            v-model="tableFilter"
            :table-filters="tableFilters"
            @update:modelValue="getPrivacyRequests"
            @search="tableFilter.page = 1; getPrivacyRequests()"
            @reset="handleReset"
            has-row-click
            @click-row="handleRowClicked"
            :loading="loading"
            :currentPerPage="tableFilter.perPage"
        >
            <template v-slot:custom-buttons>
                <custom-button v-if="canCreatePrivacyRequests" @click="showModal = true" :dark-mode="darkMode">+ Privacy Request</custom-button>
            </template>
            <template v-slot:row.col.status="{item,value}">
                <badge :color="privacyRequestHelper.STATUS_STYLES[value.id].color" :dark-mode="darkMode">{{value.title}}</badge>
            </template>
            <template v-slot:row.col.source="{item,value}">
                <div class="text-sm truncate">
                    {{value}}
                </div>
            </template>
            <template v-slot:row.col.created_at="{item,value}">
                <p>{{ $filters.dateFromTimestamp(value) }}</p>
            </template>
            <template v-slot:row.col.updated_at="{item,value}">
                <p>{{ $filters.dateFromTimestamp(value) }}</p>
            </template>
        </simple-table>
        <modal
            no-buttons
            @close="handleCloseModal"
            v-if="showModal"
            :dark-mode="darkMode"
            no-min-height
            small
            :container-classes="'pt-2 pr-8 pb-8 pl-8'"
        >
            <template v-slot:header class="font-semibold">
                {{ selectedRequest !== null ? "Data" : "Create New Privacy Request" }}
            </template>
            <template v-slot:content>
                <privacy-request-modal-content
                    :request-id="selectedRequest"
                    :dark-mode="darkMode"
                    @close="handleCloseModal"
                    @create="handleCreate"
                    @actioned="handleAction"
                />
            </template>
        </modal>
    </div>
</template>
<script>
import Api from './services/api';
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import Badge from "../Shared/components/Badge.vue";
import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum";
import {SimpleTableHiddenFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes";
import Modal from "../Shared/components/Modal.vue";
import PrivacyRequestModalContent from "./components/PrivacyRequestModalContent.vue";
import usePrivacyRequestHelper from "../../../composables/usePrivacyRequestHelper";
import CustomButton from "../Shared/components/CustomButton.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin";

const privacyRequestHelper = usePrivacyRequestHelper()

export default {
    name: "PrivacyManagement",
    components: {AlertsContainer, CustomButton, PrivacyRequestModalContent, Modal, Badge, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    mixins: [AlertsMixin],
    data() {
        return {
            permissionStore: useRolesPermissions(),
            api: Api.make(),
            privacyRequestHelper,
            data: [],
            tableFilter: {},
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'search',
                    title: 'ID or Email'
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'status',
                    title: 'Status',
                    options: [
                        {id: 'initial', name: 'Initial'},
                        {id: 'scanned', name: 'Scanned'},
                        {id: 'processing', name: 'Processing'},
                        {id: 'done', name: 'Done'},
                    ]
                },
            ],
            paginationData: {},
            headers: [
                {title: 'Id', field: 'id'},
                {title: "Email", field: 'email'},
                {title: "Source", field: 'source'},
                {title: "Status", field: 'status'},
                {title: "Created At", field: 'created_at'},
                {title: "Last Updated At", field: 'updated_at'},
            ],
            selectedRequest: null,
            showModal: false,
            loading: false,
        }
    },
    created() {
        this.tableFilter = {
            page: 1,
            perPage: 10
        }
        this.getPrivacyRequests();
    },
    computed: {
        canCreatePrivacyRequests() {
            return this.permissionStore.hasPermission(PERMISSIONS.PRIVACY_MANAGEMENT_REQUEST_CREATE);
        },
    },
    methods: {
        async getPrivacyRequests() {
            console.log(this.tableFilter);
            this.loading = true;
            try {
                const response = await this.api.getPrivacyRequests(this.tableFilter)
                const { data, links, meta } = response.data;
                this.data = data;
                this.paginationData = {links, ...meta}
            } catch (e) {
                console.error(e)
            }
            this.loading = false;
        },
        handleReset() {
            this.tableFilter = {
                ...this.tableFilter,
                page: 1,
                perPage: 10
            }
            this.getPrivacyRequests();
        },
        handleRowClicked(row) {
            this.selectedRequest = row.id;
            this.showModal = true;
        },
        handleAction(message) {
            this.showAlert('success', message)
            this.handleCloseModal()
        },
        handleCreate(message) {
            this.showAlert('success', message)
            this.getPrivacyRequests()
            this.handleCloseModal()
        },
        handleCloseModal() {
            this.showModal = false
            this.selectedRequest = null
        }
    }
}
</script>
