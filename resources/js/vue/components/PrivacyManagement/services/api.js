import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'privacy-management', 1);
    }

    getPrivacyRequests(params = {}) {
        return this.axios().get('/', {
            params
        })
    }

    getPrivacyRequest(requestId) {
        return this.axios().get(`/${requestId}`, {
            params: {
                request_id: requestId
            }
        })
    }

    createPrivacyRequest(requestData) {
        return this.axios().post(`/`, requestData)
    }

    scanSystem(requestId) {
        return this.axios().post(`/scan/${requestId}`, {
            request_id: requestId
        })
    }

    redact(requestId, website, model, dataPayload) {
        return this.axios().post(`/redact/${requestId}`, {
            request_id: requestId,
            website: website,
            model: model,
            data: dataPayload
        })
    }

    finishRedaction(requestId) {
        return this.axios().post(`/redact/${requestId}/finish`, {
            request_id: requestId,
        })
    }

    updatePrivacyRequest(requestId, requestData) {
        return this.axios().post(`/update-privacy-request/${requestId}`, requestData);
    }

}
