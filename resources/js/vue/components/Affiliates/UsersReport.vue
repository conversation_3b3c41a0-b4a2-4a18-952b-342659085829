<template>
  <div>
      <AlertsContainer v-if="alertActive" :dark-mode="darkMode" :alert-type="alertType" :text="alertText"/>
      <simple-table
          :title="affiliate.name + ' Users'"
          :dark-mode="darkMode"
          :loading="loading"
          :data="data"
          :headers="headers"
          @search="handleSearch"
          @reset="handleReset"
          row-classes="gap-5 grid items-center py-3 rounded px-5 text-sm"
          no-pagination
      >
          <template #title-actions>
              <custom-button @click="toggleAffiliateModal(null, actions.CREATE)" :dark-mode="darkMode">
                  + Add User
              </custom-button>
          </template>
          <template #row.col.view_lead_details="{item,value}">
              <badge :color="value ? simpleIcon.colors.GREEN : simpleIcon.colors.BLUE" :dark-mode="darkMode">
                  {{value ? 'Yes' : 'No'}}
              </badge>
          </template>
          <template #row.col.actions="{item}">
              <div class="flex gap-2 items-center">
                  <simple-icon v-if="canUpdate" @click="toggleAffiliateModal(item, actions.EDIT)" clickable :dark-mode="darkMode" :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.PENCIL_SQUARE" tooltip="Edit"/>
                  <simple-icon v-if="canDelete" @click="toggleAffiliateModal(item, actions.DELETE)" clickable :dark-mode="darkMode" :color="simpleIcon.colors.RED" :icon="simpleIcon.icons.BIN" tooltip="Delete"/>
              </div>
          </template>
      </simple-table>
      <affiliate-user-modal
          @confirm="handleConfirm"
          @close="toggleAffiliateModal"
          v-if="userAction"
          :dark-mode="darkMode"
          :action="userAction"
          :model-value="selectedUser"
          :affiliate-uuid="affiliate?.uuid"
      />
  </div>
</template>
<script>
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import Api from "./api/api.js"
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import useAffiliate from "../../../composables/useAffiliate.js";
import {useAlerts} from "../../composables/useAlerts.js";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store.js";
import CustomButton from "../Shared/components/CustomButton.vue";
import Badge from "../Shared/components/Badge.vue";
import AffiliateUserModal from "./modal/AffiliateUserModal.vue";
import AffiliateModal from "./modal/AffiliateModal.vue";
const simpleIcon = useSimpleIcon();
const {actions} = useAffiliate();

const {alertType, alertText, alertActive, displayAlert} = useAlerts();

export default {
    name: "UsersReport",
    components: {
        AffiliateModal,
        AffiliateUserModal,
        Badge,
        CustomButton,
        AlertsContainer,
        SimpleIcon,
        SimpleTable,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        affiliate: {
            type: Object,
            default: {},
        },
        modelValue: {
            type: Object,
            default: {},
        }
    },
    emits: ['change-screen', 'update:modelValue'],
    data() {
        return {
            alertType,
            alertText,
            alertActive,
            displayAlert,
            actions,
            simpleIcon,
            Api,
            loading: false,
            headers: [
                {title: 'ID', field: 'id'},
                {title: 'Name', field: 'name', cols: 2},
                {title: 'Email', field: 'email', cols: 2},
                {title: 'Email Verified', field: 'email_verified_at'},
                {title: 'View Lead Details', field: 'view_lead_details'},
                {title: 'Actions', field: 'actions'},
            ],
            data: [],
            permissionStore: useRolesPermissions(),
            showPayoutStrategyModal: false,
            selectedUser: {},
            userAction: null,
        }
    },
    computed: {
        canUpdate() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_AFFILIATES_UPDATE)
        },
        canDelete() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_AFFILIATES_DELETE)
        },
    },
    created() {
        this.handleSearch();
    },
    methods: {
        async handleSearch() {
            await this.search();
        },
        async handleReset() {
            await this.search();
        },
        async search() {
            this.loading = true;
            const response = await Api.getUsers(this.affiliate.uuid);
            this.data = response.data.data;
            this.loading = false;
        },
        toggleAffiliateModal(user = {}, action = '') {
            this.selectedUser = user;
            this.userAction = action;
        },
        handleConfirm(message, type) {
            displayAlert(message, type);
            if (type === 'success') {
                this.search();
                this.toggleAffiliateModal();
            }
        },
    },
}
</script>
