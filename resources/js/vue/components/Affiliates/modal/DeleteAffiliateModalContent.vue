
<template>
    <div class="relative">
        <div class="font-medium whitespace-pre-line">
            Are you sure you want to delete  <span class="text-primary-500 font-semibold">{{ modelValue.name }}</span>?
            Deleting this affiliate will prevent any future leads from being assigned to this affiliate's campaigns
        </div>
        <div class="flex flex-1 justify-end sticky bottom-0 bg-light-module pt-5">
            <custom-button
                @click="destroy"
                color="red"
                :dark-mode="darkMode">
                Delete
            </custom-button>
        </div>
    </div>
</template>
<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import Api from "../api/api.js";

export default {
    name: "DeleteAffiliateModalContent",
    components: {CustomButton},
    emits: ['update:modelValue', 'confirm'],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            loading: false,
            api: Api,
        }
    },
    methods: {
        async destroy() {
            this.loading = true;
            this.api.deleteAffiliate(this.modelValue.uuid)
                .then(() => this.$emit('confirm', 'Deleted Affiliate', 'success'))
                .catch(() => this.$emit('confirm', 'Failed to delete affiliate', 'error'));
            this.loading = false;
        }
    }
}
</script>
