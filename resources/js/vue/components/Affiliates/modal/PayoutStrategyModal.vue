<template>
    <pop-upify classes="absolute shadow rounded-lg w-1/4" :dark-mode="darkMode">
        <card-wrapper
            :dark-mode="darkMode"
            title="Payment Strategy Log"
            :loading="loading"
        >
            <template v-slot:header-actions>
                <button class="modal-default-button mr-3" @click="$emit('close')"
                        :class="{'text-black': !darkMode, 'text-grey-120': darkMode}">
                    <svg class="w-4 fill-current" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M5.9035 4.78018L9.32136 1.36237C9.47273 1.21896 9.56036 1.01977 9.56036 0.796696C9.56036 0.358513 9.20184 0 8.76366 0C8.54058 0 8.34139 0.0876191 8.19799 0.231024L4.78018 3.65686L1.36237 0.231024C1.21896 0.0876191 1.01977 0 0.796696 0C0.358513 0 0 0.358513 0 0.796696C0 1.01977 0.0876191 1.21896 0.231024 1.36237L3.65686 4.78018L0.238999 8.19799C0.0876269 8.34139 0 8.54058 0 8.76366C0 9.20184 0.358513 9.56036 0.796696 9.56036C1.01977 9.56036 1.21896 9.47274 1.36237 9.32933L4.78018 5.9035L8.19799 9.32136C8.34139 9.47273 8.54058 9.56036 8.76366 9.56036C9.20184 9.56036 9.56036 9.20184 9.56036 8.76366C9.56036 8.54058 9.47274 8.34139 9.32933 8.19799L5.9035 4.78018Z"/>
                    </svg>
                </button>
            </template>
            <div class="flex flex-col gap-2 p-4">
                <labeled-value label="Events">
                    <event-log
                        class="max-h-96"
                        :dark-mode="darkMode"
                        :data="data"
                    >
                        <template v-slot:event-icon="{event}" >
                            <simple-icon
                                v-if="event.active"
                                :icon="simpleIcon.icons.CHECK_CIRCLE"
                                :color="simpleIcon.colors.GREEN"
                            />
                        </template>
                        <template v-slot:title="{event}">
                            <div class="flex items-center gap-2 font-semibold">
                                {{
                                    payoutStrategy.typeStyle[event.type].prefix
                                    + event.value + payoutStrategy.typeStyle[event.type].suffix
                                    + ' ' + payoutStrategy.typeStyle[event.type].short_title
                                }}
                                <badge v-if="event.active" color="green">Active</badge>
                            </div>
                        </template>

                    </event-log>
                </labeled-value>
            </div>

        </card-wrapper>
    </pop-upify>
</template>
<script>
import Modal from "../../Shared/components/Modal.vue";
import PopUpify from "../../Shared/components/PopUpify.vue";
import EventLog from "../../Companies/components/Territory/components/shared/EventLog.vue";
import CardWrapper from "../../Companies/components/Territory/components/shared/CardWrapper.vue";
import Api from "../api/api.js";
import Badge from "../../Shared/components/Badge.vue";
import usePayoutStrategy from "../../../../composables/usePayoutStrategy.js";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";

const payoutStrategy = usePayoutStrategy();
const simpleIcon = useSimpleIcon();

export default {
    name: "PayoutStrategyModal",
    components: {SimpleIcon, LabeledValue, Badge, CardWrapper, EventLog, PopUpify, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        affiliateUuid: {
            type: [String, null],
            default: null,
        },
    },
    emits: ['close'],
    data() {
        return {
            data: [],
            loading: true,
            api: Api,
            payoutStrategy,
            simpleIcon,
        }
    },
    created() {
        this.listPayoutStrategies();
    },
    methods: {
        async listPayoutStrategies() {
            this.loading = true;
            const response = await this.api.getAffiliateStrategyEvents(this.affiliateUuid);
            this.data = response.data.data;
            this.loading = false;
        }
    }

}

</script>
