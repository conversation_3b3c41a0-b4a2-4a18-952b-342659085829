<template>
    <div class="flex flex-col">
        <h3 class="font-semibold">Active Payment Strategy</h3>
        <div class="grid grid-cols-2 gap-3">
            <labeled-value label="Type">
                <dropdown
                    v-model="modelValue.type"
                    :dark-mode="darkMode"
                    :options="options"
                    :loading="optionsLoading"
                    :disabled="optionsLoading"
                />
            </labeled-value>
            <labeled-value :label="paymentStrategyStyle.value_title">
                <simple-stepper
                    v-model="modelValue.value"
                    :dark-mode="darkMode"
                    :prefix="paymentStrategyStyle.prefix"
                    :suffix="paymentStrategyStyle.suffix"
                    :max="max"
                    :min="min"
                />
            </labeled-value>
            <simple-alert v-if="modelValue.type" class="col-span-full"
                          content="Payout calculation is done on a per lead basis"/>
        </div>
    </div>
</template>
<script>
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import ApiService from "../api/strategy-api.js";
import usePayoutStrategy from "../../../../composables/usePayoutStrategy.js";
import SimpleStepper from "../SimpleStepper.vue";
import SimpleAlert from "../../Shared/components/SimpleAlert.vue";

const payoutStrategy = usePayoutStrategy();

export default {
    name: "PayoutStrategy",
    components: {SimpleAlert, SimpleStepper, Dropdown, LabeledValue},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
        }
    },
    emits: ['update:modelValue'],
    data() {
        return {
            options: [],
            api: ApiService.make(),
            optionsLoading: false,
            payoutStrategy,
            min: null,
            max: null,
        }
    },
    async created() {
        await this.listTypes();
        if (!this.modelValue?.type) {
            this.$emit('update:modelValue', {...this.modelValue, type: payoutStrategy.type.REVENUE_PERCENTAGE})
        }
    },
    computed: {
        paymentStrategyStyle() {
            return payoutStrategy.getTypeStyle(this.modelValue.type)
        }
    },
    methods: {
        async listTypes() {
            this.optionsLoading = true;
            const response = await this.api.listTypes();
            this.options = response.data.data;
            this.optionsLoading = false;
        },
    },
    watch: {
        'modelValue.type'(newValue) {
            if (this.options.length > 0) {
                const payload = this.options.find(option => option.id === newValue).payload;

                this.modelValue.value = payload.default;
                this.min = payload.boundary.min;
                this.max = payload.boundary.max;
            }
        }
    }
}
</script>
