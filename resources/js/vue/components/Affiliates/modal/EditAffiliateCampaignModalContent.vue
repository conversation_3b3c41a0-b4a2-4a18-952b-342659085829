<template>
    <div class="flex flex-col gap-2">
        <h3 class="font-semibold">Name</h3>
        <custom-input
            :dark-mode="darkMode"
            placeholder="Enter Affiliate Name"
            v-model="modelValue.name"
        />
        <h3 class="font-semibold">Account ID</h3>
        <custom-input
            :dark-mode="darkMode"
            placeholder="Enter Account ID"
            v-model="modelValue.account_id"
        />
        <h3 class="font-semibold">Campaign ID</h3>
        <custom-input
            :dark-mode="darkMode"
            placeholder="Enter Campaign ID"
            v-model="modelValue.campaign_id"
        />
        <div class="flex flex-1 justify-end sticky bottom-0 bg-light-module pt-5">
            <custom-button
                @click="update"
                :dark-mode="darkMode"
            >
                Update
            </custom-button>
        </div>
    </div>
</template>
<script>
import Api from "../api/api.js";
import CustomButton from "../../Shared/components/CustomButton.vue";
import PayoutStrategy from "./PayoutStrategy.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";

export default {
    name: "EditAffiliateCampaignModalContent",
    components: {CustomInput, PayoutStrategy: PayoutStrategy, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {},
        },
        affiliateUuid: {
            type: [String, null],
            default: null,
        }
    },
    data() {
        return {
            loading: false,
            api: Api,
        }
    },
    emits: ['update:modelValue', 'confirm'],
    methods: {
        async update() {
            this.loading = true;
            await this.api.updateCampaign(this.affiliateUuid, this.modelValue.id, this.modelValue)
                .then(() => this.$emit('confirm', 'Updated affiliate campaign', 'success'))
                .catch(() => this.$emit('confirm', 'Failed to update affiliate campaign', 'error'))

            this.loading = false;
        }
    }
}
</script>
