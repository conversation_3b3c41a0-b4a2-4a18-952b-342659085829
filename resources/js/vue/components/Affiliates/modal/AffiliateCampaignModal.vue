<template>
     <modal
         :dark-mode="darkMode"
         @close="$emit('close')"
         small
         no-buttons
         no-min-height
    >
         <template v-slot:header>
             <span class="font-medium flex items-center gap-2">
                 <simple-icon
                     :dark-mode="darkMode"
                     :icon="icon[action]"
                 />
                 {{title[action]}}
             </span>
         </template>
         <template v-slot:content>
            <component
                :dark-mode="darkMode"
                :is="component[action]"
                :model-value="campaign"
                :affiliate-uuid="affiliateUuid"
                @confirm="(message, status) => $emit('confirm', message, status)"
            />
         </template>
    </modal>
</template>
<script>
import Modal from "../../Shared/components/Modal.vue";
import Api from "../api/api.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import useAffiliate from "../../../../composables/useAffiliate.js";
import {markRaw} from "vue";
import EditAffiliateCampaignModalContent from "./EditAffiliateCampaignModalContent.vue";
const simpleIcon = useSimpleIcon();
const {actions} = useAffiliate()

const title = {
    [actions.EDIT]: 'Edit Affiliate',
}

const icon = {
    [actions.EDIT]: simpleIcon.icons.PENCIL_SQUARE,
}

const component = {
    [actions.EDIT]: markRaw(EditAffiliateCampaignModalContent),
}

export default {
    name: "AffiliateCampaignModal",
    components: {SimpleIcon, Modal},
    emits: ['close', 'confirm'],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        affiliateUuid: {
            type: [String, null],
            default: null,
        },
        campaignId: {
            type: [Number, null],
            default: null,
        },
        action: {
            type: String,
            default: actions.EDIT,
            required: true,
        }
    },
    data() {
        return {
            campaign: {
                name: '',
                account_id: null,
                campaign_id: null,
            },
            loading: false,
            api: Api,
            simpleIcon,
            actions,
            title,
            icon,
            component,
        }
    },
    created() {
        if (this.campaignId) {
            this.getCampaign();
        }
    },
    methods: {
        async getCampaign() {
            this.loading = true;
            const response = await this.api.getCampaign(this.affiliateUuid, this.campaignId);
            this.campaign = response.data.data;
            this.loading = false;
        },
    }
}

</script>

