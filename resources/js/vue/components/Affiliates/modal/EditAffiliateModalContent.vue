
<template>
    <div class="flex flex-col gap-2">
        <h3 class="font-semibold">Name</h3>
        <custom-input
            :dark-mode="darkMode"
            placeholder="Enter Affiliate Name"
            v-model="modelValue.name"
        />
        <payout-strategy
            :dark-mode="darkMode"
            v-model="modelValue.strategy"
        />
        <div class="flex flex-1 justify-end sticky bottom-0 bg-light-module pt-5">
            <custom-button
                @click="update"
                :dark-mode="darkMode"
            >
                Update
            </custom-button>
        </div>
    </div>
</template>
<script>
import Api from "../api/api.js";
import CustomButton from "../../Shared/components/CustomButton.vue";
import PayoutStrategy from "./PayoutStrategy.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";

export default {
    name: "EditAffiliateModalContent",
    components: {CustomInput, PayoutStrategy: PayoutStrategy, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            loading: false,
            api: Api,
        }
    },
    emits: ['update:modelValue', 'confirm'],
    methods: {
        async update() {
            this.loading = true;
            await this.api.updateAffiliate(this.modelValue.uuid, this.modelValue)
                .then(() => this.$emit('confirm', 'Updated affiliate', 'success'))
                .catch(() => this.$emit('confirm', 'Failed to update affiliate', 'error'))

            this.loading = false;
        }
    }
}
</script>
