<template>
     <modal
         :dark-mode="darkMode"
         @close="$emit('close')"
         small
         no-min-height
         no-buttons
    >
         <template v-slot:header>
             <span class="font-medium flex items-center gap-2">
                 <simple-icon
                     :dark-mode="darkMode"
                     :icon="icon[action]"
                 />
                 {{title[action]}}
             </span>
         </template>
         <template v-slot:content>
             <div v-if="action !== actions.DELETE" class="flex flex-col gap-2">
                 <labeled-value label="Name">
                     <custom-input
                         placeholder="Enter Name"
                         v-model="user.name"
                         :dark-mode="darkMode"
                     />
                 </labeled-value>
                 <labeled-value label="Email">
                     <custom-input
                         placeholder="Enter Email"
                         v-model="user.email"
                         :dark-mode="darkMode"
                     />
                 </labeled-value>
                 <labeled-value label="View Lead Details">
                     <custom-checkbox v-model="user.view_lead_details" :dark-mode="darkMode"/>
                 </labeled-value>
             </div>
             <div v-else>
                 Are you sure you want to delete
                 <span class="font-semibold text-primary-500">{{user.name}}</span>?
             </div>
             <div class="flex flex-1 justify-end sticky bottom-0 bg-light-module pt-5">
                 <custom-button
                     v-if="action === actions.CREATE"
                     @click="createAffiliateUser"
                     :dark-mode="darkMode"
                 >
                     Create
                 </custom-button>
                 <custom-button
                     v-else-if="action === actions.EDIT"
                     @click="updateAffiliateUser"
                     :dark-mode="darkMode"
                 >
                     Update
                 </custom-button>
                 <custom-button
                     v-else
                     @click="deleteAffiliateUser"
                     :dark-mode="darkMode"
                     color="red"
                 >
                     Delete
                 </custom-button>
             </div>
         </template>
    </modal>
</template>
<script>
import Modal from "../../Shared/components/Modal.vue";
import Api from "../api/api.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import useAffiliate from "../../../../composables/useAffiliate.js";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomCheckbox from "../../Shared/SlideWizard/components/CustomCheckbox.vue";
const simpleIcon = useSimpleIcon();
const {actions} = useAffiliate()

const title = {
    [actions.CREATE]: 'Create Affiliate User',
    [actions.EDIT]: 'Edit Affiliate User',
    [actions.DELETE]: 'Delete Affiliate User',
}

const icon = {
    [actions.CREATE]: simpleIcon.icons.PLUS,
    [actions.EDIT]: simpleIcon.icons.PENCIL_SQUARE,
    [actions.DELETE]: simpleIcon.icons.BIN,
}

export default {
    name: "AffiliateUserModal",
    components: {CustomCheckbox, CustomButton, CustomInput, LabeledValue, SimpleIcon, Modal},
    emits: ['close', 'confirm'],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        affiliateUuid: {
            type: String,
            default: null,
        },
        modelValue: {
            type: Object,
            default: {},
        },
        action: {
            type: String,
            default: actions.CREATE,
            required: true,
        }
    },
    data() {
        return {
            loading: false,
            api: Api,
            simpleIcon,
            actions,
            title,
            icon,
            user: {
                id: null,
                name: '',
                email: '',
                view_lead_details: false,
            }
        }
    },
    created() {
        if (this.modelValue) {
            this.user = {...this.modelValue}
        }
    },
    methods: {
        async createAffiliateUser() {
            this.loading = true;
            this.api.createUser(this.affiliateUuid, this.user)
                .then(() => this.$emit('confirm', 'Created User', 'success'))
                .catch(() => this.$emit('confirm', 'Failed to Create User', 'error'));
            this.loading = false;
        },
        async updateAffiliateUser() {
            this.loading = true;
            this.api.updateUser(this.affiliateUuid, this.user.id, {...this.user})
                .then(() => this.$emit('confirm', 'Updated User', 'success'))
                .catch(() => this.$emit('confirm', 'Failed to Update User', 'error'));
            this.loading = false;
        },
        async deleteAffiliateUser() {
            this.loading = true;
            this.api.deleteUser(this.affiliateUuid, this.user.id)
                .then(() => this.$emit('confirm', 'Deleted User', 'success'))
                .catch(() => this.$emit('confirm', 'Failed to Delete User', 'error'));
            this.loading = false;
        }
    }
}

</script>

