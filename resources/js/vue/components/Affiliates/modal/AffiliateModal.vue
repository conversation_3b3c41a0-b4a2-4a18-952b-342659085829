<template>
     <modal
         :dark-mode="darkMode"
         @close="$emit('close')"
         small
         no-buttons
         no-min-height
    >
         <template v-slot:header>
             <span class="font-medium flex items-center gap-2">
                 <simple-icon
                     :dark-mode="darkMode"
                     :icon="icon[action]"
                 />
                 {{title[action]}}
             </span>
         </template>
         <template v-slot:content>
            <component
                :dark-mode="darkMode"
                :is="component[action]"
                :model-value="affiliate"
                @confirm="(message, status) => $emit('confirm', message, status)"
            />
         </template>
    </modal>
</template>
<script>
import Modal from "../../Shared/components/Modal.vue";
import Api from "../api/api.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import useAffiliate from "../../../../composables/useAffiliate.js";
import {markRaw} from "vue";
import CreateAffiliateModalContent from "./CreateAffiliateModalContent.vue";
import EditAffiliateModalContent from "./EditAffiliateModalContent.vue";
import DeleteAffiliateModalContent from "./DeleteAffiliateModalContent.vue";
const simpleIcon = useSimpleIcon();
const {actions} = useAffiliate()

const title = {
    [actions.CREATE]: 'Create Affiliate',
    [actions.EDIT]: 'Edit Affiliate',
    [actions.DELETE]: 'Delete Affiliate',
}

const icon = {
    [actions.CREATE]: simpleIcon.icons.PLUS,
    [actions.EDIT]: simpleIcon.icons.PENCIL_SQUARE,
    [actions.DELETE]: simpleIcon.icons.BIN,
}

const component = {
    [actions.CREATE]: markRaw(CreateAffiliateModalContent),
    [actions.EDIT]: markRaw(EditAffiliateModalContent),
    [actions.DELETE]: markRaw(DeleteAffiliateModalContent),
}

export default {
    name: "AffiliateModal",
    components: {SimpleIcon, Modal},
    emits: ['close', 'confirm'],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        affiliateUuid: {
            type: [String, null],
            default: null,
        },
        action: {
            type: String,
            default: actions.CREATE,
            required: true,
        }
    },
    data() {
        return {
            affiliate: {
                name: '',
                strategy: {
                    type: '',
                    value: 0,
                }
            },
            loading: false,
            api: Api,
            simpleIcon,
            actions,
            title,
            icon,
            component,
        }
    },
    computed: {
        editing() {
            return !!this.affiliateUuid
        }
    },
    created() {
        if (this.affiliateUuid) {
            this.getAffiliate();
        }
    },
    methods: {
        async getAffiliate() {
            this.loading = true;
            const response = await this.api.getAffiliate(this.affiliateUuid);
            this.affiliate = response.data.data;
            this.loading = false;
        },
    }
}

</script>

