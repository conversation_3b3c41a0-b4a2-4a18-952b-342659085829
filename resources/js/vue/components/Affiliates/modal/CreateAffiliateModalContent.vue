<template>
    <div class="flex flex-col gap-2">
        <h3 class="font-semibold">Name</h3>
        <custom-input
            :dark-mode="darkMode"
            placeholder="Enter Affiliate Name"
            v-model="modelValue.name"
        />
        <payout-strategy
            :dark-mode="darkMode"
            v-model="modelValue.strategy"
        />
        <div class="flex flex-1 justify-end sticky bottom-0 bg-light-module pt-5">
            <custom-button
                @click="create"
                :dark-mode="darkMode"
            >
                Create
            </custom-button>
        </div>
    </div>
</template>
<script>
import Api from "../api/api.js";
import CustomButton from "../../Shared/components/CustomButton.vue";
import PayoutStrategy from "./PayoutStrategy.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";

export default {
    name: "CreateAffiliateModalContent",
    components: {CustomInput, PayoutStrategy: PayoutStrategy, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            api: Api,
            loading: false,
        }
    },
    emits: ['update:modelValue', 'confirm'],
    methods: {
        async create() {
            this.loading = true;
            this.api.createAffiliate(this.modelValue)
                .then(() => this.$emit('confirm', 'Created Affiliate', 'success'))
                .catch(() => this.$emit('confirm', 'Failed to create affiliate', 'error'));

            this.loading = false;
        }
    }
}
</script>
