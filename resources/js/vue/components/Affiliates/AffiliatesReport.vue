<template>
  <div>
      <AlertsContainer v-if="alertActive" :dark-mode="darkMode" :alert-type="alertType" :text="alertText"/>
      <simple-table
          :dark-mode="darkMode"
          :loading="loading"
          v-model="filters"
          :data="data"
          :table-filters="tableFilters"
          :pagination-data="pagination"
          :headers="headers"
          @search="handleSearch"
          @reset="handleReset"
          @page-change="handleSearch"
          @per-page-change="handleSearch"
          row-classes="gap-5 grid items-center py-3 rounded px-5 text-sm"
      >
          <template #custom-buttons>
              <custom-button
                  v-if="canCreate"
                  :dark-mode="darkMode"
                  @click="toggleAffiliateModal(null,actions.CREATE)"
              >
                  + Add Affiliate
              </custom-button>
          </template>
          <template #row.col.name="{item,value}">
              <div class="flex items-center gap-2">
                  {{value}}
                  <simple-icon
                      v-if="canShadow"
                      :dark-mode="darkMode"
                      :icon="simpleIcon.icons.EYE"
                      tooltip="Shadow Affiliate"
                      clickable
                      :color="simpleIcon.colors.BLUE"
                      @click="shadowAffiliate(item)"
                  />
              </div>
          </template>
          <template #row.col.strategy="{item}">
              <div class="flex gap-2 items-center">
                  <payout-strategy-badge
                      :dark-mode="darkMode"
                      :type="item.strategy?.type"
                      :value="item.strategy?.value"
                  />
                  <badge
                      @click="togglePayoutStrategyModal(item)"
                      :dark-mode="darkMode"
                      class="cursor-pointer text-primary-500"
                      v-if="item.strategy_count > 1"
                  >
                      All
                  </badge>
              </div>
          </template>
          <template #row.col.view="{item}">
              <div class="flex gap-2 items-center">
                  <simple-icon @click="changeScreen(item, 'users')" clickable :dark-mode="darkMode" :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.USER_GROUP" tooltip="Users"/>
                  <simple-icon @click="changeScreen(item, 'campaigns')" clickable :dark-mode="darkMode" :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.MEGAPHONE" tooltip="Campaigns"/>
                  <simple-icon @click="changeScreen(item, 'lead-details')" clickable :dark-mode="darkMode" :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.IDENTIFICATION" tooltip="Leads"/>
              </div>
          </template>
          <template #row.col.actions="{item}">
              <div class="flex gap-2 items-center">
                  <simple-icon v-if="canUpdate" @click="toggleAffiliateModal(item, actions.EDIT)" clickable :dark-mode="darkMode" :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.PENCIL_SQUARE" tooltip="Edit"/>
                  <simple-icon v-if="canDelete" @click="toggleAffiliateModal(item, actions.DELETE)" clickable :dark-mode="darkMode" :color="simpleIcon.colors.RED" :icon="simpleIcon.icons.BIN" tooltip="Delete"/>
              </div>
          </template>
      </simple-table>
      <affiliate-modal
          @confirm="handleConfirm"
          @close="toggleAffiliateModal"
          v-if="affiliateAction"
          :dark-mode="darkMode"
          :action="affiliateAction"
          :affiliate-uuid="newAffiliateUuid"
      />
      <payout-strategy-modal
          v-if="showPayoutStrategyModal"
          :dark-mode="darkMode"
          :affiliate-uuid="newAffiliateUuid"
          @close="showPayoutStrategyModal = false"
      />
  </div>
</template>
<script>
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import Api from "./api/api.js"
import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import PayoutStrategyBadge from "./PayoutStrategyBadge.vue";
import Badge from "../Shared/components/Badge.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import useAffiliate from "../../../composables/useAffiliate.js";
import AffiliateModal from "./modal/AffiliateModal.vue";
import {useAlerts} from "../../composables/useAlerts.js";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store.js";
import PayoutStrategyModal from "./modal/PayoutStrategyModal.vue";
const simpleIcon = useSimpleIcon();
const {actions} = useAffiliate();


const DEFAULT_TABLE_FILTER = {
    date_range: {
        from: new Date(new Date().setDate(new Date().getDate() - 14)).toISOString(),
        to: new Date(new Date().setHours(23, 59, 59, 999)).toISOString(),
    },
    page: 1,
    perPage: 100,
    sort_by: [
        'revenue:desc',
    ]
}


const {alertType, alertText, alertActive, displayAlert} = useAlerts();

export default {
    name: "AffiliatesReport",
    components: {
        PayoutStrategyModal,
        CustomButton,
        AlertsContainer,
        AffiliateModal,
        SimpleIcon,
        Badge, PayoutStrategyBadge,
        SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {},
        },
        globalFilters: {
            type: [Array, null],
            default: null
        }
    },
    emits: ['change-screen', 'update:modelValue'],
    data() {
        return {
            DEFAULT_TABLE_FILTER,
            alertType,
            alertText,
            alertActive,
            displayAlert,
            actions,
            simpleIcon,
            Api,
            loading: false,
            headers: [
                {title: 'ID', field: 'id', sortable: true},
                {title: 'Name', field: 'name', cols: 2, sortable: true},
                {title: 'Total Revenue', field: 'revenue', sortable: true},
                {title: 'Total Payout', field: 'payout', sortable: true},
                {title: 'Leads Created', field: 'lead_count', sortable: true},
                {title: 'Leads Good To Sell', field: 'lead_gts_count', sortable: true},
                {title: 'Legs Sold', field: 'leg_count', sortable: true},
                {title: 'Strategy', field: 'strategy'},
                {title: 'View', field: 'view'},
                {title: 'Actions', field: 'actions'},
            ],
            data: [],
            filters: {},
            pagination: {},
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'name',
                    title: 'Affiliate Name'
                },
            ],
            newAffiliateUuid: null,
            affiliateAction: null,
            permissionStore: useRolesPermissions(),
            showPayoutStrategyModal: false,
        }
    },
    computed: {
        canShadow() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_AFFILIATES_SHADOW)
        },
        canCreate() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_AFFILIATES_CREATE)
        },
        canUpdate() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_AFFILIATES_UPDATE)
        },
        canDelete() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_AFFILIATES_DELETE)
        },
    },
    created() {
        this.tableFilters = [...this.tableFilters, ...this.globalFilters]
        this.filters = {...DEFAULT_TABLE_FILTER, ...this.modelValue};
        this.handleSearch();
    },
    methods: {
        async handleSearch() {
            await this.search();
        },
        async handleReset() {
            this.filters = {
                ...DEFAULT_TABLE_FILTER
            }
            await this.search();
        },
        async search() {
            this.loading = true;
            const response = await Api.listAffiliates(this.filters);
            const { data, links, meta} = response.data;
            this.data = data;
            this.pagination = {links, ...meta};
            this.loading = false;
        },
        toggleAffiliateModal(item = {}, action = '') {
            this.newAffiliateUuid = item?.uuid;
            this.affiliateAction = action;
        },
        handleConfirm(message, type) {
            displayAlert(message, type);
            if (type === 'success') {
                this.search();
                this.toggleAffiliateModal();
            }
        },
        changeScreen(affiliate, screen) {
            this.$emit('change-screen', {affiliate: affiliate, newScreen: screen});
        },
        shadowAffiliate(affiliate) {
            Api.getAffiliateShadowUrl(affiliate.uuid).then(resp => window.open(resp.data.data.url, '_blank'))
        },
        togglePayoutStrategyModal(item = {}) {
            this.newAffiliateUuid = item?.uuid;
            this.showPayoutStrategyModal = !this.showPayoutStrategyModal;
        }
    },
    watch: {
        'filters.date_range'(newValue) {
            this.$emit('update:modelValue', {...this.modelValue, date_range: newValue})
        },
        'filters.industries'(newValue) {
            this.$emit('update:modelValue', {...this.modelValue, industries: newValue})
        },
    }
}
</script>

