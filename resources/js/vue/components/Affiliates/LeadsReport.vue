<template>
  <div>
      <simple-table
          :dark-mode="darkMode"
          :loading="loading"
          v-model="filters"
          :data="data"
          :table-filters="tableFilters"
          :pagination-data="pagination"
          :headers="headers"
          @search="handleSearch"
          @reset="handleReset"
          @per-page-change="handleSearch"
          @page-change="handleSearch"
          row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
      >
          <template v-slot:row.col.id="{item,value}">
              <a
                  class="text-primary-500 text-sm"
                  :href="`consumer-product/?consumer_id=${item.consumer.id}`"
                  target="_blank">
                  {{value}}
              </a>
          </template>
          <template v-slot:row.col.created_at="{item,value}">
              <div class="flex gap-2 text-sm">
                  {{value}}
              </div>
          </template>
          <template v-slot:row.col.good_to_sell="{item,value}">
              <div class="text-sm">
                  {{value ? 'Yes' : 'No'}}
              </div>
          </template>
          <template v-slot:row.col.name="{item,value}">
              <div class="text-sm">
                  {{item.consumer.name}}
              </div>
          </template>
          <template v-slot:row.col.email="{item,value}">
              <div class="text-sm truncate">
                  {{item.consumer.email}}
              </div>
          </template>
          <template v-slot:row.col.phone="{item,value}">
              <div class="text-sm">
                  {{item.consumer.phone}}
              </div>
          </template>
          <template v-slot:row.col.legs_registered="{item,value}">
              <div class="text-sm">
                  {{item.consumer.legs_requested}}
              </div>
          </template>
          <template v-slot:row.col.address="{item,value}">
              <div class="flex flex-col text-sm">
                  <div>{{ item.address.street }}</div>
                  <div>{{ item.address.city }}, {{ item.address.state }} {{ item.address.zip_code }}</div>
              </div>
          </template>
          <template v-slot:row.col.industry="{item,value}">
              <div class="text-sm">
                  {{item.service_product.industry_service.industry.name}}
              </div>
          </template>
          <template v-slot:row.col.payout="{item,value}">
              <div class="text-sm flex gap-2 items-center">
                  {{value}}
                  <payout-strategy-badge
                      v-if="item.strategy"
                      :type="item.strategy.type"
                      :value="item.strategy.value"
                      :dark-mode="darkMode"
                  />
              </div>
          </template>
      </simple-table>
  </div>
</template>
<script>
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import Api from "./api/api.js"
import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {SimpleTableHiddenFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import PayoutStrategyBadge from "./PayoutStrategyBadge.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
const simpleIcon = useSimpleIcon();
const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 100,
}

export default {
    name: "LeadsReport",
    components: {
        PayoutStrategyBadge,
        SimpleIcon,
        SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        affiliate: {
            type: Object,
            default: {},
        },
        campaign: {
            type: Object,
            default: null,
        },
        modelValue: {
            type: Object,
            default: {},
        },
        globalFilters: {
            type: [Array, null],
            default: false,
        }
    },
    emits: ['change-screen', 'update:modelValue'],
    data() {
        return {
            DEFAULT_TABLE_FILTER,
            simpleIcon,
            Api,
            loading: false,
            headers: [
                {title:'ID', field: 'id'},
                {title:'Date Created', field: 'created_at'},
                {title:'Good to Sell', field: 'good_to_sell'},
                {title:'Status', field: 'status'},
                {title:'Name', field: 'name'},
                {title:'Email', field: 'email', cols: 2},
                {title:'Phone', field: 'phone'},
                {title:'Legs Registered', field: 'legs_registered'},
                {title:'Address', field: 'address', cols: 2,},
                {title:'Industry', field: 'industry'},
                {title:'Revenue', field: 'revenue'},
                {title:'Payout', field: 'payout', cols: 2},
                {title:'Legs Sold', field: 'legs_sold'},
            ],
            data: [],
            filters: {},
            pagination: {},
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'consumer_name',
                    title: 'Consumer Name',
                },
            ],
        }
    },
    created() {
        this.tableFilters = [...this.tableFilters, ...this.globalFilters]
        this.filters = {...DEFAULT_TABLE_FILTER, ...this.modelValue};
        this.handleSearch();
    },
    methods: {
        async handleSearch() {
            await this.search();
        },
        async handleReset() {
            this.filters = {...DEFAULT_TABLE_FILTER};
            await this.search();
        },
        async search() {
            this.loading = true;

            const response = this.campaign
                ? await Api.listCampaignConsumers(this.affiliate.uuid, this.campaign.id, this.filters)
                : await Api.listAffiliateConsumers(this.affiliate.uuid, this.filters)

            const { data, links, meta} = response.data;
            this.data = data;
            this.pagination = {links, ...meta};
            this.loading = false;
        },
    },
    watch: {
        'filters.date_range'(newValue) {
            this.$emit('update:modelValue', {...this.modelValue, date_range: newValue})
        },
        'filters.industries'(newValue) {
            this.$emit('update:modelValue', {...this.modelValue, industries: newValue})
        },
    }
}
</script>
