<template>
  <div>
      <simple-table
          :dark-mode="darkMode"
          :loading="loading"
          v-model="filters"
          :data="data"
          :table-filters="tableFilters"
          :pagination-data="pagination"
          :headers="headers"
          @search="handleSearch"
          @reset="handleReset"
          @page-change="handleSearch"
          @per-page-change="handleSearch"
          row-classes="gap-5 grid items-center py-3 rounded px-5 text-sm"
      >
          <template #row.col.view="{item}">
              <div class="flex gap-2 items-center">
                  <simple-icon
                      v-if="canUpdate"
                      @click="toggleAffiliateModal(item, actions.EDIT)"
                      clickable
                      :dark-mode="darkMode"
                      :color="simpleIcon.colors.BLUE"
                      :icon="simpleIcon.icons.PENCIL_SQUARE"
                      tooltip="Edit"
                  />
                  <simple-icon
                      @click="changeScreen(item, 'lead-details')"
                      clickable
                      :dark-mode="darkMode"
                      :color="simpleIcon.colors.BLUE"
                      :icon="simpleIcon.icons.IDENTIFICATION"
                      tooltip="Leads"
                  />
              </div>
          </template>
      </simple-table>
      <affiliate-campaign-modal
          @confirm="handleConfirm"
          @close="toggleAffiliateModal"
          v-if="affiliateAction"
          :dark-mode="darkMode"
          :action="affiliateAction"
          :campaign-id="campaignId"
          :affiliate-uuid="affiliate.uuid"
      />
  </div>
</template>
<script>
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import Api from "./api/api.js"
import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {SimpleTableHiddenFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store.js";
import useAffiliate from "../../../composables/useAffiliate.js";
import AffiliateModal from "./modal/AffiliateModal.vue";
import AffiliateCampaignModal from "./modal/AffiliateCampaignModal.vue";
import {useAlerts} from "../../composables/useAlerts.js";
const simpleIcon = useSimpleIcon();
const {actions} = useAffiliate();
const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 100,
    sort_by: [
        'revenue:desc',
    ]
}

const {alertType, alertText, alertActive, displayAlert} = useAlerts();

export default {
    name: "CampaignsReport",
    components: {
        AffiliateCampaignModal,
        AffiliateModal,
        SimpleIcon,
        SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        affiliate: {
            type: Object,
            default: {},
        },
        modelValue: {
            type: Object,
            default: {},
        },
        globalFilters: {
            type: [Array, null],
            default: null,
        }
    },
    emits: ['change-screen', 'update:modelValue'],
    data() {
        return {
            DEFAULT_TABLE_FILTER,
            actions,
            simpleIcon,
            Api,
            loading: false,
            headers: [
                {title: 'ID', field: 'id', sortable: true},
                {title: 'Name', field: 'name', cols: 2, sortable: true},
                {title: 'Total Revenue', field: 'revenue', sortable: true},
                {title: 'Total Payout', field: 'payout', sortable: true},
                {title: 'Leads Created', field: 'lead_count', sortable: true},
                {title: 'Leads Good To Sell', field: 'lead_gts_count', sortable: true},
                {title: 'Legs Sold', field: 'leg_count', sortable: true},
                {title: 'View', field: 'view'},
            ],
            data: [],
            filters: {},
            pagination: {},
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'name',
                    title: 'Campaign Name'
                },
            ],
            permissionStore: useRolesPermissions(),
            campaignId: null,
            affiliateAction: null,
        }
    },
    computed: {
        canUpdate() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_AFFILIATES_UPDATE)
        },
    },
    created() {
        this.tableFilters = [...this.tableFilters, ...this.globalFilters]
        this.filters = {...DEFAULT_TABLE_FILTER, ...this.modelValue};
        this.handleSearch();
    },
    methods: {
        toggleAffiliateModal(item = {}, action = '') {
            this.campaignId = item?.id;
            this.affiliateAction = action;
        },
        handleConfirm(message, type) {
            displayAlert(message, type);
            if (type === 'success') {
                this.search();
                this.toggleAffiliateModal();
            }
        },
        async handleSearch() {
            await this.search();
        },
        async handleReset() {
            this.filters = {...DEFAULT_TABLE_FILTER, ...this.modelValue};
            await this.search();
        },
        async search() {
            this.loading = true;
            const response = await Api.listCampaigns(this.affiliate.uuid, this.filters);
            const { data, links, meta} = response.data;
            this.data = data;
            this.pagination = {links, ...meta};
            this.loading = false;
        },
        changeScreen(campaign, screen) {
            this.$emit('change-screen', {affiliate: this.affiliate, newScreen: screen, campaign: campaign});
        },
    },
    watch: {
        'filters.date_range'(newValue) {
            this.$emit('update:modelValue', {...this.modelValue, date_range: newValue})
        },
        'filters.industries'(newValue) {
            this.$emit('update:modelValue', {...this.modelValue, industries: newValue})
        },
    }
}
</script>
