<template>
    <div class="flex items-center">
        <button
            @click="decrement"
            type="button"
            class="bg-slate-100 h-9  border border-gray-300 rounded-s p-3 focus:ring-gray-100 focus:ring-2 focus:outline-none"
            :disabled="minReached"
            :class="[minReached ? '' : 'hover:bg-gray-200']"
        >
            <svg
                class="w-3 h-3"
                :class="[minReached ? 'text-slate-400' : 'text-gray-900']"
                 aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                 fill="none" viewBox="0 0 18 2">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M1 1h16"/>
            </svg>
        </button>
        <div
            class="border flex items-center bg-gray-50 border-x-0 border-gray-300 text-center h-9 gap-1 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 px-2">
            <div v-if="prefix" class=" text-gray-500 text-sm">
                {{ prefix }}
            </div>
            <input
                type="number"
                class="border-0 ring-0 focus:border-none focus:outline-none focus:ring-0 p-0 bg-transparent"
                required
                :style="{width: modelValueSize}"
                :value="modelValue"
                @input="$emit('update:modelValue', $event.target.value)"
                :max="max"
                :min="min"
            />
            <div v-if="suffix" class=" text-gray-500 text-sm">
                {{ suffix }}
            </div>
        </div>
        <button type="button"
                @click="increment"
                class="bg-gray-100 h-9 border border-gray-300 rounded-e p-3  "
                :disabled="maxReached"
                :class="[maxReached ? '' : 'hover:bg-gray-200 focus:ring-gray-100 focus:ring-2 focus:outline-none text-gray-900']"
        >
            <svg
                class="w-3 h-3 "
                :class="[maxReached ? 'text-slate-400' : '']"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                 fill="none" viewBox="0 0 18 18">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 1v16M1 9h16"/>
            </svg>
        </button>
    </div>
</template>
<script>
export default {
    name: "SimpleStepper",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Number,
            default: 0,
        },
        prefix: {
            type: [String, null],
            default: null,
        },
        suffix: {
            type: [String, null],
            default: null,
        },
        max: {
            type: Number,
            default: null,
        },
        min: {
            type: Number,
            default: null
        }
    },
    emits: ['update:modelValue'],
    computed: {
        modelValueSize(){
            const size = this.modelValue?.toString()?.length ?? 1;
            return `${size}ch`
        },
        maxReached() {
            if (this.max !==null) {
                return this.modelValue >= this.max
            }
        },
        minReached() {
            if (this.min !==null) {
                return this.modelValue <= this.min
            }
        }
    },
    methods: {
        decrement() {
            this.$emit('update:modelValue', +this.modelValue - 1);
        },
        increment() {
            this.$emit('update:modelValue', +this.modelValue + 1);
        }
    },
    watch: {
        'modelValue'(newValue) {
            if (this.max !== null && newValue > this.max) {
                this.$emit('update:modelValue', +this.max);
            }
            if (this.min !== null && newValue < this.min) {
                this.$emit('update:modelValue', +this.min);
            }
            if (!newValue) {
                this.$emit('update:modelValue', +this.min);
            }
        }
    }
}
</script>
