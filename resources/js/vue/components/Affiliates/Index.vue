<template>
    <page :dark-mode="darkMode" :title="screenTitle[screen]">
        <template #title>
            <div class="flex gap-2 items-center font-medium">
                <div v-for="(item, index) in breadcrumbs" :key="index" class="flex items-center py-2 text-grey-300">
                    <simple-icon
                        v-if="index > 0"
                        :icon="simpleIcon.icons.CHEVRON_RIGHT"
                        :dark-mode="darkMode"
                        :color="index === breadcrumbs.length - 1 ? simpleIcon.colors.BLACK : ''"
                    />
                    <h3
                        :class="[
                            index === breadcrumbs.length - 1 ? 'text-black' : '',
                            item.onClick ? 'hover:text-grey-400 transition duration-200 cursor-pointer' : ''
                            ]"
                        @click="item.onClick && item.onClick()"
                    >
                        {{ item.title }}
                    </h3>
                </div>
            </div>
        </template>
        <template #content>
            <component
                :is="componentMap[screen]"
                @change-screen="changeScreen"
                :dark-mode="darkMode"
                :affiliate="userAffiliate"
                :campaign="campaign"
                v-model="activeFilters"
                :global-filters="filters"
            />
        </template>
    </page>
</template>

<script setup>
import {computed, markRaw, onMounted, ref} from 'vue'
    import Page from "../Shared/components/Page.vue";
    import AffiliatesReport from "./AffiliatesReport.vue";
    import CampaignsReport from "./CampaignsReport.vue";
    import LeadsReport from "./LeadsReport.vue";
    import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
    import useSimpleIcon from "../../../composables/useSimpleIcon.js";
    import UsersReport from "./UsersReport.vue";
    import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
    import {SimpleTableHiddenFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
    import Api from "./api/api.js"



    const simpleIcon = useSimpleIcon();

    const props = defineProps({
        darkMode: {
            type: Boolean,
            default: false
        }
    });

    const pages = {
        AFFILIATES: 'affiliates',
        USERS: 'users',
        CAMPAIGNS: 'campaigns',
        LEAD: 'lead-details',
    }

    const activeFilters = ref({})
    const filters = ref([
        {
            location: SimpleTableFilterTypesEnum.HIDDEN,
            type: SimpleTableHiddenFilterTypesEnum.DATE_RANGE,
            field: 'date_range',
            title: 'Date Range',
            removable: false
        },
        {
            location: SimpleTableFilterTypesEnum.HIDDEN,
            type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
            field: 'industries',
            title: 'Industry',
            options: [],
        },
    ])

    const screen = ref(pages.AFFILIATES);

    const screenTitle = {
        [pages.AFFILIATES]: "Affiliates",
        [pages.USERS]: "Users",
        [pages.CAMPAIGNS]: "Campaigns",
        [pages.LEAD]: "Lead Details",
    };

    const componentMap = {
        [pages.AFFILIATES]: markRaw(AffiliatesReport),
        [pages.USERS]: markRaw(UsersReport),
        [pages.CAMPAIGNS]: markRaw(CampaignsReport),
        [pages.LEAD]: markRaw(LeadsReport),
    }

    const userAffiliate = ref(null);
    const campaign = ref(null);

    const changeScreen = (e) => {
        screen.value = e.newScreen;

        userAffiliate.value = [pages.USERS, pages.CAMPAIGNS, pages.LEAD].includes(e.newScreen) ? e.affiliate : null;
        campaign.value = e.newScreen === pages.LEAD ? e.campaign : null;
    };

    const breadcrumbs = computed(() => {
        const items = [
            {
                title: screenTitle[pages.AFFILIATES],
                onClick: () => changeScreen({newScreen: pages.AFFILIATES})
            }
        ];

        if (userAffiliate.value) {
            items.push({
                title: userAffiliate.value.name,
            });
        }

        if (campaign.value) {
            items.push({
                title: screenTitle[pages.CAMPAIGNS],
                onClick: () => changeScreen({newScreen: pages.CAMPAIGNS, affiliate: userAffiliate.value})
            });

            items.push({ title: campaign.value.name });
        }

        if (screen.value !== pages.AFFILIATES) {
            items.push({
                title: screenTitle[screen.value],
            });
        }

        return items;
    })

    async function getGlobalFilters() {
        try {
            const response = await Api.getFilterOptions();
            const industriesFilterIndex = filters.value.findIndex(f => f.field === 'industries');

            filters.value[industriesFilterIndex].options = response.data.data.industries;
        } catch (error) {
            console.error('Failed to fetch options:', error)
        }
    }

    onMounted(() => {
        getGlobalFilters();
    });
</script>
