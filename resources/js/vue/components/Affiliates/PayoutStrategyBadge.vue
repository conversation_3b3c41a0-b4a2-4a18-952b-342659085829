<template>
    <badge :dark-mode="darkMode">
        {{ strategy }}
    </badge>
</template>
<script>
import usePayoutStrategy from "../../../composables/usePayoutStrategy.js";
import Badge from "../Shared/components/Badge.vue";
const payoutStrategy = usePayoutStrategy();
export default {
    name: "PayoutStrategyBadge",
    components: {Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: '',
        },
        value: {
            type: Number,
            default: null,
        }
    },
    data() {
        return {
            payoutStrategy,
        }
    },
    computed: {
        strategy() {
            if(Object.values(this.payoutStrategy.type).includes(this.type)) {
                const style = this.payoutStrategy.getTypeStyle(this.type);

                return style.prefix + this.value + style.suffix + ' ' + style.short_title;
            }

            return 'No Strategy'
        }
    }
}
</script>
