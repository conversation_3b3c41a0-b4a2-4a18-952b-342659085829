import axios from 'axios';

export default class Api {
    static #axios() {
        return axios.create({
            baseURL: '/internal-api/v1/affiliates-portal'
        });
    }

    static listAffiliates(params) {
        return Api.#axios().get('affiliates', {params})
    }

    static listCampaigns(uuid, params) {
        return Api.#axios().get(`/affiliates/${uuid}/campaigns/`, {params});
    }

    static listAffiliateConsumers(uuid, params) {
        return Api.#axios().get(`affiliates/${uuid}/lead-details`, {params})
    }

    static listCampaignConsumers(uuid, campaignId, params) {
        return Api.#axios().get(`affiliates/${uuid}/campaigns/${campaignId}/lead-details`, {params})
    }

    static createAffiliate(data) {
        return Api.#axios().post(`affiliates`, data);
    }

    static updateAffiliate(uuid, data) {
        return Api.#axios().patch(`affiliates/${uuid}`, data);
    }

    static deleteAffiliate(uuid) {
        return Api.#axios().delete(`affiliates/${uuid}`);
    }

    static getAffiliate(uuid) {
        return Api.#axios().get(`affiliates/${uuid}`)
    }

    static getCampaign(uuid, campaignId) {
        return Api.#axios().get(`affiliates/${uuid}/campaigns/${campaignId}`)
    }

    static updateCampaign(uuid, campaignId, data) {
        return Api.#axios().patch(`affiliates/${uuid}/campaigns/${campaignId}`, data);
    }

    static getUsers(uuid) {
        return Api.#axios().get(`affiliates/${uuid}/users`);
    }

    static createUser(uuid, data) {
        return Api.#axios().post(`affiliates/${uuid}/users`, data);
    }

    static updateUser(uuid, userId, data) {
        return Api.#axios().patch(`affiliates/${uuid}/users/${userId}`, data);
    }

    static deleteUser(uuid, userId) {
        return Api.#axios().delete(`affiliates/${uuid}/users/${userId}`);
    }

    static getAffiliateShadowUrl(uuid) {
        return Api.#axios().get(`affiliates/${uuid}/shadow-url`)
    }

    static getAffiliateStrategyEvents(uuid) {
        return Api.#axios().get(`affiliates/${uuid}/strategy-events`)
    }

    static getFilterOptions() {
        return Api.#axios().get(`filter-options`);
    }
}
