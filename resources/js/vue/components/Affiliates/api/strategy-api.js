
export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    static make() {
        return new ApiService('internal-api', 'affiliates-portal/affiliates',1);
    }

    axios() {
        const config = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`,
        }

        return axios.create(config);
    }

    listTypes() {
        return this.axios().get('/payment-strategy/types')
    }
}
