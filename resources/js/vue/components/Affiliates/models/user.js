export default class User {
    id = 0;
    email_verified_at = '';
    affiliate_id = '';
    created_at = '';
    updated_at = '';
    name = '';
    email = '';
    view_lead_details = 0;

    constructor(user) {
        const classMembers = Object.getOwnPropertyNames(this);

        for(const [key, value] of Object.entries(user || {})) {
            if(classMembers.includes(key)) {
                this[key] = value;
            }
        }
    }
}
