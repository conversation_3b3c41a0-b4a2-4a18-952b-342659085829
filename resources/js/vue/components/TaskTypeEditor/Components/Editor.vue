<template>
    <div class="border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="px-5 pt-5 pb-4 flex items-center justify-between">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Task View Editor</h5>
        </div>
        <div class="border-t border-b px-5 h-120 overflow-y-auto"
             :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">

            <div v-if="payload.id !== null">
                <div class="flex items-center justify-between pt-5 pb-3">
                    <p class="text-md text-grey-300 font-semibold">
                        Edit Task Type:
                        <span :class="{'text-grey-800': !darkMode, 'text-white ': darkMode}">
                            {{ payload.name }}
                        </span>
                    </p>
                    <ActionsHandle @delete="deleteTaskType" :noEditButton="true" :dark-mode="darkMode"></ActionsHandle>
                </div>

                <div class="grid grid-cols-2 mt-4">
                    <div>
                        <p class="uppercase font-semibold text-xs mb-1"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Task Name
                        </p>
                        <input class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                               placeholder="Task Name"
                               v-model="payload.name"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
                    </div>
                </div>

                <div class="grid grid-cols-1 mt-4">
                    <div>
                        <p class="uppercase font-semibold text-xs mb-1"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Modules
                        </p>
                        <dropdown-selector v-model="payload.modules" dropdown-title="Select Module" :dropdown-options="modules"></dropdown-selector>
                    </div>
                </div>

                <div class="col-span-2 mt-5">
                    <button @click="save" class="transition duration-200 font-semibold bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5">
                        Save
                    </button>
                    <button @click="cancel" class="transition duration-200 ml-3 text-sm font-medium focus:outline-none py-2 rounded-md px-4"
                            :class="{'bg-grey-250 hover:bg-light-background text-white': !darkMode, 'bg-grey-500 hover:bg-grey-600 text-white': darkMode}">
                        Cancel
                    </button>
                </div>
            </div>
            <div v-else-if="payload.id == null" class="flex h-full justify-center items-center">
                <div :class="{'text-grey-200': !darkMode, 'text-blue-500': darkMode}">
                    <div class="flex justify-center mb-3">
                        <svg class="fill-current" width="80" height="84" viewBox="0 0 80 84" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M67.3684 4.2C60.4042 4.2 54.7368 9.8532 54.7368 16.8C54.7368 18.9084 55.3095 20.8656 56.2274 22.617L51.5284 28.0854C48.7488 26.2021 45.4654 25.1967 42.1053 25.2C38.9937 25.2 36.1053 26.1072 33.6 27.5772L27.6463 21.6384C28.829 19.515 29.4574 17.129 29.4737 14.7C29.4737 6.594 22.8632 0 14.7368 0C6.61053 0 0 6.594 0 14.7C0 22.806 6.61053 29.4 14.7368 29.4C17.2674 29.4 19.6126 28.7028 21.6926 27.5772L27.6463 33.516C26.1041 36.0799 25.281 39.0101 25.2632 42C25.2632 46.1874 26.8632 49.9758 29.4105 52.9242L22.2863 60.0306L22.3916 60.1356C20.6702 59.2684 18.7706 58.8112 16.8421 58.8C9.8779 58.8 4.21053 64.4532 4.21053 71.4C4.21053 78.3468 9.8779 84 16.8421 84C23.8063 84 29.4737 78.3468 29.4737 71.4C29.4737 69.4008 28.9642 67.5402 28.1347 65.8644L28.24 65.9694L36.4674 57.7626C38.24 58.3926 40.1179 58.8 42.1053 58.8C51.3937 58.8 58.9474 51.2652 58.9474 42C58.9293 39.391 58.2961 36.8228 57.099 34.503L62.3747 28.3626C63.9074 29.0262 65.5916 29.4 67.3684 29.4C74.3326 29.4 80 23.7468 80 16.8C80 9.8532 74.3326 4.2 67.3684 4.2ZM16.8421 75.6C15.7254 75.6 14.6544 75.1575 13.8648 74.3699C13.0752 73.5822 12.6316 72.5139 12.6316 71.4C12.6316 70.2861 13.0752 69.2178 13.8648 68.4301C14.6544 67.6425 15.7254 67.2 16.8421 67.2C17.9588 67.2 19.0298 67.6425 19.8194 68.4301C20.609 69.2178 21.0526 70.2861 21.0526 71.4C21.0526 72.5139 20.609 73.5822 19.8194 74.3699C19.0298 75.1575 17.9588 75.6 16.8421 75.6ZM8.42105 14.7C8.42105 11.2266 11.2547 8.4 14.7368 8.4C18.2189 8.4 21.0526 11.2266 21.0526 14.7C21.0526 18.1734 18.2189 21 14.7368 21C11.2547 21 8.42105 18.1734 8.42105 14.7ZM42.1053 50.4C37.4611 50.4 33.6842 46.6326 33.6842 42C33.6842 37.3674 37.4611 33.6 42.1053 33.6C46.7495 33.6 50.5263 37.3674 50.5263 42C50.5263 46.6326 46.7495 50.4 42.1053 50.4ZM67.3684 21C66.2517 21 65.1808 20.5575 64.3911 19.7698C63.6015 18.9822 63.1579 17.9139 63.1579 16.8C63.1579 15.6861 63.6015 14.6178 64.3911 13.8302C65.1808 13.0425 66.2517 12.6 67.3684 12.6C68.4851 12.6 69.5561 13.0425 70.3457 13.8302C71.1353 14.6178 71.5789 15.6861 71.5789 16.8C71.5789 17.9139 71.1353 18.9822 70.3457 19.7698C69.5561 20.5575 68.4851 21 67.3684 21Z"/>
                        </svg>
                    </div>
                    <p class="font-semibold">
                        Select Task Type To Edit It
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import DropdownSelector from "../../Shared/components/DropdownSelector.vue";

export default {
    name: "Editor",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        modelValue: {
            type: Object,
            default: null,
        },
        modules: {
            type: Object,
            default: null
        }
    },
    emits: ['update:modelValue', 'delete-task-type', 'save', 'cancel'],
    components: {DropdownSelector, ActionsHandle},
    data: function () {
        return {
            id: null,
            name: null,
            modules: []
        }
    },
    created() {

    },
    computed: {
        payload: {
            get: function() { return this.modelValue; },
            set: function(value) { this.$emit('update:modelValue', this.payload); }
        }
    },
    methods: {
        deleteTaskType() {
            this.$emit('delete-task-type', this.payload.id);
        },
        save() {
            this.$emit('save');
        },
        cancel() {
            this.$emit('cancel');
        }
    }
}
</script>

<style lang="scss">

</style>
