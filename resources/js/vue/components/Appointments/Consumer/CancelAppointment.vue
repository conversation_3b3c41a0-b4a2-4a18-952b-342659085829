<template>
    <div class="bg-gradient-to-b from-gray-100 to-white h-auto">
        <div class="flex flex-row items-center justify-center w-full bg-white border-b py-3">
            <roofing-app-logo v-if="appointmentIndustry === 'Roofing'" />
            <app-logo v-else/>
        </div>

        <div class="border container mx-auto my-5 rounded text-center w-11/12 lg:w-1/2 text-2xl lg:text-base h-auto bg-white">
            <div class="font-medium uppercase py-3 border-b">
                Appointment
            </div>

            <div v-if="loading" class="p-5">
                Loading...
            </div>
            <div v-else-if="message.length > 0" class="p-3">
                {{ message }}
            </div>
            <div v-else-if="validCreds" class="flex flex-col justify-center items-center p-3 gap-3">
                <div class="uppercase font-medium">
                    Company
                </div>
                <div class="">
                    {{ companyName }}
                </div>

                <div class="uppercase font-medium">
                    Date & Time
                </div>
                <div class="">
                    {{ appointmentDateTime }}
                </div>

                <div class="uppercase font-medium">
                    Type
                </div>
                <div class="">
                    {{ appointmentType }}
                </div>

                <div class="uppercase font-medium">
                    Location
                </div>
                <div class="flex flex-col items-center justify-center gap-1">
                    <div>
                        {{ consumerFirstName }}'s residence in {{ cityStateZip }}
                    </div>
                </div>

                <button
                    @click="toggleCancellationModal"
                    class="border hover:text-white px-5 py-3 rounded w-full lg:w-1/2"
                    :class="{
                        'border-blue-400 hover:bg-blue-400 text-blue-400': !isRescheduledAppointment,
                        'border-red-500 hover:bg-red-500 text-red-500': isRescheduledAppointment
                    }">
                    {{ isRescheduledAppointment ? 'Cancel' : 'Reschedule / Cancel' }}
                </button>
            </div>
        </div>
    </div>

    <modal v-if="showCancellationModal" @confirm="validate" @close="toggleCancellationModal(false)" class="font-medium">
        <template v-slot:header>
            {{ isRescheduledAppointment ? 'Cancel' : 'Reschedule / Cancel' }} Appointment
        </template>
        <template v-slot:content>
            <div v-if="modalMessage" class="text-red-500">
                {{ modalMessage }}
            </div>
            <div>
                Please select the reason for {{ isRescheduledAppointment ? 'cancelling' : 'rescheduling or cancelling' }}<span class="text-red-500">*</span>
            </div>
            <div class="my-2 md:w-1/3 w-full">
                <select v-model="cancellationReason" class="rounded p-1 w-full">
                    <option value="" disabled>Choose a reason</option>
                    <option v-for="(label, reason) in cancellationReasonOptions" :value="reason" :key="reason">{{ label }}</option>
                </select>
            </div>
            <div class="w-full">
                <char-limit-text-area v-if="cancellationReason === 'other'" v-model="cancellationNote" :textarea-class="'w-full'" />
                <div v-else-if="cancellationReason === 'rescheduled'" class="md:w-1/3 w-full">
                    Rescheduled date<span class="text-red-500">*</span>
                    <date-picker v-model="rescheduledDate" :required="true" :min-date="minDatepickerDate" :is-24="false" auto-apply :close-on-auto-apply="false"/>
                </div>
            </div>
        </template>
    </modal>
</template>

<script>
    import Api from './services/api.js'
    import AppLogo from "../../Shared/components/AppLogo.vue";
    import RoofingAppLogo from "../../Shared/components/RoofingAppLogo.vue";
    import Modal from "../../Shared/components/Modal.vue";
    import CharLimitTextArea from "../../Shared/components/CharLimitTextArea.vue";
    import DatePicker from "@vuepic/vue-datepicker";
    import {DateTime} from "luxon";

    export default {
        name: "ConsumerAppointment",
        components: {
            CharLimitTextArea,
            Modal,
            AppLogo,
            DatePicker,
            RoofingAppLogo
        },
        data: function() {
            return {
                api: Api.make(),

                appointmentCode: '',
                appointmentToken: '',
                appointmentIndustry: '',

                companyName: '',
                appointmentDateTime: '',
                cityStateZip: '',
                appointmentType: '',
                consumerFirstName: '',
                isRescheduledAppointment: false,

                showCancellationModal: false,
                cancellationReason: '',
                cancellationNote: '',
                rescheduledDate: null,
                cancellationReasonOptions: {},

                loading: true,
                message: '',
                modalMessage: '',

                validCreds: false
            };
        },
        created: function() {
            const urlParams = new URLSearchParams(window.location.search);

            this.appointmentToken = urlParams.get('k');
            this.appointmentCode = urlParams.get('c');
            this.appointmentIndustry = urlParams.get('i');

            this.getAppointment().then(() => {
                return this.getCancellationReasons();
            }).finally(() => {
                this.loading = false;
            });
        },
        computed: {
            minDatepickerDate() {
                return DateTime.now().endOf('hour').plus({hour: 1}).toJSDate();
            }
        },
        methods: {
            setMessage(message = '') {
                this.message = message;
            },
            getCancellationReasons() {
                return this.api.getAppointmentCancellationReasons(this.appointmentToken, this.appointmentCode).then(res => {
                    if(res.data.data.status === true) {
                        this.cancellationReasonOptions = res.data.data.cancellation_reason_options;

                        if(this.isRescheduledAppointment) {
                            delete this.cancellationReasonOptions.rescheduled;
                        }

                        return Promise.resolve();
                    }
                    else {
                        this.setMessage('Problem retrieving cancellation reasons');

                        return Promise.reject();
                    }
                }).catch(() => {
                    this.setMessage('Error retrieving cancellation reasons');
                })
            },
            getAppointment() {
                this.setMessage();

                this.companyName = '';
                this.appointmentDateTime = '';
                this.cityStateZip = '';
                this.appointmentType = '';
                this.consumerFirstName = '';

                return this.api.getConsumerAppointment(this.appointmentToken, this.appointmentCode).then(res => {
                    if(res.data.data.status === true) {
                        this.companyName = res.data.data.company_name;
                        this.appointmentDateTime = res.data.data.appointment_time;
                        this.cityStateZip = res.data.data.city_state_zip;
                        this.appointmentType = res.data.data.appointment_type;
                        this.consumerFirstName = res.data.data.consumer_first_name;
                        this.isRescheduledAppointment = res.data.data.is_rescheduled;

                        this.validCreds = true;

                        return Promise.resolve();
                    }
                    else {
                        this.setMessage('Invalid appointment');

                        return Promise.reject();
                    }
                }).catch((err) => {
                    if(err.response?.status === 401) {
                        this.setMessage('Invalid appointment');
                    }
                    else {
                        this.setMessage('Appointment error');
                    }
                })
            },
            toggleCancellationModal(show) {
                if(show) {
                    this.cancellationReason = '';
                    this.cancellationNote = '';
                    this.rescheduledDate = null;
                    this.modalMessage = '';
                }

                this.showCancellationModal = !!show;
            },
            validate() {
                this.modalMessage = '';

                if(!this.cancellationReason) {
                    this.modalMessage = 'Missing reason';

                    return false;
                }

                if(this.cancellationReason === 'rescheduled') {
                    if(!this.rescheduledDate) {
                        this.modalMessage = 'Missing rescheduled date';

                        return false
                    }
                    else if(this.rescheduledDate.getTime() <= this.minDatepickerDate.getTime()) {
                        this.modalMessage = 'Rescheduled date/time is before the next allowed appointment time';

                        return false;
                    }
                }

                this.cancelAppointment();
            },
            cancelAppointment() {
                this.toggleCancellationModal(false);
                this.setMessage();

                this.loading = true;

                this.api.cancelConsumerAppointment(
                    this.appointmentToken,
                    this.appointmentCode,
                    this.cancellationReason,
                    this.cancellationNote,
                    this.rescheduledDate ? Math.floor(this.rescheduledDate.getTime() / 1000) : 0
                ).then(res => {
                    if(res.data.data.status === true) {
                        this.setMessage('Appointment ' + (this.cancellationReason === 'rescheduled' ? 'rescheduled' : 'cancelled'));
                    }
                    else {
                        this.setMessage('Problem ' + (this.cancellationReason === 'rescheduled' ? 'rescheduling' : 'cancelling') + ' appointment');
                    }
                }).catch(() => {
                    this.setMessage('Error ' + (this.cancellationReason === 'rescheduled' ? 'rescheduling' : 'cancelling') + ' appointment');
                }).finally(() => {
                    this.appointmentCode = '';
                    this.appointmentToken = '';
                    this.companyName = '';
                    this.appointmentDateTime = '';
                    this.cityStateZip = '';
                    this.appointmentType = '';
                    this.consumerFirstName = '';
                    this.isRescheduledAppointment = false;
                    this.cancellationReason = '';
                    this.cancellationNote = '';
                    this.rescheduledDate = null;
                    this.modalMessage = '';

                    this.validCreds = false;

                    this.loading = false;
                });
            }
        }
    }
</script>
