import axios from 'axios';

export default class Api {
    constructor(baseUrl, baseEndpoint) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new Api('api', 'consumer-appointment');
    }

    getConsumerAppointment(appointmentToken, appointmentCode) {
        return this.axios().get('/get', {
            params: {
                k: appointmentToken,
                c: appointmentCode
            }
        });
    }

    cancelConsumerAppointment(appointmentToken, appointmentCode, cancellationReason, cancellationNote = '', rescheduledDate = 0) {
        return this.axios().post('/cancel', {
            k: appointmentToken,
            c: appointmentCode,
            cancellation_reason: cancellationReason.toLowerCase(),
            cancellation_note: cancellationNote,
            rescheduled_date: rescheduledDate
        });
    }

    getAppointmentCancellationReasons(appointmentToken, appointmentCode) {
        return this.axios().get('/cancellation-reasons', {
            params: {
                k: appointmentToken,
                c: appointmentCode
            }
        });
    }
}
