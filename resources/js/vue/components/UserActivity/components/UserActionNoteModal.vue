<template>
    <modal :dark-mode="darkMode" @close="closeModal" :small="true" :confirm-text="confirmText" :close-text="'Cancel'" @confirm="saveAction" :z-index-hundred="zIndexHundred">
        <template v-slot:header>
            <h4 class="text-xl font-medium">{{ action ? 'Edit' : 'Add' }} Action</h4>
        </template>
        <template v-slot:content>
            <div class="grid gap-4">
                <alerts-container
                    v-if="alertActive"
                    :alert-type="alertType"
                    :text="alertText"
                    :dark-mode="darkMode">
                </alerts-container>
                <div>
                    <p class="capitalize font-semibold text-sm" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Subject</p>
                    <div class="mt-2">
                        <custom-input v-model="subject" :dark-mode="darkMode"
                                      placeholder="Enter subject" type="text"/>
                    </div>
                </div>
                <div>
                    <p class="capitalize font-semibold text-sm" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Display Date (optional)</p>
                    <div class="mt-2">
                        <custom-input v-model="displayDate" :dark-mode="darkMode"
                                      placeholder="Enter subject" type="date"
                        />
                    </div>
                </div>
                <div>
                    <div class="my-2 grid grid-cols-2 gap-2" v-if="taggableUsers.length > 0">
                        <div>
                            <p class="capitalize font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Tag Staff</p>
                            <multi-select
                                text-place-holder="Tag Staff"
                                :options="taggableUsers"
                                :dark-mode="darkMode"
                                :show-search-box="true"
                                :selected-ids="tags"
                            />
                        </div>
                        <div v-if="tags.length >= 1">
                            <p class="capitalize font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Notify By Email</p>
                            <input
                                id="email-tagged-users"
                                name="emailTaggedUsers"
                                type="checkbox"
                                v-model="tag_by_email"
                                style="background-color: rgb(0, 76, 149);"
                                class="h-4 w-4 ml-2 rounded disabled:opacity-50 disabled:border-white border-blue-400 bg-dark-background text-blue-600"
                            >
                            <label for="email-tagged-users"
                                   class="block text-xs font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Newly tagged staff will receive a notification and an email.
                            </label>
                        </div>
                    </div>
                </div>
                <div class="pb-10">
                    <p class="capitalize font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Notes</p>
                    <wysiwyg-editor
                        v-model="note"
                        :dark-mode="darkMode"
                        auto-width="98%"
                        editor-height="350"
                    >
                    </wysiwyg-editor>
                </div>
            </div>
        </template>
    </modal>
</template>

<script>

import AlertsMixin from "../../../mixins/alerts-mixin";
import MarkdownEditorMixin from "../../Shared/mixins/markdown-editor-mixin";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Modal from "../../Shared/components/Modal.vue";
import MarkdownEditor from "../../Shared/components/MarkdownEditor.vue";
import WysiwygEditor from "../../Shared/components/WysiwygEditor.vue";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import MultiSelect from "../../Shared/components/MultiSelect.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import SharedApiService from "../../Shared/services/api";

export default {
    name: "ActionNoteModal",
    components: {
        CustomInput,
        MultiSelect,
        AlertsContainer,
        WysiwygEditor,
        MarkdownEditor,
        MarkdownEditorMixin,
        Modal,
        Dropdown
    },
    mixins: [
        MarkdownEditorMixin, AlertsMixin
    ],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        action: {
            type: Object,
            default: () => {}
        },
        companyId: {
            type: Number,
            default: 0
        },
        userId: {
            type: Number,
            default: null
        },
        categoryDefault: {
            type: String,
            default: null
        },
        zIndexHundred: {
            type: Boolean,
            default: false
        }
    },
    data: function() {
        return {
            sharedApi: SharedApiService.make(),
            companyContacts: [],
            taggableUsers: [],

            actionId: this.action?.id || 0,
            subject: this.action?.subject || '',
            displayDate: this.action?.display_date || null,
            categoryId: this.action?.action_category_id || 0,
            note: this.action?.message || '',
            tags: this.action?.tags || [],
            tag_by_email: this.action?.tag_by_email || false,
            targetType: this.action?.target_type || 'company',
            companyContactId: this.action?.target_type === 'company_contact' ? this.action?.target_id || 0 : 0,
            updateSalesStatus: -1,

            confirmText: 'Save',
            saving: false,
            salesStatusOptions: [],

            errorMessages: {
                getCategories: 'Error fetching Action Categories',
                saveAction: 'An unknown error occurred while saving an Action',
                getTaggableUsers: 'An unknown error occurred while fetching users',
            },
        };
    },
    async created() {
        await this.getTaggableUsers();
    },
    methods: {
        async getTaggableUsers() {
            return this.sharedApi.getUsers().then(response => {
                if (response.data.data.status) {
                    response.data.data.users.forEach((user) => {
                        this.taggableUsers.push({
                            id: user.id,
                            name: user.name
                        });
                    });
                } else {
                    this.showAlert('error', this.errorMessages.getTaggableUsers);
                }
            }).catch(err => {
                this.showAlert('error', err.response?.data?.message || this.errorMessages.getTaggableUsers);
            });
        },
        saveAction() {
            if (this.saving)
                return;

            this.saving = true;
            this.confirmText  = 'Saving...';

            this.sharedApi.saveUserAction(
                this.actionId,
                this.userId,
                this.subject,
                this.note,
                this.displayDate,
                this.tags,
                this.tags.length > 0 ? this.tag_by_email : false,
                this.updateSalesStatus
            ).then(() => {
              this.reloadActions();
              this.closeModal();
            }).catch(err => {
              this.showAlert('error', err.response?.data?.message || this.errorMessages.saveAction);
            }).finally(() => {
              this.confirmText = 'Save';
              this.saving = false;
            });

        },
        closeModal() {
            if (window.tinymce?.editors.length) {
                window.tinymce?.editors.forEach(editor => window.tinymce.remove(editor));
            }
            this.$emit('close');
        },
        reloadActions() {
            this.$emit('reload-actions');
        },
    }
}
</script>
