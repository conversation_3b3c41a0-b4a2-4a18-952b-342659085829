<template>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 px-10 pt-5 pb-10">
        <div class="border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="px-5 pt-5 pb-4">
                <div class="flex items-center justify-between">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">User Activity</h5>
                    <button
                        class="ml-2 my-1 transition duration-200 inline-flex items-center font-semibold bg-grey-475 hover:bg-grey-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                        @click="toggleNoteModal(true)">
                        Add Action
                    </button>
                </div>

            </div>

            <div class="border-t border-b overflow-y-auto"
                 :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border', tableHeight]">
                <template v-if="!loading">
                    <AlertsContainer :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
                    <template v-for="(action, idx) in actions" :key="action.id">
                        <div
                             class="relative"
                             :class="{ 'border-l-primary-500 border-l-4': action.pinned }">
                            <div class="absolute top-1 select-none cursor-pointer w-6 scale-x-75 "
                                 :class="{ 'text-primary-500': action.pinned, 'text-slate-300 hover:text-primary-200': !action.pinned && !darkMode, 'text-slate-600 hover:text-primary-200': !action.pinned && darkMode }"
                                 @click="toggleActionPinned(action.id)"
                                 @mouseover="mouseOverPin = action.id"
                                 @mouseleave="mouseOverPin = null"
                            >
                                <svg v-if="action.pinned && mouseOverPin == action.id" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                                    <path d="M3.53 2.47a.75.75 0 00-1.06 1.06l18 18a.75.75 0 101.06-1.06l-18-18zM20.25 5.507v11.561L5.853 2.671c.15-.043.306-.075.467-.094a49.255 49.255 0 0111.36 0c1.497.174 2.57 1.46 2.57 2.93zM3.75 21V6.932l14.063 14.063L12 18.088l-7.165 3.583A.75.75 0 013.75 21z" />
                                </svg>
                                <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                                    <path fill-rule="evenodd" d="M6.32 2.577a49.255 49.255 0 0111.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 01-1.085.67L12 18.089l-7.165 3.583A.75.75 0 013.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                        <div class="absolute top-1 right-2 select-none text-xs uppercase"
                             :class="{ 'text-primary-300': darkMode, 'text-primary-700': !darkMode }">
                            {{ action.pinned ? ('Pinned' + (action.category && action.category.length > 0 ? ' - ' : ''))  : '' }}
                            {{ action.category ?? '' }}
                        </div>
                        <div @click="toggleExpandActivity(action.id)"  class="grid grid-cols-1 gap-x-3 border-b px-5"
                             :class="{
                                'bg-primary-50 hover:bg-primary-100': action.pinned && !darkMode,
                                'bg-dark-module hover:bg-dark-module': action.pinned && darkMode,
                                'hover:bg-light-module': !action.pinned && !darkMode,
                                'hover:bg-dark-module': !action.pinned && darkMode,
                                'text-slate-900 border-light-border': !darkMode,
                                'text-slate-100 hover:bg-dark-module border-dark-border': darkMode
                            }">
                            <div class="py-4 cursor-pointer">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center w-full">
                                        <svg :class="{'transform transition duration-200 rotate-90' : openedActivities.includes(action.id) }" class="mr-4" width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 9L5 5L1 1" stroke="#0081FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <div class="w-full">
                                            <div class="flex justify-start items-center">
                                                <p class="text-xs text-slate-500 mr-2" v-if="action.display_date && action.display_date.length > 0">
                                                    For: {{ action.display_date ? $filters.dateFromTimestamp(action.display_date) : 'No Date Set' }}
                                                </p>
                                                <p class="text-xs text-slate-500">
                                                    Created: {{ $filters.dateFromTimestamp(action.created_timestamp, 'usWithTime') }}
                                                </p>
                                                <p v-if="action.updated_timestamp && action.updated_timestamp > action.created_timestamp" class="text-xs text-slate-500 ml-2">
                                                    Last Updated: {{ $filters.dateFromTimestamp(action.updated_timestamp, 'usWithTime') }}
                                                </p>
                                            </div>

                                            <div>
                                                <p class="pb-0 text-sm font-semibold">
                                                    {{ action.subject }}
                                                </p>
                                            </div>
                                        </div>
                                        <div>
                                            <actions-handle :dark-mode="darkMode" :no-delete-button="true" @edit="toggleNoteModal(true, action)" @click.stop/>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-5 pt-2" v-if="openedActivities.includes(action.id)">
                                    <p class="text-sm border-l border-primary-500 pl-3" :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}" v-html="$filters.scrubHtml(action.message)">
                                    </p>

                                    <p class="text-sm mt-4" v-if="action.tags.length > 0">
                                        <span class="font-semibold">Tagged:</span>
                                        <span v-for="(tagId, index) in action.tags" :key="index" class="text-slate-500">
                                            @{{ getTaggedUserById(tagId) }}
                                            <span v-if="index < action.tags.length - 1">, </span>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </template>
                </template>
                <div v-else class="flex items-center justify-center h-full">
                    <loading-spinner :dark-mode="darkMode"></loading-spinner>
                </div>
            </div>
            <div v-if="paginationData && paginationData.to" class="p-3">
                <Pagination truncated :dark-mode="darkMode" :pagination-data="paginationData" :show-pagination="true" :show-total-records-detail="false" @change-page="handlePaginationEvent"></Pagination>
            </div>

            <user-action-note-modal
                v-if="showNoteModal"
                :dark-mode="darkMode"
                :action="editAction"
                :user-id="userId"
                @close="toggleNoteModal(false)"
                @reload-actions="getActions"
            />
        </div>
    </div>
</template>

<script>
import MarkdownEditorMixin from "../../Shared/mixins/markdown-editor-mixin";
import AlertsMixin from "../../../mixins/alerts-mixin";
import SharedApiService from "../../Shared/services/api";
import Pagination from "../../Shared/components/Pagination.vue";
import Modal from "../../Shared/components/Modal.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import UserActionNoteModal from "./UserActionNoteModal.vue";

export default {
    name: "UserActivityPage",
    components: {
        UserActionNoteModal,
        ActionsHandle,
        AlertsContainer,
        LoadingSpinner,
        Modal,
        Pagination
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        userId: {
            type: Number,
            default: null,
        },
        showFilters: {
            type: Boolean,
            default: false
        },
        tableHeight: {
            type: String,
            default: 'h-100'
        }
    },
    mixins: [ MarkdownEditorMixin , AlertsMixin ],
    data() {
        return {
            expandActivity: false,
            openedActivities: [],
            api: SharedApiService.make(),
            paginationData: null,
            actions: [],
            loading: false,
            showNoteModal: false,
            editAction: null,
            saving: false,
            mouseOverPin: null,
            users: [],
            errorMessages: {
                pinAction: 'An unknown error occurred while pinning an Action.'
            }
        }
    },
    created() {
      this.getUsers();
        if (this.userId) {
            this.getActions();
        }
    },
    watch: {

    },
    computed: {

    },
    methods: {
        toggleExpandActivity(id) {
            if(this.openedActivities.includes(id))
                this.openedActivities.splice(this.openedActivities.indexOf(id), 1);
            else
                this.openedActivities.push(id);
        },
        getUsers() {
          this.api.getUsers().then(resp => {
            this.users.push({
              id: 0,
              name: 'All'
            });

            resp.data.data.users.forEach((user) => {
              this.users.push({
                id: user.id,
                name: user.name
              });
            });
          });
        },
        getActions() {
          this.loading = true;
          this.api.getUserActions(this.userId).then(resp => this.addPaginatedData(resp))
              .finally(() => this.loading = false);
        },
        toggleNoteModal(show, action = null) {
            this.editAction = show && action ? action : null;
            this.showNoteModal = show;
        },
        async handlePaginationEvent(newPageUrl) {
            this.loading = true;
            await axios.get(newPageUrl.link).then(resp => this.addPaginatedData(resp))
                .finally(() => this.loading = false);
        },
        addPaginatedData(resp) {
            if(resp.data.data.status === true) {
                let {data, ...paginationData} = resp.data.data.actions;
                this.actions = data;
                this.paginationData = paginationData;
            }
        },
        toggleActionPinned(actionId) {
            //todo
        },
        getTaggedUserById(userId) {
          const user = this.users.find(user => user.id === userId);
          return user ? user.name : "Unknown User";
        }
    }
}
</script>

<style scoped>

</style>
