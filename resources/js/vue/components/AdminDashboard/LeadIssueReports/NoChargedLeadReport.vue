<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto py-5 relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="border rounded-lg p-8 "
                     :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border text-grey-120': darkMode}">
                    <nav-lead-report :navData="navData"></nav-lead-report>
                    <loading-spinner v-if="loading" :dark-mode="darkMode"/>
                    <div v-else class="mt-8">
                        <table-lead-report :datas="data" :thead="tHeadData" :tbody="tBodyData"
                                           v-if="data.length > 0"></table-lead-report>
                        <div v-if="data.length == 0" class="text-center my-32 text-sm text-gray-500">
                            No leads in the past {{ daysCount }} day(s).
                        </div>
                        <div class="mt-8" v-if="data.length > 0">
                            <Pagination :dark-mode="darkMode" :pagination-data="paginationData" :show-pagination="true"
                                        @change-page="handlePaginationEvent"></Pagination>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import TableLeadReport from "./components/TableLeadReport.vue";
import ApiService from "./services/api";
import Pagination from "../../Shared/components/Pagination.vue";
import NavLeadReport from "./components/NavLeadReport.vue";
export default {
    name: "NoChargedLeadReport",
    components: {
        LoadingSpinner,
        NavLeadReport,
        TableLeadReport,
        Pagination
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data: function () {
        return {
            data: null,
            loading: true,
            paginationData: null,
            tHeadData: ['Delivery date', 'Lead ID', 'Charge status', 'Company Name', 'Company ID', 'Cost', 'Account Manager'],
            tBodyData: ['delivery_date', 'lead_id', 'chargestatus', 'companyname', 'companyid', 'cost', 'account_manager'],
            navData: {
                breadcrumb: {
                    parent: 'Lead Issues Report',
                    current: 'No Charged Lead',
                },
                description: 'All leads that have been “no-charged”',
                showFilters: false,
                showDatePicker: false
            },
            page: 1,
            daysCount: null
        }
    },
    created() {
        this.apiService = ApiService.make();
        this.getData();
    },
    methods: {
        async handlePaginationEvent(newPageUrl) {
            this.loading = true;
            this.apiService.getPaginatedLead(newPageUrl.link).then(resp => {
                let {data, ...paginationData} = resp.data.data.leads;
                this.data = data;
                this.paginationData = paginationData;
                this.page = paginationData.page;
            }).catch(e => console.error(e)).finally(() => this.loading = false);
        },
        async getData() {
            this.loading = true;
            this.apiService.getNoChargedLead({
                page: this.page,
            }).then(resp => {
                if (resp.data.data.status === true) {
                    let {data, ...paginationData} = resp.data.data.leads;
                    this.data = data;
                    this.paginationData = paginationData;
                } else {
                    console.log('Something went wrong!');
                }
            }).catch(e => console.log(e)).finally(() => {
                this.loading = false;
            });
        }
    }
}
</script>
<style scoped>
</style>
