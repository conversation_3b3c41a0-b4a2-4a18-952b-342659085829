import axios from 'axios';
import {BaseApiService} from "./base";

export class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getFilterOptions() {
        return this.axios().get('/filter-options');
    }

    saveUserPreset(filterPayload) {
        return this.axios().put('/save-preset', filterPayload);
    }

    deleteUserPreset(filterName) {
        return this.axios().delete('/delete-preset', {
            params: {
                name: filterName
            }
        });
    }

}
