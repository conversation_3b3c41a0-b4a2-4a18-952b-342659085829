<template>
    <div>
        <div class="border-y overflow-y-auto h-100 divide-y" :class="[darkMode ? 'border-dark-border divide-dark-border bg-dark-background' : 'border-light-border divide-slate-200 bg-light-background']">
            <div v-for="missedProduct in missedProducts" :key="missedProduct.id" class="px-5 min-h-[140px] py-6 grid grid-cols-9 gap-5 text-sm items-center" :class="[darkMode ? 'text-slate-100 bg-dark-background hover:bg-dark-module' : 'text-slate-900 bg-light-background hover:bg-light-module']">
                <div class="flex flex-col justify-start gap-1">
                    <a target="_blank" :href="`/consumer-product/?consumer_product_id=${ missedProduct.id }`"  class="text-primary-500 font-bold flex items-center">
                        <p>{{ missedProduct.id }}</p>
                        <p class="text-slate-500 ml-2">(Lead #{{ missedProduct.consumer.id }})</p>
                    </a>
                    <p class="font-semibold">{{ missedProduct.product_name }}</p>
                </div>
                <div class="flex flex-col justify-start gap-1 col-span-2 truncate">
                    <p :title="missedProduct.consumer.name" class="font-bold">{{ missedProduct.consumer.name }}</p>
                    <p :title="missedProduct.consumer.email" class="font-semibold text-slate-500">{{ missedProduct.consumer.email }}</p>
                    <div class="inline-flex flex-wrap items-center">
                        <p class="font-semibold text-slate-500 mr-2">{{ $filters.formatPhoneNumber(missedProduct.consumer.phone) }}</p>
                        <div v-if="missedProduct.consumer.verified" class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-emerald-500' : 'text-emerald-700']">
                            <svg class="mr-1 fill-current" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.25 1.75C5.72501 1.75 4.26247 2.3558 3.18414 3.43414C2.1058 4.51247 1.5 5.97501 1.5 7.5C1.5 8.2551 1.64873 9.00281 1.93769 9.70043C2.22666 10.3981 2.6502 11.0319 3.18414 11.5659C3.71807 12.0998 4.35195 12.5233 5.04957 12.8123C5.74719 13.1013 6.4949 13.25 7.25 13.25C8.0051 13.25 8.75281 13.1013 9.45043 12.8123C10.1481 12.5233 10.7819 12.0998 11.3159 11.5659C11.8498 11.0319 12.2733 10.3981 12.5623 9.70043C12.8513 9.00281 13 8.2551 13 7.5C13 5.97501 12.3942 4.51247 11.3159 3.43414C10.2375 2.3558 8.77499 1.75 7.25 1.75ZM2.12348 2.37348C3.48311 1.01384 5.32718 0.25 7.25 0.25C9.17282 0.25 11.0169 1.01384 12.3765 2.37348C13.7362 3.73311 14.5 5.57718 14.5 7.5C14.5 8.45208 14.3125 9.39484 13.9481 10.2745C13.5838 11.1541 13.0497 11.9533 12.3765 12.6265C11.7033 13.2997 10.9041 13.8338 10.0245 14.1981C9.14484 14.5625 8.20208 14.75 7.25 14.75C6.29792 14.75 5.35516 14.5625 4.47554 14.1981C3.59593 13.8338 2.7967 13.2997 2.12348 12.6265C1.45025 11.9533 0.91622 11.1541 0.551873 10.2745C0.187527 9.39484 0 8.45208 0 7.5C0 5.57718 0.763837 3.73311 2.12348 2.37348ZM9.947 5.52523C10.2399 5.81812 10.2399 6.29299 9.947 6.58589L7.05811 9.47477C6.76521 9.76767 6.29034 9.76767 5.99745 9.47477L4.553 8.03033C4.26011 7.73744 4.26011 7.26256 4.553 6.96967C4.8459 6.67678 5.32077 6.67678 5.61366 6.96967L6.52778 7.88378L8.88634 5.52523C9.17923 5.23233 9.6541 5.23233 9.947 5.52523Z"/></svg>
                            SMS Verified
                        </div>
                        <div v-else class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-red-500' : 'text-red-700']">
                            <svg class="mr-1 fill-current" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.75C5.60761 1.75 4.27226 2.30312 3.28769 3.28769C2.30312 4.27226 1.75 5.60761 1.75 7C1.75 7.68944 1.8858 8.37213 2.14963 9.00909C2.41347 9.64605 2.80018 10.2248 3.28769 10.7123C3.7752 11.1998 4.35395 11.5865 4.99091 11.8504C5.62787 12.1142 6.31056 12.25 7 12.25C7.68944 12.25 8.37213 12.1142 9.00909 11.8504C9.64605 11.5865 10.2248 11.1998 10.7123 10.7123C11.1998 10.2248 11.5865 9.64605 11.8504 9.00909C12.1142 8.37213 12.25 7.68944 12.25 7C12.25 5.60761 11.6969 4.27226 10.7123 3.28769C9.72774 2.30312 8.39239 1.75 7 1.75ZM2.22703 2.22703C3.4929 0.961159 5.20979 0.25 7 0.25C8.79021 0.25 10.5071 0.961159 11.773 2.22703C13.0388 3.4929 13.75 5.20979 13.75 7C13.75 7.88642 13.5754 8.76417 13.2362 9.58311C12.897 10.4021 12.3998 11.1462 11.773 11.773C11.1462 12.3998 10.4021 12.897 9.58311 13.2362C8.76417 13.5754 7.88642 13.75 7 13.75C6.11358 13.75 5.23584 13.5754 4.41689 13.2362C3.59794 12.897 2.85382 12.3998 2.22703 11.773C1.60023 11.1462 1.10303 10.4021 0.763813 9.58311C0.424594 8.76417 0.25 7.88642 0.25 7C0.25 5.20979 0.961159 3.4929 2.22703 2.22703ZM5.13634 5.13634C5.42923 4.84344 5.9041 4.84344 6.197 5.13634L7 5.93934L7.803 5.13634C8.0959 4.84344 8.57077 4.84344 8.86366 5.13634C9.15656 5.42923 9.15656 5.9041 8.86366 6.197L8.06066 7L8.86366 7.803C9.15656 8.0959 9.15656 8.57077 8.86366 8.86366C8.57077 9.15656 8.0959 9.15656 7.803 8.86366L7 8.06066L6.197 8.86366C5.9041 9.15656 5.42923 9.15656 5.13634 8.86366C4.84344 8.57077 4.84344 8.0959 5.13634 7.803L5.93934 7L5.13634 6.197C4.84344 5.9041 4.84344 5.42923 5.13634 5.13634Z"/></svg>
                            SMS Unverified
                        </div>
                    </div>
                </div>
                <div class="flex flex-col justify-start gap-1">
                    <p class="font-bold">{{ missedProduct.industry }}</p>
                    <div class="inline-flex items-center font-medium relative group">
                        <p>{{ missedProduct.requested_service }}</p>
                    </div>
                </div>
                <div class="col-span-2">
                    <p class="font-bold">{{ missedProduct.address }}</p>
                    <p class="font-semibold text-slate-500">{{ missedProduct.timezone }}</p>
                    <a v-if="missedProduct.map_url" :href="missedProduct.map_url" target="_blank" class="text-primary-500">View map</a>
                </div>
                <div class="flex flex-col justify-start gap-1">
                    <p class="font-bold">${{ $filters.formatPrice(missedProduct.price) }}</p>
                </div>
                <div class="flex flex-col justify-start gap-1">
                    <p class="font-bold mb-2">Quotes Remaining: {{ missedProduct.appointments_remaining }}</p>
                    <p>Quotes Requested: {{ missedProduct.appointments_requested }}</p>
                    <p>Quotes Sold: {{ missedProduct.appointments_requested - missedProduct.appointments_remaining }}</p>
                </div>
                <div class="flex flex-col justify-start gap-1">
                    <p class="font-bold">{{ $filters.dateFromTimestamp(missedProduct.missed_lead_created_at, 'usWithTime') }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "CompanyMissedLeadsSearchTableBody",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        missedProducts: {
            type: Array,
            default: []
        },
    },
}
</script>

<style scoped>

</style>
