<template>
    <div class="px-5 grid grid-cols-9 gap-5 pb-3" :class="[darkMode ? 'text-slate-400' : 'text-slate-500', missedProducts.length > 5 ? 'mr-3' : '']">
        <p class="table-header-text">ID/TYPE</p>
        <p class="table-header-text col-span-2">CONSUMER</p>
        <p class="table-header-text">INDUSTRY/SERVICE</p>
        <p class="table-header-text col-span-2">ADDRESS</p>
        <p class="table-header-text">PRICE</p>
        <p class="table-header-text">DETAILS</p>
        <p class="table-header-text">CREATED AT</p>
    </div>
</template>

<script>
export default {
    name: "CompanyMissedLeadsSearchTableHeader",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        missedProducts: {
            type: Array,
            default: []
        },
    }
}
</script>

<style scoped>
    .table-header-text {
        @apply uppercase text-xs font-bold rounded;
    }
</style>
