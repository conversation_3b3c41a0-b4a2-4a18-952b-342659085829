<template>
    <div
        v-if="grouping > 0"
        class="border-y overflow-y-auto divide-y max-h-[66vh]"
        :class="[darkMode ? 'border-dark-border divide-dark-border bg-dark-background' : 'border-light-border divide-slate-200 bg-light-background']"
    >
        <div v-if="!leads.length" class="w-full min-h-96 flex items-center justify-center">
            <p class="text-sm font-semibold">No data found</p>
        </div>
        <div v-else v-for="lead in leads" :key="lead.id" class="px-5 min-h-[140px] py-6 grid grid-cols-7 gap-5 text-sm items-center" :class="[darkMode ? 'text-slate-100 bg-dark-background hover:bg-dark-module' : 'text-slate-900 bg-light-background hover:bg-light-module']">
            <div>
                  {{ getLocationString(lead) }}
            </div>
            <div>
                {{ lead.consumer_count ?? 0 }}
            </div>
            <div>
                {{ $filters.currency(lead.average_revenue ?? 0) }}
            </div>
            <div>
                {{ $filters.currency(0) }}
            </div>
            <div>
                {{ $filters.currency(getEstimatedRevenue(lead) )}}
            </div>
            <div class="flex items-center gap-x-3">
                <p>Select group</p>
                <custom-checkbox v-model="lead.selected" :input-disabled="processing"></custom-checkbox>
            </div>
        </div>
    </div>
    <div
        v-else
        class="border-y overflow-y-auto divide-y max-h-[66vh]"
        :class="[darkMode ? 'border-dark-border divide-dark-border bg-dark-background' : 'border-light-border divide-slate-200 bg-light-background']"
    >
        <div v-if="!leads.length" class="w-full min-h-96 flex items-center justify-center">
            <p class="text-sm font-semibold">No data found</p>
        </div>
        <div v-else v-for="lead in leads" :key="lead.id" class="px-5 min-h-[140px] py-6 grid grid-cols-8 gap-5 text-sm items-center" :class="[darkMode ? 'text-slate-100 bg-dark-background hover:bg-dark-module' : 'text-slate-900 bg-light-background hover:bg-light-module']">
            <div class="flex flex-col gap-1">
                <a :href="lead.url" class="font-bold text-primary-500 cursor-pointer" target="_blank">{{ lead.id }}</a>
                <p class="font-bold">Lead</p>
                <p class="font-medium text-slate-500">{{ lead.date }}</p>
            </div>
            <div class="font-semibold">{{ lead.status  }}</div>
            <div class="flex flex-col gap-1">
                <p class="font-bold">{{ lead.consumer?.full_name }}</p>
                <p class="font-semibold text-slate-500">{{ lead.consumer?.email }}</p>
                <p class="font-semibold">{{ lead.consumer?.phone }}</p>
                <div v-if="lead.consumer?.verified" class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-emerald-500' : 'text-emerald-700']">
                    <svg class="mr-1 fill-current" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M7.25 1.75C5.72501 1.75 4.26247 2.3558 3.18414 3.43414C2.1058 4.51247 1.5 5.97501 1.5 7.5C1.5 8.2551 1.64873 9.00281 1.93769 9.70043C2.22666 10.3981 2.6502 11.0319 3.18414 11.5659C3.71807 12.0998 4.35195 12.5233 5.04957 12.8123C5.74719 13.1013 6.4949 13.25 7.25 13.25C8.0051 13.25 8.75281 13.1013 9.45043 12.8123C10.1481 12.5233 10.7819 12.0998 11.3159 11.5659C11.8498 11.0319 12.2733 10.3981 12.5623 9.70043C12.8513 9.00281 13 8.2551 13 7.5C13 5.97501 12.3942 4.51247 11.3159 3.43414C10.2375 2.3558 8.77499 1.75 7.25 1.75ZM2.12348 2.37348C3.48311 1.01384 5.32718 0.25 7.25 0.25C9.17282 0.25 11.0169 1.01384 12.3765 2.37348C13.7362 3.73311 14.5 5.57718 14.5 7.5C14.5 8.45208 14.3125 9.39484 13.9481 10.2745C13.5838 11.1541 13.0497 11.9533 12.3765 12.6265C11.7033 13.2997 10.9041 13.8338 10.0245 14.1981C9.14484 14.5625 8.20208 14.75 7.25 14.75C6.29792 14.75 5.35516 14.5625 4.47554 14.1981C3.59593 13.8338 2.7967 13.2997 2.12348 12.6265C1.45025 11.9533 0.91622 11.1541 0.551873 10.2745C0.187527 9.39484 0 8.45208 0 7.5C0 5.57718 0.763837 3.73311 2.12348 2.37348ZM9.947 5.52523C10.2399 5.81812 10.2399 6.29299 9.947 6.58589L7.05811 9.47477C6.76521 9.76767 6.29034 9.76767 5.99745 9.47477L4.553 8.03033C4.26011 7.73744 4.26011 7.26256 4.553 6.96967C4.8459 6.67678 5.32077 6.67678 5.61366 6.96967L6.52778 7.88378L8.88634 5.52523C9.17923 5.23233 9.6541 5.23233 9.947 5.52523Z"/>
                    </svg>
                    Verified
                </div>
            </div>
            <div class="flex flex-col gap-1">
                <p class="font-bold">{{ lead.address?.full_address }}</p>
                <a :href="lead.address?.map_url" target="_blank" class="text-primary-500 cursor-pointer">View map</a>
            </div>
            <div class="flex flex-col gap-1">
                <p class="font-bold">{{ lead.industry }}</p>
                <p class="font-medium">{{ lead.service }}</p>
                <p class="font-bold">{{ lead.contact_requests }} Quotes Requested</p>
            </div>
            <div class="flex flex-col gap-1">
                <p class="text-slate-500" v-if="!lead.contact_timestamps.length">Not yet contacted</p>
                <p class="font-semibold text-nowrap text-slate-500" v-for="timestamp in lead.contact_timestamps.sort()">{{ formatDate(timestamp) }}</p>
            </div>
            <div>
                <custom-checkbox v-model="lead.selected" :input-disabled="processing"></custom-checkbox>
            </div>
        </div>
    </div>
</template>

<script>
import CustomCheckbox from "../../Shared/SlideWizard/components/CustomCheckbox.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import { RecyclableConsumerGrouping } from "../RecyclableLeadSearch.vue";

export default {
    name: "RecyclableLeadSearchTableBody",
    components: {CustomButton, CustomCheckbox},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        leads: {
            type: Array,
            default: []
        },
        processing: {
            type: Boolean,
            default: false
        },
        grouping: {
            type: Number,
            default: 0,
        },
        conversionRate: {
            type: Number,
            default: 10,
        }
    },
    methods: {
        formatDate(timestamp) {
            return new Date(timestamp * 1000).toLocaleString('en', {timeZone: 'America/Denver'})
        },
        getLocationString(consumer) {
            if (this.grouping === RecyclableConsumerGrouping.STATE) {
                return consumer.state;
            }
            else if (this.grouping === RecyclableConsumerGrouping.COUNTY) {
                return `${consumer.county} (${consumer.state_abbr})`;
            }
            else if (this.grouping === RecyclableConsumerGrouping.ZIP_CODE) {
                return `${consumer.zip_code}, ${consumer.county} (${consumer.state_abbr})`;
            }
        },
        getEstimatedRevenue(consumer) {
            return consumer.average_revenue * (this.conversionRate/100) * consumer.consumer_count;
        },
    }
}
</script>
