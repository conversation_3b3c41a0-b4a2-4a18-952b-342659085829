import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'recyclable-lead-search', 1);
    }

    searchLeads(params = {}) {
        return this.axios().post('/search', params);
    }

    contactLeads(data) {
        return this.axios().post('/contact-leads', data)
    }

    getFilterOptions() {
        return this.axios().get('/filter-options');
    }

    getFilterOptionUpdates(payload) {
        return this.axios().post('/filter-option-updates', payload);
    }
}
