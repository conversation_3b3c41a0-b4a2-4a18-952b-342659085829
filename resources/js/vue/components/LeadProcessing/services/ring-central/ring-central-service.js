// import {SDK} from "@ringcentral/sdk";
// import WebPhone from "ringcentral-web-phone";
// import {RingCentralCall} from "ringcentral-call";
// import Subscriptions from "@ringcentral/subscriptions";
import {DateTime} from "luxon";

const WEBHOOK_EVENT_FILTERS = [
    "/restapi/v1.0/account/~/extension/~/message-store/instant?type=SMS",
    "/restapi/v1.0/account/~/telephony/sessions",
];

const RECONNECT_WEBPHONE_TIME = 2000;
const TIMEOUT_WEBPHONE_TIME = 30000;

/**
 * The service for interfacing with the ring central SDK.
 *
 * This service will facilitate our use cases of ring central, in a centralised location
 * such that we can add/remove functionality where it is necessary.
 */
export default class RingCentralService {
    constructor(clientId, clientSecret, webhookUrl, webhookVerificationToken, appName = "SolarReviews", appVersion = "v1.0.0") {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.webhookUrl = webhookUrl;
        this.webhookVerificationToken = webhookVerificationToken;
        this.appName = appName;
        this.appVersion = appVersion;
        this.useSandbox = false;
        this._sdk = null;
        this._webPhone = null;
        this._callInstance = null;
        this._subscriptions = null;
    }

    /**
     * A getter that handles creating the SDK if it has not yet been made.
     * This getter will return the ring central SDK.
     *
     * @returns {SDK}
     */
    get sdk() {
        if(this._sdk !== null)
            return this._sdk;

        return this._initializeRingCentralSDK();
    }

    /**
     * Handles returning the subscriptions api.
     *
     * @returns {null|Subscriptions}
     */
    get subscriptions() {
        if(this._subscriptions !== null)
            return this._subscriptions;

        return this._subscriptions = new Subscriptions({sdk: this.sdk});
    }

    /**
     * Returns the ring central platform used for logging in and authentication.
     *
     * @returns {Platform}
     */
    get platform() {
        return this.sdk.platform();
    }

    /**
     * Handles initializing the ring central SDK when it has not been initialized yet.
     *
     * @private
     */
    _initializeRingCentralSDK() {
        this._sdk = new SDK({
            server: this.useSandbox ? SDK.server.sandbox : SDK.server.production,
            clientId: this.clientId,
            clientSecret: this.clientSecret,
            appName: this.appName,
            appVersion: this.appVersion,
            redirectUri: window.location.origin + window.location.pathname
        });

        return this._sdk;
    }

    /**
     * Handles enabling sandbox mode for this service.
     * Ring central will not use the production
     * api when this is true.
     */
    enableSandboxMode() {
        this.useSandbox = true;
    }

    /**
     * Handles disabling sandbox mode for this service.
     * Ring central will use the production
     * api when this is false.
     */
    disableSandboxMode() {
        this.useSandbox = false;
    }

    /**
     * Handles checking if a user is logged in or not.
     *
     * @returns {Promise<boolean>}
     */
    isUserLoggedIn() {
        return new Promise(resolve => {
           this.sdk.ensureLoggedIn().then(status => resolve(true)).catch(status => resolve(false));
        });
    }

    /**
     * Handles authenticating a user with the ring central platform.
     *
     * @returns {Promise<boolean>}
     */
    authenticate() {
        return new Promise((resolve) => {
            this.isUserLoggedIn().then(status => {
                if(!status) {
                    const loginUrl = this.sdk.loginUrl({implicit: false});
                    window.location.assign(loginUrl);
                    resolve(false);
                } else {
                    resolve(true);
                }
            });
        });
    }

    /**
     * Handles finalizing the authentication with ring central.
     *
     * @returns {Promise<boolean>}
     */
    finalizeLogin() {
        return new Promise(resolve => {
            const options = this.sdk.parseLoginRedirect(window.location.hash || window.location.search);

            return this.sdk.login(options).then(resp => {
                window.history.pushState({}, document.title, window.location.pathname);
                this.createOrRenewSubscription().then(() => {
                    window.location.reload();
                }).catch(() => {});

                if(resp.status === 200)
                    resolve(true);
                else
                    resolve(false);
            });
        })
    }

    /**
     * Handles logging out the user from ring central.
     *
     * @returns {Promise<boolean>}
     */
    logout() {
        return new Promise(resolve => {
            this.isUserLoggedIn().then(status => {
               if(status) {
                   this.sdk.logout().then(() => resolve(true)).catch(() => resolve(true));
               }
            });
        });
    }

    getWebPhone(reattempt = false) {
        return new Promise((resolve, reject) => {
            if(this._webPhone !== null)
                return resolve(this._webPhone);

            this.isUserLoggedIn().then(resp => {
               if(!resp)
                   return reject({message: "WebPhone: User is not authenticated with Ring Central."});

               const local = document.getElementById('ring-central-audio-local');
               const remote = document.getElementById('ring-central-audio');

               if(local)
                local.volume = 0;

                this.platform.post('/restapi/v1.0/client-info/sip-provision', {sipInfo: [{transport: 'WSS'}]}).then(resp => {
                    return resp.json();
                }).then(resp => {
                    this._webPhone = new WebPhone(resp, {
                        appKey: this.clientId,
                        appName: this.appName,
                        appVersion: this.appVersion,
                        logLevel: 1,
                        enableQos: true,
                        audioHelper: {enabled: true, incoming: "https://assets.mixkit.co/sfx/preview/mixkit-on-hold-ringtone-1361.mp3", outgoing: "https://assets.mixkit.co/sfx/preview/mixkit-on-hold-ringtone-1361.mp3"},
                        media:{
                            remote,
                            local,
                        },
                    });

                    resolve(this._webPhone);
                });
            }).catch(e => {
                if(!reattempt) {
                    setTimeout(() => {
                        this.getWebPhone(true).then(resp => resolve(resp)).catch(e => reject(e));
                    }, 60000);
                } else {
                    reject(e);
                }
            });
        });
    }

    getCallInstance() {
        return new Promise((resolve, reject) => {
            if(this._callInstance !== null)
                return resolve(this._callInstance);

            this.getWebPhone().then(phone => {
                this._callInstance = new RingCentralCall({webphone: phone, sdk: this.sdk, subscriptions: this.subscriptions});

                return resolve(this._callInstance);
            }).catch(e => reject(e));
        });
    }

    getCallControl() {
        return new Promise((resolve, reject) => {
            return this.getCallInstance().then(instance => {
                return resolve(instance.callControl);
            }).catch(e => reject(e));
        });
    }

    getUserPhoneNumber(reattempt = false) {
        return new Promise((resolve, reject) => {
            return this.getCallControl().then(control => {
                if(control.devices.length <= 0 || control.devices[0].phoneLines.length <= 0) {
                    if(!reattempt) {
                        this._webPhone = null;
                        this._callInstance = null;
                        this.getUserPhoneNumber(true).then(resp => {
                            resolve(resp);
                        }).catch(e => reject({'message': 'No devices available.'}));
                        return;
                    } else {
                        return reject({'message': "No devices available."});
                    }
                }

                const device = control.devices[0];
                resolve(device.phoneLines[0].phoneInfo.phoneNumber);
            }).catch(e => reject(e));
        });
    }

    registerAndMaintainWebPhone() {
        this.getCallInstance().then(instance => {
            instance.on('webphone-unregistered', () => this._webPhoneUnregistered());
            instance.on('webphone-registration-failed', (resp, cause) => this._webPhoneRegistrationFailed(resp, cause));
        });
    }

    _webPhoneUnregistered() {
        setTimeout(() => this._attemptWebPhoneRegistration(), RECONNECT_WEBPHONE_TIME);
    }

    _webPhoneRegistrationFailed(resp, cause) {
        setTimeout(() => this._attemptWebPhoneRegistration(), resp.reasonPhrase === "Too Many Contacts" ? TIMEOUT_WEBPHONE_TIME : RECONNECT_WEBPHONE_TIME);
    }

    _attemptWebPhoneRegistration() {
        this._webPhone = null;
        this.getWebPhone().then(phone => {
            this.getCallInstance().then(instance => {
               instance.setWebphone(phone);
            });
        });
    }

    /**
     * Handles the registration, creation & renewal of webhooks for a given processor.
     *
     * @returns {Promise<unknown>}
     */
    createOrRenewSubscription() {
        return new Promise(async (resolve, reject) => {
            this.isUserLoggedIn().then(async resp => {
                if(!resp)
                    return reject({message: "Create/Renew Subscription: User is not authenticated with Ring Central."});

                this.cancelSubscriptions().then(async () => {
                    const params = {
                        eventFilters: WEBHOOK_EVENT_FILTERS,
                        deliveryMode: {
                            transportType: "WebHook",
                            address: this.webhookUrl,
                            verificationToken: this.webhookVerificationToken
                        },
                        expiresIn: 7776000
                    };

                    try {
                        const resp = await this.platform.post('/restapi/v1.0/subscription', params);
                        const obj = await resp.json();
                        window.localStorage.setItem("ring-central-subscription-id", obj.id);
                        resolve();
                    } catch (e) {
                        console.log("Failed to create subscription", e);
                        reject(e);
                    }
                });
            })
        });
    }

    cancelSubscriptions() {
        return new Promise(async (resolve) => {
           this.isUserLoggedIn().then(async resp => {
               const req = await this.platform.get('/restapi/v1.0/subscription');
               const data = await req.json();

               if(data.records && data.records.length > 0) {
                   const records = data.records;

                   for(const record of records) {
                       await this.platform.delete(`/restapi/v1.0/subscription/${record.id}`);
                   }
               }

               resolve(true);
           });
        });
    }

    sendSMS(number, message) {
        return new Promise((resolve, reject) => {
            this.isUserLoggedIn().then(resp => {
                if(!resp)
                    return reject({message: "SendSMS: User is not authenticated with ring central."});

                this.getUserPhoneNumber().then(userNumber => {
                    this.sdk.post('/restapi/v1.0/account/~/extension/~/sms', {
                        from: {phoneNumber: userNumber},
                        to: [{phoneNumber: number}],
                        text: message
                    }).then(resp => resp.json()).then(resp => {
                        resolve(resp);
                    }).catch(resp => reject(resp));
                }).catch(e => reject({message: "User does not have a phone number.", e}))
            });
        });
    }

    async getBlobURLForRecording(url) {
        const result = await this.platform.get(url);
        const blob = await result.blob();

        return blob ? URL.createObjectURL(blob) : null;
    }

    getRecordings(number) {
        return new Promise((resolve, reject) => {
            this.isUserLoggedIn().then(async resp => {
                if(!resp)
                    return reject({message: "getRecordings: User is not authenticated with ring central."});

                if(number.startsWith("+"))
                    number = number.substring(1);

                const req = await this.platform.get(
                    `/restapi/v1.0/account/~/call-log`,
                    {
                        phoneNumber: number,
                        withRecording: true,
                        dateFrom: DateTime.now().minus({days: 30}).toISO()
                    }
                );

                const logs = await req.json();
                const records = logs.records;

                console.log(records, logs);
                let recordings = [];
                for(const log of records) {
                    if(log.recording) {
                        const recording = await this.platform.get(log.recording.uri);
                        const data = await recording.json();

                        recordings.push({metadata: {...data, creationTime: log.startTime}, contentUri: log.recording.contentUri});
                    }
                }

                resolve(recordings);
            })
        });
    }

    getVoicemails(number) {
        return new Promise((resolve, reject) => {
            this.isUserLoggedIn().then(async resp => {
                if(!resp)
                    return reject({message: "getVoicemails: User is not authenticated with ring central."});

                if(number.startsWith("+"))
                    number = number.substring(1);

                const req = await this.platform.get(
                    `/restapi/v1.0/account/~/call-log`,
                    {
                        phoneNumber: number,
                        direction: "Inbound",
                        dateFrom: DateTime.now().minus({days: 30}).toISO(),
                        view: "Detailed"
                    }
                );

                const data = await req.json();
                const logs = data.records.filter(record => record.result === "Voicemail");
                console.log(logs);
                const voicemails = [];

                for(const record of logs) {
                    const legs = record.legs ? record.legs : []
                    for(const leg of legs) {
                        if(leg.message && leg.message.type === "VoiceMail") {
                            const res = await this.platform.get(leg.message.uri);
                            const vm = await res.json();

                            if(vm.attachments.length > 0)
                                voicemails.push({metadata: vm, contentUri: vm.attachments.length > 0 ? vm.attachments[0].uri : null})
                        }
                    }
                }

                resolve(voicemails);
            }).catch(e => {reject({message: "getVoicemails: User is not authenticated with ring central.", e})})
        });
    }
}
