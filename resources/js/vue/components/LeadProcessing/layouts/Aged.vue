<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto pt-3 pb-8 relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div v-if="data.lead && !loading">
                        <lead-header
                            :dark-mode="darkMode"
                            :lead="data.lead"
                            :user-queue="userQueue"
                            @toggle-history="toggleHistory"
                        >
                            <template v-slot:buttons>
                                <div class="flex items-center gap-3" v-if="!locked">
                                    <aged-lead-clear-and-refill-button :dark-mode="darkMode" />

                                    <CustomButton
                                        :dark-mode="darkMode"
                                        @click="remove"
                                        color="gray"
                                    >
                                        Cancel
                                    </CustomButton>

                                    <CustomButton
                                        :dark-mode="darkMode"
                                        @click="showModal('skip-aged')"
                                        color="slate"
                                    >
                                        Skip Lead
                                    </CustomButton>

                                    <CustomButton
                                        :dark-mode="darkMode"
                                        @click="showModal('allocate_aged')"
                                    >
                                        Allocate Lead
                                    </CustomButton>
                                </div>
                                <div class="flex py-2 text-red-500" v-if="readOnly">
                                    This lead is currently locked by {{ lockedByUser?.name }}
                                </div>
                            </template>
                        </lead-header>
                        <div>
                            <div class="grid md:grid-cols-1 lg:grid-cols-3 gap-4" v-if="data.lead != null">
                                <div class="lg:col-span-2">
                                    <div class="mb-4">
                                        <div class="grid grid-cols-2 gap-4 h-full" v-if="data.consumerProductData">
                                            <consumer-product-date-info
                                                :consumer-product-data="data.consumerProductData"
                                                :dark-mode="darkMode"/>
                                            <consumer-product-utility
                                                v-if="data.lead.basic.industry === 'Solar'"
                                                :consumer-product-data="data.consumerProductData"
                                                :dark-mode="darkMode"/>
                                            <consumer-contact
                                                :consumer-product-data="data.consumerProductData"
                                                :consumer-product-id="data.lead.basic.id"
                                                :api-driver="apiDriver"
                                                :dark-mode="darkMode"/>
                                            <consumer-product-basic-info
                                                :consumer-product-data="data.consumerProductData"
                                                :consumer-product-type="data.lead.basic.type"
                                                :dark-mode="darkMode"/>
                                        </div>
                                    </div>
                                    <consumer-product-assignments :consumer-product-id="data.lead.basic.id" :dark-mode="darkMode" :disable-allocation="true"/>
                                    <interactive-map :dark-mode="darkMode" :address="data.lead.basic.address"></interactive-map>
                                </div>
                                <div class="lg:col-span-1 lg:col-start-3 lg:row-span-1">
                                    <consumer-product-verification :dark-mode="darkMode" :consumer-product-id="data.lead.basic.id" class="mb-4"/>
                                    <AgedQueueSkipReasons class="mb-4" :dark-mode="darkMode" :consumer-product-id="data.lead.basic.id"></AgedQueueSkipReasons>
                                    <comments class="mb-4" :dark-mode="darkMode" :consumer-product-id="data.lead.basic.id"></comments>
                                    <consumer-product-previous-leads class="mb-4" :consumer-product-id="data.lead.basic.id" :dark-mode="darkMode"/>
                                    <consumer-reviews class="mb-4" :dark-mode="darkMode" :consumer-product-id="data.lead.basic.id"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else-if="loading">
                        <loading-spinner :dark-mode="darkMode"/>
                    </div>
                    <div v-else>
                        <div>
                            <button class="px-5 py-1 rounded text-blue-550 outline-none my-3"
                                    :class="{
                                        'bg-cyan-100 hover:bg-cyan-300': !darkMode,
                                        'bg-dark-background hover:bg-blue-600': darkMode
                                    }"
                                    @click="toggleHistory(true)">
                                History
                            </button>
                        </div>
                        <div class="flex flex-row w-full justify-center items-center text-2xl">
                            No leads in queue
                        </div>
                    </div>
                </div>

                <Modal
                    :dark-mode="darkMode"
                    @close="hideModal"
                    @confirm="confirmSelection('Qualified via conversation', null, false, {clone: true})"
                    v-if="displayModal"
                >
                    <template v-slot:header>
                        <h4 class="text-xl font-medium">{{ modalConfiguration.title }}</h4>
                    </template>

                    <template v-slot:content>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 my-4">
                            <button
                                v-for="option in modalConfiguration.options" :key="option.id ? option.id : option" @click="() => selectModalOption(option)"
                                class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5 font-bold"
                                :class="optionClasses(option)"
                            >
                                {{ option.value ? option.value : option }}
                            </button>
                        </div>

                        <div class="my-2 font-bold text-red-500" v-if="modalConfiguration.warning">
                            {{ modalConfiguration.warning }}
                        </div>

                    </template>
                </Modal>
                <processing-history v-if="showHistory" :dark-mode="darkMode" :api="api" @close="toggleHistory(false)"></processing-history>
            </div>
        </div>
    </div>
</template>

<script>
import InteractiveMap from "../components/InteractiveMap.vue";
import Modal from "../components/Modal.vue";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import ProcessingHistory from "../components/ProcessingHistory.vue";
import QueueInteractionMixin from "../mixins/queue-interaction-mixin";
import ModalsMixin from "../mixins/modals-mixin";
import ConsumerProductBasicInfo from "../../Shared/modules/Consumer/ConsumerProductBasicInfo.vue";
import ConsumerProductPreviousLeads from "../../Shared/modules/Consumer/ConsumerProductPreviousLeads.vue";
import ConsumerContact from "../../Shared/modules/Consumer/ConsumerContact.vue";
import ConsumerReviews from "../../Shared/modules/Consumer/ConsumerReviews.vue";
import ConsumerProductUtility from "../../Shared/modules/Consumer/ConsumerProductUtility.vue";
import ConsumerProductVerification from "../../Shared/modules/Consumer/ConsumerProductVerification.vue";
import ConsumerProductDateInfo from "../../Shared/modules/Consumer/ConsumerProductDateInfo.vue";
import LegacyAdminMixin from "../../Shared/mixins/legacy-admin-mixin";
import Comments from "../components/Comments.vue";
import LeadHeader from "../components/LeadHeader.vue";
import ConsumerProductAssignments from "../../Shared/modules/Consumer/ConsumerProductAssignments.vue";
import AgedQueueSkipReasons from "../components/AgedQueueSkipReasons.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import ConfirmModal from "../../Shared/components/ConfirmationModal.vue";
import AgedLeadClearAndRefillButton from "../components/AgedLeadClearAndRefillButton.vue";

export default {
    name: 'Aged',
    components: {
        AgedLeadClearAndRefillButton,
        ConfirmModal,
        CustomButton,
        AgedQueueSkipReasons,
        ConsumerProductAssignments,
        LeadHeader,
        ConsumerContact,
        ConsumerProductUtility,
        ConsumerReviews,
        ConsumerProductBasicInfo,
        ConsumerProductPreviousLeads,
        LoadingSpinner,
        InteractiveMap,
        Modal,
        ProcessingHistory,
        ConsumerProductVerification,
        ConsumerProductDateInfo,
        Comments
    },
    mixins: [
        QueueInteractionMixin,
        ModalsMixin,
        LegacyAdminMixin
    ],
    props: ["darkMode", "apiDriver", 'userQueue', "api"],
    data() {
        return {
            showHistory: false,
        }
    },
    methods: {
        toggleHistory(show) {
            this.showHistory = show;
        },
        remove() {
            this.removeFromQueue();
        },
    },
};
</script>

<style scoped>

</style>
