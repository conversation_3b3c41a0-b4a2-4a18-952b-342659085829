<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto pt-3 pb-8 relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div v-if="data.lead && !loading">
                        <lead-header
                            :dark-mode="darkMode"
                            :lead="data.lead"
                            @toggle-history="toggleHistory"
                        >
                            <template v-slot:buttons>
                                <div class="flex items-center" v-if="!locked">
                                    <button class="transition duration-200 text-sm font-semibold focus:outline-none py-2 rounded-md px-5 mr-3" @click="remove"
                                            :class="{'bg-grey-250 hover:bg-grey-400 text-white': !darkMode, 'bg-grey-500 hover:bg-grey-600 text-white': darkMode}">
                                        Remove From Queue
                                    </button>
                                    <button @click="showModal('allocate_affiliate')"
                                            class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5">
                                        Allocate Lead
                                    </button>
                                </div>
                                <div class="flex py-2 text-red-500" v-if="readOnly">
                                    This lead is currently locked by {{ lockedByUser?.name }}
                                </div>
                            </template>
                        </lead-header>
                        <div>
                            <div class="grid md:grid-cols-1 lg:grid-cols-3 gap-4" v-if="data.lead != null">
                                <div class="lg:col-span-2">
                                    <div class="mb-4">
                                        <div class="grid grid-cols-2 gap-4 h-full" v-if="data.consumerProductData">
                                            <consumer-product-date-info
                                                :consumer-product-data="data.consumerProductData"
                                                :dark-mode="darkMode"/>
                                            <consumer-product-utility
                                                v-if="data.lead.basic.industry === 'Solar'"
                                                :consumer-product-data="data.consumerProductData"
                                                :dark-mode="darkMode"/>
                                            <consumer-contact
                                                :consumer-product-data="data.consumerProductData"
                                                :consumer-product-id="data.lead.basic.id"
                                                :api-driver="apiDriver"
                                                :dark-mode="darkMode"/>
                                            <consumer-product-basic-info
                                                :consumer-product-data="data.consumerProductData"
                                                :consumer-product-type="data.lead.basic.type"
                                                :dark-mode="darkMode"/>
                                        </div>
                                    </div>
                                    <consumer-product-assignments :consumer-product-id="data.lead.basic.id" :dark-mode="darkMode" :disable-allocation="true"/>
                                    <interactive-map :dark-mode="darkMode" :address="data.lead.basic.address"></interactive-map>
                                </div>
                                <div class="lg:col-span-1 lg:col-start-3 lg:row-span-1">
                                    <AffiliateLeadInfo class="mb-4" :dark-mode="darkMode" :consumer-product-id="data.lead.basic.id"></AffiliateLeadInfo>
                                    <AffiliateLeadInternalTracking class="mb-4" :dark-mode="darkMode" :consumer-product-id="data.lead.basic.id"></AffiliateLeadInternalTracking>
                                    <consumer-product-verification :dark-mode="darkMode" :consumer-product-id="data.lead.basic.id" class="mb-4"/>
                                    <comments class="mb-4" :dark-mode="darkMode" :consumer-product-id="data.lead.basic.id"></comments>
                                    <consumer-product-previous-leads class="mb-4" :consumer-product-id="data.lead.basic.id" :dark-mode="darkMode"/>
                                    <consumer-reviews class="mb-4" :dark-mode="darkMode" :consumer-product-id="data.lead.basic.id"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else-if="loading">
                        <loading-spinner :dark-mode="darkMode"/>
                    </div>
                    <div v-else>
                        <div>
                            <button class="px-5 py-1 rounded text-blue-550 outline-none my-3"
                                    :class="{
                                        'bg-cyan-100 hover:bg-cyan-300': !darkMode,
                                        'bg-dark-background hover:bg-blue-600': darkMode
                                    }"
                                    @click="toggleHistory(true)">
                                History
                            </button>
                        </div>
                        <div class="flex flex-row w-full justify-center items-center text-2xl">
                            No leads in queue
                        </div>
                    </div>
                </div>

                <Modal
                    :dark-mode="darkMode"
                    @close="hideModal"
                    @confirm="confirmSelection('Qualified via conversation', null, {})"
                    v-if="displayModal"
                >
                    <template v-slot:header>
                        <h4 class="text-xl font-medium">{{ modalConfiguration.title }}</h4>
                    </template>

                    <template v-slot:content>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 my-4">
                            <button
                                v-for="option in modalConfiguration.options" :key="option.id ? option.id : option" @click="() => selectModalOption(option)"
                                class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5 font-bold"
                                :class="optionClasses(option)"
                            >
                                {{ option.value ? option.value : option }}
                            </button>
                        </div>

                        <div class="my-2 font-bold text-red-500" v-if="modalConfiguration.warning">
                            {{ modalConfiguration.warning }}
                        </div>

                    </template>
                </Modal>
                <processing-history v-if="showHistory" :dark-mode="darkMode" :api="api" @close="toggleHistory(false)"></processing-history>
            </div>
        </div>
    </div>
</template>

<script>
import InteractiveMap from "../components/InteractiveMap.vue";
import Modal from "../components/Modal.vue";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import ProcessingHistory from "../components/ProcessingHistory.vue";
import QueueInteractionMixin from "../mixins/queue-interaction-mixin";
import ModalsMixin from "../mixins/modals-mixin";
import ConsumerProductBasicInfo from "../../Shared/modules/Consumer/ConsumerProductBasicInfo.vue";
import ConsumerProductPreviousLeads from "../../Shared/modules/Consumer/ConsumerProductPreviousLeads.vue";
import ConsumerContact from "../../Shared/modules/Consumer/ConsumerContact.vue";
import ConsumerReviews from "../../Shared/modules/Consumer/ConsumerReviews.vue";
import ConsumerProductUtility from "../../Shared/modules/Consumer/ConsumerProductUtility.vue";
import ConsumerProductVerification from "../../Shared/modules/Consumer/ConsumerProductVerification.vue";
import ConsumerProductDateInfo from "../../Shared/modules/Consumer/ConsumerProductDateInfo.vue";
import LegacyAdminMixin from "../../Shared/mixins/legacy-admin-mixin";
import Comments from "../components/Comments.vue";
import AffiliateLeadInfo from "../components/AffiliateLeadInfo.vue";
import LeadHeader from "../components/LeadHeader.vue";
import ConsumerProductAssignments from "../../Shared/modules/Consumer/ConsumerProductAssignments.vue";
import AffiliateModal from "../../Affiliates/modal/AffiliateModal.vue";
import AffiliateLeadInternalTracking from "../components/AffiliateLeadInternalTracking.vue";

export default {
    name: 'Affiliate',
    components: {
        AffiliateLeadInternalTracking,
        AffiliateLeadInfo,
        AffiliateModal,
        ConsumerProductAssignments,
        LeadHeader,
        ConsumerContact,
        ConsumerProductUtility,
        ConsumerReviews,
        ConsumerProductBasicInfo,
        ConsumerProductPreviousLeads,
        LoadingSpinner,
        InteractiveMap,
        Modal,
        ProcessingHistory,
        ConsumerProductVerification,
        ConsumerProductDateInfo,
        Comments
    },
    mixins: [
        QueueInteractionMixin,
        ModalsMixin,
        LegacyAdminMixin
    ],
    props: ["darkMode", "apiDriver"],
    data() {
        return {
            showHistory: false
        }
    },
    methods: {
        toggleHistory(show) {
            this.showHistory = show;
        },
        remove() {
            this.removeFromAffiliateQueue();
        }
    },
};
</script>

<style scoped>

</style>
