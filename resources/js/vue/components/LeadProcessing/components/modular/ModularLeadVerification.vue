<template>
    <div>
        <div class="">

            <div id="" class="row-span-3 border rounded-lg"
                 :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">

                <div class="flex items-center h-full justify-center" v-if="loading">
                    <loading-spinner></loading-spinner>
                </div>
                <div v-else>
                    <!-- Confidence -->
                    <div class="flex items-center justify-between p-5">
                        <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Lead Verification</h5>
                        <div class="text-sm rounded-full flex items-center"
                             :class="{'text-grey-400': !darkMode, 'text-blue-400': darkMode}">
                            Confidence:
                            <div class="rounded-full text-sm pl-2 h-full font-medium">
                                {{ Math.round(lead.confidence) }}%</div>
                        </div>
                    </div>

                    <!-- Phone -->
                    <div class="border-b"
                         :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
                        <div class="flex items-center px-5 pb-4 flex-wrap gap-2">
                            <h5 class="text-sm font-medium mr-2"
                                :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Phone:</h5>
                            <div>
                                <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                     :class="{'text-green-550' : lead.phone.is_valid === true, 'text-red-350' : lead.phone.is_valid !== true}">
                                    <svg v-if="lead.phone.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                                    </svg>
                                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <p v-if="lead.phone.is_valid === true" class="text-xs">Valid</p>
                                    <p v-else class="text-xs">Invalid</p>
                                </div>
                                <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                     :class="{'text-green-550 bg-green-150' : lead.phone.is_valid === true, 'text-red-350 bg-red-75' : lead.phone.is_valid !== true}">
                                    <svg v-if="lead.phone.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                                    </svg>
                                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <p v-if="lead.phone.is_valid === true" class="text-xs">Valid</p>
                                    <p v-else class="text-xs">Invalid</p>
                                </div>
                            </div>
                            <div class="mr-2">
                                <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                                     :class="{'text-green-550' : lead.phone.name_match === true, 'text-red-350' : lead.phone.name_match !== true}">
                                    <svg v-if="lead.phone.name_match === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                                    </svg>
                                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <p v-if="lead.phone.name_match === true" class="text-xs">Match: <span class="font-medium truncate">{{ lead.phone.subscriber_name !== null ? lead.phone.subscriber_name : '' }}</span></p>
                                    <p v-else class="text-xs">No Name Match</p>
                                </div>
                                <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                                     :class="{'text-green-550 bg-green-150' : lead.phone.name_match === true, 'text-red-350 bg-red-75' : lead.phone.name_match !== true}">
                                    <svg v-if="lead.phone.name_match === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                                    </svg>
                                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <p v-if="lead.phone.name_match === true" class="text-xs">Match: <span class="font-medium truncate">{{ lead.phone.subscriber_name !== null ? lead.phone.subscriber_name : '' }}</span></p>
                                    <p v-else class="text-xs">No Name Match</p>
                                </div>
                            </div>
                            <div>
                                <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                                     :class="{'text-blue-550' : lead.phone.line_type, 'text-red-350' : lead.phone.line_type == null || lead.phone.line_type == ''}">
                                    <svg v-if="lead.phone.line_type" class="mr-1 flex-shrink-0" width="11" height="10" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 0H11V1.66667H0V0ZM0 4.16667H11V5.83333H0V4.16667ZM0 8.33333H11V10H0V8.33333Z" fill="#0081FF"/>
                                    </svg>
                                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <p v-if="lead.phone.line_type" class="text-xs">Line Type: {{ lead.phone.line_type }}</p>
                                    <p v-else class="text-xs">Line Type: Unknown</p>
                                </div>
                                <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                                     :class="{'text-blue-550 bg-cyan-125' : lead.phone.line_type, 'text-red-350 bg-red-75' : lead.phone.line_type == null || lead.phone.line_type == ''}">
                                    <svg v-if="lead.phone.line_type" class="mr-1 flex-shrink-0" width="11" height="10" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 0H11V1.66667H0V0ZM0 4.16667H11V5.83333H0V4.16667ZM0 8.33333H11V10H0V8.33333Z" fill="#0081FF"/>
                                    </svg>
                                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <p v-if="lead.phone.line_type" class="text-xs">Line Type: {{ lead.phone.line_type }}</p>
                                    <p v-else class="text-xs">Line Type: Unknown</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="border-b"
                         :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
                        <div class="flex items-center px-5 py-4">
                            <h5 class="text-sm font-medium mr-2"
                                :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Address:</h5>
                            <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                 :class="{'text-green-550' : lead.address.is_valid === true, 'text-red-350' : lead.address.is_valid !== true}">
                                <svg v-if="lead.address.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                                </svg>
                                <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p v-if="lead.address.is_valid === true" class="text-xs">Valid</p>
                                <p v-else class="text-xs">Invalid</p>
                            </div>
                            <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                 :class="{'text-green-550 bg-green-150' : lead.address.is_valid === true, 'text-red-350 bg-red-75' : lead.address.is_valid !== true}">
                                <svg v-if="lead.address.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                                </svg>
                                <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p v-if="lead.address.is_valid === true" class="text-xs">Valid</p>
                                <p v-else class="text-xs">Invalid</p>
                            </div>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="border-b"
                         :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
                        <div class="flex items-center px-5 py-4">
                            <h5 class="text-sm font-medium mr-2"
                                :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Email:</h5>
                            <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                 :class="{'text-green-550' : lead.email.is_valid === true, 'text-red-350' : lead.email.is_valid !== true}">
                                <svg v-if="lead.email.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                                </svg>
                                <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p v-if="lead.email.is_valid === true" class="text-xs">Valid</p>
                                <p v-else class="text-xs">Invalid</p>
                            </div>
                            <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                 :class="{'text-green-550 bg-green-150' : lead.email.is_valid === true, 'text-red-350 bg-red-75' : lead.email.is_valid !== true}">
                                <svg v-if="lead.email.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                                </svg>
                                <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p v-if="lead.email.is_valid === true" class="text-xs">Valid</p>
                                <p v-else class="text-xs">Invalid</p>
                            </div>
                        </div>
                    </div>

                    <!-- IP Address -->
                    <div>
                        <div class="flex items-center px-5 py-4">
                            <h5 class="text-sm font-medium mr-2"
                                :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">IP Address:</h5>
                            <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                 :class="{'text-green-550' : lead.ip.is_valid === true, 'text-red-350' : lead.ip.is_valid !== true}">
                                <svg v-if="lead.ip.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                                </svg>
                                <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p v-if="lead.ip.is_valid === true" class="text-xs">Valid</p>
                                <p v-else class="text-xs">Invalid</p>
                            </div>
                            <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                                 :class="{'text-green-550 bg-green-150' : lead.ip.is_valid === true, 'text-red-350 bg-red-75' : lead.ip.is_valid !== true}">
                                <svg v-if="lead.ip.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                                </svg>
                                <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p v-if="lead.ip.is_valid === true" class="text-xs">Valid</p>
                                <p v-else class="text-xs">Invalid</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>
    </div>
</template>

<script>
import {ApiFactory} from "../../services/api/factory";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import HasAlertsMixin, {AlertTypes} from "../../../Shared/mixins/has-alerts-mixin";

export default {
    name: "ModularLeadVerification",
    components: {LoadingSpinner, AlertsContainer},
    props: {
        leadId: {
            type: Number,
            default: null
        },
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    mixins: [HasAlertsMixin],
    data() {
        return {
            loading: false,
            lead: null,
            api: ApiFactory.makeApiService("api"),
        }
    },
    created() {
        if(this.lead === null && this.leadId !== null) {
            this.getLeadVerification();
        }
    },
    watch: {
        leadId(newVal, oldVal) {
            if(newVal !== oldVal) {
                this.getLeadVerification();
            }
        }
    },
    methods: {
        getLeadVerification() {
            this.loading = true;
            this.lead    = null;

            this.api.getLeadVerification(this.leadId).then(resp => {
                if (resp.data.data.status === true) {
                    this.lead = resp.data.data.lead;
                    return;
                }
                this.showStatusError();
            })
            .catch((e)  => this.showAlert(AlertTypes.error, e.response.data.message))
            .finally(() => this.loading = false);
        },
        showStatusError() {
            this.showAlert(AlertTypes.error, "The status was unsuccessful.");
        }
    }
}
</script>

<style scoped>

</style>
