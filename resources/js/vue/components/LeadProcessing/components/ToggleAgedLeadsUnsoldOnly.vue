<template>
    <labeled-value label="Unsold Leads Only">
        <toggle-switch
            :disabled="loading"
            :dark-mode="darkMode"
            :model-value="config?.configuration_payload?.data?.unsold_only"
            @change="updateConfig"
        />
    </labeled-value>
</template>
<script>
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import SharedApiService from "../../Shared/services/api.js";
const AGED_QUEUE_CONFIG_KEY = 'aged_queue';
export default {
    name: "ToggleAgedLeadsUnsoldOnly",
    components: {LabeledValue, ToggleSwitch},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            api: SharedApiService.make(),
            config: {
                configuration_payload: {
                    data: {
                        unsold_only: false,
                    }
                }
            },
            loading: false,
            AGED_QUEUE_CONFIG_KEY,
        }
    },
    created() {
        this.getUnsoldOnlyConfig();
    },
    methods: {
        async getUnsoldOnlyConfig() {
            this.loading = true;
            const response = await this.api.getConfigurations({config_key: AGED_QUEUE_CONFIG_KEY});
            let agedConfig = response.data.data.configurations[0];
            if (agedConfig) {
                this.config = agedConfig;
            }

            this.loading = false;
        },
        async updateConfig(newValue) {
            this.config.configuration_payload.data.unsold_only = newValue;
            await this.api.updateConfiguration(this.config)
        }
    }
}
</script>
