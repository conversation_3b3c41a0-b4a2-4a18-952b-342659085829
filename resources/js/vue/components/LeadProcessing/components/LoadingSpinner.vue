<template>
    <div class="relative flex flex-auto justify-center items-center">
        <div>
            <div
                class="loading-spinner rounded-full border-r-3 border-r-2 border-primary-500 mx-auto my-4 relative"
                :class="[spinnerSize]"
            >
            </div>
            <p class="text-center"
               :class="{'text-grey-120' : darkMode, 'text-grey-400': !darkMode}"
               v-if="label.length > 0">{{ label }}</p>
        </div>
    </div>
</template>

<script>
export default {
    name: "LoadingSpinner",
    props: {
        label: {
            type: String,
            default: ""
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        size: {
            type: String,
            default: 'm'
        }
    },
    computed: {
        spinnerSize() {
            const sizes = {
                xs: 'w-4 h-4',
                m: 'w-16 h-16'
            }

            return sizes[this.size] ?? sizes.m
        }
    }
}
</script>

<style scoped>
.loading-spinner {
    animation: spin 1.3s linear infinite;
}

@keyframes spin {
    0%   { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

