<template>
    <div id="comments" class="row-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="pb-6">
            <div class="pt-5 px-5 pb-4 flex justify-between">
                <h5 class=" text-primary-500 text-sm uppercase font-semibold leading-tight">Ping Post Logs</h5>
                <svg @click="getLogs()" class="cursor-pointer mt-2" fill="#3A7EF9" height="20px" width="20px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 489.698 489.698" xml:space="preserve">
                    <g stroke-width="0"></g><g stroke-linecap="round" stroke-linejoin="round"></g> <g> <g> <path d="M468.999,227.774c-11.4,0-20.8,8.3-20.8,19.8c-1,74.9-44.2,142.6-110.3,178.9c-99.6,54.7-216,5.6-260.6-61l62.9,13.1 c10.4,2.1,21.8-4.2,23.9-15.6c2.1-10.4-4.2-21.8-15.6-23.9l-123.7-26c-7.2-1.7-26.1,3.5-23.9,22.9l15.6,124.8 c1,10.4,9.4,17.7,19.8,17.7c15.5,0,21.8-11.4,20.8-22.9l-7.3-60.9c101.1,121.3,229.4,104.4,306.8,69.3 c80.1-42.7,131.1-124.8,132.1-215.4C488.799,237.174,480.399,227.774,468.999,227.774z"></path> <path d="M20.599,261.874c11.4,0,20.8-8.3,20.8-19.8c1-74.9,44.2-142.6,110.3-178.9c99.6-54.7,216-5.6,260.6,61l-62.9-13.1 c-10.4-2.1-21.8,4.2-23.9,15.6c-2.1,10.4,4.2,21.8,15.6,23.9l123.8,26c7.2,1.7,26.1-3.5,23.9-22.9l-15.6-124.8 c-1-10.4-9.4-17.7-19.8-17.7c-15.5,0-21.8,11.4-20.8,22.9l7.2,60.9c-101.1-121.2-229.4-104.4-306.8-69.2 c-80.1,42.6-131.1,124.8-132.2,215.3C0.799,252.574,9.199,261.874,20.599,261.874z"></path> </g> </g>
                </svg>
            </div>
            <div class="grid grid-cols-11 mb-2 px-5 ml-2">
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Date</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Publisher</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Type</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-66">Log</p>
            </div>
            <loading-spinner v-if="loading" :dark-mode="darkMode" />
            <div v-else class="border-t border-l border-r h-auto max-h-48 overflow-y-auto"
                 :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div class="border-b" :class="{ 'border-light-border': !darkMode, 'border-dark-border': darkMode }" v-if="logs.length > 0">
                    <div class="grid grid-cols-11 items-center px-5 py-1 border-b ml-1" v-for="log in logs"
                         :class="{ 'text-grey-800 hover:bg-light-module border-light-border': !darkMode, 'text-grey-200 hover:bg-dark-module border-dark-border': darkMode }">
                        <p class="text-xs col-span-2">{{ log.day }} <br> {{ log.time }}</p>
                        <p class="text-xs col-span-2">{{ log.publisher }}</p>
                        <p class="text-xs col-span-1">{{ log.type }}</p>
                        <p class="text-xs col-span-6">{{ log.log }}</p>
                    </div>
                </div>
                <div v-else>
                    <div class="px-5 py-2 text-s border-b"
                         :class="{'text-grey-800 border-light-border': !darkMode, 'text-grey-200 border-dark-border': darkMode}">
                        No Ping Post Logs
                    </div>
                </div>
            </div>
            <div class="text-xs px-5 py-2 border-t border-b"
                 :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}"
            >
                <div class="flex flex-col gap-2 items-center"
                     :class="{ 'text-grey-800 border-light-border': !darkMode, 'text-grey-200 border-dark-border': darkMode }">
                    <div class="flex items-center w-full justify-end gap-x-3">
                        <div v-if="!pingPostAvailable">
                            <p class="text-sm ml-2 mt-2"> Post Not Available: </p>
                            <div v-for="error in validationErrors">
                                <p class="text-sm ml-4"> {{error}} </p>
                            </div>
                        </div>
                        <div class="flex items-center gap-x-1">
                            <CustomButton
                                :dark-mode="darkMode"
                                :disabled="!hasPermissionToPing"
                                :icon="true"
                                height="h-7"
                                @click="ping"
                            >
                                <template v-slot:icon>
                                    <simple-icon :icon="simpleIcon.icons.PAPER_AIRPLANE" class="mr-1"/>
                                </template>
                                <template v-slot:default>
                                    Ping
                                </template>
                            </CustomButton>
                        </div>
                        <CustomButton
                            v-if="canPingPost"
                            :dark-mode="darkMode"
                            :disabled="!canPingPost"
                            :icon="true"
                            height="h-7"
                            @click="pingPost"
                        >
                            <template v-slot:icon>
                                <simple-icon :icon="simpleIcon.icons.PAPER_AIRPLANE" class="mr-1"/>
                            </template>
                            <template v-slot:default>
                                Ping Post
                            </template>
                        </CustomButton>
                    </div>
                </div>
            </div>
        </div>
        <AlertsContainer v-if="alertActive" :text="alertText" :alert-type="alertType" :dark-mode="darkMode" />
    </div>
</template>

<script>
    import ConsumerApiService from "../../Shared/services/consumer_api";
    import LoadingSpinner from "./LoadingSpinner.vue";
    import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
    import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
    import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
    import CustomInput from "../../Shared/components/CustomInput.vue";
    import CustomButton from "../../Shared/components/CustomButton.vue";
    import AlertsMixin from "../../../mixins/alerts-mixin.js";
    import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
    import Toggle from "../../Inputs/Toggle/Toggle.vue";
    import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
    const simpleIcon = useSimpleIcon()

    export default {
        name: "PingPostLogs",
        components: { ToggleSwitch, Toggle, AlertsContainer, CustomButton, CustomInput, SimpleIcon, LoadingSpinner},
        props: {
            consumerProductId: {
                type: Number,
                default: null,
            },
            darkMode: {
                type: Boolean,
                default: false,
            },
        },
        mixins: [AlertsMixin],
        data: function() {
            return {
                api: ConsumerApiService.make(),
                logs: [],
                loading: false,
                permissionStore: useRolesPermissions(),
                simpleIcon,
                pingPostAvailable: false,
                validationErrors: [],
            };
        },
        created() {
            if (this.consumerProductId)
                this.getLogs();
        },
        unmounted() {
            this.logs = [];
        },
        watch: {
            consumerProductId(newVal, oldVal) {
                if (newVal !== oldVal) this.getLogs();
            }
        },
        computed: {
            canPingPost() {
                return this.pingPostAvailable && this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_ADMIN);
            },
            hasPermissionToPing() {
                return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_ADMIN);
            },
        },
        methods: {
            async getLogs() {
                this.loading = true;

                this.api.getPingPostLogs(this.consumerProductId).then((resp) => {
                    if (resp.data.data.status) {
                        this.logs = resp.data.data.logs;
                        this.pingPostAvailable = resp.data.data.available;
                        this.validationErrors = resp.data.data.validation_errors;
                    }
                    else this.showAlert('error', `Error fetching Ping Post logs from server.`);
                }).catch(() => this.showAlert('error', `Error fetching Ping Post logs from server (Request error).`)
                ).finally(() => this.loading = false);
            },
            async ping() {
                this.loading = true;
                this.api.pingPostPingOnly(this.consumerProductId).then((resp) => {
                    if (!resp.data.data.status) {
                        this.loading = false;
                    }
                }).catch(e => {
                    this.loading = false;
                    this.showAlert('error', e.message || `Error with ping post ping attempt.`);
                }).finally(() => {
                    this.loading = false;
                    this.getLogs();
                });
            },
            async pingPost() {
                if (this.loading)
                    return;
                this.loading = true;
                this.api.pingPost(this.consumerProductId).then((resp) => {
                    if (!resp.data.data.status) {
                        this.loading = false;
                    }
                }).catch(e => {
                    this.loading = false;
                    this.showAlert('error', e.message || `Error with ping post attempt.`);
                }).finally(() => {
                    this.loading = false;
                    this.getLogs();
                });
            },
        }
    }
</script>

<style scoped>

</style>
