<template>
    <div class="fixed w-screen flex justify-center mt-6 z-[500]">
        <alert :text="text" :alert-type="alertType" :dark-mode="darkMode"></alert>
    </div>
</template>

<script>
import Alert from "./Alert.vue";
export default {
    name: "AlertsContainer",
    components: {
        Alert,
    },
    data() {
        return {
            showAlert: false,
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        alertType: {
            type: String,
            default: null
        },
        text: {
            type: String,
            default: null
        }
    }
}
</script>

<style scoped>

</style>
