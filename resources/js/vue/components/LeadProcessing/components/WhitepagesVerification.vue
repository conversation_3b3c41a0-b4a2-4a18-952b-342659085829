<template>
    <div>
        <div class="border-b"
             :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
            <div class="flex items-center px-5 py-4 flex-wrap gap-2">
                <h5 class="text-sm font-medium mr-2"
                    :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Phone:</h5>
                <div>
                    <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                         :class="{'text-green-550' : data.phone.is_valid === true, 'text-red-350' : data.phone.is_valid !== true}">
                        <svg v-if="data.phone.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="data.phone.is_valid === true" class="text-xs">Valid</p>
                        <p v-else class="text-xs">Invalid</p>
                    </div>
                    <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                         :class="{'text-green-550 bg-green-150' : data.phone.is_valid === true, 'text-red-350 bg-red-75' : data.phone.is_valid !== true}">
                        <svg v-if="data.phone.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="data.phone.is_valid === true" class="text-xs">Valid</p>
                        <p v-else class="text-xs">Invalid</p>
                    </div>
                </div>
                <div class="mr-2">
                    <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                         :class="{'text-green-550' : data.phone.name_match === true, 'text-red-350' : data.phone.name_match !== true}">
                        <svg v-if="data.phone.name_match === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="data.phone.name_match === true" class="text-xs">Match: <span class="font-medium truncate">{{ data.phone.subscriber_name !== null ? data.phone.subscriber_name : '' }}</span></p>
                        <p v-else class="text-xs">No Name Match</p>
                    </div>
                    <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                         :class="{'text-green-550 bg-green-150' : data.phone.name_match === true, 'text-red-350 bg-red-75' : data.phone.name_match !== true}">
                        <svg v-if="data.phone.name_match === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="data.phone.name_match === true" class="text-xs">Match: <span class="font-medium truncate">{{ data.phone.subscriber_name !== null ? data.phone.subscriber_name : '' }}</span></p>
                        <p v-else class="text-xs">No Name Match</p>
                    </div>
                </div>
                <div>
                    <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                         :class="{'text-blue-550' : data.phone.line_type, 'text-red-350' : data.phone.line_type == null || data.phone.line_type == ''}">
                        <svg v-if="data.phone.line_type" class="mr-1 flex-shrink-0" width="11" height="10" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0H11V1.66667H0V0ZM0 4.16667H11V5.83333H0V4.16667ZM0 8.33333H11V10H0V8.33333Z" fill="#0081FF"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="data.phone.line_type" class="text-xs">Line Type: {{ data.phone.line_type }}</p>
                        <p v-else class="text-xs">Line Type: Unknown</p>
                    </div>
                    <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium"
                         :class="{'text-blue-550 bg-cyan-125' : data.phone.line_type, 'text-red-350 bg-red-75' : data.phone.line_type == null || data.phone.line_type == ''}">
                        <svg v-if="data.phone.line_type" class="mr-1 flex-shrink-0" width="11" height="10" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0H11V1.66667H0V0ZM0 4.16667H11V5.83333H0V4.16667ZM0 8.33333H11V10H0V8.33333Z" fill="#0081FF"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p v-if="data.phone.line_type" class="text-xs">Line Type: {{ data.phone.line_type }}</p>
                        <p v-else class="text-xs">Line Type: Unknown</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="border-b"
             :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
            <div class="flex items-center px-5 py-4">
                <h5 class="text-sm font-medium mr-2"
                    :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Address:</h5>
                <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                     :class="{'text-green-550' : data.address.is_valid === true, 'text-red-350' : data.address.is_valid !== true}">
                    <svg v-if="data.address.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                    </svg>
                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <p v-if="data.address.is_valid === true" class="text-xs">Valid</p>
                    <p v-else class="text-xs">Invalid</p>
                </div>
                <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                     :class="{'text-green-550 bg-green-150' : data.address.is_valid === true, 'text-red-350 bg-red-75' : data.address.is_valid !== true}">
                    <svg v-if="data.address.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                    </svg>
                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <p v-if="data.address.is_valid === true" class="text-xs">Valid</p>
                    <p v-else class="text-xs">Invalid</p>
                </div>
            </div>
        </div>

        <div class="border-b"
             :class="{'border-light-border' : !darkMode, 'border-dark-border' : darkMode}">
            <div class="flex items-center px-5 py-4">
                <h5 class="text-sm font-medium mr-2"
                    :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">Email:</h5>
                <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                     :class="{'text-green-550' : data.email.is_valid === true, 'text-red-350' : data.email.is_valid !== true}">
                    <svg v-if="data.email.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                    </svg>
                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <p v-if="data.email.is_valid === true" class="text-xs">Valid</p>
                    <p v-else class="text-xs">Invalid</p>
                </div>
                <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                     :class="{'text-green-550 bg-green-150' : data.email.is_valid === true, 'text-red-350 bg-red-75' : data.email.is_valid !== true}">
                    <svg v-if="data.email.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                    </svg>
                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <p v-if="data.email.is_valid === true" class="text-xs">Valid</p>
                    <p v-else class="text-xs">Invalid</p>
                </div>
            </div>
        </div>

        <div>
            <div class="flex items-center px-5 py-4">
                <h5 class="text-sm font-medium mr-2"
                    :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">IP Address:</h5>
                <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                     :class="{'text-green-550' : data.ip.is_valid === true, 'text-red-350' : data.ip.is_valid !== true}">
                    <svg v-if="data.ip.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                    </svg>
                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <p v-if="data.ip.is_valid === true" class="text-xs">Valid</p>
                    <p v-else class="text-xs">Invalid</p>
                </div>
                <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                     :class="{'text-green-550 bg-green-150' : data.ip.is_valid === true, 'text-red-350 bg-red-75' : data.ip.is_valid !== true}">
                    <svg v-if="data.ip.is_valid === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                    </svg>
                    <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <p v-if="data.ip.is_valid === true" class="text-xs">Valid</p>
                    <p v-else class="text-xs">Invalid</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "WhitepagesVerification",
        props: {
            data: Object,
            darkMode: {
                type: Boolean,
                default: false
            }
        }
    }
</script>
