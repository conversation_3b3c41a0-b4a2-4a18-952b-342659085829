<template>
    <div class="flex items-center justify-between flex-wrap pt-4 pb-2">
        <div class="flex items-center py-1">
            <h3 class="text-xl font-medium pb-0 mr-4">Lead Processing</h3>
            <button class="px-1 mr-3 py-1 rounded text-blue-550 outline-none"
                    :class="{
                        'bg-light-module hover:bg-grey-100 text-grey-350': !darkMode,
                        'bg-dark-module hover:bg-dark-background text-blue-400': darkMode
                    }"
                    @click="toggleHistory(true)">
                <svg class="fill-current" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 4.99976V9.99976H15V7.99976H12V4.99976H10Z" />
                    <path d="M19.292 5.49664C18.8409 4.42668 18.1861 3.45465 17.364 2.63464C16.1196 1.3902 14.5377 0.537739 12.814 0.182636C11.6176 -0.0608787 10.3844 -0.0608787 9.188 0.182636C7.46267 0.535471 5.87947 1.38863 4.636 2.63564C3.81589 3.45652 3.16133 4.42749 2.708 5.49564C2.23911 6.60425 1.99832 7.79594 2 8.99964L2.001 9.02464H0L3 12.9996L6 9.02464H4.001L4 8.99964C3.99712 7.60501 4.41346 6.24171 5.195 5.08664C5.69912 4.34132 6.34095 3.69915 7.086 3.19464C7.84394 2.68404 8.69412 2.32603 9.589 2.14064C11.4075 1.76674 13.3001 2.13027 14.8507 3.15131C16.4013 4.17235 17.483 5.76733 17.858 7.58564C18.0466 8.51752 18.0466 9.47775 17.858 10.4096C17.675 11.3053 17.3168 12.1559 16.804 12.9126C16.554 13.2836 16.267 13.6326 15.95 13.9486C15.3112 14.5867 14.5558 15.0962 13.725 15.4496C13.3018 15.6285 12.862 15.7652 12.412 15.8576C11.4804 16.0461 10.5206 16.0461 9.589 15.8576C8.6943 15.6742 7.84447 15.3164 7.088 14.8046C6.71595 14.5532 6.36871 14.2669 6.051 13.9496L4.637 15.3636C5.47212 16.1999 6.46403 16.8632 7.5559 17.3155C8.64777 17.7679 9.81815 18.0003 11 17.9996C12.2031 17.9991 13.3939 17.7588 14.503 17.2926C16.1106 16.6125 17.486 15.4799 18.462 14.0326C19.4665 12.5465 20.0022 10.7934 20 8.99964C20.0025 7.79619 19.7617 6.60465 19.292 5.49664Z"/>
                </svg>
            </button>
            <a class="mr-5 font-semibold"
               :class="{
                    'text-grey-350': !darkMode,
                    'text-blue-400': darkMode
                }"
               v-if="lead != null"
               :href="getLegacyAdminQuoteUrl(lead.basic.legacy_lead_id)" target="_blank">
                #{{ lead.basic.id }} (Legacy: {{ lead.basic.legacy_lead_id }})
            </a>
            <div class="border text-sm font-medium px-5 rounded-full mr-5" v-if="lead != null"
                 :class="{'bg-cyan-100 border-primary-500 text-blue-550': !darkMode, 'bg-dark-background border-primary-500 text-blue-550': darkMode}">
                {{ lead.basic.status }}
            </div>
            <div class="border text-sm font-medium px-5 rounded-full mr-5" v-if="lead != null"
                 :class="{'bg-cyan-100 border-primary-500 text-blue-550': !darkMode, 'bg-dark-background border-primary-500 text-blue-550': darkMode}">
                {{ lead.basic.classification }}
            </div>
            <div class="border text-sm font-medium px-5 rounded-full mr-5" v-if="lead != null"
                 :class="{'bg-cyan-100 border-primary-500 text-blue-550': !darkMode, 'bg-dark-background border-primary-500 text-blue-550': darkMode}">
                {{ lead.basic.type }}
            </div>
            <div v-if="lead?.basic?.email_marketing_clone" class="border text-sm font-medium px-5 rounded-full mr-5"
                 :class="{'bg-orange-100 border-orange-500 text-amber-700': !darkMode, 'bg-dark-background border-orange-500 text-amber-700': darkMode}">
                Email Marketing Lead
            </div>
            <div v-if="lead?.basic?.channel === 6" class="border text-sm font-medium px-5 rounded-full mr-5"
                 :class="{'bg-orange-100 border-orange-500 text-amber-700': !darkMode, 'bg-dark-background border-orange-500 text-amber-700': darkMode}">
                Auto Revalidated Lead
            </div>
            <div class="border text-sm font-medium px-5 rounded-full mr-5" v-if="userQueue?.toLowerCase() === 'aged'"
                 :class="{'bg-orange-100 border-orange-500 text-amber-700': !darkMode, 'bg-dark-background border-orange-500 text-amber-700': darkMode}">
                Queue: Aged
            </div>
        </div>
        <div v-if="canProcess">
            <slot name="buttons" />
        </div>
    </div>
    <div class="flex items-center flex-wrap pb-4 ml-2 justify-between">
        <div class="flex flex-row">
            <div class="border font-medium px-5 rounded-full mr-5"
                 v-if="lead != null"
                 :class="industryClasses()"
                 :style='{borderColor : industryColor, color: industryColor }'
            >
                {{ lead.basic.industry }}
            </div>
            <div class="border font-medium px-5 rounded-full mr-5" v-if="lead != null"
                 :class="industryClasses()">
                {{ lead.basic.industry_service.name }}
            </div>
            <Tooltip v-if="lead?.basic?.secondary_services?.length"
                :dark-mode="darkMode"
            >
                <template v-slot:icon>
                    <div />
                </template>
                <template v-slot:title>
                    <div class=" cursor-pointer border font-medium px-5 rounded-full mr-5 text-slate-500 border-slate-500">
                        Secondary Services
                    </div>
                </template>
                <template v-slot:default>
                    <p>This lead can also sell legs in the following Industry Service(s):</p>
                    <p class="mt-2 font-semibold">{{ lead.basic.secondary_services.join("\n") }}</p>
                </template>
            </Tooltip>
        </div>

        <div class="flex flex-row">
            <div class="border text-sm font-medium px-5 rounded-full" v-if="lead != null"
                 :class="{'bg-cyan-100 border-primary-500 text-blue-550': !darkMode, 'bg-dark-background border-primary-500 text-blue-550': darkMode}">
                <span v-if="lead.basic.next_allocation_attempt">
                    Allocate {{ lead.basic.next_allocation_attempt }}
                </span>
                <span v-else>
                    No allocation scheduled
                </span>
            </div>
        </div>
    </div>
</template>

<script>
import LegacyAdminMixin from "../../Shared/mixins/legacy-admin-mixin.js";
import Tooltip from "../../Shared/components/Tooltip.vue";
import { PERMISSIONS, useRolesPermissions } from "../../../../stores/roles-permissions.store.js";

export default {
    name: "LeadHeader",
    components: { Tooltip },
    mixins: [
        LegacyAdminMixin
    ],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        lead: {
            type: Object,
            default: null,
        },
        userQueue: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            industryColor: null,
            permissionStore: useRolesPermissions(),
            PERMISSIONS: PERMISSIONS,
        }
    },
    emits: ['toggle-history'],
    watch: {
        darkMode() {
            if (!this.darkMode) this.industryColor = this.lead.basic.industry_color_light;
            if (this.darkMode) this.industryColor = this.lead.basic.industry_color_dark;
        }
    },
    computed: {
        canProcess() {
            return this.permissionStore.hasPermission(this.PERMISSIONS.PERMISSION_LEAD_PROCESSING);
        },
    },
    mounted() {
        if (!this.darkMode) this.industryColor = this.lead.basic.industry_color_light;
        if (this.darkMode) this.industryColor = this.lead.basic.industry_color_dark;
    },
    methods: {
        industryClasses() {
            if (this.lead.basic.industry === "Roofing") {
                if (this.darkMode) {
                    return [ 'bg-dark-background', 'border-grey-50', 'text-grey-50' ];
                } else {
                    return [ 'bg-grey-100', 'border-grey-600', 'text-grey-600' ];
                }
            }

            if (this.darkMode) {
                return [ 'bg-dark-background', 'border-primary-500', 'text-blue-550' ];
            } else {
                return [ 'bg-cyan-100', 'border-primary-500', 'text-blue-550' ];
            }
        },
        toggleHistory(show) {
            this.$emit('toggle-history', !!show);
        },
    },
}
</script>
