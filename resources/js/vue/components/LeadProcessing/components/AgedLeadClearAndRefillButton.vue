<template>
    <CustomButton
        v-if="canRefreshQueue"
        @click="askClearAndRefillQueue"
        :dark-mode="darkMode"
        :disabled="clearingQueue"
        color="orange"
    >
        Clear and refill queue
    </CustomButton>
    <ConfirmModal
        v-show="confirmModal?.show"
        :title="confirmModal?.title"
        :dark-mode="darkMode"
        :text="confirmModal.text"
        @choice="handleAgedQueueFlushChoice"
    />
    <Modal
        v-if="clearingQueue"
        hide-confirm
        hide-cancel
        no-close-button
        no-min-height
        no-buttons
    >
        <template #header>
            <slot name="header">
                <h2 class="text-xl font-medium">The queue is being cleared and filled</h2>
            </slot>
        </template>
        <template #content>
            <p class="text-lg">You will be redirected in {{countdown.count}}s...</p>
        </template>
    </Modal>
</template>

<script>

import CustomButton from "../../Shared/components/CustomButton.vue";
import ConfirmModal from "../../Shared/components/ConfirmationModal.vue";
import {useCountdown} from "../../Shared/composables/countdown.js";
import {useToastNotificationStore} from "../../../../stores/billing/tost-notification.store.js";
import {ApiFactory} from "../services/api/factory.js"
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import Modal from "./Modal.vue";

export default {
    name: "AgedLeadClearAndRefillButton",
    components: {Modal, ConfirmModal, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            api: ApiFactory.makeApiService('api'),
            showHistory: false,
            clearingQueue: false,
            confirmModal: {},
            countdown: useCountdown(),
            toastNotificationStore: useToastNotificationStore(),
        }
    },
    computed: {
        canRefreshQueue() {
            return this.rolePermissions.hasPermission(PERMISSIONS.AGED_LEAD_QUEUE_REFRESH_QUEUE)
        },
        rolePermissions(){
            return useRolesPermissions()
        }
    },
    methods: {
        askClearAndRefillQueue(){
            this.confirmModal = {
                show: true,
                title: 'Confirm to clear and refill aged queue',
                text: 'Do you want to clear and refill aged queue?',
            }
        },
        handleAgedQueueFlushChoice(choice){
            if (choice) {
                this.clearAndRefillQueue()
            }

            this.confirmModal = {
                show: false,
            }
        },
        async clearAndRefillQueue(){
            this.clearingQueue = true

            this.countdown.startCountdown(
                () => location.reload()
            )

            try {
                await this.api.refreshAgedLeadQueue()
                this.toastNotificationStore.notifySuccess(
                    'Success! The queue will be populated in a few minutes.'
                )
            } catch (err) {
                console.error(err)
                this.toastNotificationStore.notifyError('Error trying to clear and refill queue')
            }
        }
    }
}
</script>
