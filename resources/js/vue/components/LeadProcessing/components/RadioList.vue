<template>
    <div>
        <div @click="() => selectOption(option)" class="flex cursor-pointer items-center mb-3" v-for="option in options" :key="option.id ? option.id : option">
            <div class="rounded-full h-6 w-6 border-2 border-primary-500 inline-flex justify-center items-center mr-2">
                <div v-if="isSelected(option)" class="bg-primary-500 w-4 h-4 rounded-full"></div>
            </div>
            <div>
                {{ option.name ? option.name : option }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "RadioList",
    props: {
        options: {
            type: Array,
            default: () => []
        },
        selected: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            selectedOption: this.selected,
            radioSelected: false
        }
    },
    emits: ['update:modelValue'],
    methods: {
        toggleSelectedRadio() {
            this.radioSelected = ! this.radioSelected
        },
        selectOption(option) {
            this.selectedOption = option;

            this.$emit('update:modelValue', option.id ? option.id : option);
        },
        isSelected(option) {
            if(this.selectedOption) {
                return this.selectedOption.id === option.id;
            }

            return false;
        }
    }
}
</script>

<style scoped>

</style>
