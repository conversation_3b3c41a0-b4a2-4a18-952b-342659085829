<template>
    <div id="comments" class="row-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="pb-6">
            <div class="pt-5 px-5 pb-4 flex justify-between">
                <h5 class=" text-primary-500 text-sm uppercase font-semibold leading-tight">Affiliate Lead Internal Tracking</h5>
                <slot name="header-actions"></slot>
            </div>
            <loading-spinner v-if="loading" :dark-mode="darkMode" />
            <div v-else class="border-t border-l border-r h-auto max-h-64 overflow-y-auto"
                 :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div v-if="manualFields">
                    <div v-for="(value, key) in manualFields" class="grid grid-cols-2 gap-2 items-center px-5 py-2 border-b"
                         :class="{ 'text-grey-800 hover:bg-light-module border-light-border': !darkMode, 'text-grey-200 hover:bg-dark-module border-dark-border': darkMode }">
                        <div class="text-xs">
                            <p v-if="checkInitialKeys(key)">{{ key }}</p>
                            <input
                                v-else
                                v-model="newKeys[key]"
                                class="rounded"
                                :class="{ 'text-grey-800 bg-light-module border-light-border': !darkMode, 'text-grey-200 bg-dark-module border-dark-border': darkMode }"
                                :id="key"
                                type="text"
                            />
                        </div>
                        <div class="text-xs inline-flex" >
                            <input
                                v-model="manualFields[key]"
                                class="rounded"
                                :class="{ 'text-grey-800 bg-light-module border-light-border': !darkMode, 'text-grey-200 bg-dark-module border-dark-border': darkMode }"
                                :id="key"
                                type="text"
                            />
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="20" height="20" viewBox="0 0 24 24" class="ml-2 mt-2 cursor-pointer" @click="deleteField(key)">
                                <path d="M 10 2 L 9 3 L 4 3 L 4 5 L 5 5 L 5 20 C 5 20.522222 5.1913289 21.05461 5.5683594 21.431641 C 5.9453899 21.808671 6.4777778 22 7 22 L 17 22 C 17.522222 22 18.05461 21.808671 18.431641 21.431641 C 18.808671 21.05461 19 20.522222 19 20 L 19 5 L 20 5 L 20 3 L 15 3 L 14 2 L 10 2 z M 7 5 L 17 5 L 17 20 L 7 20 L 7 5 z M 9 7 L 9 18 L 11 18 L 11 7 L 9 7 z M 13 7 L 13 18 L 15 18 L 15 7 L 13 7 z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="px-5 py-2 flex justify-between">
                        <div>
                            <button
                                v-if="isModified"
                                class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5"
                                @click="saveChanges">
                                Save
                            </button>
                            <button
                                v-if="isModified"
                                class="transition duration-200 bg-red-450 hover:bg-red-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5 ml-2"
                                @click="getAffiliateInfo">
                                Cancel
                            </button>
                        </div>
                        <button
                            class="transition duration-200 text-sm font-semibold focus:outline-none py-2 rounded-md px-5"
                            :class="{'bg-grey-250 hover:bg-grey-400 text-white': !darkMode, 'bg-grey-500 hover:bg-grey-600 text-white': darkMode}"
                            @click="addField">
                            Add Field
                        </button>
                    </div>
                </div>
                <div v-else class="text-xs px-5 py-2 border-b" :class="{ 'border-light-border': !darkMode, 'border-dark-border': darkMode }">
                    No Affiliate Data
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import ConsumerApiService from "../../Shared/services/consumer_api";
    import LoadingSpinner from "./LoadingSpinner.vue";
    import {DateTime} from "luxon";
    import CursorPagination from "../../Shared/components/CursorPagination.vue";
    import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
    import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
    import {ROLES, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
    const simpleIcon = useSimpleIcon()

    export default {
        name: "AffiliateLeadInternalTracking",
        components: {SimpleIcon, CursorPagination, LoadingSpinner},
        props: {
            consumerProductId: {
                type: Number,
                default: null,
            },
            darkMode: {
                type: Boolean,
                default: false,
            },
        },
        data: function() {
            return {
                api: ConsumerApiService.make(),
                affiliateName: null,
                affiliateData: [],
                manualFields: [],
                initialKeys: [],
                newKeys: {},
                newKeyCounter: 0,
                loading: false,
                permissionStore: useRolesPermissions(),
                simpleIcon,
                isModified: false,
            };
        },
        created() {
            if (this.consumerProductId) this.getAffiliateInfo();
        },
        unmounted() {
            this.affiliateName = null;
            this.originalData = [];
            this.manualFields = [];
            this.initialKeys = [];
            this.isModified = false;
        },
        computed: {
            DateTime() {
                return DateTime
            },
        },
        methods: {
            async getAffiliateInfo() {
                this.loading = true;

                this.api.getAffiliateLeadInfo(this.consumerProductId).then((resp) => {
                    if (resp.data.data.status) this.addAffiliateInfo(resp.data.data.info, resp.data.data.manual, resp.data.data.name);
                    else this.showAlert('error', `Error fetching Consumer Product affiliate info.`);
                }).catch(() => this.showAlert('error', `Error fetching Consumer Product affiliate info from the server.`)
                ).finally(() => this.loading = false);
            },
            async saveChanges() {
                this.loading = true;

                Object.keys(this.manualFields).forEach(key => {
                    if (Object.keys(this.newKeys).includes(key)) {
                        this.manualFields[this.newKeys[key]] = this.manualFields[key];
                        delete this.manualFields[key];
                    }
                });

                this.api.saveAffiliateManualTrackingInfo(this.consumerProductId, { manual: this.manualFields }).then((resp) => {
                    if (!resp.data.data.status) this.showAlert('error', `Error saving manual tracking data.`)
                    else {
                        this.isModified = false;
                        this.initialKeys = Object.keys(this.manualFields);
                    }
                }).catch(() => this.showAlert('error', `Server Error saving manual tracking data.`)
                ).finally(() => this.loading = false);
            },
            deleteField(key) {
                delete this.manualFields[key];
            },
            addField() {
                const tempNewKeyString = 'new_key_' + this.newKeyCounter;
                this.manualFields[tempNewKeyString] = '';
                this.newKeys[tempNewKeyString] = '';
                this.newKeyCounter += 1;
            },
            checkInitialKeys(key) {
                return this.initialKeys.includes(key);
            },
            addAffiliateInfo(data, manual, name) {
                this.affiliateName = name;
                this.originalData = { ...manual};
                this.manualFields = manual;
                this.initialKeys = Object.keys(manual);
            },
            checkChanges() {
                this.isModified = JSON.stringify(this.manualFields) !== JSON.stringify(this.originalData);
            },
        },
        watch: {
            // Watch for any changes in the data and check if a change happened
            manualFields: {
                handler() {
                    this.checkChanges();
                },
                deep: true
            }
        }
    }
</script>

<style scoped>

</style>
