<template>
    <div id="comments" class="row-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="pb-6">
            <div class="pt-5 px-5 pb-4 flex justify-between">
                <h5 class=" text-primary-500 text-sm uppercase font-semibold leading-tight">Affiliate Lead Info</h5>
                <slot name="header-actions"></slot>
            </div>
            <loading-spinner v-if="loading" :dark-mode="darkMode" />
            <div v-else class="border-t border-l border-r h-auto max-h-64 overflow-y-auto"
                 :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div v-if="affiliateName" class="px-5 pt-2 pb-2">
                    Affiliate Source: <b>{{affiliateName}}</b>
                </div>
                <div v-if="affiliateData">
                    <div v-for="(value, key) in affiliateData" class="grid grid-cols-2 gap-2 items-center px-5 py-2 border-b"
                         :class="{ 'text-grey-800 hover:bg-light-module border-light-border': !darkMode, 'text-grey-200 hover:bg-dark-module border-dark-border': darkMode }">
                        <div class="text-xs">
                            <p>{{ key }}</p>
                        </div>
                        <div class="text-xs " >
                            {{ value }}
                        </div>
                    </div>
                </div>
                <div v-else class="text-xs px-5 py-2 border-b" :class="{ 'border-light-border': !darkMode, 'border-dark-border': darkMode }">
                    No Affiliate Data
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import ConsumerApiService from "../../Shared/services/consumer_api";
    import LoadingSpinner from "./LoadingSpinner.vue";
    import {DateTime} from "luxon";
    import CursorPagination from "../../Shared/components/CursorPagination.vue";
    import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
    import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
    import {ROLES, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
    const simpleIcon = useSimpleIcon()

    export default {
        name: "AffiliateLeadInfo",
        components: {SimpleIcon, CursorPagination, LoadingSpinner},
        props: {
            consumerProductId: {
                type: Number,
                default: null,
            },
            darkMode: {
                type: Boolean,
                default: false,
            },
        },
        data: function() {
            return {
                api: ConsumerApiService.make(),
                affiliateName: null,
                affiliateData: [],
                manualFields: [],
                loading: false,
                permissionStore: useRolesPermissions(),
                simpleIcon
            };
        },
        created() {
            if (this.consumerProductId) this.getAffiliateInfo();
        },
        unmounted() {
            this.affiliateName = null;
            this.affiliateData = [];
            this.manualFields = [];
        },
        computed: {
            DateTime() {
                return DateTime
            },
        },
        methods: {
            async getAffiliateInfo() {
                this.loading = true;

                this.api.getAffiliateLeadInfo(this.consumerProductId).then((resp) => {
                    if (resp.data.data.status) this.addAffiliateInfo(resp.data.data.info, resp.data.data.manual, resp.data.data.name);
                    else this.showAlert('error', `Error fetching Consumer Product affiliate info.`);
                }).catch(() => this.showAlert('error', `Error fetching Consumer Product affiliate info from the server.`)
                ).finally(() => this.loading = false);
            },
            addAffiliateInfo(data, manual, name) {
                this.affiliateName = name;
                this.affiliateData = data;
                this.manualFields = manual;
            },
        }
    }
</script>

<style scoped>

</style>
