<template>
    <div id="comments" class="row-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="pb-6">
            <div class="pt-5 px-5 pb-4 flex justify-between">
                <h5 class=" text-primary-500 text-sm uppercase font-semibold leading-tight">Comments & Activity</h5>
                <slot name="header-actions"></slot>
            </div>
            <loading-spinner v-if="loading" :dark-mode="darkMode" />
            <div v-else class="border-t border-l border-r h-auto max-h-64 overflow-y-auto"
                 :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div v-if="comments.length > 0">
                    <div v-for="comment in comments">
                        <div class="flex justify-between gap-2 items-center px-5 py-2 text-xs"
                         :class="[darkMode ? 'text-grey-200 hover:bg-dark-module border-dark-border' : 'text-grey-800 hover:bg-light-module border-light-border',
                            expanded[comment.id] ? '' : 'border-b']"
                        >
                            <div class="flex items-center gap-x-3 pb-1">
                                <div class="text-xs whitespace-nowrap">
                                    <p>{{ comment.name }}</p>
                                    <p> {{ $filters.dateFromTimestamp(comment.created_at, 'usWithTime') }}</p>
                                </div>
                                <div class="text-xs text-center w-full bg-none">
                                    {{ comment.summary }}
                                </div>
                            </div>
                            <div class="flex items-center gap-2 text-primary-500 ml-12">
                                <div v-if="comment.public"
                                     class="cursor-pointer text-green-500"
                                     title="Public Comment"
                                >
                                    <svg class="w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" />
                                        <path fill-rule="evenodd" d="M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 0 1 0-1.113ZM17.25 12a5.25 5.25 0 1 1-10.5 0 5.25 5.25 0 0 1 10.5 0Z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div v-if="canComment && comment.editable"
                                     class="cursor-pointer hover:text-primary-300"
                                     @click="editComment(comment)"
                                     title="Edit"
                                >
                                    <svg class="w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M21.731 2.269a2.625 2.625 0 0 0-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 0 0 0-3.712ZM19.513 8.199l-3.712-3.712-12.15 12.15a5.25 5.25 0 0 0-1.32 2.214l-.8 2.685a.75.75 0 0 0 .933.933l2.685-.8a5.25 5.25 0 0 0 2.214-1.32L19.513 8.2Z" />
                                    </svg>
                                </div>
                                <div v-if="comment.comment || comment.related_activity"
                                    class="cursor-pointer hover:text-primary-300"
                                     @click="toggleDetails(comment)"
                                     title="Details"
                                >
                                    <svg class="w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4.804 21.644A6.707 6.707 0 0 0 6 21.75a6.721 6.721 0 0 0 3.583-1.029c.774.182 1.584.279 2.417.279 5.322 0 9.75-3.97 9.75-9 0-5.03-4.428-9-9.75-9s-9.75 3.97-9.75 9c0 2.409 1.025 4.587 2.674 *************.277.428.254.543a3.73 3.73 0 0 1-.814 1.686.75.75 0 0 0 .44 1.223ZM8.25 10.875a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25ZM10.875 12a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Zm4.875-1.125a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25Z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div v-if="expanded[comment.id]"
                            class="px-10 py-1 border-b text-xs"
                             :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']"
                        >
                            <div v-if="editing[comment.id]">
                                <CustomInput
                                    :dark-mode="darkMode"
                                    type="textarea"
                                    v-model="comment.comment_clone"
                                />
                                <div class="flex items-center justify-end py-1 gap-x-3">
                                    <div class="flex items-center gap-x-1">
                                        <p>
                                            Send to Company:
                                        </p>
                                        <ToggleSwitch
                                            :dark-mode="darkMode"
                                            v-model="comment.public"
                                        />
                                    </div>
                                    <CustomButton
                                        :dark-mode="darkMode"
                                        height="h-7"
                                        @click="updateComment(comment)"
                                    >
                                        <template v-slot:default>
                                            Save Comment
                                        </template>
                                    </CustomButton>
                                </div>
                                <div v-if="comment.public" class="pb-2">
                                    <p class="italic">This comment will be attached to lead delivery when allocated.</p>
                                </div>
                            </div>
                            <div v-else-if="comment.comment">
                                <div class="text-xs pb-2 mt-1">
                                    User comment: {{ comment.comment }}
                                </div>
                            </div>
                            <div v-if="!loading && comment.related_activity?.id && fetchedActivities[comment.id]"
                                class="pb-2"
                            >
                                <div v-if="fetchedActivities[comment.id].type === 'call'">
                                    <p class="my-1">
                                        Duration: {{ fetchedActivities[comment.id].recording?.duration_seconds > 60
                                            ? `${Math.floor((fetchedActivities[comment.id].recording?.duration_seconds ?? 0)/60)}m ${(fetchedActivities[comment.id].recording?.duration_seconds ?? 0)%60}s`
                                            : `${fetchedActivities[comment.id].recording?.duration_seconds ?? 0}s`
                                        }}
                                    </p>
                                    <div class="flex items-center">
                                        <p class="mr-2">Recording:</p>
                                        <div v-if="fetchedActivities[comment.id].recording?.recording_link"
                                            class="cursor-pointer"
                                        >
                                            <div v-if="!fetchedActivities[comment.id].recording.playing" @click="playRecording(fetchedActivities[comment.id].recording)" class="w-6 h-6 flex justify-center items-center border-blue-550 border rounded bg-cyan-100">
                                                <svg class="w-3 fill-current text-blue-550" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M0 0V12L10 6L0 0Z" />
                                                </svg>
                                            </div>
                                            <div v-else @click="stopRecording(fetchedActivities[comment.id].recording)" class="w-6 h-6 flex justify-center items-center border-blue-550 border rounded bg-cyan-100">
                                                <svg class="w-3 fill-current text-blue-550" viewBox="0 0 10 10" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M0 0H12V12H0V0Z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <p v-else>No recording found</p>
                                    </div>
                                </div>
                                <div v-else-if="fetchedActivities[comment.id].type === 'text'">
                                    Content: {{ fetchedActivities[comment.id].content }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="text-xs px-5 py-2 border-b" :class="{ 'border-light-border': !darkMode, 'border-dark-border': darkMode }">
                    No Comments
                </div>
            </div>
            <div v-if="canComment"
                 class="text-xs px-5 py-2 border-t border-b"
                 :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}"
            >
                <div class="flex flex-col gap-2 items-center"
                     :class="{ 'text-grey-800 border-light-border': !darkMode, 'text-grey-200 border-dark-border': darkMode }">
                        <textarea v-model="newComment"
                                  class="w-full h-full p-2 rounded-lg border col-span-3 text-xs"
                                  :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                        </textarea>
                    <div class="flex items-center w-full justify-end gap-x-3">
                        <div class="flex items-center gap-x-1">
                            <p>
                                Send to Company:
                            </p>
                            <ToggleSwitch
                                :dark-mode="darkMode"
                                v-model="newCommentPublic"
                            />
                        </div>
                        <CustomButton
                            :dark-mode="darkMode"
                            :disabled="newComment === null"
                            :icon="true"
                            height="h-7"
                            @click="addComment"
                        >
                            <template v-slot:icon>
                                <simple-icon :icon="simpleIcon.icons.PAPER_AIRPLANE" class="mr-1"/>
                            </template>
                            <template v-slot:default>
                                Add Comment
                            </template>
                        </CustomButton>
                    </div>
                    <div v-if="newCommentPublic">
                        <p class="italic">This comment will be attached to lead delivery when allocated.</p>
                    </div>
                </div>
            </div>
            <div v-if="paginationData" class="flex justify-between px-3 pt-5">
                <cursor-pagination :dark-mode="darkMode" :pagination-data="paginationData" @change-page="handlePaginationEvent"></cursor-pagination>
            </div>
        </div>
        <AlertsContainer v-if="alertActive" :text="alertText" :alert-type="alertType" :dark-mode="darkMode" />
    </div>
</template>

<script>
    import ConsumerApiService from "../../Shared/services/consumer_api";
    import LoadingSpinner from "./LoadingSpinner.vue";
    import CursorPagination from "../../Shared/components/CursorPagination.vue";
    import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
    import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
    import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
    import CustomInput from "../../Shared/components/CustomInput.vue";
    import CustomButton from "../../Shared/components/CustomButton.vue";
    import AlertsMixin from "../../../mixins/alerts-mixin.js";
    import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
    import Toggle from "../../Inputs/Toggle/Toggle.vue";
    import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
    const simpleIcon = useSimpleIcon()

    export default {
        name: "Comments",
        components: { ToggleSwitch, Toggle, AlertsContainer, CustomButton, CustomInput, SimpleIcon, CursorPagination, LoadingSpinner},
        props: {
            consumerProductId: {
                type: Number,
                default: null,
            },
            darkMode: {
                type: Boolean,
                default: false,
            },
            initialComments: {
                type: [Array,null],
                default: null
            },
        },
        mixins: [AlertsMixin],
        data: function() {
            return {
                api: ConsumerApiService.make(),
                comments: [],
                legacyLeadId: null,
                paginationData: null,
                loading: false,
                newComment: null,
                newCommentPublic: false,
                permissionStore: useRolesPermissions(),
                simpleIcon,
                fetchedActivities: {},
                expanded: {},
                editing: {},
            };
        },
        created() {
            if (this.consumerProductId && this.initialComments == null)
                this.getComments();
            else {
                this.comments = this.initialComments;
            }
        },
        unmounted() {
            this.comments = [];
            this.newComment = null;
        },
        watch: {
            consumerProductId(newVal, oldVal) {
                if (newVal !== oldVal) this.getComments();
            }
        },
        computed: {
            canComment() {
                return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_LEAD_PROCESSING);
            },
        },
        methods: {
            async getComments() {
                this.loading = true;

                this.api.getConsumerComments(this.consumerProductId).then((resp) => {
                    if (resp.data.data.status) {
                        this.addPaginatedData(resp.data.data.comments);
                    }
                    else this.showAlert('error', `Error fetching Consumer comments from server.`);
                }).catch(() => this.showAlert('error', `Error fetching Consumer comments from server.`)
                ).finally(() => this.loading = false);
            },
            async handlePaginationEvent(newPageUrl) {
                this.loading = true;
                await axios.get(newPageUrl.link).then(resp => {
                    if (resp.data.data.comments.data?.length) this.addPaginatedData(resp.data.data.comments);
                    else this.showAlert('error', `Error fetching Consumer comments from server.`);
                }).catch(() => this.showAlert('error', `Error fetching Consumer comments from server.`)
                ).finally(() => this.loading = false);
            },
            addPaginatedData({ data, ...paginationData }) {
                if (data && paginationData) {
                    this.comments = data;
                    this.paginationData = paginationData;
                }
                else this.showAlert('error', `Error fetching Consumer comments from server.`);
            },
            async addComment() {
                this.loading = true;
                this.api.addAdminComment(this.consumerProductId, {
                    comment: this.newComment,
                    scope: this.newCommentPublic ? 1 : 0,
                }).then((resp) => {
                        if (resp.data.data.status) {
                            this.newComment = null;
                            this.comments.push(resp.data.data.comment);
                        }
                        else
                            this.loading = false;
                    }).catch(e => {
                        this.loading = false;
                        this.showAlert('error', e.message || `Error creating Consumer comment.`);
                    }).finally(() => {
                        this.loading = false;
                    });
            },
            async updateComment(comment) {
                if (this.loading)
                    return;
                this.loading = true;
                const resp = await this.api.updateComment(this.consumerProductId, {
                    id: comment.id,
                    comment: comment.comment_clone,
                    scope: comment.public ? 1 : 0,
                }).catch(e => void this.showAlert('error', e.message || 'An error occurred.'));

                if (resp.data?.data?.status) {
                    comment.comment = resp.data.data.comment.comment;
                    comment.summary = resp.data.data.comment.summary;
                    comment.scope = resp.data.data.comment.scope;
                    this.editing[comment.id] = false;
                }
                else
                    this.showAlert('error', resp.data?.message ?? 'An error occurred saving the comment');

                this.loading = false;
            },
            async getRelatedActivity(comment) {
                if (!this.fetchedActivities[comment.id]) {
                    this.loading = true;
                    const resp = await this.api.getRelatedActivityForComment(this.consumerProductId, comment.id)
                        .catch(e => void this.showAlert('error', e.message || 'An error occurred.'));
                    if (resp?.data?.data?.status) {
                        this.fetchedActivities[comment.id] = {
                            ...resp.data.data.activity,
                            type: resp.data.data.activity_type,
                        };
                    }
                    else
                        this.showAlert('error', resp?.data?.message ?? 'An error occurred fetching the associated activity.');

                    this.loading = false;
                }
            },
            toggleDetails(comment) {
                this.expanded[comment.id] = !this.expanded[comment.id];
                if (this.expanded[comment.id] && comment.related_activity?.id)
                    this.getRelatedActivity(comment);
            },
            editComment(comment) {
                if (this.editing[comment.id]) {
                    this.editing[comment.id] = false;
                    this.expanded[comment.id] = false;
                }
                else {
                    comment.comment_clone = comment.comment;
                    this.editing[comment.id] = true;
                    this.expanded[comment.id] = true;
                }
            },
            playRecording(recording) {
                if (!recording.audio) {
                    recording.audio = new Audio(recording.recording_link);
                    recording.audio.addEventListener('ended', () => {
                        recording.audio.playing = false
                    });
                }
                recording.audio.play();
                recording.playing = true;
            },
            stopRecording(recording) {
                if (recording.audio) {
                    recording.audio.pause();
                    recording.playing = false;
                    recording.audio.currentTime = 0;
                }
            }
        }
    }
</script>

<style scoped>

</style>
