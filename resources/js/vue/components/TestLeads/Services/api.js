class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    static make() {
        return new ApiService('internal-api', 'test-leads', 1);
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getTestLeads(params = {}) {
        return this.axios().get('/', {
            params
        })
    }

    getTestLeadCommunications(testLeadId) {
        return this.axios().get(`/${testLeadId}`)
    }

    getFilters() {
        return this.axios().get('/filters')
    }

    getPhoneFraudScore(communication) {
        return this.axios().post(`phone_fraud_score`, {
            communication_id: communication.id,
            phone: communication.content?.From,
        });
    }

    getCommunicationsCsv(testLeadId) {
        return this.axios().get(`/${testLeadId}/export-communications`)
    }
}

const testLeadApiService = ApiService.make();
export default testLeadApiService;
