<template>
    <div
        class="px-5 grid grid-cols-9 pt-6 gap-5 pb-3"
        :class="[darkMode ? 'text-slate-400' : 'text-slate-500']"
    >
        <p class="table-header-text">ID/LEAD/DATE</p>
        <p class="table-header-text">CONSUMER</p>
        <p class="table-header-text">STATUS</p>
        <p class="table-header-text">INDUSTRY/SERVICE</p>
        <p class="table-header-text">COMPANY</p>
        <p class="table-header-text">CAMPAIGN</p>
        <p class="table-header-text">GENERATED BY</p>
        <p class="table-header-text">REVEAL AT</p>
        <p class="table-header-text text-center">COMMUNICATIONS</p>
    </div>
</template>

<script>
    export default {
        name: "TestLeadsHeader",
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            testLeads: {
                type: Array,
                default: []
            },
        }
    }
</script>

<style scoped>
    .table-header-text {
        @apply uppercase text-xs font-bold rounded;
    }
</style>
