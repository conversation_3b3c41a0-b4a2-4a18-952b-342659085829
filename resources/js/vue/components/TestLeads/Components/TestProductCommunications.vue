<template>
    <modal :hide-confirm="true" @close="$emit('close')" :dark-mode="darkMode" :container-classes="'pb-8'" :button-wrapper-classes="'mr-5'">
        <template v-slot:header>
            Communications
        </template>
        <template v-slot:content>
            <div>
                <div class="pt-3 mb-3 px-8" :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                    <div class="grid grid-cols-7 uppercase text-xs font-bold rounded gap-5">
                        <div class="col-span-2">Date / Time</div>
                        <div class="col-span-2">Event</div>
                        <div class="col-span-3 text-center">Communication</div>
                    </div>
                </div>
                <div class="border-y overflow-y-auto h-100 divide-y"
                     :class="[darkMode ? 'border-dark-border divide-dark-border bg-dark-background' : 'border-light-border divide-slate-200 bg-light-background']">
                    <div v-for="(communication, index) in communications" :key="communication.id" class="px-8 min-h-[100px] py-6 grid grid-cols-7 gap-5 text-sm items-center"
                         :class="[darkMode ? 'text-slate-100 bg-dark-background hover:bg-dark-module' : 'text-slate-900 bg-light-background hover:bg-light-module']">
                        <div class="font-semibold col-span-2">
                            <div>
                                {{ communication.display_date }}
                            </div>
                            <div v-if="communication?.date_diff">({{ communication.date_diff}} later)</div>
                        </div>
                        <div class="font-medium col-span-2">
                            {{ communication.event }}
                        </div>
                        <div class="font-semibold text-slate-500 col-span-3 text-center">
                            <div v-if="communication.type === 'sms'">
                                {{ communication.content?.Body }}
                            </div>
                            <div v-if="communication.type === 'call'">
                                <a v-if="communication.content?.RecordingUrl">
                                    <svg @click="showHideCommsData(communication.content, communication.id)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="fill-current text-primary-500 cursor-pointer mx-auto"
                                         width="20" height="20">
                                        <path fill-rule="evenodd"
                                              d="M19.5 9.75a.75.75 0 0 1-.75.75h-4.5a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 1 1.5 0v2.69l4.72-4.72a.75.75 0 1 1 1.06 1.06L16.06 9h2.69a.75.75 0 0 1 .75.75Z"
                                              clip-rule="evenodd"/>
                                        <path fill-rule="evenodd"
                                              d="M1.5 4.5a3 3 0 0 1 3-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 0 1-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 0 0 6.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 0 1 1.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 0 1-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5Z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                </a>
                            </div>
                            <div v-if="communication.type === 'email'">
                                <svg @click="showHideCommsData(communication.content, communication.id)" class="mx-auto fill-current text-primary-500 cursor-pointer" width="20" height="20" viewBox="0 0 14 10" fill="none"
                                     xmlns="http://www.w3.org/2000/svg" v-if="communication.content?.Body">
                                    <path
                                        d="M6.8156 6.42855C7.19448 6.42855 7.55784 6.27804 7.82575 6.01013C8.09366 5.74223 8.24417 5.37886 8.24417 4.99998C8.24417 4.6211 8.09366 4.25774 7.82575 3.98983C7.55784 3.72192 7.19448 3.57141 6.8156 3.57141C6.43671 3.57141 6.07335 3.72192 5.80544 3.98983C5.53753 4.25774 5.38702 4.6211 5.38702 4.99998C5.38702 5.37886 5.53753 5.74223 5.80544 6.01013C6.07335 6.27804 6.43671 6.42855 6.8156 6.42855Z"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M0 5C0.91 2.10214 3.61714 0 6.81571 0C10.0143 0 12.7214 2.10214 13.6314 5C12.7214 7.89786 10.0143 10 6.81571 10C3.61714 10 0.91 7.89786 0 5ZM9.67286 5C9.67286 5.75776 9.37184 6.48449 8.83602 7.02031C8.3002 7.55612 7.57348 7.85714 6.81571 7.85714C6.05795 7.85714 5.33123 7.55612 4.79541 7.02031C4.25959 6.48449 3.95857 5.75776 3.95857 5C3.95857 4.24224 4.25959 3.51551 4.79541 2.97969C5.33123 2.44388 6.05795 2.14286 6.81571 2.14286C7.57348 2.14286 8.3002 2.44388 8.83602 2.97969C9.37184 3.51551 9.67286 4.24224 9.67286 5Z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="col-span-7 border-t p-5" v-if="showingComms.id === communication.id && commsContent && communication.type === `email`">
                            <div class="text-slate-500 mb-3">
                                <span class="font-semibold mr-1">Subject:</span>
                                <span class="font-medium">{{ commsContent.subject }}</span>
                            </div>
                            <p v-html="commsContent.Body"></p>
                        </div>
                        <div class="col-span-7 border-t"
                             v-if="showingComms.id === communication.id && commsContent && communication.type === `call`">

                            <div class="flex mt-2 items-center">
                                <button @click="getFraudScore(communication, index)"
                                        class="transition duration-200 bg-cyan-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5 mr-4">
                                    Get Spam score
                                </button>
                                <div class="ml-2">
                                    <div v-if="loading">loading...</div>
                                    <div v-else class="flex">
                                        <div>Spam score: {{ communication.fraud_score }}</div>
                                        <span v-if="communication.fraud_score <= 40"
                                              class="text-primary-500">(Low risk)</span>
                                        <span v-else-if="communication.fraud_score <= 75" class="text-red-300">(Suspicious)</span>
                                        <span v-else class="text-red-500">(High risk)</span>
                                    </div>
                                </div>
                            </div>
                            <audio-player v-if="communication.content?.RecordingUrl"
                                          :audio-file="communication.content.RecordingUrl" :dark-mode="darkMode"/>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template #buttons>
            <button class="transition duration-200 bg-cyan-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5 mr-4"
                    @click="exportToCsv(testLeadId)">Export to CSV</button>
        </template>
    </modal>
</template>

<script setup>
import Modal from "../../Shared/components/Modal.vue";
import {computed, ref} from "vue";
import AudioPlayer from "../../Shared/components/AudioPlayer.vue";
import testLeadApiService from "../Services/api";

const emit = defineEmits(['update-fraud-score'])
const loading = ref(false);

const props = defineProps({
    darkMode: {
        type: Boolean,
        default: false
    },
    communications: {
        type: Array,
        default: []
    },
    fraud_score: {
        type: Number,
        default: null
    },
    testLeadId: {
        type: Number,
        default: null
    }
});

    const showingComms = ref({id: null});
    const commsContent = ref(null);

    const showHideCommsData = (content, id) => {
        if (showingComms.value.id === id) {
            commsContent.value = null;
            showingComms.value.id = null;
            return;
        }

        commsContent.value = content;
        showingComms.value.id = id;
    };


const getFraudScore = async (communication, index) => {
    loading.value = true;
    await testLeadApiService.getPhoneFraudScore(communication).then(resp => {
        if (resp.data.data.status) {
            const fraud_score = resp.data.data?.fraud_score;
            emit('update-fraud-score', {index, fraud_score});
            loading.value = false;
        }
    });
}

const exportToCsv = async (testLeadId) => {
    await testLeadApiService.getCommunicationsCsv(testLeadId).then((response) => {
        if (response.data.data.success) {
            const csvFileName = response.data.data.csvFileName;
            const csvContent = response.data.data.csvContent;

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', csvFileName);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } else {
            console.log(response.data.data.message);
        }
    }).catch((error) => {
        console.error('Error exporting CSV:', error);
    });
}

</script>
