<template>
    <div>
        <div class="border rounded-lg"
             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border',
                saving ? 'pointer-events-none opacity-50 greyscale-[50%]' : '']"
        >
            <AlertsContainer
                v-if="alertActive"
                :alert-type="alertType"
                :text="alertText"
                :dark-mode="darkMode"
            />
            <div class="p-5 flex items-center">
                <h5 class="text-blue-550 text-sm uppercase font-semibold leading-tight">Test Leads Search</h5>
            </div>
            <simple-table-filter
                v-if="tableFilters.length > 0"
                v-model="tableFilter"
				:active-menu="activeMenu"
				@active-menu="toggleActiveMenu"
                :filters="tableFilters"
                :dark-mode="darkMode"
                :disable-default-padding="true"
                @search="handleSearch"
                @reset="handleReset"
            >
                <template v-slot:custom-buttons="slotProps">
                    <slot name="custom-buttons" v-bind="slotProps">
                        <button
                            class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                            @click="createTestLeadModal = true"
                        >
                            + Create A Test Lead
                        </button>
                        <button
                            class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                            @click.stop="handleExportToCsv"
                        >
                            Export to CSV
                        </button>
                    </slot>
                </template>
            </simple-table-filter>
            <TestLeadsHeader
                :dark-mode="darkMode"
                :test-leads="testLeads"
            />
            <LoadingSpinner v-if="loading"/>
            <TestLeadsBody
                v-else
                :dark-mode="darkMode"
                :test-leads="testLeads"
            />
            <div class="p-3 flex items-center justify-end gap-2">
                <div>
                    <span class="text-sm text-slate-500">Results Per Page</span>
                </div>
                <div>
                    <Dropdown
                        :dark-mode="darkMode" placement="top"
                        :options="perPageOptions"
                        v-model="perPage"
                        :selected="10"
                    />
                </div>
                <Pagination
                    :dark-mode="darkMode"
                    :pagination-data="paginationData ?? {}"
                    :show-pagination="true"
                    @change-page="handlePageChange"
                />
            </div>
        </div>
        <add-test-lead
            v-if="createTestLeadModal"
            :dark-mode="darkMode"
            @close-add-test-lead-module="handleCreateTestLead"
        />
    </div>
</template>

<script>
import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum";
import testLeadApiService from "./Services/api";
import {SimpleTableHiddenFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes";
import Pagination from "../Shared/components/Pagination.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import SimpleTableFilter from "../Shared/components/SimpleTable/components/SimpleTableFilter.vue";
import AlertsMixin from "../../mixins/alerts-mixin";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import TestLeadsHeader from "./Components/TestLeadsHeader.vue";
import TestLeadsBody from "./Components/TestLeadsBody.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import Modal from "../Shared/components/Modal.vue";
import AddCompanyV2 from "../Shared/modules/AddCompanyV2.vue";
import AddTestLead from "../Shared/modules/AddTestLead.vue";
import {downloadCsvString} from "../../../composables/exportToCsv";

export default {
    components: {
        AddTestLead,
            AddCompanyV2,
            Modal, CustomInput,
            CustomButton,
            LoadingSpinner,
            TestLeadsHeader,
            TestLeadsBody,
            AlertsContainer,
            SimpleTableFilter,
            Dropdown,
            Pagination,
        },
        props: {
            darkMode: {
                type: Boolean,
                default: false,
            },
        },
        mixins: [AlertsMixin],
        data () {
            return {
				activeMenu: null,
                headers: [
                    {title: "ID", field: "id"},
                    {title: "Company", field: "company_name"},
                    {title: "Industry", field: "industry"},
                    {title: "Service", field: "service"},
                    {title: "Campaign Name", field: "campaign_name"},
                    {title: "Phone", field: "phone"},
                    {title: "Email", field: "email"},
                    {title: "Status", field: "status"},
                ],
                tableFilters: [
                    {
                        location: SimpleTableFilterTypesEnum.VISIBLE,
                        field: 'company_name',
                        title: 'Company Name / ID',
                        search_icon: true,
                    },
                    //todo allow for searching legacy lead campaigns
                    {
                        location: SimpleTableFilterTypesEnum.VISIBLE,
                        field: 'campaign_name',
                        title: 'Campaign Name',
                        search_icon: true,
                    },
                    {
                        type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                        location: SimpleTableFilterTypesEnum.HIDDEN,
                        field: 'industry_types',
                        title: 'Industries',
                        options: []
                    },
                    {
                        type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                        location: SimpleTableFilterTypesEnum.HIDDEN,
                        field: 'service_types',
                        title: 'Services',
                        options: []
                    },
					{
                        type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                        location: SimpleTableFilterTypesEnum.HIDDEN,
                        field: 'product_author',
                        title: 'Generated By',
                        options: [
                            {id: 'all_products', name: 'All'}
                        ]
                    }
                ],
                tableFilter: {},
                loading: false,
                saving: false,
                testLeads: [],
                paginationData: {},
                perPageOptions: [ { id: 10, name: "10" }, { id: 20, name: "20" }, { id: 50, name: "50" }, { id: 100, name: "100" } ],
                perPage: 10,
                page: 1,
                createTestLeadModal: false,
            }
        },
        async mounted() {
            await this.getTestLeads()
            await this.getFilters()
        },
        methods: {
            handleExportToCsv(){
                testLeadApiService.getTestLeads({...this.getParams(), no_pagination: 1}).then(resp => {
                    const keys = {
                        'campaign_id': 'Campaign ID',
                        'campaign_name': 'Campaign Name',
                        'company_id': 'Company Id',
                        'company_name': 'Company Name',
                        'created_at': 'Created At',
                        'email': 'Email',
                        'industry': 'Industry',
                        'lead_id': 'Lead Id',
                        'legacy_campaign_id': 'Legacy Campaign Id',
                        'legacy_campaign_name': 'Legacy Campaign Name',
                        'name': 'Name',
                        'phone': 'Phone',
                        'product_id': 'Product Id',
                        'reveal_at': 'Reveal At',
                        'service': 'Service',
                        'status': 'Status',
                        'verified': 'Verified',
                        'created_by_id': 'Created by Id',
                        'created_by_name': 'Created by Name'
                    }

                    const transformed = resp.data.data.map(e => {
                        return Object.keys(keys).reduce((prev, current) => {
                            prev.push(e[current] ?? null)
                            return prev;
                        }, [])
                    })

                    const fileName = `TestLeadsExport_${+new Date()}`

                    downloadCsvString(Object.values(keys), transformed, `${fileName}.csv`)
                });
            },
			toggleActiveMenu(item) {
				this.activeMenu === item ? this.activeMenu = null : this.activeMenu = item;
			},
            handleCreateTestLead(closeModal, createdLead) {
                if (createdLead) {
                    this.showAlert('success', 'A test lead is being created and will soon be available');
                }
                if (closeModal) {
                    this.createTestLeadModal = false
                }
            },
            handleSearch(){
                this.getTestLeads()
            },

            async getTestLeads() {
                this.loading = true;
                await testLeadApiService.getTestLeads(this.getParams()).then(resp => {
                    const {data, links, meta} = resp.data;
                    this.testLeads = data;
                    this.paginationData = { links, ...meta} ?? {}
                });

                this.loading = false;
            },
            getParams()  {
                return {
                    per_page: this.perPage,
                    page: this.page,
                    ...this.tableFilter
                };
            },
            handlePageChange({ newPage }){
                if (this.page === newPage) return

                this.page = newPage
                this.getTestLeads()
            },
            handlePerPageChange(perPage){
                if (this.perPage === perPage) return

                this.page = 1
                this.perPage = perPage
                this.getTestLeads()
            },

            async getFilters() {
                await testLeadApiService.getFilters().then(resp => {
                    const {industries, services, generated_by_users} = resp.data;

                    this.assignFilters('industry_types', industries);
                    this.assignFilters('service_types', services);
                    this.assignFilters('product_author', generated_by_users)
                });
            },

            assignFilters(filterField, filters) {
                const index = this.tableFilters.findIndex(e => e.field === filterField)
                this.tableFilters[index].options.push(...filters);
            },

            handleReset() {
                this.page=1;
                this.perPage=10;
                this.getTestLeads();
            },

        }
    }

</script>
