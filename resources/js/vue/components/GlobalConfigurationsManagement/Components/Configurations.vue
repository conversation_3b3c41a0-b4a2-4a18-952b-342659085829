<template>
    <div class="grid lg:grid-cols-4 gap-4">
        <div class="lg:col-span-4 rounded-lg bg-red-100 p-4 mb-3" v-if="error">
            <p class="text-sm font-medium text-red-800">{{ error }}</p>
        </div>
        <div class="lg:col-span-4 border rounded-lg"
             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
            <div class="p-5 flex items-center justify-between">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Configurations</h5>
                <div class="">
                    <button v-if="hasEditRights" @click="addConfiguration"
                            class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5">
                        Add New Configuration
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-5 gap-3 mb-2 px-5">
                <div class="text-slate-400 font-medium tracking-wide uppercase text-xs">ID</div>
                <div class="text-slate-400 font-medium tracking-wide uppercase text-xs">Key</div>
                <div class="text-slate-400 font-medium tracking-wide uppercase text-xs">Last Updated</div>
                <div class="text-slate-400 font-medium tracking-wide uppercase text-xs">Updated By</div>
                <div></div>
            </div>
            <div
                v-if="saving"
                class="flex items-center justify-center h-80"
            >
                <loading-spinner></loading-spinner>
            </div>
            <div
                v-else
                class="border-t border-b h-80 overflow-y-auto"
                :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']"
            >
                <div>
                    <div class="grid grid-cols-5 gap-x-3 border-b px-5 py-3"
                         v-for="configuration in configurations" :key="configuration.id"
                         :class="[darkMode ? 'text-slate-100 hover:bg-dark-module border-dark-border' : 'text-slate-900 hover:bg-light-module border-light-border']"
                    >
                        <div class="text-sm">
                            {{ configuration.id }}
                        </div>
                        <div class="text-sm">
                            {{ configuration.configuration_key }}
                        </div>
                        <div class="text-sm">
                            {{ configuration.updated_at }}
                        </div>
                        <div class="text-sm">
                            {{ configuration.updated_by }}
                        </div>
                        <div class="flex justify-end">
                          <ActionsHandle v-if="hasEditRights" :dark-mode="darkMode" @edit="editConfiguration(configuration)" no-delete-button />
                          <button v-else @click="editConfiguration(configuration)">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#0081FF" class="w-6 h-6">
                              <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"/>
                              <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                          </button>
                        </div>
                    </div>
                </div>
            </div>
            <Modal
                v-if="showModal"
                @confirm="saveConfiguration"
                @close="closeModal"
                :no-buttons="!hasEditRights"
                close-text="Cancel"
                :confirm-text="editingConfiguration.id ? 'Update' : 'Create'"
                :dark-mode="darkMode"
                small
            >
                <template v-slot:header>
                    <h4 class="text-xl">{{ !hasEditRights ? 'View Configuration' : editingConfiguration.id ? 'Edit Configuration' : 'Add Configuration' }}</h4>
                </template>
                <template v-slot:content>
                    <div class="mb-4">
                        <label class="block pb-2 font-medium">Key</label>
                        <custom-input v-model="editingConfiguration.configuration_key" :dark-mode="darkMode" :readonly="!hasEditRights || !!editingConfiguration?.id" />
                    </div>
                    <div>
                        <label class="block pb-2 font-medium">Configuration Payload</label>
                        <div class="mb-5 flex flex-col gap-2">
                            <div v-for="(_, key) in editingConfiguration.configuration_payload.data" :key="key" class="flex items-center justify-between gap-2">
                                <custom-input :modelValue="key" :dark-mode="darkMode" class="w-full"/>
                                <custom-input
                                    v-model="editingConfiguration.configuration_payload.data[key]"
                                    :dark-mode="darkMode"
                                    class="w-full"
                                    :readonly="!hasEditRights"
                                />
                            </div>
                        </div>
                        <div class="mb-5" v-if="hasEditRights">
                            <div class="flex justify-between items-center gap-2">
                                <custom-input v-model="newConfigurationKey" :dark-mode="darkMode"  class="w-full" />
                                <custom-input v-model="newConfigurationValue" :dark-mode="darkMode" class="w-full" />
                            </div>
                        </div>
                    </div>
                    <custom-button v-if="hasEditRights" @click="addConfigurationPayload">
                        Add
                    </custom-button>
                </template>
            </Modal>
        </div>
    </div>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";

export default {
    name: "Configurations",
    components: {ToggleSwitch, CustomButton, CustomInput, ActionsHandle, Dropdown, Modal, LoadingSpinner},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        configurations: {
            type: Array,
            default: []
        },
        error: {
            type: String,
            default: null
        },
        saving: {
            type: Boolean,
            default: false
        },
        hasEditRights: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            editingConfiguration: {},
            newConfigurationKey: '',
            newConfigurationValue: '',
            showModal: false,
        }
    },
    methods: {
        addConfiguration() {
            this.editingConfiguration = { configuration_key: null, configuration_payload: {data: {}}};
            this.newConfigurationKey = '';
            this.newConfigurationValue = '';
            this.showModal = true;
        },
        addConfigurationPayload() {
            this.newConfigurationKey = this.newConfigurationKey.trim()
            this.newConfigurationValue = this.newConfigurationValue.trim()

            if (
                this.newConfigurationKey.length === 0 &&
                this.newConfigurationValue.length === 0
            ) return;

            let normalizedKey = this.normalizeKey(this.newConfigurationKey);
            this.updateConfigurationPayload(normalizedKey, this.newConfigurationValue);
            this.newConfigurationKey = '';
            this.newConfigurationValue = '';
            this.showModal = true;
        },
        updateConfigurationPayload(key, value) {
            if(!this.editingConfiguration.configuration_payload) {
                this.editingConfiguration.configuration_payload = {
                    data: {}
                };
            }

            this.editingConfiguration.configuration_payload.data[key] = value;
        },
        editConfiguration(configuration) {
            this.editingConfiguration = {...configuration};

            // Convert to a string to prevent issues with the text component
            this.editingConfiguration.configuration_payload.data = Object.entries(
                this.editingConfiguration.configuration_payload.data
            ).reduce(
                (prev, [key, val]) =>
                    Object.assign(prev, {
                        [key]: val != null ? val.toString() : "",
                    }),
                {}
            );

            this.showModal = true;
        },
        saveConfiguration() {
            this.editingConfiguration.configuration_key = this.normalizeKey(this.editingConfiguration.configuration_key);

            if(Object.keys(this.editingConfiguration.configuration_payload.data).length === 0) {
                this.editingConfiguration.configuration_payload = null;
            }

            this.addConfigurationPayload()

            if(this.hasEditRights) {
                if (this.editingConfiguration.id) this.$emit('update-configuration', this.editingConfiguration);
                else this.$emit('create-configuration', this.editingConfiguration);
            }
            this.closeModal();
        },
        closeModal() {
            this.editingConfiguration = {};
            this.newConfigurationKey = '';
            this.newConfigurationValue = '';
            this.showModal = false;
        },
        normalizeKey(key) {
          const step1 = key.replace(/[^a-zA-Z0-9_\s]/g, '').trim();
          const step2 = step1.replace(/[A-Z]/g, (match) => ' ' + match.toLowerCase());
          const step3 = step2.replace(/\s+/g, '_');

          return step3.trim();
        },
    }
}
</script>

<style scoped>

</style>
