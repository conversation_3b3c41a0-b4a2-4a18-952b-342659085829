<template>
    <div class="w-full flex-auto pt-3 relative"
         :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
        <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
            <div class="flex items-center justify-between flex-wrap py-4">
                <div class="flex items-center py-1">
                    <h3 class="text-xl font-medium pb-0 mr-4">Global Configurations Management</h3>
                </div>
            </div>
            <div class="grid">
                <div>
                    <configurations
                        :has-edit-rights="hasEditRights"
                        :dark-mode="darkMode"
                        :configurations="configurations"
                        :saving="savingConfiguration"
                        :error="configurationError"
                        @create-configuration="createConfiguration"
                        @update-configuration="updateConfiguration"
                    >
                    </configurations>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Configurations from "./Components/Configurations.vue";
import SharedApiService from "../Shared/services/api";

export default {
    name: "GlobalConfigurationsManagement",
    components: {Configurations},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        allConfigurations: {
            type: Object,
            default: {}
        },
        hasEditRights: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            api: SharedApiService.make(),
            configurations: this.allConfigurations,
            configurationError: null,
            savingConfiguration: false
        }
    },
    methods: {
        getConfigurations() {
            this.api.getConfigurations().then(resp => this.configurations = resp.data.data.configurations);
        },
        createConfiguration(configuration) {
            this.configurationError = null;
            this.savingConfiguration = true;

            this.api.createConfiguration(configuration)
                .then(resp => {
                    this.getConfigurations();
                })
                .catch(resp => this.configurationError = resp.response.data.message)
                .finally(() => this.savingConfiguration = false);
        },
        updateConfiguration(configuration) {
            this.configurationError = null;
            this.savingConfiguration = true;

            this.api.updateConfiguration(configuration)
                .then(resp => {
                    this.getConfigurations();
                })
                .catch(resp => this.configurationError = resp.response.message)
                .finally(() => this.savingConfiguration = false)
        },
    }
}

</script>
<style scoped>

</style>

