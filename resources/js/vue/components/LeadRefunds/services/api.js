import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'lead-refunds', 1);
    }

    getStatuses() {
        return this.axios().get('/statuses')
    }

    list(filter) {
        return this.axios().get('/', {
            params: filter
        })
    }

    show(id) {
        return this.axios().get(`/approvals/${id}`)
    }

    submitReview(requestId, data) {
        return this.axios().post(`/approvals/${requestId}/review`, data)
    }

    addComments(requestId, data) {
        return this.axios().post(`/approvals/${requestId}/comments`, data)
    }

    requestLeadsRefund(data){
        return this.axios().post('/', data)
    }

    getLeadRefundData(productAssignmentIds){
        return this.axios().get('/refund-data', {
            params: {
                product_assignment_ids: productAssignmentIds
            }
        })
    }
}
