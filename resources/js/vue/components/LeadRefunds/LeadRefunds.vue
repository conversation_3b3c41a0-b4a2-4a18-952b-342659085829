<template>
    <div class="w-full flex-auto pt-3 relative"
         :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
        <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
            <div class="flex items-center justify-between flex-wrap py-4">
                <div class="flex items-center py-1">
                    <h3 class="text-xl font-medium pb-0 mr-4">Lead refunds</h3>
                </div>
            </div>
            <div>
                <simple-table
                    v-model="filter"
                    :dark-mode="darkMode"
                    :data="data"
                    :headers="headers"
                    :table-filters="tableFilters"
                    :loading="loading"
                    has-row-click
                    @search="handleSearch"
                    @reset="handleReset"
                    :pagination-data="paginationData"
                    @click-row="e => handleShowReviewModal(true, e)"
                >
                    <template v-slot:visible-filters>
                        <company-search-autocomplete
                            v-model="filter.company_id"
                            :dark-mode="darkMode"
                        />
                        <user-search-autocomplete
                            v-model="filter.requested_by"
                            :dark-mode="darkMode"
                            placeholder="Requested by"
                        />
                        <user-search-autocomplete
                            v-model="filter.reviewed_by"
                            :dark-mode="darkMode"
                            placeholder="Reviewed by"
                        />
                    </template>
                    <template v-slot:row.col.requested_by="{value, item}">
                        <div class="flex flex-col">
                            <p class="font-semibold">{{ value.name }}</p>
                            <div class="flex gap-1 items-center">
                                <simple-icon icon="calendar" tooltip="Requested at"/>
                                <p class="text-xs">{{ item.requested_at }}</p>
                            </div>
                        </div>
                    </template>
                    <template v-slot:row.col.company="{value}">
                        <a class="text-blue-500 cursor-pointer" :href="'/companies/' + value.id" target="_blank">
                            {{ value.name }}
                        </a>
                    </template>
                    <template v-slot:row.col.total="{item, value}">
                        <div class="flex flex-col gap-1">
                            <p>{{ value }}</p>
                            <p class="font-light text-gray-500 text-xs">{{ item.items.length }} leads</p>
                        </div>
                    </template>
                    <template v-slot:row.col.status="{value}">
                        <lead-refund-status-badge :status="value" />
                    </template>
                    <template v-slot:row.col.reviewed_by="{value, item}">
                        <div class="flex flex-col">
                            <p class="font-semibold">{{ value?.name }}</p>
                            <div v-if="item.reviewed_at" class="flex gap-1 items-center">
                                <simple-icon icon="calendar" tooltip="Requested at"/>
                                <p class="text-xs">{{ item.reviewed_at }}</p>
                            </div>
                        </div>
                    </template>
                </simple-table>
            </div>
        </div>
        <review-refund-lead-request
            v-if="showReviewModal"
            :refund-request="selectedItem"
            :lead-refund-request-id="selectedLeadRefundRequestId"
            @close="handleShowReviewModal(false)"
        />
    </div>
</template>

<script>

import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import ApiService from "./services/api.js";
import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {SimpleTableHiddenFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import Badge from "../Shared/components/Badge.vue";
import ReviewRefundLeadRequest from "./ReviewRefundLeadRequestModal.vue";
import CompanySearchAutocomplete from "../Shared/components/Company/CompanySearchAutocomplete.vue";
import LeadRefundStatusBadge from "./components/LeadRefundStatusBadge.vue";
import useLeadRefundsHelper from "../../../composables/leadRefundsHelper.js";
import UserSearchAutocomplete from "../Shared/components/User/UserSearchAutocomplete.vue";
import useObjectHelper from "../../../composables/useObjectHelper.js";
import useQueryParams from "../../../composables/useQueryParams.js";

const leadRefundsHelper = useLeadRefundsHelper();
const objectHelper = useObjectHelper();

export default {
    name: "LeadRefunds",
    components: {
        UserSearchAutocomplete,
        LeadRefundStatusBadge,
        CompanySearchAutocomplete,
        ReviewRefundLeadRequest,
        Badge,
        SimpleIcon,
        SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },

    data() {
        return {
            queryParamsHelper: useQueryParams(),
            loading: false,
            api: ApiService.make(),
            data: [],
            filter: {},
            paginationData: {},
            selectedLeadRefundRequestId: null,
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'lead_id_legacy_id',
                    title: 'Lead id or legacy id',
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'status',
                    title: 'Status',
                    options: leadRefundsHelper.getLeadStatusOptions()
                }
            ],
            headers: [
                {title: 'ID', field: 'id'},
                {title: 'Company', field: 'company'},
                {title: 'Total', field: 'total'},
                {title: 'Requested by', field: 'requested_by'},
                {title: 'Reviewed by', field: 'reviewed_by'},
                {title: 'Status', field: 'status'},
            ],
            selectedItem: null,
            showReviewModal: false
        }
    },

    mounted() {
        this.handleSearch();
        this.handleDynamicModalOpening();
    },

    methods: {
        handleDynamicModalOpening(){
            const {lead_refund_id: leadRefundId, ...rest} = this.queryParamsHelper.getCurrentParams()

            if (leadRefundId) {
                this.queryParamsHelper.setQueryParamsOnCurrentUrl(rest)
                this.selectedLeadRefundRequestId = leadRefundId
                this.showReviewModal = true
            }
        },
        handleShowReviewModal(show, item = null) {
            this.selectedItem = item
            this.showReviewModal = show
            this.selectedLeadRefundRequestId = null

            if (!show) {
                this.handleSearch()
            }
        },
        async handleSearch() {
            this.loading = true
            const response = await this.api.list(objectHelper.removeEmptyProperties(this.filter))
            const { data, links, meta } = response.data;
            this.data = data;
            this.paginationData = {links, ...meta}
            this.loading = false
        },

        handleReset(){
            this.filter = {}
            console.log(this.filter)
            this.handleSearch()
        },
    },
}
</script>

<style scoped>

</style>
