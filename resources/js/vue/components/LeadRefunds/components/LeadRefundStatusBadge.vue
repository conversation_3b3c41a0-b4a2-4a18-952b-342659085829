<template>
    <badge class="w-fit" :color="statusPresentation.badge_color">
        {{statusPresentation.name}}
    </badge>
</template>
<script>
import useLeadRefundsHelper from "../../../../composables/leadRefundsHelper.js";
import Badge from "../../Shared/components/Badge.vue"

const leadRefundsHelper = useLeadRefundsHelper();

export default {
    name: 'LeadRefundStatusBadge',
    components: {Badge},
    props: {
        status: {
            type: String,
            required: true
        }
    },
    computed: {
        statusPresentation(){
            return leadRefundsHelper.getLeadRefundStatusPresentationData(this.status)
        },
    }
}
</script>
