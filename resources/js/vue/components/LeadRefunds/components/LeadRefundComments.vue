<template>
    <div class="flex flex-col gap-4">
        <p class="font-semibold text-lg">
            Comments
        </p>
        <div v-if="comments?.length === 0">
            <span>No comments added yet</span>
        </div>
        <div v-for="comment in comments" class="flex flex-col gap-2">
            <div class="text-xs flex justify-between">
                <p>{{ comment.author }}</p>
                <p>{{ comment.created_at }}</p>
            </div>
            <div class="flex gap-2">
                <div class="text-xs p-4 shadow rounded text-justify flex-1 min-h-24">
                    {{ comment.content }}
                </div>
                <simple-icon
                    v-if="comment.newComment"
                    icon="bin"
                    color="red"
                    @click="handleRemoveComment(comment)"
                    clickable
                />
            </div>
        </div>
        <div v-if="isAddCommentSectionVisible">
            <textarea
                v-model="comment"
                placeholder="Add a comment..."
                class="min-h-88 w-full border rounded px-4 py-2 focus:outline-none focus:border focus:border-primary-500 mb-2"
                type="text"
                :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"
            />
            <custom-button :dark-mode="darkMode" @click="addComment">Add comment</custom-button>
        </div>
    </div>
</template>
<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";

export default {
    name: 'LeadRefundComments',
    components: {SimpleIcon, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
        },
        comments: {
            type: Array,
            default: []
        },
        isAddCommentSectionVisible: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            comment: ''
        }
    },
    methods: {
        handleRemoveComment(comment){
            this.$emit('removed', comment)
        },
        addComment(){
            if (this.comment.length === 0) {
                return
            }

            const parsedComment = {
                author: 'Me',
                created_at: new Date().toDateString(),
                content: this.comment,
                newComment: true,
            }
            this.$emit('added', parsedComment)
            this.comment = ''
        },
    }
}
</script>
