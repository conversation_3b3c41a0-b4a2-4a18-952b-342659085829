<template>
    <modal
        v-if="localRefundRequest"
        :dark-mode="darkMode"
        @close="handleClose"
        @confirm="handleConfirm"
        container-classes=''
        :restrict-width="false"
        :full-width="false"
        no-buttons
    >
        <template v-slot:header>
            <div class="font-bold">
                Lead Refund Request #{{ localRefundRequest.id }}
            </div>
        </template>
        <template v-slot:content>
            <loading-spinner v-if="loading"/>
            <div v-else class="container flex flex-col relative">
                <div class="overflow-auto flex-col flex gap-4 p-10">
                    <div class="flex">
                        <simple-alert v-if="alert.content" :content="alert.content" :variant="alert.variant"></simple-alert>
                    </div>
                    <div
                        class="grid gap-2 border shadow p-6 rounded-lg shadow-primary-500/10 border-primary-500 bg-grey-50"
                        :class="[localRefundRequest.reviewed_by?.name ? 'grid-cols-6' : 'grid-cols-5']"
                    >
                        <div class="flex gap-1 text-sm flex-col">
                            <p class="font-semibold">Company:</p>
                            <a
                                class="text-blue-500 cursor-pointer"
                                :href="'/companies/' + localRefundRequest.company?.id"
                                target="_blank"
                            >
                                {{ localRefundRequest.company?.name }}
                            </a>
                        </div>
                        <div class="flex gap-1 text-sm flex-col">
                            <p class="font-semibold">Total:</p>
                            <p class="text-slate-700">{{ localRefundRequest.total }}</p>
                            <p class="font-light text-gray-500 text-xs">{{ localRefundRequest?.items?.length }}
                                leads</p>
                        </div>
                        <div class="flex gap-1 text-sm flex-col">
                            <p class="font-semibold">Requested by:</p>
                            <p class="text-slate-700">{{ localRefundRequest.requested_by?.name }}</p>
                            <div class="flex gap-1 items-center">
                                <simple-icon icon="calendar" tooltip="Requested at"/>
                                <p class="text-xs">{{ localRefundRequest.requested_at }}</p>
                            </div>
                        </div>
                        <div v-if="localRefundRequest.reviewed_by?.name && localRefundRequest.reviewed_at"
                             class="flex gap-1 text-sm flex-col">
                            <p class="font-semibold">Reviewed by:</p>
                            <p class="text-slate-700">{{ localRefundRequest.reviewed_by?.name }}</p>
                            <div class="flex gap-1 items-center">
                                <simple-icon icon="calendar" tooltip="Reviewed at"/>
                                <p class="text-xs">{{ localRefundRequest.reviewed_at }}</p>
                            </div>
                        </div>
                        <div class="flex gap-1 text-sm flex-col">
                            <p class="font-semibold">Status:</p>
                            <lead-refund-status-badge :status="localRefundRequest.status" />
                        </div>
                        <div class="flex gap-1 text-sm flex-col">
                            <p class="font-semibold">Refunds:</p>
                            <p>{{localRefundRequest.number_of_successful_refunds}} / {{localRefundRequest.items.length}}</p>
                        </div>
                    </div>
                    <div class="flex flex-col gap-4 mt-4">
                        <div>
                            <p class="font-semibold text-lg">
                                Leads
                            </p>
                            <lead-refund-items-table :items="localRefundRequest.items" is-reason-disabled>
                                <template v-slot:actions="{item, idx}">
                                    <div class="flex gap-1">
                                        <simple-icon
                                            v-for="action in tableActions"
                                            :icon="action.icon"
                                            :color="item.status === action.slug ? action.color : 'text-gray-500'"
                                            :tooltip="action.title"
                                            :clickable="areReviewButtonsActive"
                                            @click.stop="reviewLeadItem(action.slug, idx)"
                                        ></simple-icon>
                                    </div>
                                </template>
                            </lead-refund-items-table>
                        </div>
                        <lead-refund-comments
                            :comments="localRefundRequest.comments"
                            @added="handleCommentAdded"
                            @removed="handleCommentRemoved"
                            :is-add-comment-section-visible="leadRefundsStore.canRequestRefund()"
                        />
                    </div>
                </div>
                <div
                    v-if="leadRefundsStore.canReviewRefund() || leadRefundsStore.canRequestRefund()"
                    class="sticky bottom-0 left-0 w-full h-75 gap-3 px-8 py-4 bg-grey-50 border rounded-b-lg shadow-lg z-100 flex justify-end"
                >
                    <custom-button
                        :dark-mode="darkMode"
                        @click="handleConfirm"
                        :disabled="loadingConfirm || submitted"
                    >
                        Submit
                    </custom-button>
                </div>
            </div>
        </template>
    </modal>
</template>
<script>


import Modal from "../Shared/components/Modal.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import Badge from "../Shared/components/Badge.vue";
import LeadRefundItemsTable from "../Shared/components/LeadRefunds/components/LeadRefundItemsTable.vue";
import ApiService from "./services/api.js";
import LeadRefundComments from "./components/LeadRefundComments.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import SimpleAlert, {SIMPLE_ALERT_VARIANTS} from "../Shared/components/SimpleAlert.vue";
import LeadRefundStatusBadge from "./components/LeadRefundStatusBadge.vue";
import useLeadRefundsHelper from "../../../composables/leadRefundsHelper.js";
import {useLeadRefundsStore} from "../../../stores/lead-refunds-store.js";
import useErrorHandler from "../../../composables/useErrorHandler.js";

const simpleIcon = useSimpleIcon()
const leadRefundsHelper = useLeadRefundsHelper();


export default {
    name: "ReviewRefundLeadRequestModal",
    components: {
        LeadRefundStatusBadge,
        SimpleAlert,
        SimpleTable,
        LoadingSpinner, LeadRefundComments, LeadRefundItemsTable, Badge, CustomButton, SimpleIcon, Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        refundRequest: {
            type: Object,
            default: {},
            required: false,
        },
        leadRefundRequestId: {
            type: Number,
            default: null,
            required: false,
        },
        disableReviewButtons: {
            type: Boolean,
            default: false,
        }
    },
    async mounted() {
        this.localRefundRequest = this.refundRequest

        if (this.leadRefundsStore.canReviewRefund()) {
            this.alert = {
                color: SIMPLE_ALERT_VARIANTS.LIGHT_BLUE,
                content: 'Please review all items to submit the review'
            }
        }

        if (this.leadRefundRequestId) {
            this.getLeadRefund()
        }
    },
    data() {
        return {
            leadRefundsStore: useLeadRefundsStore(),
            loadingConfirm: false,
            submitted: false,
            errorHandler: useErrorHandler(),
            simpleIcon,
            alert: {
                variant: null,
                content: null,
            },
            api: ApiService.make(),
            localRefundRequest: null,
            loading: false,
            tableActions: [
                {
                    title: 'Approved',
                    color: 'text-green-500',
                    slug: 'approved',
                    icon: simpleIcon.icons.HAND_THUMB_UP_SOLID
                },
                {
                    title: 'Rejected',
                    color: 'text-red-500',
                    slug: 'rejected',
                    icon: simpleIcon.icons.HAND_THUMB_DOWN_SOLID
                },
                {
                    title: 'More information needed',
                    color: 'text-yellow-500',
                    slug: 'more_information_needed',
                    icon: simpleIcon.icons.HAND_RAISED
                },
            ]
        }
    },

    computed: {
        areReviewButtonsActive() {
            return !this.disableReviewButtons && [
                leadRefundsHelper.leadRefundStatus.PENDING_REVIEW,
                leadRefundsHelper.leadRefundStatus.MORE_INFORMATION_NEEDED
            ].includes(this.localRefundRequest.status) && this.leadRefundsStore.canReviewRefund()
        },
    },

    methods: {
        async getLeadRefund(){
            this.loading = true
            const res = await this.api.show(this.leadRefundRequestId ?? this.localRefundRequest.id)
            this.localRefundRequest = res.data.data;
            this.loading = false
        },
        handleCommentRemoved(comment) {
            this.localRefundRequest.comments = this.localRefundRequest.comments.filter(c => c !== comment)
        },
        handleCommentAdded(comment) {
            if (!this.localRefundRequest.comments) {
                this.localRefundRequest.comments = []
            }

            this.localRefundRequest.comments.push(comment)
        },
        reviewLeadItem(status, idx) {
            if (this.disableReviewButtons || !this.areReviewButtonsActive) {
                return
            }
            this.localRefundRequest.items[idx].status = status
        },
        showAlert(content, variant){
            this.alert = {
                content: content,
                variant: variant,
            }
        },

        async handleConfirm() {
            this.loadingConfirm = true;

            const formattedItems = this.localRefundRequest.items.map(i => ({
                id: i.refund_item_id,
                status: i.status,
            }))

            const newComments = this.localRefundRequest.comments.filter(c => c.newComment).map(c => c.content)

            try {
                if (this.leadRefundsStore.canReviewRefund()) {
                    await this.api.submitReview(this.localRefundRequest.id, {
                        items: formattedItems,
                        comments: newComments
                    })
                } else {
                    await this.api.addComments(this.localRefundRequest.id, {
                        comments: newComments
                    })
                }
                this.submitted = true;
                this.showAlert('Review successfully submitted', SIMPLE_ALERT_VARIANTS.LIGHT_GREEN)
            } catch (err) {
                this.errorHandler.handleError(err)
                this.showAlert(this.errorHandler.message, SIMPLE_ALERT_VARIANTS.LIGHT_RED)
            }

            this.loadingConfirm = false;

            if (this.localRefundRequest.id) {
                this.getLeadRefund(this.localRefundRequest.id)
            } else {
                this.handleClose()
            }

        },
        handleClose() {
            this.$emit('close')
        },
    }
}
</script>
