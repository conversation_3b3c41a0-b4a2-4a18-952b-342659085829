import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'outreach-cadence', 1);
    }

    getAllTemplates() {
        return this.axios().get('configuration/templates/');
    }

    getEmailTemplates() {
        return this.axios().get('configuration/templates/email');
    }

    updateEmailTemplate(template) {
        return this.axios().patch('configuration/templates/email/update/' + template.id, template);
    }

    createEmailTemplate(template) {
        return this.axios().post('configuration/templates/email/create/', template);
    }

    deleteEmailTemplate(template) {
        return this.axios().delete('configuration/templates/email/delete/' + template.id);
    }

    getSmsTemplates() {
        return this.axios().get('configuration/templates/sms');
    }

    updateSmsTemplate(template) {
        return this.axios().patch('configuration/templates/sms/update/' + template.id, template);
    }

    createSmsTemplate(template) {
        return this.axios().post('configuration/templates/sms/create/', template);
    }

    deleteSmsTemplate(template) {
        return this.axios().delete('configuration/templates/sms/delete/' + template.id);
    }

    getTaskTemplates() {
        return this.axios().get('configuration/templates/task');
    }

    updateTaskTemplate(template) {
        return this.axios().patch('configuration/templates/task/update/' + template.id, template);
    }

    createTaskTemplate(template) {
        return this.axios().post('configuration/templates/task/create/', template);
    }

    deleteTaskTemplate(template) {
        return this.axios().delete('configuration/templates/task/delete/' + template.id);
    }

    getRoutineConfigs() {
        return this.axios().get('configuration/routines/owned');
    }

    getAllAvailableRoutineConfigs() {
        return this.axios().get('configuration/routines/all-available');
    }

    updateRoutineConfig(config) {
        return this.axios().patch('configuration/routines/update/' + config.id, config);
    }

    createRoutineConfig(config) {
        return this.axios().post('configuration/routines/create/', config);
    }

    deleteRoutineConfig(config) {
        return this.axios().delete('configuration/routines/delete/' + config.id);
    }

    assignCompanyRoutines(companyIds, companySearchParams, allCompanies, selectedCadenceRoutineId, isV2 = false, useAccountManager = false) {
        const route = isV2 ? 'company-routines/mass-assign-v2' : 'company-routines/mass-assign'
        return this.axios().post(route, {
            companyIds,
            companySearchParams,
            allCompanies,
            selectedCadenceRoutineId,
            useAccountManager,
        });
    }

    terminateCompanyRoutines(companyIds, companySearchParams, allCompanies, isV2 = false) {
        const route = isV2 ? 'company-routines/mass-terminate-v2' : 'company-routines/mass-terminate'
        return this.axios().post(route, {
            companyIds,
            companySearchParams,
            allCompanies
        });
    }

    getCompanyRoutines(filterParams) {
        return this.axios().get('company-routines', filterParams);
    }

    skipActionGroup(groupId) {
        return this.axios().patch('company-routines/skip-group/' + groupId);
    }

    unSkipActionGroup(groupId) {
        return this.axios().patch('company-routines/un-skip-group/' + groupId);
    }

    terminateRoutine(routineId) {
        return this.axios().delete('company-routines/' + routineId);
    }

    getCompanyRoutinesForCompany(companyId) {
        return this.axios().get('company-routines/find-for-company/' + companyId);
    }

    getCadenceContacts(routineId) {
        return this.axios().get('company-routines/contacts-for-routine/' + routineId);
    }

    updateContacts(routineId, contactUpdates) {
        return this.axios().patch('company-routines/contacts-for-routine/' + routineId, {
            'contact-updates': contactUpdates
        })
    }

    getUserHeaderAndFooter() {
        return this.axios().get('configuration/templates/user-email-header-footer/')
    }

    updateUserHeader(content) {
        return this.axios().patch('configuration/templates/user-email-header/', {
            'content': content
        })
    }

    updateUserFooter(content) {
        return this.axios().patch('configuration/templates/user-email-footer/', {
            'content': content
        })
    }

    saveImage(name, imageDataUrl, templateType, templateId = null) {
        return this.axios().post('configuration/templates/save-image', {
            image_name: name,
            image_data_url: imageDataUrl,
            template_type: templateType,
            template_id: templateId
        })
    }

    getEmailPreview(templateId, subject, body) {
        return this.axios().post('configuration/templates/preview-email', {
            template_id: templateId,
            subject: subject,
            body: body
        })
    }

    cloneEmailTemplate(templateId) {
        return this.axios().post('configuration/templates/email/clone', {template_id: templateId})
    }

    cloneSmsTemplate(templateId) {
        return this.axios().post('configuration/templates/sms/clone', {template_id: templateId})
    }

    cloneTaskTemplate(templateId) {
        return this.axios().post('configuration/templates/task/clone', {template_id: templateId})
    }

    cloneRoutine(routineId) {
        return this.axios().post('configuration/routines/clone', {routine_id: routineId})
    }

    saveConfigActionOrder(routineId, groupIdOrder) {
        return this.axios().post('configuration/routines/reorder-actions', {routine_id: routineId, new_group_order: groupIdOrder})
    }
}
