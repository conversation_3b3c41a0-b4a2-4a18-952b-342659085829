<template>
    <div>
        <div class="flex space-x-1 px-9 py-2 sticky top-9 z-10" :class="[darkMode ? 'bg-primary-900 border-dark-border' : 'bg-primary-100 border-light-border']">
            <p class="font-semibold text-sm" :class="[darkMode ? 'text-slate-300' : 'text-slate-700']">{{ name }}</p>
            <p class="font-semibold text-sm text-slate-500">({{ actions.length }})</p>
        </div>
        <div :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
            <div v-for="actionGroup in actions" class="py-2 pl-10 pr-5 flex items-center justify-between border-b"
                 :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']">
                <p>
                    <span class="font-medium text-sm capitalize">{{ actionGroup.actions[0].action_type }}</span>
                    <span :title="actionGroup.actions[0].template?.name ?? 'Communication Failure Resolution (complete task to progress cadence)'"
                        class="font-semibold text-sm text-slate-500 capitalize truncate"> - {{ actionGroup.actions[0].template?.name ?? 'Communication Failure Resolution (complete task to progress cadence)' }}</span>
                    <span
                        class="font-semibold text-sm text-slate-500"> {{ getTimeMessage(actionGroup) }}</span>
                </p>
                <ActionsHandle
                    v-if="customActions(actionGroup).length > 0"
                    :dark-mode="darkMode"
                    :custom-actions="customActions(actionGroup)"
                    :no-custom-action="false"
                    :no-edit-button="true"
                    :no-delete-button="true"
                    @view-details="viewDetails(actionGroup)"
                    @skip-action-group="skipActionGroup(actionGroup)"
                    @un-skip-action-group="unSkipActionGroup(actionGroup)"
                />
            </div>
        </div>
        <Modal
            v-if="actionDetails"
            :dark-mode="darkMode"
            :small="true"
            :restrict-width="true"
            :no-buttons="true"
            @close="actionDetails = null"
        >
            <template v-slot:header>
                <p>
                    <span class="font-semibold capitalize">{{ actionDetails.actions[0].action_type }}</span>
                    <span class="font-semibold text-sm text-slate-500 capitalize"> - {{ actionDetails.status }}</span>
                </p>

            </template>
            <template v-slot:content>
                <div class="grid gap-4">
                    <div class="flex items-center space-x-2">
                        <p class="font-semibold text-slate-500">Execution Time:</p>
                        <p class="font-medium">{{ actionDetails.target_execution_timestamp }}</p>
                    </div>
                    <div v-if="actionDetails.actions[0].preview && actionDetails.actions[0].preview.length > 0" class="overflow-y-auto h-[20rem]">
                        <p class="font-semibold mb-2 text-slate-500">Content:</p>
                        <p class="font-semibold mb-1 whitespace-pre-line">{{ actionDetails.actions[0].preview }}</p>
                    </div>
                    <div v-if="actionDetails.resolution_notes.length > 0" class="overflow-y-auto h-[20rem]">
                        <p class="font-semibold mb-2 text-slate-500">Resolution Notes:</p>
                        <ul>
                            <li class="capitalize" v-for="note in actionDetails.resolution_notes" :key="note">{{note.message}}</li>
                        </ul>
                    </div>
                </div>
                <div class="flex items-center justify-end">
                    <CustomButton @click="actionDetails = null">Done</CustomButton>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import ActionsHandle from "../../../../Shared/components/ActionsHandle.vue";
import Modal from "../../../../Shared/components/Modal.vue";
import CustomButton from "../../../../Shared/components/CustomButton.vue";

export default {
    name: "DetailsGroup",
    components: {CustomButton, Modal, ActionsHandle},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        name: {
            type: String,
            default: '',
        },
        count: {
            type: Number,
            default: 0,
        },
        actions: {
            type: Array,
            default: [],
        },
    },
    data() {
        return {
            actionDetails: null,
        }
    },
    computed: {
        customActions() {
            return actionGroup => {
                let actions = [];
                if (actionGroup.status === 'concluded')
                    actions.push({event: 'view-details', name: 'View Details'})
                if (actionGroup.status === 'queued' && actionGroup.actions[0].action_type !== 'task')
                    if (actionGroup.skip)
                        actions.push({event: 'un-skip-action-group', name: 'Cancel Skip'})
                    else
                        actions.push({event: 'skip-action-group', name: 'Skip'})
                return actions
            }
        }
    },
    methods: {
        viewDetails(groupAction) {
            this.actionDetails = groupAction;
        },
        skipActionGroup(actionGroup) {
            this.$emit('skip-action-group', actionGroup)
        },
        unSkipActionGroup(actionGroup) {
            this.$emit('un-skip-action-group', actionGroup)
        },
        getTimeMessage(actionGroup) {
            if(actionGroup.target_execution_timestamp)
                if(actionGroup.skip && actionGroup.status !== 'skipped')
                    return ' - Scheduled To Skip: ' + actionGroup.target_execution_timestamp
                else
                    return ' - ' + actionGroup.target_execution_timestamp
            return '';
        },
    }
}
</script>

<style scoped>

</style>
