<template>
    <div class="px-10">
        <alerts-container :dark-mode="darkMode" v-if="successfulUpdate" alert-type="success" text="Saved."></alerts-container>
        <alerts-container :dark-mode="darkMode" v-if="successfulDelete" alert-type="warning" text="Deleted."></alerts-container>
        <div class="border rounded-lg" :class="[darkMode ? 'bg-dark-module text-slate-50 border-dark-border' : 'bg-light-module text-slate-900 border-light-border']">
            <div class="grid grid-cols-2 items-start">
                <div class="border-r" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                    <div class="flex justify-between items-center px-5 pt-5 pb-3">
                        <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Routine Configuration</h5>
                        <div @click="expandFilters =! expandFilters" class="inline-flex items-center gap-3">
                            <CustomButton :dark-mode="darkMode" color="slate-inverse">
                                <svg class="mr-1 fill-current w-4" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M11 1.83333C11 2.17084 10.7264 2.44444 10.3889 2.44444L4.78404 2.44444C4.69374 2.69983 4.54711 2.9345 4.35192 3.1297C4.0081 3.47351 3.54179 3.66667 3.05556 3.66667C2.56933 3.66667 2.10301 3.47351 1.75919 3.1297C1.564 2.9345 1.41737 2.69983 1.32707 2.44444L0.611112 2.44444C0.273605 2.44444 1.0972e-06 2.17084 1.11196e-06 1.83333C1.12671e-06 1.49583 0.273605 1.22222 0.611112 1.22222L1.32707 1.22222C1.41737 0.966836 1.564 0.732162 1.75919 0.53697C2.10301 0.193154 2.56933 -3.68516e-07 3.05556 -3.47263e-07C3.54179 -3.26009e-07 4.0081 0.193154 4.35192 0.536971C4.54711 0.732162 4.69374 0.966837 4.78404 1.22222L10.3889 1.22222C10.7264 1.22222 11 1.49583 11 1.83333ZM11 5.5C11 5.83751 10.7264 6.11111 10.3889 6.11111L9.67293 6.11111C9.58263 6.3665 9.436 6.60117 9.24081 6.79636C8.89699 7.14018 8.43067 7.33333 7.94444 7.33333C7.45821 7.33333 6.9919 7.14018 6.64808 6.79636C6.45289 6.60117 6.30626 6.3665 6.21596 6.11111L0.611112 6.11111C0.273605 6.11111 9.36927e-07 5.83751 9.5168e-07 5.5C9.66433e-07 5.16249 0.273605 4.88889 0.611112 4.88889L6.21596 4.88889C6.30626 4.6335 6.45289 4.39883 6.64808 4.20364C6.9919 3.85982 7.45821 3.66667 7.94444 3.66667C8.43068 3.66667 8.89699 3.85982 9.24081 4.20364C9.436 4.39883 9.58263 4.6335 9.67293 4.88889L10.3889 4.88889C10.7264 4.88889 11 5.16249 11 5.5ZM11 9.16667C11 9.50418 10.7264 9.77778 10.3889 9.77778L4.78404 9.77778C4.69374 10.0332 4.54711 10.2678 4.35192 10.463C4.0081 10.8068 3.54179 11 3.05556 11C2.56933 11 2.10301 10.8068 1.75919 10.463C1.564 10.2678 1.41737 10.0332 1.32707 9.77778L0.611112 9.77778C0.273605 9.77778 7.76652e-07 9.50417 7.91405e-07 9.16667C8.06158e-07 8.82916 0.273605 8.55556 0.611112 8.55556L1.32707 8.55556C1.41737 8.30017 1.564 8.0655 1.75919 7.8703C2.10301 7.52649 2.56933 7.33333 3.05556 7.33333C3.54179 7.33333 4.0081 7.52649 4.35192 7.8703C4.54711 8.0655 4.69374 8.30017 4.78404 8.55556L10.3889 8.55556C10.7264 8.55556 11 8.82916 11 9.16667ZM8.55556 5.5C8.55556 5.33792 8.49117 5.18249 8.37657 5.06788C8.26196 4.95327 8.10652 4.88889 7.94444 4.88889C7.78237 4.88889 7.62693 4.95327 7.51232 5.06788C7.39772 5.18249 7.33333 5.33792 7.33333 5.5C7.33333 5.66208 7.39772 5.81751 7.51232 5.93212C7.62693 6.04673 7.78237 6.11111 7.94444 6.11111C8.10652 6.11111 8.26196 6.04673 8.37657 5.93212C8.49117 5.81752 8.55556 5.66208 8.55556 5.5ZM3.66667 1.83333C3.66667 1.67126 3.60228 1.51582 3.48768 1.40121C3.37307 1.28661 3.21763 1.22222 3.05556 1.22222C2.89348 1.22222 2.73804 1.28661 2.62344 1.40121C2.50883 1.51582 2.44445 1.67126 2.44445 1.83333C2.44445 1.99541 2.50883 2.15085 2.62344 2.26545C2.73804 2.38006 2.89348 2.44444 3.05556 2.44444C3.21763 2.44444 3.37307 2.38006 3.48768 2.26545C3.60228 2.15085 3.66667 1.99541 3.66667 1.83333ZM3.66667 9.16667C3.66667 9.00459 3.60228 8.84915 3.48768 8.73455C3.37307 8.61994 3.21763 8.55556 3.05556 8.55556C2.89348 8.55556 2.73804 8.61994 2.62344 8.73455C2.50883 8.84915 2.44444 9.00459 2.44444 9.16667C2.44444 9.32874 2.50883 9.48418 2.62344 9.59879C2.73804 9.71339 2.89348 9.77778 3.05556 9.77778C3.21763 9.77778 3.37307 9.71339 3.48768 9.59879C3.60228 9.48418 3.66667 9.32874 3.66667 9.16667Z" />
                                </svg>
                                Filters
                            </CustomButton>
                            <CustomButton @click="createNewRoutine()">Create New</CustomButton>
                        </div>
                    </div>
                    <div v-if="expandFilters" class="flex items-center space-x-3 pb-5 px-5">
                        <p class="text-sm font-semibold">Filters:</p>
                        <custom-input class="flex-grow max-w-sm" :dark-mode="darkMode" v-model="filter" placeholder="Routine Name Or Author"></custom-input>
                        <div class="inline-flex items-center justify-start w-40 gap-2">
                            <toggle-switch @click="selectedAvailableRoutine = null" v-model="shared" :dark-mode="darkMode"></toggle-switch>
                            <svg v-if="shared" class="fill-current text-slate-500" width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 3C12 3.79565 11.6839 4.55871 11.1213 5.12132C10.5587 5.68393 9.79565 6 9 6C8.20435 6 7.44129 5.68393 6.87868 5.12132C6.31607 4.55871 6 3.79565 6 3C6 2.20435 6.31607 1.44129 6.87868 0.87868C7.44129 0.316071 8.20435 0 9 0C9.79565 0 10.5587 0.316071 11.1213 0.87868C11.6839 1.44129 12 2.20435 12 3ZM17 5C17 5.53043 16.7893 6.03914 16.4142 6.41421C16.0391 6.78929 15.5304 7 15 7C14.4696 7 13.9609 6.78929 13.5858 6.41421C13.2107 6.03914 13 5.53043 13 5C13 4.46957 13.2107 3.96086 13.5858 3.58579C13.9609 3.21071 14.4696 3 15 3C15.5304 3 16.0391 3.21071 16.4142 3.58579C16.7893 3.96086 17 4.46957 17 5ZM13 12C13 10.9391 12.5786 9.92172 11.8284 9.17157C11.0783 8.42143 10.0609 8 9 8C7.93913 8 6.92172 8.42143 6.17157 9.17157C5.42143 9.92172 5 10.9391 5 12V15H13V12ZM5 5C5 5.53043 4.78929 6.03914 4.41421 6.41421C4.03914 6.78929 3.53043 7 3 7C2.46957 7 1.96086 6.78929 1.58579 6.41421C1.21071 6.03914 1 5.53043 1 5C1 4.46957 1.21071 3.96086 1.58579 3.58579C1.96086 3.21071 2.46957 3 3 3C3.53043 3 4.03914 3.21071 4.41421 3.58579C4.78929 3.96086 5 4.46957 5 5ZM15 15V12C15.0014 10.9833 14.7433 9.98303 14.25 9.094C14.6933 8.98054 15.1568 8.96984 15.6049 9.06272C16.053 9.1556 16.474 9.34959 16.8357 9.62991C17.1974 9.91023 17.4903 10.2695 17.6921 10.6802C17.8939 11.091 17.9992 11.5424 18 12V15H15ZM3.75 9.094C3.25675 9.98305 2.9986 10.9833 3 12V15H2.6572e-07V12C-0.000192468 11.542 0.104463 11.0901 0.305947 10.6789C0.507431 10.2676 0.800394 9.90793 1.16238 9.62742C1.52437 9.3469 1.94578 9.15298 2.39431 9.06052C2.84284 8.96806 3.30658 8.97951 3.75 9.094Z"/>
                            </svg>
                            <svg v-else width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.375 3.375C3.375 5.23575 4.88925 6.75 6.75 6.75C8.61075 6.75 10.125 5.23575 10.125 3.375C10.125 1.51425 8.61075 0 6.75 0C4.88925 0 3.375 1.51425 3.375 3.375ZM12.75 14.25H13.5V13.5C13.5 10.6058 11.1442 8.25 8.25 8.25H5.25C2.355 8.25 0 10.6058 0 13.5V14.25H12.75Z" fill="#64748B"/>
                            </svg>
                            <p class="text-sm font-semibold text-slate-500">{{shared ? 'Shared' : 'Mine Only'}}</p>
                        </div>
                    </div>
                    <div>
                        <div class="grid grid-cols-3 gap-3 mb-2 px-5">
                            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Name</p>
                            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Created By</p>
                        </div>
                        <div v-if="!(configsLoaded && templatesLoaded)" class="border-t border-b overflow-y-auto divide-y flex items-center justify-center"
                             :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border', expandFilters ? 'h-[26.5rem]' : 'h-100']">
                            <loading-spinner></loading-spinner>
                        </div>
                        <div v-else class="border-t border-b overflow-y-auto divide-y"
                             :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border', expandFilters ? 'h-[26.5rem]' : 'h-100']">
                            <div @click="selectAvailableRoutine(routine)" v-for="routine in availableRoutines" :key="routine" class="cursor-pointer grid grid-cols-3 gap-x-3 py-3 px-5 group relative transition duration-100 items-center" :class="[selectedAvailableRoutine === routine ? (darkMode ? 'bg-slate-800 bg-opacity-50 text-primary-500 font-medium' : 'bg-primary-50 text-primary-500 font-medium') : (darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module')]">
                                <div class="absolute left-0 h-full w-1" :class="[selectedAvailableRoutine === routine ? (darkMode ? 'bg-primary-500' : 'bg-primary-500') : (darkMode ? 'bg-slate-600 invisible group-hover:visible' : 'bg-slate-400 invisible group-hover:visible') ]"></div>
                                <p class="text-sm">
                                    {{ routine.name }}
                                </p>
                                <p class="text-sm">
                                    {{ routine.user?.name ?? currentUser.name }}
                                </p>
                                <div class="absolute right-5">
                                    <svg class="fill-current" :class="[selectedAvailableRoutine === routine ? (darkMode ? 'text-primary-500' : 'text-primary-500') : (darkMode ? 'text-dark-border' : 'text-light-border')]" width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L6.70711 5.29289C7.09763 5.68342 7.09763 6.31658 6.70711 6.70711L1.70711 11.7071C1.31658 12.0976 0.683417 12.0976 0.292893 11.7071C-0.0976311 11.3166 -0.0976311 10.6834 0.292893 10.2929L4.58579 6L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"/></svg>
                                </div>
                            </div>
                        </div>
                        <div class="p-3"></div>
                    </div>
                </div>
                <ConfigurationScreen :dark-mode="darkMode"
                                     :shared="shared"
                                     :configuration="selectedAvailableRoutine"
                                     @delete-routine="deleteRoutine"
                                     @deselect-routine="deselectRoutine"
                                     @update-or-create-selected="updateOrCreateSelected"
                                     @update-or-create-action="updateOrCreateAction"
                                     @clone-routine="cloneRoutine"
                                     :email-templates="emailTemplates"
                                     :sms-templates="smsTemplates"
                                     :task-templates="taskTemplates"
                                     :successful-update="successfulUpdate"
                                     :current-user-id="currentUser.id"
                                     @action-group-order-updated="updateGroupActionOrder"
                >
                </ConfigurationScreen>
            </div>
        </div>
    </div>
</template>

<script>
import CustomButton from "../../../Shared/components/CustomButton.vue";
import ConfigurationScreen from "./components/ConfigurationScreen.vue";
import ApiService from "../../services/api";
import {isDefined} from "@vueuse/core";
import LoadingSpinner from "../../../LeadProcessing/components/LoadingSpinner.vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";

export default {
    name: "RoutineConfiguration",
    components: {CustomInput, ToggleSwitch, AlertsContainer, LoadingSpinner, ConfigurationScreen, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        currentUser: {
            type: Object,
            default: {},
        },
    },
    data () {
        return {
            apiService: ApiService.make(),
            allAvailableRoutines: [],
            selectedAvailableRoutine: null,
            successfulUpdate: false,
            successfulDelete: false,
            configsLoaded: false,
            templatesLoaded: false,
            emailTemplates: {
                type: Array,
                default: []
            },
            smsTemplates: {
                type: Array,
                default: []
            },
            taskTemplates: {
                type: Array,
                default: []
            },
            shared: false,
            filter: '',
            expandFilters: false,
        }
    },
    computed: {
        availableRoutines() {
            let routines = [];
            if(this.shared)
                routines =  this.allAvailableRoutines.filter(routine => 'user' in routine && routine.user.id !== this.currentUser.id)
            else
                routines =  this.allAvailableRoutines.filter(routine => !('user' in routine) || routine.user.id === this.currentUser.id)
            if(this.filter.length > 0)
                routines = routines.filter(routine => {
                    return routine.name.toLowerCase().indexOf(this.filter.toLowerCase()) >= 0 ||
                        routine.user.name.toLowerCase().indexOf(this.filter.toLowerCase()) >= 0
                })
            return routines
        },
    },
    mounted() {
        this.getTemplates();
        this.getConfigs();

    },
    methods: {
        selectAvailableRoutine(routine) {
            if(this.selectedAvailableRoutine === routine) {
                this.selectedAvailableRoutine = null;
            }
            else {
                this.selectedAvailableRoutine = routine;
            }
        },
        deleteRoutine(routine) {
            if(isDefined(routine.id)){
                this.apiService.deleteRoutineConfig(routine).then(data => {
                    this.allAvailableRoutines = data.data.data;
                }).finally(() => {
                    this.successfulDelete = true
                    setTimeout(() => this.successfulDelete = false, 3000);
                });
            }else if(this.allAvailableRoutines.includes(routine)){
                this.allAvailableRoutines = this.allAvailableRoutines.filter(function(temp){
                    return routine !== temp;
                });
            }
            this.selectedAvailableRoutine = null;
        },
        deselectRoutine() {
            this.selectedAvailableRoutine = null;
        },
        createNewRoutine() {
            this.allAvailableRoutines.unshift({
                name: 'New Routine',
                domain: 'fixr.com',
                account_manager: null,
                contact_decision_makers_only: true,
                contact_on_weekdays_only: true,
                scheduled_groups: [],
                global: true
            })
            this.selectedAvailableRoutine = this.allAvailableRoutines[0]
            this.shared = false
        },
        getConfigs() {
            this.configsLoaded = false
            this.apiService.getAllAvailableRoutineConfigs().then(data => {
                this.allAvailableRoutines = data.data.data;
                this.configsLoaded = true
            });
        },
        getTemplates() {
            this.templatesLoaded = false
            this.apiService.getAllTemplates().then(data => {
                this.emailTemplates = data.data.data.email;
                this.smsTemplates = data.data.data.sms;
                this.taskTemplates = data.data.data.task;
                this.templatesLoaded = true
            });
        },
        updateOrCreateSelected() {
            const id = this.selectedAvailableRoutine.id;
            if(isDefined(id)){
                this.apiService.updateRoutineConfig(this.selectedAvailableRoutine).then(data => {
                    this.allAvailableRoutines = data.data.data;
                    this.selectedAvailableRoutine = this.getRoutineByIdOrNewest(id);
                }).finally(() => {
                    this.successfulUpdate = true
                    setTimeout(() => this.successfulUpdate = false, 3000);
                });
            }else {
                this.apiService.createRoutineConfig(this.selectedAvailableRoutine).then(data => {
                    this.allAvailableRoutines = data.data.data;
                    this.selectedAvailableRoutine = this.getRoutineByIdOrNewest(id);
                }).finally(() => {
                    this.successfulUpdate = true
                    setTimeout(() => this.successfulUpdate = false, 3000);
                });
            }
        },
        getRoutineByIdOrNewest(routineId) {
            if(isDefined(routineId)){
                return this.allAvailableRoutines.filter((routine) => {
                    return routine.id === routineId;
                })[0] ?? null;
            }else{
                return this.allAvailableRoutines.sort(function(a,b) {
                    const keyA = new Date(a.created_at), keyB = new Date(b.created_at);
                    return keyB - keyA;
                })[0] ?? null;
            }
        },
        updateOrCreateAction(action) {
            this.selectedAvailableRoutine.scheduled_groups = [action];
            this.updateOrCreateSelected();
        },
        cloneRoutine(routineId) {
            this.apiService.cloneRoutine(routineId).then(resp => {
                this.allAvailableRoutines = resp.data.data;
                this.shared = false;
                this.selectedAvailableRoutine = this.getRoutineByIdOrNewest();
            })
        },
        updateGroupActionOrder(params) {

            const routineId = params['routineId']
            const groupId = params['groupId']
            const ordinalValue = params['ordinalValue']
            const routine = this.allAvailableRoutines.find(routine => routine.id === routineId)

            // assure we have ordinal values for current order
            routine.scheduled_groups.forEach((actionGroup, i) => {actionGroup.ordinal_value = i+1})

            // update ordinal values
            routine.scheduled_groups.forEach(actionGroup => {
                if(actionGroup.id == groupId)
                    actionGroup.ordinal_value = ordinalValue
                else if(actionGroup.ordinal_value >= ordinalValue)
                    actionGroup.ordinal_value++
            })

            // re-order list
            routine.scheduled_groups = routine.scheduled_groups.sort((a,b) => (a.ordinal_value > b.ordinal_value) ? 1 : ((b.ordinal_value > a.ordinal_value) ? -1 : 0))

            // save new order
            this.apiService.saveConfigActionOrder(params.routineId, routine.scheduled_groups.map(group => group['id']))
        },
    }
}
</script>

<style scoped>

</style>
