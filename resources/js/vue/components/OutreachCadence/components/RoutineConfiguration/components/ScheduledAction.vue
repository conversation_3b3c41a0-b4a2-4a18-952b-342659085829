<template>
    <div>
        <div class="inline-flex space-x-1">
            <p class="font-semibold text-sm">Scheduled Actions</p>
            <p class="font-semibold text-sm text-slate-500">({{scheduledActions.length}})</p>
        </div>
        <div v-if="loading" class="border-t border-b overflow-y-auto divide-y flex items-center justify-center"
             :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
            <loading-spinner></loading-spinner>
        </div>
        <div v-else class="h-[17rem] overflow-y-auto">
            <div class="grid columns-1">



                <div class="h-2 w-full" @drop="onDrop($event, 1)" @dragenter.prevent="onDragEnter($event)" @dragleave="onDragLeave($event)" @dragover.prevent></div>

                <div v-for="(actionGroup, index) in scheduledActions" :key="actionGroup" >

                    <div
                        :draggable="!shared"
                        @dragstart="startDrag($event, actionGroup)"
                        :class="[darkMode ? 'bg-dark-background hover:bg-dark-border' : 'bg-light-background hover:bg-primary-100']"
                        class="rounded-md py-2 px-4 flex justify-between"
                    >
                        <p>
                            <span class="font-medium text-sm capitalize">{{actionGroup.actions[0].action_type}}</span>
                            <span class="font-semibold text-sm text-slate-500 capitalize"> - {{getTemplateName(actionGroup.actions[0])}}</span>
                            <span class="font-semibold text-sm text-slate-500"> - Delay: {{getDelayString(actionGroup)}}</span>
                            <span class="font-semibold text-sm text-slate-500"> - Time: {{getTimeString(actionGroup)}}</span>
                        </p>
                        <ActionsHandle
                            v-if="!shared"
                            @edit="toggleEditAction(actionGroup)"
                            @delete="deleteActionGroup(actionGroup)"
                            :dark-mode="darkMode"
                        ></ActionsHandle>
                    </div>

                    <div class="h-2 w-full" @drop="onDrop($event, index + 2)" @dragenter.prevent="onDragEnter($event)" @dragleave="onDragLeave($event)" @dragover.prevent></div>

                </div>





                <p v-if="!shared" @click="this.createNewAction()" class="text-primary-500 font-semibold text-sm cursor-pointer pb-6">+ Create New Action</p>
            </div>
        </div>
        <Modal
            v-if="editAction"
            :dark-mode="darkMode"
            :small="true"
            :restrict-width="true"
            :no-buttons="true"
            :no-close-button="true"
        >
            <template v-slot:header>
                <p class="font-semibold">Edit Action</p>
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <p class="mb-1 font-semibold">Action Type</p>
                        <Dropdown :options="actionTypes" v-model="editAction.actions[0].action_type" :dark-mode="darkMode"></Dropdown>
                    </div>
                    <div>
                        <p class="mb-1 font-semibold">Execution Delay</p>
                        <div class="inline-flex items-center space-x-2">
                            <CustomInput :dark-mode="darkMode" v-model="editActionRelativeDelay"></CustomInput>
                            <Dropdown :options="delayUnits" v-model="editAction.delay_unit" :dark-mode="darkMode"></Dropdown>
                        </div>
                    </div>
                    <div>
                        <div class="inline-flex items-center space-x-2">
                            <p class="mb-1 font-semibold">Execution Type</p>
                            <div class="group relative">
                                <svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M5.5 0C2.4673 0 0 2.4673 0 5.5C0 8.5327 2.4673 11 5.5 11C8.5327 11 11 8.5327 11 5.5C11 2.4673 8.5327 0 5.5 0ZM5.5 9.9C3.07395 9.9 1.1 7.92605 1.1 5.5C1.1 3.07395 3.07395 1.1 5.5 1.1C7.92605 1.1 9.9 3.07395 9.9 5.5C9.9 7.92605 7.92605 9.9 5.5 9.9Z" fill="#64748B"/>
                                    <path d="M5 5H6V8H5V5ZM5 3H6V4H5V3Z" fill="#64748B"/>
                                </svg>
                                <div class="invisible group-hover:visible absolute top-4 left-0 p-4 border rounded-lg shadow-module z-40 w-80"
                                     :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                                    <ul>
                                        <li><b class="underline">None</b> - Action will execute as soon as delay is fulfilled, regardless of the time of day</li>
                                        <li><b class="underline"> Exact</b> - Action will execute at the specified time, only after the delay has been fulfilled</li>
                                        <li><b class="underline">Window</b> - Action will execute within the specified window, only after the delay has been fulfilled</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <Dropdown  v-on:change="this.clearAllTimes()" :options="executionTypes" v-model="editAction.execution_type" :dark-mode="darkMode"></Dropdown>
                    </div>
                    <div>
                        <div v-if="editAction.execution_type === 'exact'">
                            <p class="mb-1 font-semibold">Time</p>
                            <CustomInput :dark-mode="darkMode" v-model="editAction.execution_time_exact" type="time"></CustomInput>
                        </div>
                    </div>
                    <div v-if="editAction.execution_type === 'window'" class="col-span-2 grid grid-cols-2 gap-4">
                        <div>
                            <p class="mb-1 font-semibold">Start</p>
                            <CustomInput :dark-mode="darkMode" v-model="editAction.execution_time_window_start" type="time"></CustomInput>
                        </div>
                        <div>
                            <p class="mb-1 font-semibold">End</p>
                            <CustomInput :dark-mode="darkMode" v-model="editAction.execution_time_window_end" type="time"></CustomInput>
                        </div>
                    </div>
                    <div class="col-span-2">

                        <p class="mb-1 font-semibold">Filter Templates</p>

                        <div class="flex gap-3 mb-3">
                            <custom-input class="flex-grow" :dark-mode="darkMode" v-model="filter" placeholder="Template Name Or Author"></custom-input>
                            <div class="inline-flex items-center justify-start w-40 gap-2">
                                <toggle-switch v-model="sharedTemplates" :dark-mode="darkMode"></toggle-switch>
                                <svg v-if="sharedTemplates" class="fill-current text-slate-500" width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 3C12 3.79565 11.6839 4.55871 11.1213 5.12132C10.5587 5.68393 9.79565 6 9 6C8.20435 6 7.44129 5.68393 6.87868 5.12132C6.31607 4.55871 6 3.79565 6 3C6 2.20435 6.31607 1.44129 6.87868 0.87868C7.44129 0.316071 8.20435 0 9 0C9.79565 0 10.5587 0.316071 11.1213 0.87868C11.6839 1.44129 12 2.20435 12 3ZM17 5C17 5.53043 16.7893 6.03914 16.4142 6.41421C16.0391 6.78929 15.5304 7 15 7C14.4696 7 13.9609 6.78929 13.5858 6.41421C13.2107 6.03914 13 5.53043 13 5C13 4.46957 13.2107 3.96086 13.5858 3.58579C13.9609 3.21071 14.4696 3 15 3C15.5304 3 16.0391 3.21071 16.4142 3.58579C16.7893 3.96086 17 4.46957 17 5ZM13 12C13 10.9391 12.5786 9.92172 11.8284 9.17157C11.0783 8.42143 10.0609 8 9 8C7.93913 8 6.92172 8.42143 6.17157 9.17157C5.42143 9.92172 5 10.9391 5 12V15H13V12ZM5 5C5 5.53043 4.78929 6.03914 4.41421 6.41421C4.03914 6.78929 3.53043 7 3 7C2.46957 7 1.96086 6.78929 1.58579 6.41421C1.21071 6.03914 1 5.53043 1 5C1 4.46957 1.21071 3.96086 1.58579 3.58579C1.96086 3.21071 2.46957 3 3 3C3.53043 3 4.03914 3.21071 4.41421 3.58579C4.78929 3.96086 5 4.46957 5 5ZM15 15V12C15.0014 10.9833 14.7433 9.98303 14.25 9.094C14.6933 8.98054 15.1568 8.96984 15.6049 9.06272C16.053 9.1556 16.474 9.34959 16.8357 9.62991C17.1974 9.91023 17.4903 10.2695 17.6921 10.6802C17.8939 11.091 17.9992 11.5424 18 12V15H15ZM3.75 9.094C3.25675 9.98305 2.9986 10.9833 3 12V15H2.6572e-07V12C-0.000192468 11.542 0.104463 11.0901 0.305947 10.6789C0.507431 10.2676 0.800394 9.90793 1.16238 9.62742C1.52437 9.3469 1.94578 9.15298 2.39431 9.06052C2.84284 8.96806 3.30658 8.97951 3.75 9.094Z"/>
                                </svg>
                                <svg v-else width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.375 3.375C3.375 5.23575 4.88925 6.75 6.75 6.75C8.61075 6.75 10.125 5.23575 10.125 3.375C10.125 1.51425 8.61075 0 6.75 0C4.88925 0 3.375 1.51425 3.375 3.375ZM12.75 14.25H13.5V13.5C13.5 10.6058 11.1442 8.25 8.25 8.25H5.25C2.355 8.25 0 10.6058 0 13.5V14.25H12.75Z" fill="#64748B"/>
                                </svg>

                                <p class="text-sm font-semibold text-slate-500">{{sharedTemplates ? 'All' : 'Mine Only'}}</p>
                            </div>
                        </div>

                        <p class="mb-1 font-semibold">Template</p>
                        <div>
                            <Dropdown v-if="editAction.actions[0].action_type === 'email'" placeholder="Choose Template" :options="filteredTemplates" v-model="editAction.actions[0].email_template_id" :dark-mode="darkMode"></Dropdown>
                            <Dropdown v-if="editAction.actions[0].action_type === 'sms'" placeholder="Choose Template" :options="filteredTemplates" v-model="editAction.actions[0].sms_template_id" :dark-mode="darkMode"></Dropdown>
                            <Dropdown v-if="editAction.actions[0].action_type === 'task'" placeholder="Choose Template" :options="filteredTemplates" v-model="editAction.actions[0].task_template_id" :dark-mode="darkMode"></Dropdown>
                        </div>
                        <span class="text-xs">{{filteredTemplates.length}} results</span>
                    </div>
                </div>
                <div v-if="editAction" class="flex items-center justify-end space-x-2 mt-5">
                    <CustomButton color="slate-inverse" @click="editAction = null">Cancel</CustomButton>
                    <CustomButton @click="saveCurrentAction()">Save Changes</CustomButton>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import ActionsHandle from "../../../../Shared/components/ActionsHandle.vue";
import Modal from "../../../../Shared/components/Modal.vue";
import Dropdown from "../../../../Shared/components/Dropdown.vue";
import CustomInput from "../../../../Shared/components/CustomInput.vue";
import CustomButton from "../../../../Shared/components/CustomButton.vue";
import {isDefined} from "@vueuse/core";
import LoadingSpinner from "../../../../LeadProcessing/components/LoadingSpinner.vue";
import ToggleSwitch from "../../../../Shared/components/ToggleSwitch.vue";
import {number} from "tailwindcss/lib/util/dataTypes";

export default {
    name: "ScheduledAction",
    components: {ToggleSwitch, LoadingSpinner, CustomButton, CustomInput, Dropdown, Modal, ActionsHandle},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        scheduledActions: {
            type: Object,
            default: null,
        },
        emailTemplates: {
            type: Object,
            default: {}
        },
        smsTemplates: {
            type: Object,
            default: {}
        },
        taskTemplates: {
            type: Object,
            default: {}
        },
        shared: {
            type: Boolean,
            default: {},
        },
        currentUserId: {
            type: number,
            default: null
        },
    },
    data() {
        return {
            editAction: null,
            actionTypes: [
                {id: 'email', name: 'Email'},
                {id: 'sms', name: 'SMS'},
                {id: 'task', name: 'Task'},
            ],
            executionTypes: [
                {id: 'none', name: 'None'},
                {id: 'exact', name: 'Exact'},
                {id: 'window', name: 'Window'},
            ],
            delayUnits: [
                {id: 'minutes', name: 'Minutes'},
                {id: 'hours', name: 'Hours'},
                {id: 'days', name: 'Days'},
                {id: 'weeks', name: 'Weeks'},
                {id: 'months', name: 'Months'},
            ],
            updateCount: 0,
            loading: false,
            filter: '',
            sharedTemplates: true,
        }
    },
    mounted() {
        this.scheduledActions.forEach(actionGroup => this.determineDelayUnits(actionGroup))
    },
    watch: {
        scheduledActions: function(){
            this.scheduledActions.forEach(actionGroup => this.determineDelayUnits(actionGroup))
            this.updateCount++
            if(this.updateCount === 2){
                this.updateCount = 0
                this.loading = false
            }

        }
    },
    computed: {
        editActionRelativeDelay: {
            get: function() {
                return this.editAction.execution_delay_minutes / this.getUnitFactor(this.editAction.delay_unit);
            },
            set: function(relativeDelay) {
                this.editAction.execution_delay_minutes = relativeDelay * this.getUnitFactor(this.editAction.delay_unit)
            },
        },
        filteredTemplates() {
            const action = this.editAction.actions[0]
            let availableTemplates = this.availableTemplates(action.action_type)
            let templates = [...availableTemplates]
            if(!this.sharedTemplates)
                templates = availableTemplates.filter(template => template.user_id == this.currentUserId)
            if(this.filter.length > 0)
                templates = availableTemplates.filter(template => {
                    return template.name.toLowerCase().indexOf(this.filter.toLowerCase()) >= 0 ||
                        template.user.name.toLowerCase().indexOf(this.filter.toLowerCase()) >= 0
                })

            let selectedTemplateId = null;
            switch (action.action_type){
                case 'email':
                    selectedTemplateId = action.email_template_id
                    break
                case 'sms':
                    selectedTemplateId = action.sms_template_id
                    break
                case 'task':
                    selectedTemplateId = action.task_template_id
                    break
            }

            if(selectedTemplateId && !templates.find(template => template.id === selectedTemplateId))
                templates.unshift(availableTemplates.find(template => template.id === selectedTemplateId))
            return templates
        }
    },
    methods: {
        availableTemplates(actionType) {
            let templates = [];
            switch(actionType){
                case 'email':
                    templates = this.emailTemplates
                    break
                case 'sms':
                    templates = this.smsTemplates
                    break
                case 'task':
                    templates = this.taskTemplates
                    break
            }
            return templates.filter(template => !template.deleted_at)
        },
        toggleEditAction(action) {
            action['execution_type'] = this.determineExecutionType(action);
            this.editAction = action;
        },
        createNewAction() {
            const validEmailTemplates = this.availableTemplates('email');
            const validSmsTemplates = this.availableTemplates('sms');
            const validTaskTemplates = this.availableTemplates('task');
            this.editAction = {
                'actions': [{
                    'action_type': 'email',
                    'email_template_id': validEmailTemplates.length > 0 ? validEmailTemplates[0]['id'] : null,
                    'sms_template_id': validSmsTemplates.length > 0 ? validSmsTemplates[0]['id'] : null,
                    'task_template_id': validTaskTemplates.length > 0 ? validTaskTemplates[0]['id'] : null,
                }],
                'execution_type': 'none',
                'delay_unit': 'days',
                'execution_delay_minutes': 1440,
                'execution_time_exact': null,
                'execution_time_window_end': null,
                'execution_time_window_start': null,
            };
        },
        clearAllTimes() {
            this.editAction.execution_time_exact = null;
            this.editAction.execution_time_window_start = null;
            this.editAction.execution_time_window_end = null;
        },
        determineExecutionType(action) {
            if (isDefined(action.execution_time_exact))
                return 'exact';
            else if (isDefined(action.execution_time_window_start) || isDefined(action.execution_time_window_end))
                return 'window';
            else
                return 'none';
        },
        determineDelayUnits(action) {
            const delay = action.execution_delay_minutes;
            let units = 'minutes';
            switch(true){
                case (delay >= 60 * 24 * 7 * 4):
                    units = 'months'
                    break;
                case (delay >= 60 * 24 * 7):
                    units = 'weeks'
                    break;
                case (delay >= 60 * 24):
                    units = 'days'
                    break;
                case (delay >= 60):
                    units = 'hours'
                    break;
            }
            action.delay_unit = units;
        },
        getUnitFactor(unit) {
            let factor = 1;
            switch (unit){
                case 'months':
                    factor = 60 * 24 * 7 * 4;
                    break
                case 'weeks':
                    factor = 60 * 24 * 7;
                    break
                case 'days':
                    factor = 60 * 24;
                    break
                case 'hours':
                    factor = 60;
                    break
            }
            return factor;
        },
        saveCurrentAction() {
            this.loading = true
            this.$emit('update-or-create-action', this.editAction)
            this.editAction = null;
        },
        deleteActionGroup(actionGroup) {
            this.loading = true
            actionGroup.delete = true;
            this.$emit('update-or-create-action', actionGroup);
        },
        getTemplateName(action) {
            let template = null
            switch (action.action_type) {
                case 'email':
                    template =  this.emailTemplates.filter(template => template.id === action.email_template_id)[0]
                    break
                case 'sms':
                    template =  this.smsTemplates.filter(template => template.id === action.sms_template_id)[0]
                    break
                case 'task':
                    template =  this.taskTemplates.filter(template => template.id === action.task_template_id)[0]
                    break
            }
            let name = ''
            if(template)
                name = template.deleted_at ? '[DELETED] ' + template.name : template.name

            return name
        },
        getDelayString(actionGroup) {
            if(actionGroup.execution_delay_minutes === 0)
                return 'None'
            const delay = +(actionGroup.execution_delay_minutes / this.getUnitFactor(actionGroup.delay_unit)).toFixed(3)
            switch (actionGroup.delay_unit) {
                case 'minutes':
                    return delay + ' Minute(s)'
                case 'hours':
                    return delay + ' Hour(s)'
                case 'days':
                    return delay + ' Day(s)'
                case 'weeks':
                    return delay + ' Week(s)'
                case 'months':
                    return delay + ' Month(s)'
            }
        },
        getTimeString(actionGroup) {
            if(actionGroup.execution_time_exact){
                return actionGroup.execution_time_exact
            }else if(actionGroup.execution_time_window_start && actionGroup.execution_time_window_end){
                return actionGroup.execution_time_window_start + '-' + actionGroup.execution_time_window_end
            }else{
                return 'Any'
            }
        },
        startDrag(e, actionGroup) {
            e.dataTransfer.dropEffect = 'move'
            e.dataTransfer.effectAllowed = 'move'
            e.dataTransfer.setData('groupId', actionGroup.id)
        },
        onDragEnter(e) {
            const el = e.toElement
            el.classList.remove('h-2')
            el.classList.add('h-16')
        },
        onDragLeave(e) {
            const el = e.toElement
            el.classList.remove('h-16')
            el.classList.add('h-2')
        },
        onDrop(e, ordinalValue) {
            const el = e.toElement
            el.classList.remove('h-16')
            el.classList.add('h-2')
            const groupId = e.dataTransfer.getData('groupId')
            this.updateGroupActionOrder(groupId, ordinalValue);
        },
        updateGroupActionOrder(groupId, ordinalValue) {
            this.$emit('action-group-order-updated', {groupId: groupId, ordinalValue: ordinalValue})
        },
    }
}
</script>

<style scoped>

</style>
