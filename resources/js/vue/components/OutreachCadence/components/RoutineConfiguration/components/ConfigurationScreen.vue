<template>
    <div class="relative h-full">
        <div class="flex justify-between items-center p-5">
            <div class="inline-flex flex-grow items-center space-x-3">
                <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Configure</h5>
                <svg v-if="configuration" width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.292787 9.69471C0.105316 9.50718 0 9.25288 0 8.98771C0 8.72255 0.105316 8.46824 0.292787 8.28071L3.58579 4.98771L0.292787 1.69471C0.110629 1.50611 0.00983372 1.25351 0.0121121 0.991311C0.0143906 0.729114 0.11956 0.478302 0.304968 0.292894C0.490376 0.107485 0.741189 0.00231622 1.00339 3.78025e-05C1.26558 -0.00224062 1.51818 0.0985542 1.70679 0.280712L5.70679 4.28071C5.89426 4.46824 5.99957 4.72255 5.99957 4.98771C5.99957 5.25288 5.89426 5.50718 5.70679 5.69471L1.70679 9.69471C1.51926 9.88218 1.26495 9.9875 0.999786 9.9875C0.734622 9.9875 0.480314 9.88218 0.292787 9.69471Z" fill="#64748B"/></svg>
                <div class="flex flex-grow mr-4" v-if="configuration">
                    <p v-if="!renamingRoutine" class="font-semibold">{{configuration.name}}</p>
                    <div v-if="renamingRoutine" class="relative flex items-center w-full">
                        <CustomInput class="w-full" :dark-mode="darkMode" v-model="configuration.name"></CustomInput>
                        <div class="absolute right-3 inline-flex items-center">
                            <div @click="updateOrCreateSelected" class="w-6 h-6 rounded bg-emerald-500 hover:bg-green-600 cursor-pointer inline-flex items-center justify-center">
                                <svg width="12" height="9" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.91869 6.71136L1.17719 4.0464L0 5.19072L3.91869 9L12 1.14432L10.8228 0L3.91869 6.71136Z" fill="white"/></svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <ActionsHandle :dark-mode="darkMode"
                           v-if="configuration"
                           :custom-actions="customActions"
                           :no-custom-action="false"
                           :no-edit-button="true"
                           :no-delete-button="true"
                           @rename-routine="renameRoutine"
                           @delete-routine="deleteRoutine(configuration)"
                           @clone="cloneRoutine"
            />
        </div>
        <div v-if="configuration">
            <div class="px-5 grid gap-5 mb-5">

                <div>
                    <p class="mb-1 font-semibold">From Domain</p>
                    <Dropdown :disabled="shared" :dark-mode="darkMode" :options="domainOptions" v-model="configuration.domain"></Dropdown>
                </div>

                <div class="inline-flex items-center space-x-2">
                    <toggle-switch :disabled="shared" v-model="configuration.contact_decision_makers_only" :dark-mode="darkMode"></toggle-switch>
                    <p class="font-semibold text-sm">Contact Decision Makers Only</p>
                </div>
                <div class="inline-flex items-center space-x-2">
                    <toggle-switch :disabled="shared" v-model="configuration.contact_on_weekdays_only" :dark-mode="darkMode"></toggle-switch>
                    <p class="font-semibold text-sm">Contact On Weekdays Only</p>
                </div>
            </div>
            <div class="px-5 grid gap-5">
                <ScheduledAction
                    :shared="shared"
                    :dark-mode="darkMode"
                    :scheduled-actions="configuration.scheduled_groups"
                    :email-templates="emailTemplates"
                    :sms-templates="smsTemplates"
                    :task-templates="taskTemplates"
                    :current-user-id="currentUserId"
                    @update-or-create-action="updateOrCreateAction"
                    @action-group-order-updated="updateGroupActionOrder"
                ></ScheduledAction>
            </div>

            <div v-if="!shared" class="w-full border-t" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                <div class="px-5 py-3 flex items-center space-x-3 float-left">
                    <CustomButton :dark-mode="darkMode" @click="deselectRoutine" color="slate-light">Cancel</CustomButton>
                    <CustomButton :dark-mode="darkMode" @click="this.updateOrCreateSelected()">Save</CustomButton>
                </div>
                <div class="inline-flex items-center space-x-2 float-right pr-4 my-4">
                    <toggle-switch v-model="configuration.global" :dark-mode="darkMode"></toggle-switch>
                    <p class="font-semibold text-sm">Globally Available</p>
                </div>
            </div>

        </div>
        <div v-else class="absolute inset-0 flex items-center justify-center">
            <div class="text-center">
                <div class="flex justify-center">
                    <svg width="23" height="19" viewBox="0 0 23 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.8571 -4.99559e-08C15.1602 -3.67067e-08 15.4509 0.0790176 15.6653 0.21967C15.8796 0.360322 16 0.551088 16 0.75L16 2.25C16 2.44891 15.8796 2.63968 15.6653 2.78033C15.4509 2.92098 15.1602 3 14.8571 3L1.14286 3C0.839753 3 0.549062 2.92098 0.334735 2.78033C0.120408 2.63968 8.46629e-07 2.44891 8.55324e-07 2.25L9.20891e-07 0.749999C9.29586e-07 0.551087 0.120408 0.360321 0.334735 0.219669C0.549062 0.0790169 0.839753 -6.62675e-07 1.14286 -6.49426e-07L14.8571 -4.99559e-08ZM14.8571 4.5C15.1602 4.5 15.4509 4.57902 15.6653 4.71967C15.8796 4.86032 16 5.05109 16 5.25L16 6.75C16 6.94891 15.8796 7.13968 15.6653 7.28033C15.4509 7.42098 15.1602 7.5 14.8571 7.5L1.14286 7.5C0.839753 7.5 0.549062 7.42098 0.334735 7.28033C0.120408 7.13968 6.49928e-07 6.94891 6.58622e-07 6.75L7.2419e-07 5.25C7.32884e-07 5.05109 0.120408 4.86032 0.334735 4.71967C0.549062 4.57902 0.839753 4.5 1.14286 4.5L14.8571 4.5ZM16 9.75C16 9.55109 15.8796 9.36032 15.6653 9.21967C15.4509 9.07902 15.1602 9 14.8571 9L1.14286 9C0.839753 9 0.549062 9.07902 0.334735 9.21967C0.120407 9.36032 5.36183e-07 9.55109 5.27488e-07 9.75L4.61921e-07 11.25C4.53226e-07 11.4489 0.120407 11.6397 0.334735 11.7803C0.549062 11.921 0.839753 12 1.14286 12L14.8571 12C15.1602 12 15.4509 11.921 15.6653 11.7803C15.8796 11.6397 16 11.4489 16 11.25L16 9.75Z" fill="#CBD5E1"/><path fill-rule="evenodd" clip-rule="evenodd" d="M11.672 2.91098C11.6033 2.65478 11.4357 2.43636 11.2059 2.30378C10.9762 2.17119 10.7032 2.13529 10.447 2.20398C10.1908 2.27267 9.9724 2.44033 9.83981 2.67006C9.70723 2.89979 9.67133 3.17278 9.74002 3.42898L9.99902 4.39498C10.0677 4.65118 10.2354 4.8696 10.4651 5.00219C10.6948 5.13478 10.9678 5.17067 11.224 5.10198C11.4802 5.03329 11.6986 4.86564 11.8312 4.63591C11.9638 4.40617 11.9997 4.13318 11.931 3.87698L11.672 2.91098ZM7.42902 5.73998C7.30194 5.70534 7.16927 5.69614 7.03863 5.71292C6.90799 5.7297 6.78195 5.77212 6.66775 5.83775C6.55355 5.90338 6.45344 5.99092 6.37317 6.09535C6.2929 6.19978 6.23405 6.31904 6.20001 6.44627C6.16596 6.57351 6.15738 6.70622 6.17477 6.83678C6.19215 6.96734 6.23516 7.09319 6.30132 7.20708C6.36748 7.32097 6.45549 7.42067 6.56029 7.50045C6.6651 7.58023 6.78463 7.63853 6.91202 7.67198L7.87802 7.93098C8.13376 7.99814 8.40568 7.96128 8.63432 7.82847C8.86295 7.69566 9.02968 7.4777 9.09803 7.22228C9.16638 6.96686 9.13079 6.69476 8.99904 6.46551C8.8673 6.23626 8.65012 6.06852 8.39502 5.99898L7.42902 5.73998ZM16.243 5.17098C16.3359 5.07807 16.4095 4.96778 16.4597 4.84642C16.5099 4.72505 16.5358 4.59498 16.5357 4.46363C16.5357 4.33228 16.5098 4.20223 16.4595 4.08089C16.4091 3.95956 16.3354 3.84933 16.2425 3.75648C16.1496 3.66364 16.0393 3.59 15.918 3.53978C15.7966 3.48956 15.6665 3.46373 15.5352 3.46378C15.4038 3.46383 15.2738 3.48974 15.1524 3.54005C15.0311 3.59036 14.9209 3.66407 14.828 3.75698L14.121 4.46398C14.0281 4.55689 13.9544 4.66719 13.9041 4.78859C13.8538 4.90998 13.828 5.04009 13.828 5.17148C13.828 5.30288 13.8538 5.43298 13.9041 5.55438C13.9544 5.67577 14.0281 5.78607 14.121 5.87898C14.2139 5.97189 14.3242 6.04559 14.4456 6.09588C14.567 6.14616 14.6971 6.17204 14.8285 6.17204C14.9599 6.17204 15.09 6.14616 15.2114 6.09588C15.3328 6.04559 15.4431 5.97189 15.536 5.87898L16.243 5.17098ZM9.17202 12.243L9.87902 11.536C10.0668 11.3485 10.1724 11.0941 10.1726 10.8287C10.1728 10.5633 10.0675 10.3088 9.88002 10.121C9.69251 9.93321 9.43809 9.82761 9.17273 9.82743C8.90736 9.82724 8.65279 9.93247 8.46502 10.12L7.75702 10.827C7.56938 11.0146 7.46396 11.2691 7.46396 11.5345C7.46396 11.7998 7.56938 12.0543 7.75702 12.242C7.94466 12.4296 8.19916 12.535 8.46452 12.535C8.72988 12.535 8.98438 12.4296 9.17202 12.242V12.243ZM12.372 7.07198C12.1903 6.99924 11.9912 6.98143 11.7995 7.02077C11.6077 7.0601 11.4317 7.15485 11.2933 7.29326C11.1549 7.43168 11.0601 7.60767 11.0208 7.79942C10.9815 7.99117 10.9993 8.19025 11.072 8.37198L15.072 18.372C15.1437 18.551 15.2656 18.7054 15.423 18.8167C15.5804 18.928 15.7666 18.9915 15.9593 18.9994C16.1519 19.0073 16.3427 18.9594 16.5087 18.8614C16.6747 18.7634 16.8089 18.6195 16.895 18.447L18.275 15.688L21.293 18.708C21.4807 18.8955 21.7351 19.0008 22.0004 19.0007C22.2656 19.0006 22.52 18.8951 22.7075 18.7075C22.895 18.5198 23.0003 18.2654 23.0002 18.0001C23.0001 17.7349 22.8947 17.4805 22.707 17.293L19.688 14.273L22.448 12.894C22.6202 12.8076 22.7637 12.6734 22.8615 12.5075C22.9592 12.3415 23.0069 12.1508 22.9989 11.9584C22.9908 11.766 22.9274 11.58 22.8162 11.4227C22.705 11.2655 22.5508 11.1437 22.372 11.072L12.372 7.07198Z" fill="#64748B"/></svg>
                </div>
                <p class="text-slate-500">Select a Routine to edit it’s configuration, <br/>or create a new Routine.</p>
            </div>
        </div>
    </div>
</template>

<script>
import ActionsHandle from "../../../../Shared/components/ActionsHandle.vue";
import ToggleSwitch from "../../../../Shared/components/ToggleSwitch.vue";
import CustomInput from "../../../../Shared/components/CustomInput.vue";
import ScheduledAction from "./ScheduledAction.vue";
import CustomButton from "../../../../Shared/components/CustomButton.vue";
import Dropdown from "../../../../Shared/components/Dropdown.vue";
import {number} from "tailwindcss/lib/util/dataTypes";

export default {
    name: "ConfigurationScreen",
    components: {Dropdown, CustomButton, ScheduledAction, CustomInput, ToggleSwitch, ActionsHandle},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        configuration: {
            type: Object,
            default: null,
        },
        emailTemplates: {
            type: Object,
            default: {}
        },
        smsTemplates: {
            type: Object,
            default: {}
        },
        taskTemplates: {
            type: Object,
            default: {}
        },
        shared: {
            type: Boolean,
            default: false,
        },
        currentUserId: {
            type: number,
            default: null
        },
    },
    watch: {
        configuration: function() {
            this.renamingRoutine = false
        }
    },
    data() {
        return {
            domainOptions: [
                {id: 'solarreviews.com', name: 'SolarReviews'},
                {id: 'roofingcalculator.com', name: 'RoofingCalculator'},
                {id: 'fixr.com', name: 'FIXr'},
            ],
            renamingRoutine: false,
            configuringAction: null,
        }
    },
    computed: {
        customActions() {
            let actions = !this.shared ? [
                {event: 'rename-routine', name: 'Rename'},
                {event: 'delete-routine', name: 'Delete'},
            ] : [];
            if(this.configuration?.id)
                actions.push({event: 'clone', name: 'Clone'})

            return actions
        }
    },
    methods: {
        renameRoutine() {
            this.renamingRoutine = true;
        },
        deleteRoutine(routine) {
            this.$emit('delete-routine', routine)
        },
        deselectRoutine() {
            this.$emit('deselect-routine')
        },
        updateOrCreateSelected() {
            this.$emit('update-or-create-selected')
            if(this.renamingRoutine) {
                this.renamingRoutine = false
            }
        },
        updateOrCreateAction(action) {
            this.$emit('update-or-create-action', action)
        },
        cloneRoutine() {
            this.$emit('clone-routine', this.configuration.id)
        },
        updateGroupActionOrder(params) {
            params['routineId'] = this.configuration.id
            this.$emit('action-group-order-updated', params)
        },
    }
}
</script>

<style scoped>

</style>
