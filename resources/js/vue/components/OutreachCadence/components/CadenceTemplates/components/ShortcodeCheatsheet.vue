<template>
    <div @click="shortcodeCheatsheet = !shortcodeCheatsheet" @mouseleave="shortcodeCheatsheet = false"
         class="relative cursor-help">
        <div class="inline-flex items-center space-x-2">
            <svg width="13" height="9" viewBox="0 0 13 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.13645 5.72728C6.46195 5.72728 6.77411 5.59798 7.00427 5.36782C7.23442 5.13766 7.36373 4.8255 7.36373 4.50001C7.36373 4.17452 7.23442 3.86235 7.00427 3.6322C6.77411 3.40204 6.46195 3.27274 6.13645 3.27274C5.81096 3.27274 5.4988 3.40204 5.26864 3.6322C5.03848 3.86235 4.90918 4.17452 4.90918 4.50001C4.90918 4.8255 5.03848 5.13766 5.26864 5.36782C5.4988 5.59798 5.81096 5.72728 6.13645 5.72728Z" fill="#64748B"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0 4.5C0.819297 1.89193 3.25661 0 6.13636 0C9.01612 0 11.4534 1.89193 12.2727 4.5C11.4534 7.10807 9.01612 9 6.13636 9C3.25661 9 0.819297 7.10807 0 4.5ZM8.70872 4.5C8.70872 5.18199 8.43771 5.83604 7.9553 6.31827C7.47289 6.80051 6.8186 7.07143 6.13636 7.07143C5.45413 7.07143 4.79984 6.80051 4.31743 6.31827C3.83502 5.83604 3.564 5.18199 3.564 4.5C3.564 3.81801 3.83502 3.16396 4.31743 2.68173C4.79984 2.19949 5.45413 1.92857 6.13636 1.92857C6.8186 1.92857 7.47289 2.19949 7.9553 2.68173C8.43771 3.16396 8.70872 3.81801 8.70872 4.5Z" fill="#64748B"/>
            </svg>
            <p class="text-sm font-medium text-slate-500">Add Shortcode</p>
        </div>
        <div v-if="shortcodeCheatsheet" class="absolute top-4 right-0 p-4 border rounded-lg shadow-module z-40 w-80" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
            <div class="flex flex-col gap-2">
                <a class="text-sm font-semibold" :class="[darkMode ? 'hover:bg-dark-175' : 'hover:bg-light-border']" v-for="shortcode in shortcodes" key="{{shortcode.value}}" href="#" onclick="return false" @click="selectShortCode(shortcode.value)">{{shortcode.label}}</a>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "ShortcodeCheatsheet",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            shortcodeCheatsheet: false,
            shortcodes: [
                {label: 'Client Company Name', value: '{{company_name}}'},
                {label: 'Client Company Industries (Or)', value: '{{company_industries_or}}'},
                {label: 'Client Company Industries (And)', value: '{{company_industries_and}}'},
                {label: 'Client Company Industry (Default)', value: '{{company_industry_default}}'},
                {label: 'Client Contact Full Name', value: '{{contact_name}}'},
                {label: 'Client Contact First Name', value: '{{contact_first_name}}'},
                {label: 'Client Contact Last Name', value: '{{contact_last_name}}'},
                {label: 'Account Manager Full Name', value: '{{account_manager_name}}'},
                {label: 'Account Manager First Name', value: '{{account_manager_first_name}}'},
                {label: 'Account Manager Last Name', value: '{{account_manager_last_name}}'},
                {label: 'Account Manager Phone Number', value: '{{account_manager_phone}}'},
                {label: 'Account Manager Email Address', value: '{{account_manager_email}}'},
                {label: 'Account Manager Meeting Url', value: '{{account_manager_meeting_url}}'},
                {label: 'Relevant Web Domain', value: '{{domain}}'},
            ]
        }
    },
    methods : {
        selectShortCode(shortCode) {
            this.$emit('shortcode-selected', shortCode)
        },
    },
}
</script>

<style scoped>

</style>
