<template>
    <div class="px-10">
        <div class="border rounded-lg"
             :class="[darkMode ? 'bg-dark-module text-slate-50 border-dark-border' : 'bg-light-module text-slate-900 border-light-border']">
            <div class="grid grid-cols-2 items-start">
                <div class="border-r" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                    <div class="flex justify-between items-center p-5 h-20">
                        <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Header</h5>
                        <CustomButton v-if="headerUpdated" @click="saveHeader">Save</CustomButton>
                    </div>
                    <div class="px-5 pb-5">

                        <div v-if="loading"
                             class="h-100 flex items-center justify-center"
                             :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']">
                            <loading-spinner></loading-spinner>
                        </div>
                        <MarkdownEditor
                            @update:content="headerUpdated = true"
                            v-if="!loading"
                            :dark-mode="darkMode"
                            v-model:content="headerContent"
                            v-model:images="headerImages"
                            :additional-plugins="editorPlugins"
                        ></MarkdownEditor>

                    </div>
                </div>
                <div class="border-r" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                    <div class="flex justify-between items-center p-5 h-20">
                        <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Footer</h5>
                        <CustomButton v-if="footerUpdated" @click="saveFooter">Save</CustomButton>
                    </div>
                    <div class="px-5 pb-5">

                        <div v-if="loading"
                             class="h-100 flex items-center justify-center"
                             :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']">
                            <loading-spinner></loading-spinner>
                        </div>
                        <MarkdownEditor
                            @update:content="footerUpdated = true"
                            v-if="!loading"
                            :dark-mode="darkMode"
                            v-model:content="footerContent"
                            v-model:images="footerImages"
                            :additional-plugins="editorPlugins"
                        ></MarkdownEditor>

                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LoadingSpinner from "../../../LeadProcessing/components/LoadingSpinner.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import ConfigurationScreen from "../RoutineConfiguration/components/ConfigurationScreen.vue";
import MarkdownEditor from "../../../Shared/components/MarkdownEditor.vue";
import ApiService from "../../services/api";
import {
    replaceMarkdownImageTags,
    replaceShortcodes
} from "../../../Shared/services/markdownImageTags";
import ShortcodeCheatsheet from "./components/ShortcodeCheatsheet.vue";
import shortcodesPlugin from "../../../Shared/includes/shortcodesPlugin";

export default {
    name: "CadenceEmailHeaderFooterConfig",
    components: {ShortcodeCheatsheet, MarkdownEditor, ConfigurationScreen, CustomButton, LoadingSpinner},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            apiService: ApiService.make(),
            loading: true,
            headerContent: '',
            headerImages: {},
            headerUpdated: false,
            footerContent: '',
            footerImages: {},
            footerUpdated: false,
            editorPlugins: [[shortcodesPlugin, [
                {label: 'Client Company Name', value: '{company_name}'},
                {label: 'Client Company Industries (Or)', value: '{company_industries_or}'},
                {label: 'Client Company Industries (And)', value: '{company_industries_and}'},
                {label: 'Client Company Industry (Default)', value: '{company_industry_default}'},
                {label: 'Client Contact Full Name', value: '{contact_name}'},
                {label: 'Client Contact First Name', value: '{contact_first_name}'},
                {label: 'Client Contact Last Name', value: '{contact_last_name}'},
                {label: 'Account Manager Full Name', value: '{account_manager_name}'},
                {label: 'Account Manager First Name', value: '{account_manager_first_name}'},
                {label: 'Account Manager Last Name', value: '{account_manager_last_name}'},
                {label: 'Account Manager Phone Number', value: '{account_manager_phone}'},
                {label: 'Account Manager Email Address', value: '{account_manager_email}'},
                {label: 'Account Manager Meeting Url', value: '{account_manager_meeting_url}'},
                {label: 'Relevant Web Domain', value: '{domain}'},
            ]]],
        }
    },
    mounted() {
        this.getUserHeaderAndFooter();
    },
    methods: {
        getUserHeaderAndFooter() {
            this.apiService.getUserHeaderAndFooter().then(resp => {
                if (resp.data.data['header']) {
                    this.headerContent = resp.data.data.header.content
                    this.headerImages = resp.data.data.header.content_images
                }
                if (resp.data.data['footer']) {
                    this.footerContent = resp.data.data.footer.content
                    this.footerImages = resp.data.data.footer.content_images
                }
                this.headerContent = replaceShortcodes(this.headerContent, this.headerImages)
                this.footerContent = replaceShortcodes(this.footerContent, this.footerImages)
                this.loading = false
            })
        },
        saveHeader() {
            this.apiService.updateUserHeader(replaceMarkdownImageTags(this.headerContent)).then(() => this.headerUpdated = false)
            this.saveImages(this.headerImages, 'header')
        },
        saveFooter() {
            this.apiService.updateUserFooter(replaceMarkdownImageTags(this.footerContent)).then(() => this.footerUpdated = false)
            this.saveImages(this.footerImages, 'footer')
        },
        saveImages(images, templateType) {
            let promises = [];
            Object.entries(images).forEach(([name, imageDataUrl]) => {
                promises.push(this.apiService.saveImage(name, imageDataUrl, templateType));
            });

            return Promise.all(promises).then(responses => {
                let success = true;
                let failedImages = [];
                for(const res of responses) {
                    if(res.data.data.status !== true) {
                        success = false;
                        failedImages.push(res.data.data.filename);
                    }
                }
                if(success) {
                    // this.store.showAlert('success', 'Images uploaded');
                }
                else {
                    const failedImageNames = failedImages.join(', ');
                    // this.store.showAlert('error', `Failed to upload images: ${failedImageNames}`);
                }
            }).catch(err => {
                // this.store.showAlert('error', 'Error uploading images');
            });

        },
    },
}
</script>

<style scoped>

</style>
