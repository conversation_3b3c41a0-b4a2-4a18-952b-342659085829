<template>
    <div class="px-10">
        <alerts-container :dark-mode="darkMode" v-if="error" alert-type="error" :text="error"></alerts-container>
        <alerts-container :dark-mode="darkMode" v-if="successfulUpdate" alert-type="success" text="Saved."></alerts-container>
        <alerts-container :dark-mode="darkMode" v-if="successfulDelete" alert-type="warning" text="Deleted."></alerts-container>

        <div class="border rounded-lg" :class="[darkMode ? 'bg-dark-module text-slate-50 border-dark-border' : 'bg-light-module text-slate-900 border-light-border']">
            <div class="grid grid-cols-3 items-start">
                <div class="" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                    <div class="flex justify-between items-center p-5">
                        <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Email Templates</h5>

                        <div class="inline-flex items-center justify-start w-40 gap-2">
                            <toggle-switch @click="selectedTemplate = null; renamingTemplate = false" v-model="shared" :dark-mode="darkMode"></toggle-switch>
                            <svg v-if="shared" class="fill-current text-slate-500" width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 3C12 3.79565 11.6839 4.55871 11.1213 5.12132C10.5587 5.68393 9.79565 6 9 6C8.20435 6 7.44129 5.68393 6.87868 5.12132C6.31607 4.55871 6 3.79565 6 3C6 2.20435 6.31607 1.44129 6.87868 0.87868C7.44129 0.316071 8.20435 0 9 0C9.79565 0 10.5587 0.316071 11.1213 0.87868C11.6839 1.44129 12 2.20435 12 3ZM17 5C17 5.53043 16.7893 6.03914 16.4142 6.41421C16.0391 6.78929 15.5304 7 15 7C14.4696 7 13.9609 6.78929 13.5858 6.41421C13.2107 6.03914 13 5.53043 13 5C13 4.46957 13.2107 3.96086 13.5858 3.58579C13.9609 3.21071 14.4696 3 15 3C15.5304 3 16.0391 3.21071 16.4142 3.58579C16.7893 3.96086 17 4.46957 17 5ZM13 12C13 10.9391 12.5786 9.92172 11.8284 9.17157C11.0783 8.42143 10.0609 8 9 8C7.93913 8 6.92172 8.42143 6.17157 9.17157C5.42143 9.92172 5 10.9391 5 12V15H13V12ZM5 5C5 5.53043 4.78929 6.03914 4.41421 6.41421C4.03914 6.78929 3.53043 7 3 7C2.46957 7 1.96086 6.78929 1.58579 6.41421C1.21071 6.03914 1 5.53043 1 5C1 4.46957 1.21071 3.96086 1.58579 3.58579C1.96086 3.21071 2.46957 3 3 3C3.53043 3 4.03914 3.21071 4.41421 3.58579C4.78929 3.96086 5 4.46957 5 5ZM15 15V12C15.0014 10.9833 14.7433 9.98303 14.25 9.094C14.6933 8.98054 15.1568 8.96984 15.6049 9.06272C16.053 9.1556 16.474 9.34959 16.8357 9.62991C17.1974 9.91023 17.4903 10.2695 17.6921 10.6802C17.8939 11.091 17.9992 11.5424 18 12V15H15ZM3.75 9.094C3.25675 9.98305 2.9986 10.9833 3 12V15H2.6572e-07V12C-0.000192468 11.542 0.104463 11.0901 0.305947 10.6789C0.507431 10.2676 0.800394 9.90793 1.16238 9.62742C1.52437 9.3469 1.94578 9.15298 2.39431 9.06052C2.84284 8.96806 3.30658 8.97951 3.75 9.094Z"/>
                            </svg>
                            <svg v-else width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.375 3.375C3.375 5.23575 4.88925 6.75 6.75 6.75C8.61075 6.75 10.125 5.23575 10.125 3.375C10.125 1.51425 8.61075 0 6.75 0C4.88925 0 3.375 1.51425 3.375 3.375ZM12.75 14.25H13.5V13.5C13.5 10.6058 11.1442 8.25 8.25 8.25H5.25C2.355 8.25 0 10.6058 0 13.5V14.25H12.75Z" fill="#64748B"/>
                            </svg>

                            <p class="text-sm font-semibold text-slate-500">{{shared ? 'Shared' : 'Mine Only'}}</p>
                        </div>

                        <CustomButton @click="createNewTemplate">Create New</CustomButton>
                    </div>

                    <div>
                        <div class="grid grid-cols-2 gap-3 mb-2 px-5">
                            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Template</p>
                            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Created by</p>
                        </div>
                        <div v-if="loading" class="border-t border-b h-[36.5rem] overflow-y-auto divide-y flex items-center justify-center"
                             :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                            <loading-spinner></loading-spinner>
                        </div>
                        <div v-else class="border-t border-b h-[36.5rem] overflow-y-auto divide-y" :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                            <div @click="selectTemplate(template)" v-for="template in availableTemplates" :key="template" class="cursor-pointer grid grid-cols-2 gap-x-3 py-3 px-5 group relative transition duration-100 items-center" :class="[selectedTemplate === template ? (darkMode ? 'bg-slate-800 bg-opacity-50 text-primary-500 font-medium' : 'bg-primary-50 text-primary-500 font-medium') : (darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module')]">
                                <div class="absolute left-0 h-full w-1" :class="[selectedTemplate === template ? (darkMode ? 'bg-primary-500' : 'bg-primary-500') : (darkMode ? 'bg-slate-600 invisible group-hover:visible' : 'bg-slate-400 invisible group-hover:visible') ]"></div>
                                <p class="text-sm">
                                    {{ template.name }}
                                </p>
                                <p class="text-sm">
                                    {{ template?.user?.name ?? currentUser.name }}
                                </p>
                                <div class="absolute right-5">
                                    <svg class="fill-current" :class="[selectedTemplate === template ? (darkMode ? 'text-primary-500' : 'text-primary-500') : (darkMode ? 'text-dark-border' : 'text-light-border')]" width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L6.70711 5.29289C7.09763 5.68342 7.09763 6.31658 6.70711 6.70711L1.70711 11.7071C1.31658 12.0976 0.683417 12.0976 0.292893 11.7071C-0.0976311 11.3166 -0.0976311 10.6834 0.292893 10.2929L4.58579 6L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"/></svg>
                                </div>
                            </div>
                        </div>
                        <div class="p-3"></div>
                    </div>

                </div>
                <div class="col-span-2 relative h-full border-x" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                    <div class="flex justify-between items-center p-5">
                        <div class="inline-flex flex-grow items-center space-x-3">
                            <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Email Editor</h5>
                            <svg v-if="selectedTemplate" width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.292787 9.69471C0.105316 9.50718 0 9.25288 0 8.98771C0 8.72255 0.105316 8.46824 0.292787 8.28071L3.58579 4.98771L0.292787 1.69471C0.110629 1.50611 0.00983372 1.25351 0.0121121 0.991311C0.0143906 0.729114 0.11956 0.478302 0.304968 0.292894C0.490376 0.107485 0.741189 0.00231622 1.00339 3.78025e-05C1.26558 -0.00224062 1.51818 0.0985542 1.70679 0.280712L5.70679 4.28071C5.89426 4.46824 5.99957 4.72255 5.99957 4.98771C5.99957 5.25288 5.89426 5.50718 5.70679 5.69471L1.70679 9.69471C1.51926 9.88218 1.26495 9.9875 0.999786 9.9875C0.734622 9.9875 0.480314 9.88218 0.292787 9.69471Z" fill="#64748B"/></svg>
                            <div class="flex flex-grow mr-4" v-if="selectedTemplate">
                                <p v-if="!renamingTemplate" class="font-semibold">{{selectedTemplate.name}}</p>
                                <div v-if="renamingTemplate" class="relative flex items-center w-full">
                                    <CustomInput class="w-full" :dark-mode="darkMode" v-model="selectedTemplate.name"></CustomInput>
                                    <div class="absolute right-3 inline-flex items-center">
                                        <div @click="updateOrCreateSelected" class="w-6 h-6 rounded bg-emerald-500 hover:bg-green-600 cursor-pointer inline-flex items-center justify-center">
                                            <svg width="12" height="9" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.91869 6.71136L1.17719 4.0464L0 5.19072L3.91869 9L12 1.14432L10.8228 0L3.91869 6.71136Z" fill="white"/></svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ActionsHandle
                            v-if="selectedTemplate"
                            :dark-mode="darkMode"
                            :no-edit-button="true"
                            :no-delete-button="true"
                            @rename="renamingTemplate = true"
                            @delete="deleteTemplate(selectedTemplate)"
                            :no-custom-action="false"
                            :custom-actions="customActions"
                            @clone="cloneTemplate"
                        ></ActionsHandle>
                    </div>
                    <div v-if="selectedTemplate" class="grid gap-5">
                        <div class="px-5">
                            <div class="flex items-center justify-between mb-1">
                                <p class="font-semibold text-sm">Subject</p>
                                <ShortcodeCheatsheet v-if="!shared" :dark-mode="darkMode" @shortcode-selected="insertSubjectShortCode"></ShortcodeCheatsheet>
                            </div>
                            <CustomInput
                                id="email-subject"
                                @click="updateSubjectCursor"
                                @keyup="updateSubjectCursor"
                                :disabled="shared"
                                :dark-mode="darkMode"
                                placeholder="Enter a subject"
                                v-model="selectedTemplate.subject"
                            ></CustomInput>
                        </div>
                        <div class="px-5">
                            <p class="font-semibold text-sm mb-1">Body</p>
                            <MarkdownEditor
                                :disabled="shared"
                                :dark-mode="darkMode"
                                v-model:content="selectedTemplate.body"
                                v-model:images="selectedTemplate.content_images"
                                :additional-plugins="editorPlugins"
                            ></MarkdownEditor>
                        </div>
                        <div class="justify-between border-t px-5 py-3 flex items-center space-x-3" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                            <div v-if="!shared" class="flex items-center space-x-3">
                                <CustomButton @click="updateOrCreateSelected()">Save</CustomButton>
                                <div class="inline-flex items-center space-x-2 my-4">
                                    <toggle-switch v-model="selectedTemplate.global" :dark-mode="darkMode"></toggle-switch>
                                    <p class="font-semibold text-sm">Globally Available</p>
                                </div>
                            </div>
                            <CustomButton :disabled="!selectedTemplate.id" @click="preview" >Preview</CustomButton>
                        </div>
                    </div>
                    <div v-else class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center">
                            <div class="flex justify-center">
                                <svg width="23" height="19" viewBox="0 0 23 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.8571 -4.99559e-08C15.1602 -3.67067e-08 15.4509 0.0790176 15.6653 0.21967C15.8796 0.360322 16 0.551088 16 0.75L16 2.25C16 2.44891 15.8796 2.63968 15.6653 2.78033C15.4509 2.92098 15.1602 3 14.8571 3L1.14286 3C0.839753 3 0.549062 2.92098 0.334735 2.78033C0.120408 2.63968 8.46629e-07 2.44891 8.55324e-07 2.25L9.20891e-07 0.749999C9.29586e-07 0.551087 0.120408 0.360321 0.334735 0.219669C0.549062 0.0790169 0.839753 -6.62675e-07 1.14286 -6.49426e-07L14.8571 -4.99559e-08ZM14.8571 4.5C15.1602 4.5 15.4509 4.57902 15.6653 4.71967C15.8796 4.86032 16 5.05109 16 5.25L16 6.75C16 6.94891 15.8796 7.13968 15.6653 7.28033C15.4509 7.42098 15.1602 7.5 14.8571 7.5L1.14286 7.5C0.839753 7.5 0.549062 7.42098 0.334735 7.28033C0.120408 7.13968 6.49928e-07 6.94891 6.58622e-07 6.75L7.2419e-07 5.25C7.32884e-07 5.05109 0.120408 4.86032 0.334735 4.71967C0.549062 4.57902 0.839753 4.5 1.14286 4.5L14.8571 4.5ZM16 9.75C16 9.55109 15.8796 9.36032 15.6653 9.21967C15.4509 9.07902 15.1602 9 14.8571 9L1.14286 9C0.839753 9 0.549062 9.07902 0.334735 9.21967C0.120407 9.36032 5.36183e-07 9.55109 5.27488e-07 9.75L4.61921e-07 11.25C4.53226e-07 11.4489 0.120407 11.6397 0.334735 11.7803C0.549062 11.921 0.839753 12 1.14286 12L14.8571 12C15.1602 12 15.4509 11.921 15.6653 11.7803C15.8796 11.6397 16 11.4489 16 11.25L16 9.75Z" fill="#CBD5E1"/><path fill-rule="evenodd" clip-rule="evenodd" d="M11.672 2.91098C11.6033 2.65478 11.4357 2.43636 11.2059 2.30378C10.9762 2.17119 10.7032 2.13529 10.447 2.20398C10.1908 2.27267 9.9724 2.44033 9.83981 2.67006C9.70723 2.89979 9.67133 3.17278 9.74002 3.42898L9.99902 4.39498C10.0677 4.65118 10.2354 4.8696 10.4651 5.00219C10.6948 5.13478 10.9678 5.17067 11.224 5.10198C11.4802 5.03329 11.6986 4.86564 11.8312 4.63591C11.9638 4.40617 11.9997 4.13318 11.931 3.87698L11.672 2.91098ZM7.42902 5.73998C7.30194 5.70534 7.16927 5.69614 7.03863 5.71292C6.90799 5.7297 6.78195 5.77212 6.66775 5.83775C6.55355 5.90338 6.45344 5.99092 6.37317 6.09535C6.2929 6.19978 6.23405 6.31904 6.20001 6.44627C6.16596 6.57351 6.15738 6.70622 6.17477 6.83678C6.19215 6.96734 6.23516 7.09319 6.30132 7.20708C6.36748 7.32097 6.45549 7.42067 6.56029 7.50045C6.6651 7.58023 6.78463 7.63853 6.91202 7.67198L7.87802 7.93098C8.13376 7.99814 8.40568 7.96128 8.63432 7.82847C8.86295 7.69566 9.02968 7.4777 9.09803 7.22228C9.16638 6.96686 9.13079 6.69476 8.99904 6.46551C8.8673 6.23626 8.65012 6.06852 8.39502 5.99898L7.42902 5.73998ZM16.243 5.17098C16.3359 5.07807 16.4095 4.96778 16.4597 4.84642C16.5099 4.72505 16.5358 4.59498 16.5357 4.46363C16.5357 4.33228 16.5098 4.20223 16.4595 4.08089C16.4091 3.95956 16.3354 3.84933 16.2425 3.75648C16.1496 3.66364 16.0393 3.59 15.918 3.53978C15.7966 3.48956 15.6665 3.46373 15.5352 3.46378C15.4038 3.46383 15.2738 3.48974 15.1524 3.54005C15.0311 3.59036 14.9209 3.66407 14.828 3.75698L14.121 4.46398C14.0281 4.55689 13.9544 4.66719 13.9041 4.78859C13.8538 4.90998 13.828 5.04009 13.828 5.17148C13.828 5.30288 13.8538 5.43298 13.9041 5.55438C13.9544 5.67577 14.0281 5.78607 14.121 5.87898C14.2139 5.97189 14.3242 6.04559 14.4456 6.09588C14.567 6.14616 14.6971 6.17204 14.8285 6.17204C14.9599 6.17204 15.09 6.14616 15.2114 6.09588C15.3328 6.04559 15.4431 5.97189 15.536 5.87898L16.243 5.17098ZM9.17202 12.243L9.87902 11.536C10.0668 11.3485 10.1724 11.0941 10.1726 10.8287C10.1728 10.5633 10.0675 10.3088 9.88002 10.121C9.69251 9.93321 9.43809 9.82761 9.17273 9.82743C8.90736 9.82724 8.65279 9.93247 8.46502 10.12L7.75702 10.827C7.56938 11.0146 7.46396 11.2691 7.46396 11.5345C7.46396 11.7998 7.56938 12.0543 7.75702 12.242C7.94466 12.4296 8.19916 12.535 8.46452 12.535C8.72988 12.535 8.98438 12.4296 9.17202 12.242V12.243ZM12.372 7.07198C12.1903 6.99924 11.9912 6.98143 11.7995 7.02077C11.6077 7.0601 11.4317 7.15485 11.2933 7.29326C11.1549 7.43168 11.0601 7.60767 11.0208 7.79942C10.9815 7.99117 10.9993 8.19025 11.072 8.37198L15.072 18.372C15.1437 18.551 15.2656 18.7054 15.423 18.8167C15.5804 18.928 15.7666 18.9915 15.9593 18.9994C16.1519 19.0073 16.3427 18.9594 16.5087 18.8614C16.6747 18.7634 16.8089 18.6195 16.895 18.447L18.275 15.688L21.293 18.708C21.4807 18.8955 21.7351 19.0008 22.0004 19.0007C22.2656 19.0006 22.52 18.8951 22.7075 18.7075C22.895 18.5198 23.0003 18.2654 23.0002 18.0001C23.0001 17.7349 22.8947 17.4805 22.707 17.293L19.688 14.273L22.448 12.894C22.6202 12.8076 22.7637 12.6734 22.8615 12.5075C22.9592 12.3415 23.0069 12.1508 22.9989 11.9584C22.9908 11.766 22.9274 11.58 22.8162 11.4227C22.705 11.2655 22.5508 11.1437 22.372 11.072L12.372 7.07198Z" fill="#64748B"/></svg>
                            </div>
                            <p class="text-slate-500">Select a template to edit it’s configuration, <br/>or create a new template.</p>
                        </div>
                    </div>
                </div>
            </div>
            <Modal
                :dark-mode="darkMode"
                v-if="previewing"
                :no-buttons="true"
                :close-text="'Done'"
                @close="previewing = false"
            >
                <template v-slot:header>
                    <b class="capitalize">{{selectedTemplate.name}}</b>
                </template>
                <template v-slot:content>
                    <div v-if="loadingPreview" class="border-t border-b h-full overflow-y-auto divide-y flex items-center justify-center"
                         :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                        <loading-spinner></loading-spinner>
                    </div>
                    <div v-else>
                        <p class="mb-3"><b>Subject:</b> {{previewContent.subject}}</p>
                        <div v-html="previewContent.body" class="p-3 border overflow-y-auto h-120" :class="[darkMode ? 'border-dark-border' : 'border-light-border']"></div>
                    </div>
                </template>
            </Modal>
        </div>
    </div>
</template>

<script>
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import ShortcodeCheatsheet from "./components/ShortcodeCheatsheet.vue";
import ApiService from "../../services/api";
import {isDefined} from "@vueuse/core";
import LoadingSpinner from "../../../LeadProcessing/components/LoadingSpinner.vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import MarkdownEditor from "../../../Shared/components/MarkdownEditor.vue";
import {replaceMarkdownImageTags, replaceShortcodes} from "../../../Shared/services/markdownImageTags";
import Modal from "../../../Shared/components/Modal.vue";
import shortcodesPlugin from "../../../Shared/includes/shortcodesPlugin";

export default {
    name: "CadenceEmail",
    components: {
        Modal,
        MarkdownEditor,
        AlertsContainer,
        LoadingSpinner, ShortcodeCheatsheet, CustomButton, CustomInput, ToggleSwitch, ActionsHandle},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        currentUser: {
            type: Object,
            default: {},
        },
    },
    data () {
        return {
            apiService: ApiService.make(),
            emailTemplates: [],
            selectedTemplate: null,
            lastSelectedTemplateId: null,
            renamingTemplate: false,
            loading: true,
            successfulUpdate: false,
            successfulDelete: false,
            previewing: false,
            loadingPreview: false,
            previewContent: {
                subject: '',
                body: ''
            },
            editorPlugins: [[shortcodesPlugin, [
                {label: 'Client Company Name', value: '{company_name}'},
                {label: 'Client Company Industries (Or)', value: '{company_industries_or}'},
                {label: 'Client Company Industries (And)', value: '{company_industries_and}'},
                {label: 'Client Company Industry (Default)', value: '{company_industry_default}'},
                {label: 'Client Contact Full Name', value: '{contact_name}'},
                {label: 'Client Contact First Name', value: '{contact_first_name}'},
                {label: 'Client Contact Last Name', value: '{contact_last_name}'},
                {label: 'Account Manager Full Name', value: '{account_manager_name}'},
                {label: 'Account Manager First Name', value: '{account_manager_first_name}'},
                {label: 'Account Manager Last Name', value: '{account_manager_last_name}'},
                {label: 'Account Manager Phone Number', value: '{account_manager_phone}'},
                {label: 'Account Manager Email Address', value: '{account_manager_email}'},
                {label: 'Account Manager Meeting Url', value: '{account_manager_meeting_url}'},
                {label: 'Relevant Web Domain', value: '{domain}'},
            ]]],
            shared: false,
            error: null,
            subjectCursorPos: 0,
        }
    },
    computed: {
        customActions() {
            let actions = !this.shared ? [
                {event: 'rename', name: 'Rename'},
                {event: 'delete', name: 'Delete'},
            ] : [];
            if(this.selectedTemplate?.id)
                actions.push({event: 'clone', name: 'Clone'})

            return actions
        },
        availableTemplates() {
            if(this.shared)
                return this.emailTemplates.filter(template => 'user' in template && template.user.id !== this.currentUser.id)
            else
                return this.emailTemplates.filter(template => !('user' in template) || template.user.id === this.currentUser.id)
        },
    },
    mounted(){
        this.getTemplates();
    },
    methods: {
        selectTemplate(template) {
            if(this.selectedTemplate === template) {
                this.selectedTemplate = null;
            }
            else {
                this.selectedTemplate = template;
                this.lastSelectedTemplateId = template.id
            }
        },
        createNewTemplate() {
            this.emailTemplates.unshift(
                {name: 'New email template', subject: '', body: '', content_images: {}, global: true},
            )
            this.selectedTemplate = this.emailTemplates[0]
            this.shared = false
        },
        deleteTemplate(template) {
            this.loading = true
            if(isDefined(template.id)){
                this.apiService.deleteEmailTemplate(template).then(data => {
                    this.emailTemplates = data.data.data;
                    this.replaceShortcodesForAllTemplates()
                }).finally(() => {
                    this.loading = false
                    this.successfulDelete = true
                    setTimeout(() => this.successfulDelete = false, 3000);
                });
            }else if(this.emailTemplates.includes(template)){
                this.emailTemplates = this.emailTemplates.filter(function(temp){
                    return template !== temp;
                });
                this.loading = false
            }
            this.selectedTemplate = null;
        },
        replaceShortcodesForAllTemplates() {
            this.emailTemplates = this.emailTemplates.map(template => {
                template.body = replaceShortcodes(template.body, template.content_images)
                return template
            })
        },
        getTemplates() {
            this.apiService.getEmailTemplates().then(data => {
                this.loading = true;
                this.emailTemplates = data.data.data;
                this.replaceShortcodesForAllTemplates()
            }).finally(() => {
                this.loading = false;
            });
        },
        updateTemplate(template) {
            template.body = replaceMarkdownImageTags(template.body)
            this.saveImages(template.content_images, template).then(() => {
                this.apiService.updateEmailTemplate(template).then(resp => {
                    this.emailTemplates = resp.data.data;
                    this.replaceShortcodesForAllTemplates()
                    this.selectedTemplate = resp.data.data.filter(template => template.id === this.lastSelectedTemplateId)[0]
                    this.successfulUpdate = true
                    setTimeout(() => this.successfulUpdate = false, 3000);
                }).catch(resp => {
                    this.error = resp.response?.data?.message ?? 'Unexpected Error'
                    setTimeout(() => this.error = null, 3000);
                }).finally(() => {this.loading = false})
            }).catch(resp => {
                this.error = resp.response?.data?.message ?? 'Unexpected Error'
                setTimeout(() => this.error = null, 3000);
            }).finally(() => {this.loading = false})
        },
        updateOrCreateSelected() {
            this.renamingTemplate = false
            this.loading = true
            let template = {...this.selectedTemplate}

            if(!isDefined(template.id)){

                // save with blank body (can't save images until we have a template id)
                template.body = '__BODY_PLACEHOLDER__'
                this.apiService.createEmailTemplate( template ).then(resp => {
                    this.lastSelectedTemplateId = Math.max(...resp.data.data.map(template => template.id))
                    this.selectedTemplate.id = this.lastSelectedTemplateId

                    // now that we have an id, update the template to save images
                    this.updateOrCreateSelected()
                }).catch(resp => {
                    this.error = resp.response?.data?.message ?? 'Unexpected Error'
                    setTimeout(() => this.error = null, 3000);
                    this.loading = false
                })

            }else {
                this.updateTemplate(template)
            }
        },
        saveImages(images, template) {
            let promises = [];
            Object.entries(images).forEach(([name, imageDataUrl]) => {
                promises.push(this.apiService.saveImage(name, imageDataUrl, 'email', template.id));
            });

            return Promise.all(promises).then(responses => {
                let success = true;
                let failedImages = [];
                for(const res of responses) {
                    if(res.data.data.status !== true) {
                        success = false;
                        failedImages.push(res.data.data.filename);
                    }
                }
                if(success) {
                    // this.store.showAlert('success', 'Images uploaded');
                }
                else {
                    const failedImageNames = failedImages.join(', ');
                    // this.store.showAlert('error', `Failed to upload images: ${failedImageNames}`);
                }
            }).catch(err => {
                // this.store.showAlert('error', 'Error uploading images');
            });

        },
        preview() {
            this.loadingPreview = true
            this.previewing = true
            this.saveImages(this.selectedTemplate.content_images, this.selectedTemplate).then(() => {
                this.apiService.getEmailPreview(
                    this.selectedTemplate.id,
                    this.selectedTemplate.subject,
                    this.selectedTemplate.body
                ).then(resp => {
                    this.previewContent = {
                        subject: resp.data.data.subject,
                        body: resp.data.data.body
                    }
                    this.loadingPreview = false
                })
            })
        },
        cloneTemplate() {
            const id = this.selectedTemplate.id
            this.selectedTemplate = null
            this.apiService.cloneEmailTemplate(id).then(resp => {
                this.emailTemplates = resp.data.data;
                this.replaceShortcodesForAllTemplates()
                this.selectedTemplate = this.getNewestTemplate()
                this.shared = false
            })
        },
        getNewestTemplate() {
            return this.emailTemplates.sort(function(a,b) {
                const keyA = new Date(a.created_at), keyB = new Date(b.created_at);
                return keyB - keyA;
            })[0] ?? null;
        },
        updateSubjectCursor() {
            const el = document.getElementById('email-subject')
            this.subjectCursorPos = el.selectionStart
        },
        insertSubjectShortCode(shortCode) {
            const originalValue = this.selectedTemplate.subject
            this.selectedTemplate.subject = originalValue.slice(0, this.subjectCursorPos) + shortCode + originalValue.slice(this.subjectCursorPos)
        },
    },
}
</script>

<style scoped>

</style>
