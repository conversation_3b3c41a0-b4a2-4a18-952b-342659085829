<template>
    <div>
        <div class="w-full mb-6"  :class="[darkMode ? 'bg-dark-module text-slate-50' : 'bg-light-module text-slate-900']">
            <h3 class="text-xl font-medium pt-8 pb-4 leading-none px-10">Outreach Cadence</h3>
            <Tab :dark-mode="darkMode" :tabs="tabs" @selected="tabSelected" :default-tab-index="selectedTabIndex" :tabs-classes="'w-full xl:w-3/4'" :show-total="false">
                <template v-slot:extra-tab><div class="flex-grow" :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']"></div></template>
            </Tab>
        </div>
        <RoutineConfiguration :dark-mode="darkMode" v-if="selectedTabName === 'Routines'" :current-user="currentUser"/>
        <CadenceEmailHeaderFooterConfig :dark-mode="darkMode" v-if="selectedTabName === 'Email Header/Footer'"></CadenceEmailHeaderFooterConfig>
        <CadenceEmail :dark-mode="darkMode" v-if="selectedTabName === 'Email Templates'" :current-user="currentUser"></CadenceEmail>
        <CadenceSMS :dark-mode="darkMode" v-if="selectedTabName === 'SMS Templates'" :current-user="currentUser"></CadenceSMS>
        <CadenceTask :dark-mode="darkMode" v-if="selectedTabName === 'Task Templates'" :current-user="currentUser"></CadenceTask>
    </div>
</template>

<script>
import Tab from "../Shared/components/Tab.vue";
import OngoingRoutines from "./components/OngoingRoutines/OngoingRoutines.vue";
import RoutineConfiguration from "./components/RoutineConfiguration/RoutineConfiguration.vue";
import CadenceEmail from "./components/CadenceTemplates/CadenceEmail.vue";
import CadenceSMS from "./components/CadenceTemplates/CadenceSMS.vue";
import CadenceTask from "./components/CadenceTemplates/CadenceTask.vue";
import CadenceEmailHeaderFooterConfig from "./components/CadenceTemplates/CadenceEmailHeaderFooterConfig.vue";

export default {
    name: "OutreachCadence",
    components: {
        CadenceEmailHeaderFooterConfig,
        CadenceTask, CadenceSMS, CadenceEmail, RoutineConfiguration, OngoingRoutines, Tab
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        currentUser: {
            type: Object,
            default: {}
        },
    },
    data () {
        return {
            tabs: [
                {name: 'Routines', current: true},
                {name: 'Email Header/Footer', current: false},
                {name: 'Email Templates', current: false},
                {name: 'SMS Templates', current: false},
                {name: 'Task Templates', current: false},
            ],
            selectedTabName: 'Routines',
            selectedTabIndex: 0,
        }
    },
    methods: {
        tabSelected(tabName) {
            this.selectedTabName = tabName;
        }
    }
}
</script>

<style scoped>

</style>
