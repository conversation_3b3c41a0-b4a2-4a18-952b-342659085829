import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'prospecting/configuration', 2);
    }

    fetchGlobalConfigs()
    {
        return this.axios().get('/global')
    }

    fetchUserConfigs()
    {
        return this.axios().get('/user')
    }

    fetchProspectingUsers()
    {
        return this.axios().get('/users')
    }

    saveGlobalConfigs(configs)
    {
        return this.axios().patch('/global', {configs})
    }

    saveUserConfigs(config)
    {
        return this.axios().patch(`/user/${config.id}`, {
            configs: config.configurations,
        })
    }

    createUserConfig(config)
    {
        return this.axios().post(`/user`, config)
    }

}
