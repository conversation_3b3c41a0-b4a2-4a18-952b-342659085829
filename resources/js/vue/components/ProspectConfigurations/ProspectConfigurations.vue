<script setup>
import {computed, onMounted, ref} from "vue";
import ToggleSwitch from "../Shared/components/ToggleSwitch.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import MultiSelect from "../Shared/components/MultiSelect.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import BaseTable from "../Shared/components/BaseTable.vue";
import ActionsHandle from "../Shared/components/ActionsHandle.vue";
import Modal from "../Shared/components/Modal.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import ApiService from "./api.js";

const api = ApiService.make()

const props = defineProps({
    darkMode: Boolean,
})

onMounted(() => {
    fetchGlobalConfigs()
    fetchUserConfigs()
})

const fetchGlobalConfigs = () => {
    api.fetchGlobalConfigs().then(res => {
        globalConfigurations.value = res.data.data
    })
}

const fetchingUserConfigs = ref(true)
const fetchUserConfigs = () => {
    fetchingUserConfigs.value = true
    api.fetchUserConfigs().then(res => {
        userConfigurations.value = res.data.data
    }).finally(() => fetchingUserConfigs.value = false)
}

const themeClasses = computed(() => {
    if(props.darkMode === true) {
        return 'bg-dark-module border-dark-border text-slate-50'
    }
    else {
        return 'bg-light-module border-light-border text-slate-900'
    }
})

const globalConfigurations = ref([])
function saveGlobalConfigurations() {
    api.saveGlobalConfigs(globalConfigurations.value)
}

const userConfigurations = ref([])
const userActions = ref([
    {event: 'edit-configurations', name: 'Edit'},
])

const editingUserConfig = ref(null)
function editUserConfig(userConfig) {
    editingUserConfig.value = userConfig
}
function saveUserConfigurations(userConfig) {
    api.saveUserConfigs(userConfig).then(res => editUserConfig(null))
}

const creatingUserConfig = ref(false)
const newUserConfig = ref({
    'user_id': '',
    'role_id': '',
    'configurations': []
});

const prospectUsers = ref([])
const userRoles = ref([])
function setUserRoleOptions(user) {
    newUserConfig.value.role_id = null
    userRoles.value = user.roles ?? []
}

const errors = ref([])
function createNewUserConfig() {
    newUserConfig.value.configurations = JSON.parse(JSON.stringify(globalConfigurations.value))
    creatingUserConfig.value = true
    loadProspectUsers()
}
function cancelCreatingConfig() {
    creatingUserConfig.value = false
}
function saveNewUserConfig() {
    api.createUserConfig({
        'user_id': newUserConfig.value.user_id,
        'role_id': newUserConfig.value.role_id,
        'configs': newUserConfig.value.configurations.map(config => ({
            key: config.key,
            value: config.value
        })),
    }).then(res => cancelCreatingConfig())
    .catch(error => errors.value = error.response.data.errors)
    .finally(() => fetchUserConfigs())
}
function loadProspectUsers() {
    api.fetchProspectingUsers().then(res => {
        if(res.data.data.length) {
            prospectUsers.value = res.data.data
        }
    })
}
</script>

<template>
    <div>
        <div class="px-4 md:px-10 pt-6">
            <h1 class="text-lg font-semibold pb-0 leading-none mb-6" :class="[darkMode ? 'text-slate-50' : 'text-slate-900']">
                Prospect Configurations
            </h1>
        </div>
        <div class="mx-4 mt-4 pb-16 md:mx-10">
            <div>
                <div class="grid gap-6">
                    <div class="border rounded-lg" :class="[themeClasses]">
                        <h3 class="inline-flex items-center font-bold text-sm uppercase text-primary-500 py-5 pl-5 pr-2">
                            <svg class="w-4 mr-2 inline text-primary-500" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.78662 3.74794C5.55494 3.93291 5.33264 4.13149 5.12098 4.34315C4.10189 5.36223 3.38615 6.62794 3.03184 8H3.77783C4.57348 8 5.33654 8.31607 5.89915 8.87868C6.46176 9.44129 6.77783 10.2044 6.77783 11V12C6.77783 12.2652 6.88319 12.5196 7.07072 12.7071C7.25826 12.8946 7.51262 13 7.77783 13C8.57348 13 9.33654 13.3161 9.89915 13.8787C10.4618 14.4413 10.7778 15.2043 10.7778 16V18C11.454 18 12.126 17.9143 12.7778 17.746V16C12.7778 15.2043 13.0939 14.4413 13.6565 13.8787C14.2191 13.3161 14.9822 13 15.7778 13H18.194C18.5795 12.047 18.7778 11.0284 18.7778 10C18.7778 8.9617 18.576 7.94422 18.194 7H17.7778C17.5126 7 17.2583 7.10536 17.0707 7.29289C16.8832 7.48043 16.7778 7.73478 16.7778 8C16.7778 8.79565 16.4618 9.55871 15.8992 10.1213C15.3365 10.6839 14.5735 11 13.7778 11C12.9822 11 12.2191 10.6839 11.6565 10.1213C11.0939 9.55871 10.7778 8.79565 10.7778 8C10.7778 7.73478 10.6725 7.48043 10.4849 7.29289C10.2974 7.10536 10.043 7 9.77783 7H9.27783C8.34957 7 7.45934 6.63125 6.80296 5.97487C6.20527 5.37719 5.84608 4.58558 5.78662 3.74794ZM6.26666 1.07534C5.32907 1.54924 4.46352 2.17218 3.70676 2.92893C2.10538 4.53032 1.10321 6.61891 0.844568 8.84632C0.840368 8.87355 0.837266 8.90115 0.83531 8.92906C0.797184 9.28311 0.777832 9.64056 0.777832 10C0.777832 11.3132 1.03649 12.6136 1.53904 13.8268C2.04158 15.0401 2.77818 16.1425 3.70676 17.0711C4.63535 17.9997 5.73774 18.7362 6.951 19.2388C7.80707 19.5934 8.70651 19.8266 9.62324 19.9331C9.65105 19.9374 9.67923 19.9406 9.70775 19.9426C10.0625 19.9808 10.4197 20 10.7778 20C11.8879 20 12.9888 19.8152 14.0357 19.4544C14.087 19.4408 14.1368 19.4231 14.1846 19.4018C14.3257 19.3507 14.4657 19.2963 14.6047 19.2388C15.8179 18.7362 16.9203 17.9997 17.8489 17.0711C18.6029 16.3171 19.2303 15.4485 19.7086 14.4991C19.7294 14.463 19.7481 14.4254 19.7643 14.3867C19.854 14.2029 19.9382 14.0162 20.0166 13.8268C20.5192 12.6136 20.7778 11.3132 20.7778 10C20.7778 8.46523 20.425 6.96684 19.7643 5.61326C19.7481 5.57456 19.7295 5.53711 19.7087 5.50107C19.234 4.55877 18.6089 3.68898 17.8489 2.92893C15.9735 1.05357 13.43 0 10.7778 0C9.24805 0 7.7544 0.350528 6.40428 1.00711C6.35646 1.02637 6.31048 1.04923 6.26666 1.07534ZM7.77783 2.5838V3.5C7.77783 3.89782 7.93587 4.27936 8.21717 4.56066C8.49848 4.84196 8.88001 5 9.27783 5H9.77783C10.5735 5 11.3365 5.31607 11.8992 5.87868C12.4618 6.44129 12.7778 7.20435 12.7778 8C12.7778 8.26522 12.8832 8.51957 13.0707 8.70711C13.2583 8.89464 13.5126 9 13.7778 9C14.043 9 14.2974 8.89464 14.4849 8.70711C14.6725 8.51957 14.7778 8.26522 14.7778 8C14.7778 7.20435 15.0939 6.44129 15.6565 5.87868C16.0532 5.48201 16.5495 5.20789 17.0866 5.0807C16.8857 4.82303 16.6682 4.57662 16.4347 4.34315C14.9344 2.84285 12.8996 2 10.7778 2C9.73953 2 8.72205 2.20184 7.77783 2.5838ZM17.0228 15H15.7778C15.5126 15 15.2583 15.1054 15.0707 15.2929C14.8832 15.4804 14.7778 15.7348 14.7778 16V16.9282C15.382 16.5794 15.9393 16.1523 16.4347 15.6569C16.6431 15.4485 16.8394 15.2292 17.0228 15ZM8.77783 17.746V16C8.77783 15.7348 8.67248 15.4804 8.48494 15.2929C8.2974 15.1054 8.04305 15 7.77783 15C6.98218 15 6.21912 14.6839 5.65651 14.1213C5.0939 13.5587 4.77783 12.7956 4.77783 12V11C4.77783 10.7348 4.67248 10.4804 4.48494 10.2929C4.2974 10.1054 4.04305 10 3.77783 10H2.77783C2.77783 11.0506 2.98476 12.0909 3.3868 13.0615C3.78883 14.0321 4.37811 14.914 5.12098 15.6569C5.86385 16.3997 6.74576 16.989 7.71636 17.391C8.06223 17.5343 8.41695 17.6528 8.77783 17.746Z" fill="currentColor"/>
                            </svg>
                            Global Configs
                        </h3>
                        <div class="px-5 pb-5">
                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 items-end gap-6">
                                <div v-for="configuration in globalConfigurations" :key="configuration.key">
                                    <p class="font-semibold mb-2 text-sm">{{configuration.display_name}}</p>
                                    <CustomInput v-if="configuration.type === 'input'" v-model="configuration.value" :dark-mode="darkMode"></CustomInput>
                                    <ToggleSwitch v-if="configuration.type === 'toggle'" class="mt-2"  v-model="configuration.value" :dark-mode="darkMode"></ToggleSwitch>
                                    <MultiSelect v-if="configuration.type === 'multiselect'" :selected-ids="configuration.value" :options="configuration.options" :text-place-holder="configuration.placeholder"></MultiSelect>
                                </div>
                            </div>
                            <CustomButton @click="saveGlobalConfigurations()" class="mt-6" :dark-mode="darkMode">Save Global Configs</CustomButton>
                        </div>
                    </div>
                    <div class="border rounded-lg" :class="[themeClasses]">
                        <div class="p-5 flex justify-between items-center">
                            <div>
                                <h3 class="inline-flex items-center font-bold text-sm uppercase text-primary-500 mb-1">
                                    <svg class="inline mr-2 w-3 text-primary-500" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M7 6C7.79565 6 8.55871 5.68393 9.12132 5.12132C9.68393 4.55871 10 3.79565 10 3C10 2.20435 9.68393 1.44129 9.12132 0.87868C8.55871 0.316071 7.79565 0 7 0C6.20435 0 5.44129 0.316071 4.87868 0.87868C4.31607 1.44129 4 2.20435 4 3C4 3.79565 4.31607 4.55871 4.87868 5.12132C5.44129 5.68393 6.20435 6 7 6ZM0 15C-1.36979e-08 14.0807 0.18106 13.1705 0.532843 12.3212C0.884626 11.4719 1.40024 10.7003 2.05025 10.0503C2.70026 9.40024 3.47194 8.88463 4.32122 8.53284C5.1705 8.18106 6.08075 8 7 8C7.91925 8 8.82951 8.18106 9.67878 8.53284C10.5281 8.88463 11.2997 9.40024 11.9497 10.0503C12.5998 10.7003 13.1154 11.4719 13.4672 12.3212C13.8189 13.1705 14 14.0807 14 15H0Z" fill="currentColor"/>
                                    </svg>
                                    User Configs
                                </h3>
                                <p class="text-sm text-slate-500">User Configurations will overwrite Global Configurations.</p>
                            </div>
                            <CustomButton @click="createNewUserConfig()" :dark-mode="darkMode" color="primary-outline" icon>
                                <template #icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-4 mr-1">
                                        <path fill-rule="evenodd" d="M19 5.5a4.5 4.5 0 0 1-4.791 4.49c-.873-.055-1.808.128-2.368.8l-6.024 7.23a2.724 2.724 0 1 1-3.837-3.837L9.21 8.16c.672-.56.855-1.495.8-2.368a4.5 4.5 0 0 1 5.873-4.575c.324.************.752L13.34 4.66a.455.455 0 0 0-.11.494 3.01 3.01 0 0 0 1.617 1.617c.17.07.363.02.493-.111l2.692-2.692c.241-.241.647-.174.752.15.14.435.216.9.216 1.382ZM4 17a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" clip-rule="evenodd" />
                                    </svg>
                                </template>
                                Create User Config
                            </CustomButton>
                        </div>
                        <BaseTable :dark-mode="darkMode" :loading="fetchingUserConfigs">
                            <template #head>
                                <tr>
                                    <th>Name</th>
                                    <th>Role</th>
                                    <th></th>
                                </tr>
                            </template>
                            <template #body>
                                <tr v-for="userConfig in userConfigurations" class="text-sm">
                                    <td>{{userConfig.user.name}}</td>
                                    <td class="text-slate-500">{{userConfig.role.name}}</td>
                                    <td>
                                        <ActionsHandle
                                            width="w-56"
                                            :custom-actions="userActions"
                                            @edit-configurations="editUserConfig(userConfig)"
                                            :dark-mode="darkMode"
                                            :no-custom-action="false"
                                            :no-delete-button="true"
                                            :no-edit-button="true">
                                        </ActionsHandle>
                                    </td>
                                </tr>
                            </template>
                        </BaseTable>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <Modal
        small
        v-if="editingUserConfig !== null"
        :dark-mode="darkMode"
        @close="editUserConfig(null)"
        @confirm="saveUserConfigurations(editingUserConfig)"
        confirm-text="Save User Configurations"
    >
        <template #header>
            <p class="text-lg font-semibold">Editing Configurations for <span class="text-primary-500">{{editingUserConfig.user.name}}</span></p>
        </template>
        <template #content>
            <p class="text-sm text-slate-500 mb-6">User Configurations will overwrite Global Configurations.</p>

            <div class="grid lg:grid-cols-2 gap-6">
                <div v-for="configuration in editingUserConfig.configurations">
                    <p class="font-semibold mb-2 text-sm">{{configuration.display_name}}</p>
                    <CustomInput v-if="configuration.type === 'input'" v-model="configuration.value" :dark-mode="darkMode"></CustomInput>
                    <ToggleSwitch v-if="configuration.type === 'toggle'" class="mt-2"  v-model="configuration.value" :dark-mode="darkMode"></ToggleSwitch>
                    <MultiSelect v-if="configuration.type === 'multiselect'" :selected-ids="configuration.value" :options="configuration.options" :text-place-holder="configuration.placeholder"></MultiSelect>
                </div>
            </div>
        </template>
    </Modal>
    <Modal
        small
        v-if="creatingUserConfig"
        :dark-mode="darkMode"
        @close="cancelCreatingConfig()"
        @confirm="saveNewUserConfig()"
        confirm-text="Save User Configuration"
    >
        <template #header>
            <p class="text-lg font-semibold">Create a User Role Configuration</p>
        </template>
        <template #content>
            <div class="space-y-4">
                <p class="text-xs text-slate-500 mb-10 font-semibold italic text-center">User Configurations will overwrite Global Configurations.</p>

                <div class="flex items-center">
                    <label for="role" class="w-1/3 block mb-1 font-semibold text-md" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']">User</label>
                    <div class="flex flex-col w-full">
                        <Dropdown @change="setUserRoleOptions" v-model="newUserConfig.user_id" :options="prospectUsers" placeholder="Select a user" :darkMode="darkMode"></Dropdown>
                        <p v-if="errors.user_id" class="mt-1 text-xs font-medium" :class="darkMode ? 'text-red-400' : 'text-red-400'">{{ errors.user_id[0] }}</p>
                    </div>
                </div>

                <div class="flex items-center">
                    <label for="role" class="w-1/3 block mb-1 font-semibold text-md" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']">Role</label>
                    <div class="flex flex-col w-full">
                        <Dropdown v-model="newUserConfig.role_id" :options="userRoles" :disabled="userRoles.length === 0" placeholder="Select a role" :darkMode="darkMode"></Dropdown>
                        <p v-if="errors.role" class="mt-1 text-xs font-medium" :class="darkMode ? 'text-red-400' : 'text-red-400'">{{ errors.role[0] }}</p>
                    </div>
                </div>

                <div class="flex flex-col">
                    <label for="configurations" class="w-1/3 block mb-1 font-semibold text-md" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']">Configurations</label>
                    <div class="grid lg:grid-cols-2 gap-6 mt-4">
                        <div v-for="configuration in newUserConfig.configurations" class="mx-2">
                            <p class="font-semibold mb-2 text-sm">{{configuration.display_name}}</p>
                            <CustomInput v-if="configuration.type === 'input'" v-model="configuration.value" :dark-mode="darkMode"></CustomInput>
                            <ToggleSwitch v-if="configuration.type === 'toggle'" class="mt-2"  v-model="configuration.value" :dark-mode="darkMode"></ToggleSwitch>
                            <MultiSelect v-if="configuration.type === 'multiselect'" :selected-ids="configuration.value" :options="configuration.options" :text-place-holder="configuration.placeholder"></MultiSelect>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </Modal>
</template>

<style scoped>

</style>
