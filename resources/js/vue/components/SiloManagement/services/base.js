class BaseApiService {
    constructor(serviceName = "BaseApiService") {
        this.serviceName = serviceName;
    }

    _throwUnimplementedError(fn) {
        throw new Error("Not implemented error. `" + this.serviceName + "::" + fn + "()`");
    }

    getSiloLocationTypes() {
        this._throwUnimplementedError("getSiloLocationTypes");

        return new Promise((resolve, reject) => reject());    }

    getSiloShortcodes() {
        this._throwUnimplementedError("getSiloShortcodes");

        return new Promise((resolve, reject) => reject());    }

    createSiloFromWizard(payload) {
        this._throwUnimplementedError("createSiloFromWizard");

        return new Promise((resolve, reject) => reject());    }

    getCollectionsData(cleanWebsiteUrl) {
        this._throwUnimplementedError("getCollectionsData");

        return new Promise((resolve, reject) => reject());
    }

    getAllSilos() {
        this._throwUnimplementedError("getAllSilos");

        return new Promise((resolve, reject) => reject());
    }

    updateSiloLocationPage(payload) {
        this._throwUnimplementedError("updateSiloLocationPage");

        return new Promise((resolve, reject) => reject());    }

    updateSilo(payload) {
        this._throwUnimplementedError("updateSilo");

        return new Promise((resolve, reject) => reject());    }
}

export { BaseApiService };
