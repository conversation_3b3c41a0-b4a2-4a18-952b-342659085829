import {BaseApiService} from "./base";

export default class DummySiloManagementApiService extends BaseApiService {
    constructor(delay = 150) {
        super("DummyApiService");

        this.delay = delay;
    }

    _makeResponse(data) {
        return new Promise((resolve) => {
            setTimeout(() => resolve({data: {data}}), this.delay);
        });
    }

    getSiloLocationTypes() {
        return this._makeResponse({
                "status": 3,
                "silo_location_types": [
                    {
                        "name": "national",
                        "id": 1,
                        "required": true
                    },
                    {
                        "name": "state",
                        "id": 2,
                        "required": false
                    },
                    {
                        "name": "city",
                        "id": 3,
                        "required": false
                    }
                ]
        });
    }

    getSiloShortcodes() {
        return this._makeResponse({
            'status': true,
            "shortcodes": {
                "City": "city",
                "Country": "country",
                "State": "state",
                "Industry": "industry"
            }
        });
    }

    createSiloFromWizard(payload) {
        return this._makeResponse({
            'status': false,
            'message': 'Dummy API, nothing created',
        });    }

    getCollectionsData(cleanWebsiteUrl) {
        const customAxiosInstance = axios.create({
            baseURL: `https://${cleanWebsiteUrl}`,
        });

        return customAxiosInstance.get('/api/silo-management/collection-reference');
    }

    getAllSilos() {
        return this._makeResponse({
            'status': true,
            'silos': [
                {
                    "id": 1,
                    "name": "Whatsit",
                    "root_path": "/pathy-wathy",
                    "collection_handle": "companies_silo",
                    "is_active": 0,
                    "website": {
                        "id": 2,
                        "name": "SolarEstimate"
                    },
                    "industry": {
                        "id": 3,
                        "name": "HVAC"
                    },
                    "flow_id": "TestFlow",
                    "revision_id": "2917ba37-1032-4326-807f-23fdd2827326",
                    "location_silos": [
                        {
                            "id": 1,
                            "is_active": 1,
                            "entry_slug": "entry-slug",
                            "relative_path": "hvac/stuff/hvac/hvac",
                            "location_type": 1,
                            "location": {
                                "id": 0,
                                "parent_id": null,
                                "name": "US"
                            }
                        },
                        {
                            "id": 2,
                            "is_active": 0,
                            "entry_slug": "entry-slug-test22",
                            "relative_path": "/hvac/US/alabama222",
                            "location_type": 2,
                            "location": {
                                "id": 761,
                                "parent_id": 0,
                                "name": "Alabama"
                            }
                        },
                        {
                            "id": 3,
                            "is_active": 1,
                            "entry_slug": "entry-slug-test",
                            "relative_path": "/hvac/US/arkansas/test",
                            "location_type": 2,
                            "location": {
                                "id": 2810,
                                "parent_id": 0,
                                "name": "Arkansas"
                            }
                        },
                        {
                            "id": 4,
                            "is_active": 1,
                            "entry_slug": "entry-slug-test",
                            "relative_path": "/hvac/US/alabama/alexander-city/test",
                            "location_type": 3,
                            "location": {
                                "id": 948,
                                "parent_id": 761,
                                "name": "Alexander City"
                            }
                        },
                        {
                            "id": 5,
                            "is_active": 1,
                            "entry_slug": "entry-slug",
                            "relative_path": "/hvac/US/alabama/addison",
                            "location_type": 3,
                            "location": {
                                "id": 1451,
                                "parent_id": 761,
                                "name": "Addison"
                            }
                        },
                        {
                            "id": 6,
                            "is_active": 1,
                            "entry_slug": "entry-slug",
                            "relative_path": "hvac-alleene-arkansas",
                            "location_type": 3,
                            "location": {
                                "id": 3034,
                                "parent_id": 2810,
                                "name": "Alleene"
                            }
                        },
                        {
                            "id": 7,
                            "is_active": 1,
                            "entry_slug": "entry-slug",
                            "relative_path": "/hvac/US/arkansas/altus",
                            "location_type": 3,
                            "location": {
                                "id": 4588,
                                "parent_id": 2810,
                                "name": "Altus"
                            }
                        }
                    ]
                },
                {
                    "id": 6,
                    "name": "Solar Companies",
                    "root_path": "/solar-companies",
                    "collection_handle": "companies_silo",
                    "is_active": 1,
                    "website": {
                        "id": 13,
                        "name": "SolarReviewsFrontend"
                    },
                    "industry": {
                        "id": 1,
                        "name": "Solar"
                    },
                    "flow_id": null,
                    "revision_id": null,
                    "location_silos": [
                        {
                            "id": 19,
                            "is_active": 1,
                            "entry_slug": "national",
                            "relative_path": "/",
                            "location_type": 1,
                            "location": {
                                "id": 0,
                                "parent_id": null,
                                "name": "US"
                            }
                        }
                    ]
                },
                {
                    "id": 7,
                    "name": "plumbers",
                    "root_path": "/plumber-companies",
                    "collection_handle": "companies_silo",
                    "is_active": 1,
                    "website": {
                        "id": 13,
                        "name": "SolarReviewsFrontend"
                    },
                    "industry": {
                        "id": 9,
                        "name": "Plomberie"
                    },
                    "flow_id": null,
                    "revision_id": null,
                    "location_silos": [
                        {
                            "id": 20,
                            "is_active": 1,
                            "entry_slug": "national",
                            "relative_path": "/",
                            "location_type": 1,
                            "location": {
                                "id": 0,
                                "parent_id": null,
                                "name": "US"
                            }
                        }
                    ]
                },
                {
                    "id": 8,
                    "name": "Flappy",
                    "root_path": "/path",
                    "collection_handle": "my_collection",
                    "is_active": 1,
                    "website": {
                        "id": 11,
                        "name": "Trees"
                    },
                    "industry": {
                        "id": 2,
                        "name": "Roofing"
                    },
                    "flow_id": null,
                    "revision_id": null,
                    "location_silos": [
                        {
                            "id": 21,
                            "is_active": 1,
                            "entry_slug": "sluggy",
                            "relative_path": "",
                            "location_type": 1,
                            "location": {
                                "id": 0,
                                "parent_id": null,
                                "name": "US"
                            }
                        }
                    ]
                },
                {
                    "id": 9,
                    "name": "Stuff",
                    "root_path": "/things",
                    "collection_handle": "colleeofjpdsogjsd",
                    "is_active": 1,
                    "website": {
                        "id": 12,
                        "name": "PagesJaunes"
                    },
                    "industry": {
                        "id": 2,
                        "name": "Roofing"
                    },
                    "flow_id": null,
                    "revision_id": null,
                    "location_silos": [
                        {
                            "id": 22,
                            "is_active": 1,
                            "entry_slug": "slugugugugugugug",
                            "relative_path": "",
                            "location_type": 1,
                            "location": {
                                "id": 0,
                                "parent_id": null,
                                "name": "US"
                            }
                        },
                        {
                            "id": 23,
                            "is_active": 1,
                            "entry_slug": "slugugugugugugug",
                            "relative_path": "roofing/california",
                            "location_type": 2,
                            "location": {
                                "id": 5870,
                                "parent_id": 0,
                                "name": "California"
                            }
                        },
                        {
                            "id": 24,
                            "is_active": 1,
                            "entry_slug": "slugugugugugugug",
                            "relative_path": "roofing/california-agoura-hills",
                            "location_type": 3,
                            "location": {
                                "id": 6368,
                                "parent_id": 5870,
                                "name": "Agoura Hills"
                            }
                        }
                    ]
                },
                {
                    "id": 10,
                    "name": "Testy",
                    "root_path": "/stuff",
                    "collection_handle": "coloofkopgjdo",
                    "is_active": 1,
                    "website": {
                        "id": 2,
                        "name": "SolarEstimate"
                    },
                    "industry": {
                        "id": 5,
                        "name": "Siding"
                    },
                    "flow_id": "TestFlow",
                    "revision_id": "d90ddc5d-933b-4236-9bd2-7a18099a8260",
                    "location_silos": [
                        {
                            "id": 25,
                            "is_active": 1,
                            "entry_slug": "slkdgnlsdg",
                            "relative_path": "siding",
                            "location_type": 1,
                            "location": {
                                "id": 0,
                                "parent_id": null,
                                "name": "US"
                            }
                        }
                    ]
                },
                {
                    "id": 11,
                    "name": "Whatsit",
                    "root_path": "/pathy-wathy",
                    "collection_handle": "companies_silo",
                    "is_active": 1,
                    "website": {
                        "id": 13,
                        "name": "SolarReviewsFrontend"
                    },
                    "industry": {
                        "id": 3,
                        "name": "HVAC"
                    },
                    "flow_id": "TestFlow",
                    "revision_id": "2917ba37-1032-4326-807f-23fdd2827326",
                    "location_silos": [
                        {
                            "id": 26,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "",
                            "location_type": 1,
                            "location": {
                                "id": 0,
                                "parent_id": null,
                                "name": "US"
                            }
                        },
                        {
                            "id": 27,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/arkansas",
                            "location_type": 2,
                            "location": {
                                "id": 0,
                                "parent_id": 0,
                                "name": "US"
                            }
                        }
                    ]
                },
                {
                    "id": 13,
                    "name": "Big Bertha",
                    "root_path": "/site-root-path",
                    "collection_handle": "companies_silo",
                    "is_active": 1,
                    "website": {
                        "id": 13,
                        "name": "SolarReviewsFrontend"
                    },
                    "industry": {
                        "id": 8,
                        "name": "Kitchens"
                    },
                    "flow_id": "Solar_Stuff",
                    "revision_id": "46fe724f-0bcc-4e8f-bfec-f77dc4765d80",
                    "location_silos": [
                        {
                            "id": 32,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens",
                            "location_type": 1,
                            "location": {
                                "id": 0,
                                "parent_id": null,
                                "name": "US"
                            }
                        },
                        {
                            "id": 33,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/arkansas",
                            "location_type": 2,
                            "location": {
                                "id": 2810,
                                "parent_id": 0,
                                "name": "Arkansas"
                            }
                        },
                        {
                            "id": 34,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/indiana",
                            "location_type": 2,
                            "location": {
                                "id": 27524,
                                "parent_id": 0,
                                "name": "Indiana"
                            }
                        },
                        {
                            "id": 35,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/maryland",
                            "location_type": 2,
                            "location": {
                                "id": 38313,
                                "parent_id": 0,
                                "name": "Maryland"
                            }
                        },
                        {
                            "id": 36,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/arkansas/antoine",
                            "location_type": 3,
                            "location": {
                                "id": 3165,
                                "parent_id": 2810,
                                "name": "Antoine"
                            }
                        },
                        {
                            "id": 37,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/arkansas/aubrey",
                            "location_type": 3,
                            "location": {
                                "id": 3731,
                                "parent_id": 2810,
                                "name": "Aubrey"
                            }
                        },
                        {
                            "id": 38,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/indiana/aurora",
                            "location_type": 3,
                            "location": {
                                "id": 27916,
                                "parent_id": 27524,
                                "name": "Aurora"
                            }
                        },
                        {
                            "id": 39,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/indiana/andrews",
                            "location_type": 3,
                            "location": {
                                "id": 29985,
                                "parent_id": 27524,
                                "name": "Andrews"
                            }
                        },
                        {
                            "id": 40,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/indiana/ashley",
                            "location_type": 3,
                            "location": {
                                "id": 29994,
                                "parent_id": 27524,
                                "name": "Ashley"
                            }
                        },
                        {
                            "id": 41,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/maryland/annapolis-junction",
                            "location_type": 3,
                            "location": {
                                "id": 38521,
                                "parent_id": 38313,
                                "name": "Annapolis Junction"
                            }
                        },
                        {
                            "id": 42,
                            "is_active": 1,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/maryland/baltimore",
                            "location_type": 3,
                            "location": {
                                "id": 39079,
                                "parent_id": 38313,
                                "name": "Baltimore"
                            }
                        },
                        {
                            "id": 43,
                            "is_active": 0,
                            "entry_slug": "state",
                            "relative_path": "/kitchens/maryland/accident",
                            "location_type": 3,
                            "location": {
                                "id": 39194,
                                "parent_id": 38313,
                                "name": "Accident"
                            }
                        }
                    ]
                },
            ],
        });
    }

    updateSiloLocationPage(payload) {
        return this._makeResponse({
            'status': false,
            'message': 'Dummy API, no update made',
        });
    }

    updateSilo(payload) {
        return this._makeResponse({
            'status': false,
            'message': 'Dummy API, no update made',
        });
    }

    getFlowRevisionMetadata() {
        return this._makeResponse({
            'status': true,
            "revision_meta": {
                "Solar_Stuff": {
                    "created_at": "2023-07-10T01:02:09.595130Z",
                        "versions": {
                        "production": null
                    },
                    "description": "",
                        "name": "Solar Stuff",
                        "revisions": [
                        {
                            "id": "46fe724f-0bcc-4e8f-bfec-f77dc4765d80",
                            "created_at": "2023-07-24T23:58:09.457437Z",
                            "description": "this is a description of a copy of plib",
                            "type": "variant",
                            "environment": "environments/andrew-development",
                            "parent_revision": "2917ba37-1032-4326-807f-23fdd2827326",
                            "version": "0.1",
                            "revision_id": "46fe724f-0bcc-4e8f-bfec-f77dc4765d80",
                            "updated_at": null,
                            "actioned_by": 2,
                            "name": "copy of plib"
                        },
                        {
                            "id": "9fb7b48f-ded0-497f-b932-bd40ae6c9b77",
                            "created_at": "2023-08-01T00:33:57.713971Z",
                            "description": "test",
                            "type": "variant",
                            "environment": "environments/andrew-development",
                            "parent_revision": "2ee865c6-e73f-4a1e-a289-70fd85c040f6",
                            "revision_id": "9fb7b48f-ded0-497f-b932-bd40ae6c9b77",
                            "version": "1.56",
                            "updated_at": "2023-09-01T01:58:48.232190Z",
                            "actioned_by": 2,
                            "name": "Doggo"
                        }
                    ]
                },
                "TestFlow": {
                    "created_at": "2023-07-05T01:59:14.630409Z",
                        "description": "",
                        "versions": {
                        "production": "d90ddc5d-933b-4236-9bd2-7a18099a8260"
                    },
                    "name": "TestFlow",
                        "revisions": [
                        {
                            "id": "2917ba37-1032-4326-807f-23fdd2827326",
                            "created_at": "2023-07-11T00:13:34.219510Z",
                            "description": "Stuff",
                            "type": "variant",
                            "environment": "environments/andrew-development",
                            "parent_revision": "7d18e741-c981-4ef6-a385-c10c60480b3d",
                            "revision_id": "2917ba37-1032-4326-807f-23fdd2827326",
                            "version": "0.46",
                            "updated_at": "2023-07-18T01:15:19.218439Z",
                            "actioned_by": 2,
                            "name": "Plib"
                        },
                        {
                            "id": "63baf2c6-a43d-4253-9728-8960882e1ca2",
                            "created_at": "2023-07-06T01:29:31.600586Z",
                            "description": "",
                            "type": "variant",
                            "environment": "environments/andrew-development",
                            "parent_revision": "7d18e741-c981-4ef6-a385-c10c60480b3d",
                            "version": "0.1",
                            "revision_id": "63baf2c6-a43d-4253-9728-8960882e1ca2",
                            "updated_at": null,
                            "actioned_by": 2,
                            "name": "Stuff"
                        },
                    ]
                },
                "solar-servicing-and-repair": {
                    "created_at": "2023-07-18T05:18:26.181466Z",
                        "versions": {
                        "production": null
                    },
                    "description": "",
                        "name": "Solar Servicing and Repair",
                        "revisions": []
                },
                "some-test-calculator": {
                    "created_at": "2023-07-10T01:05:32.631139Z",
                        "versions": {
                        "production": null
                    },
                    "description": "sdgsdfhdfh",
                        "name": "Some Test Calculator",
                        "revisions": [
                        {
                            "id": "892e6189-05c7-4617-8cf4-fd66784fbffc",
                            "created_at": "2023-08-02T00:50:30.632920Z",
                            "description": "Describe the things",
                            "type": "variant",
                            "environment": "environments/andrew-development",
                            "parent_revision": null,
                            "revision_id": "892e6189-05c7-4617-8cf4-fd66784fbffc",
                            "version": "0.59",
                            "updated_at": "2023-08-04T00:11:12.166571Z",
                            "actioned_by": 2,
                            "name": "NewVariant"
                        },
                    ]
                }
            }
        });
    }

    getWebsites() {
        return this._makeResponse({
            "status": true,
            "websites": [
                {
                    "id": 1,
                    "name": "SolarReviews",
                    "url": "www.solarreviews.com",
                    "abbreviation": "sr",
                    "cp_domain": null,
                    "created": null
                },
                {
                    "id": 2,
                    "name": "SolarEstimate",
                    "url": "www.solar-estimate.org",
                    "abbreviation": "se",
                    "cp_domain": null,
                    "created": null
                },
                {
                    "id": 3,
                    "name": "Roofing Calculator",
                    "url": "www.roofingcalculator.com",
                    "abbreviation": "rc",
                    "cp_domain": null,
                    "created": null
                },
                {
                    "id": 4,
                    "name": "Solar Power Rocks",
                    "url": "www.solarpowerrocks.com",
                    "abbreviation": "spr",
                    "cp_domain": null,
                    "created": null
                },
                {
                    "id": 5,
                    "name": "Sun Number",
                    "url": "www.sunnumber.com",
                    "abbreviation": "sn",
                    "cp_domain": null,
                    "created": null
                },
                {
                    "id": 6,
                    "name": "Cut My Bill",
                    "url": "www.cutmybill.com",
                    "abbreviation": "cmb",
                    "cp_domain": null,
                    "created": null
                },
                {
                    "id": 7,
                    "name": "Solar Brokers",
                    "url": "http://www.something.com",
                    "abbreviation": "sb",
                    "cp_domain": null,
                    "created": null
                },
                {
                    "id": 8,
                    "name": "Wave Solar",
                    "url": "",
                    "abbreviation": "",
                    "cp_domain": null,
                    "created": null
                },
                {
                    "id": 9,
                    "name": "AndrewEngines",
                    "url": "https://andrew-engines.admin-sr.com",
                    "abbreviation": "engine",
                    "cp_domain": null,
                    "created": "2023-07-17"
                },
                {
                    "id": 10,
                    "name": "Fixr",
                    "url": "www.fixr.com",
                    "abbreviation": "fixr",
                    "cp_domain": null,
                    "created": "2023-07-24"
                },
                {
                    "id": 11,
                    "name": "Trees",
                    "url": "http://iliketrees.org",
                    "abbreviation": "tree",
                    "cp_domain": null,
                    "created": "2023-07-24"
                },
                {
                    "id": 12,
                    "name": "PagesJaunes",
                    "url": "https://demo.fixr.com",
                    "abbreviation": "pj",
                    "cp_domain": null,
                    "created": "2023-09-05"
                },
                {
                    "id": 13,
                    "name": "SolarReviewsFrontend",
                    "url": "https://dev-frontend.admin-sr.com",
                    "abbreviation": "SRF",
                    "cp_domain": null,
                    "created": "2023-09-11"
                }
            ]
        });
    }

    getOdinIndustries() {
        return this._makeResponse({
                "status": true,
                "industries": [
                    {
                        "id": 1,
                        "num_id": 1,
                        "name": "Solar",
                        "slug": "solar",
                        "light_mode_color": null,
                        "dark_mode_color": null,
                        "services": [],
                        "created_at": null
                    },
                    {
                        "id": 2,
                        "num_id": 2,
                        "name": "Roofing",
                        "slug": "roofing",
                        "light_mode_color": null,
                        "dark_mode_color": null,
                        "services": [],
                        "created_at": null
                    },
                    {
                        "id": 3,
                        "num_id": 3,
                        "name": "HVAC",
                        "slug": "hvac",
                        "light_mode_color": null,
                        "dark_mode_color": null,
                        "services": [],
                        "created_at": null
                    },
                    {
                        "id": 4,
                        "num_id": 4,
                        "name": "Windows",
                        "slug": "windows",
                        "light_mode_color": null,
                        "dark_mode_color": null,
                        "services": [],
                        "created_at": null
                    },
                    {
                        "id": 5,
                        "num_id": 5,
                        "name": "Siding",
                        "slug": "siding",
                        "light_mode_color": null,
                        "dark_mode_color": null,
                        "services": [],
                        "created_at": null
                    },
                    {
                        "id": 6,
                        "num_id": 6,
                        "name": "Doors",
                        "slug": "doors",
                        "light_mode_color": null,
                        "dark_mode_color": null,
                        "services": [],
                        "created_at": null
                    },
                    {
                        "id": 7,
                        "num_id": 7,
                        "name": "Bathrooms",
                        "slug": "bathrooms",
                        "light_mode_color": null,
                        "dark_mode_color": null,
                        "services": [],
                        "created_at": null
                    },
                    {
                        "id": 8,
                        "num_id": 8,
                        "name": "Kitchens",
                        "slug": "kitchens",
                        "light_mode_color": null,
                        "dark_mode_color": null,
                        "services": [],
                        "created_at": null
                    },
                    {
                        "id": 9,
                        "num_id": 9,
                        "name": "Plomberie",
                        "slug": "plomberie",
                        "light_mode_color": "#248cdb",
                        "dark_mode_color": "#79e1ff",
                        "services": [],
                        "created_at": "2023-09-05T00:57:30+00:00"
                    }
                ]
            })
    }

    getCountries() {
        return this._makeResponse({
                "status": true,
                "countries": [
                    {
                        "name": "USA",
                        "id": 0
                    }
                ]
        });
    }

    getStates() {
        return this._makeResponse([
                {
                    "id": "alaska",
                    "name": "Alaska",
                    "stateAbbr": "AK",
                    "num_id": 1
                },
                {
                    "id": "alabama",
                    "name": "Alabama",
                    "stateAbbr": "AL",
                    "num_id": 761
                },
                {
                    "id": "arkansas",
                    "name": "Arkansas",
                    "stateAbbr": "AR",
                    "num_id": 2810
                },
                {
                    "id": "arizona",
                    "name": "Arizona",
                    "stateAbbr": "AZ",
                    "num_id": 4795
                },
                {
                    "id": "california",
                    "name": "California",
                    "stateAbbr": "CA",
                    "num_id": 5870
                },
                {
                    "id": "colorado",
                    "name": "Colorado",
                    "stateAbbr": "CO",
                    "num_id": 11022
                },
                {
                    "id": "connecticut",
                    "name": "Connecticut",
                    "stateAbbr": "CT",
                    "num_id": 12550
                },
                {
                    "id": "district-of-columbia",
                    "name": "District of Columbia",
                    "stateAbbr": "DC",
                    "num_id": 13530
                },
                {
                    "id": "delaware",
                    "name": "Delaware",
                    "stateAbbr": "DE",
                    "num_id": 13830
                },
                {
                    "id": "florida",
                    "name": "Florida",
                    "stateAbbr": "FL",
                    "num_id": 14044
                },
                {
                    "id": "georgia",
                    "name": "Georgia",
                    "stateAbbr": "GA",
                    "num_id": 16645
                },
                {
                    "id": "hawaii",
                    "name": "Hawaii",
                    "stateAbbr": "HI",
                    "num_id": 19025
                },
                {
                    "id": "iowa",
                    "name": "Iowa",
                    "stateAbbr": "IA",
                    "num_id": 19349
                },
                {
                    "id": "idaho",
                    "name": "Idaho",
                    "stateAbbr": "ID",
                    "num_id": 22373
                },
                {
                    "id": "illinois",
                    "name": "Illinois",
                    "stateAbbr": "IL",
                    "num_id": 23270
                },
                {
                    "id": "indiana",
                    "name": "Indiana",
                    "stateAbbr": "IN",
                    "num_id": 27524
                },
                {
                    "id": "kansas",
                    "name": "Kansas",
                    "stateAbbr": "KS",
                    "num_id": 30059
                },
                {
                    "id": "kentucky",
                    "name": "Kentucky",
                    "stateAbbr": "KY",
                    "num_id": 32180
                },
                {
                    "id": "louisiana",
                    "name": "Louisiana",
                    "stateAbbr": "LA",
                    "num_id": 34829
                },
                {
                    "id": "massachusetts",
                    "name": "Massachusetts",
                    "stateAbbr": "MA",
                    "num_id": 36572
                },
                {
                    "id": "maryland",
                    "name": "Maryland",
                    "stateAbbr": "MD",
                    "num_id": 38313
                },
                {
                    "id": "maine",
                    "name": "Maine",
                    "stateAbbr": "ME",
                    "num_id": 39810
                },
                {
                    "id": "michigan",
                    "name": "Michigan",
                    "stateAbbr": "MI",
                    "num_id": 41229
                },
                {
                    "id": "minnesota",
                    "name": "Minnesota",
                    "stateAbbr": "MN",
                    "num_id": 44237
                },
                {
                    "id": "missouri",
                    "name": "Missouri",
                    "stateAbbr": "MO",
                    "num_id": 46974
                },
                {
                    "id": "mississippi",
                    "name": "Mississippi",
                    "stateAbbr": "MS",
                    "num_id": 50126
                },
                {
                    "id": "montana",
                    "name": "Montana",
                    "stateAbbr": "MT",
                    "num_id": 51622
                },
                {
                    "id": "north-carolina",
                    "name": "North Carolina",
                    "stateAbbr": "NC",
                    "num_id": 52803
                },
                {
                    "id": "north-dakota",
                    "name": "North Dakota",
                    "stateAbbr": "ND",
                    "num_id": 55506
                },
                {
                    "id": "nebraska",
                    "name": "Nebraska",
                    "stateAbbr": "NE",
                    "num_id": 56710
                },
                {
                    "id": "new-hampshire",
                    "name": "New Hampshire",
                    "stateAbbr": "NH",
                    "num_id": 58480
                },
                {
                    "id": "new-jersey",
                    "name": "New Jersey",
                    "stateAbbr": "NJ",
                    "num_id": 59285
                },
                {
                    "id": "new-mexico",
                    "name": "New Mexico",
                    "stateAbbr": "NM",
                    "num_id": 61196
                },
                {
                    "id": "nevada",
                    "name": "Nevada",
                    "stateAbbr": "NV",
                    "num_id": 62347
                },
                {
                    "id": "new-york",
                    "name": "New York",
                    "stateAbbr": "NY",
                    "num_id": 62817
                },
                {
                    "id": "ohio",
                    "name": "Ohio",
                    "stateAbbr": "OH",
                    "num_id": 68263
                },
                {
                    "id": "oklahoma",
                    "name": "Oklahoma",
                    "stateAbbr": "OK",
                    "num_id": 71906
                },
                {
                    "id": "oregon",
                    "name": "Oregon",
                    "stateAbbr": "OR",
                    "num_id": 73915
                },
                {
                    "id": "pennsylvania",
                    "name": "Pennsylvania",
                    "stateAbbr": "PA",
                    "num_id": 75171
                },
                {
                    "id": "puerto-rico",
                    "name": "Puerto Rico",
                    "stateAbbr": "PR",
                    "num_id": 81024
                },
                {
                    "id": "rhode-island",
                    "name": "Rhode Island",
                    "stateAbbr": "RI",
                    "num_id": 81477
                },
                {
                    "id": "south-carolina",
                    "name": "South Carolina",
                    "stateAbbr": "SC",
                    "num_id": 81713
                },
                {
                    "id": "south-dakota",
                    "name": "South Dakota",
                    "stateAbbr": "SD",
                    "num_id": 83053
                },
                {
                    "id": "tennessee",
                    "name": "Tennessee",
                    "stateAbbr": "TN",
                    "num_id": 84232
                },
                {
                    "id": "texas",
                    "name": "Texas",
                    "stateAbbr": "TX",
                    "num_id": 86210
                },
                {
                    "id": "utah",
                    "name": "Utah",
                    "stateAbbr": "UT",
                    "num_id": 92018
                },
                {
                    "id": "virginia",
                    "name": "Virginia",
                    "stateAbbr": "VA",
                    "num_id": 92893
                },
                {
                    "id": "vermont",
                    "name": "Vermont",
                    "stateAbbr": "VT",
                    "num_id": 95941
                },
                {
                    "id": "washington",
                    "name": "Washington",
                    "stateAbbr": "WA",
                    "num_id": 96841
                },
                {
                    "id": "wisconsin",
                    "name": "Wisconsin",
                    "stateAbbr": "WI",
                    "num_id": 98584
                },
                {
                    "id": "west-virginia",
                    "name": "West Virginia",
                    "stateAbbr": "WV",
                    "num_id": 101061
                },
                {
                    "id": "wyoming",
                    "name": "Wyoming",
                    "stateAbbr": "WY",
                    "num_id": 103456
                }
            ]);
    }

    getCitiesByState() {
        return this._makeResponse([
                {
                    "id": "adona",
                    "num_id": 3268,
                    "name": "Adona"
                },
                {
                    "id": "alexander",
                    "num_id": 3272,
                    "name": "Alexander"
                },
                {
                    "id": "delight",
                    "num_id": 3189,
                    "name": "Delight"
                },
                {
                    "id": "dell",
                    "num_id": 3967,
                    "name": "Dell"
                },
                {
                    "id": "guion",
                    "num_id": 4187,
                    "name": "Guion"
                },
                {
                    "id": "marked-tree",
                    "num_id": 3848,
                    "name": "Marked Tree"
                },
                {
                    "id": "marmaduke",
                    "num_id": 4015,
                    "name": "Marmaduke"
                },
                {
                    "id": "marshall",
                    "num_id": 4369,
                    "name": "Marshall"
                },
                {
                    "id": "marvell",
                    "num_id": 3851,
                    "name": "Marvell"
                },
                {
                    "id": "prescott",
                    "num_id": 3119,
                    "name": "Prescott"
                },
                {
                    "id": "subiaco",
                    "num_id": 4688,
                    "name": "Subiaco"
                },
                {
                    "id": "success",
                    "num_id": 4079,
                    "name": "Success"
                },
                {
                    "id": "sulphur-rock",
                    "num_id": 4266,
                    "name": "Sulphur Rock"
                },
                {
                    "id": "sulphur-springs",
                    "num_id": 4560,
                    "name": "Sulphur Springs"
                },
                {
                    "id": "summers",
                    "num_id": 4563,
                    "name": "Summers"
                },
                {
                    "id": "summit",
                    "num_id": 4419,
                    "name": "Summit"
                },
                {
                    "id": "sweet-home",
                    "num_id": 3644,
                    "name": "Sweet Home"
                },
                {
                    "id": "swifton",
                    "num_id": 4082,
                    "name": "Swifton"
                },
                {
                    "id": "taylor",
                    "num_id": 3131,
                    "name": "Taylor"
                },
                {
                    "id": "tontitown",
                    "num_id": 4566,
                    "name": "Tontitown"
                },
                {
                    "id": "yorktown",
                    "num_id": 2925,
                    "name": "Yorktown"
                }
            ]);
    }}
