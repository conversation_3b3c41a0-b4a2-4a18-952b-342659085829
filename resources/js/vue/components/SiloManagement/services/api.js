import axios from 'axios';
import { BaseApiService } from "./base.js";

export default class SiloManagementApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getSiloLocationTypes() {
        return this.axios().get('/location-types');
    }

    getSiloShortcodes() {
        return this.axios().get('/shortcodes');
    }

    createSiloFromWizard(payload) {
        return this.axios().post('/create-silo-with-locations', payload);
    }

    getCollectionsData(cleanWebsiteUrl) {
        const customAxiosInstance = axios.create({
           baseURL: `https://${cleanWebsiteUrl}`,
        });

        return customAxiosInstance.get('/api/silo-management/collection-reference');
    }

    getAllSilos() {
        return this.axios().get('/all-silos');
    }

    updateSiloLocationPage(payload) {
        return this.axios().patch('/update-location-page', payload);
    }

    updateSilo(payload) {
        return this.axios().patch('/update-silo', payload);
    }

}
