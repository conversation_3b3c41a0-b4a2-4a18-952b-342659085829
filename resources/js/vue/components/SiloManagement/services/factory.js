import SiloManagementApiService from './api.js'
import DummySiloManagementApiService from "./dummy";

export default class SiloManagementApiFactory {
    static makeApiService(driver) {
        switch(driver) {
            case 'api':
                return new SiloManagementApiService('internal-api', 'silo-management', 1);

            case 'dummy':
            default:
                return new DummySiloManagementApiService();
        }
    }
}
