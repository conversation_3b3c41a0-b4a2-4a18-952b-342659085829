import { toRaw } from "vue";
import { slideMap } from "../SiloWizard.vue";

export const transformWizardData = (wizardSiloData) => {
    const clone = JSON.parse(JSON.stringify(toRaw(wizardSiloData)));
    const locationLevel = Math.max(...clone[slideMap.locationType.id].locationTypes);

    return {
        'name'                  : clone[slideMap.siloDetails.id].name,
        'root_path'             : clone[slideMap.siloDetails.id].rootPath,
        'collection_handle'     : clone[slideMap.collectionEntry.id].collectionHandle,
        'entry_slug_country'    : clone[slideMap.collectionEntry.id].entrySlugCountry,
        'entry_slug_state'      : clone[slideMap.collectionEntry.id].entrySlugState,
        'entry_slug_city'       : clone[slideMap.collectionEntry.id].entrySlugCity,
        'relative_path_national': clone[slideMap.collectionEntry.id].relativePathCountry,
        'relative_path_state'   : clone[slideMap.collectionEntry.id].relativePathState,
        'relative_path_city'    : clone[slideMap.collectionEntry.id].relativePathCity,
        'website_id'            : clone[slideMap.website.id].selectedWebsite,
        'industry_id'           : clone[slideMap.industryService.id].selectedIndustry,
        'industry_service_id'   : clone[slideMap.industryService.id].selectedService,
        'location_type'         : locationLevel,
        'is_active'             : true,
        'location_ids'          : [
            ...Object.values(clone[slideMap.location.id].locations.cities ?? {}).reduce((output, cityArray) => [...output, ...cityArray], []),
            ...clone[slideMap.location.id].locations.states,
            clone[slideMap.location.id].locations.country,
        ],
        'flow_id'               : clone[slideMap.website.id].selectedFlowId,
        'revision_id'           : clone[slideMap.website.id].selectedRevisionId,
    }
}
