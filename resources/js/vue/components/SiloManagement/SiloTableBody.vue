<template>
    <div>
        <div v-if="!silos.length" class="mt-4 ml-6">
            <p>No Directories found.</p>
        </div>
        <div v-else
             class="border-y overflow-y-auto divide-y h-[55vh]"
             :class="[darkMode ? 'border-dark-border divide-dark-border bg-dark-background' : 'border-light-border divide-light-border bg-light-background']">
            <div v-for="silo in silos"
                 :key="silo.id"
                 @click="selectSilo(silo.id)"
                 class="cursor-pointer text-sm grid grid-cols-7 gap-x-5 py-3 px-5 group relative transition duration-100 items-center"
                 :class="[selectedSiloId === silo.id ? (darkMode ? 'bg-slate-800 bg-opacity-50 text-primary-500 font-medium' : 'bg-primary-50 text-primary-500 font-medium') : (darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module')]">
                <div class="absolute left-0 h-full w-1" :class="[selectedSiloId === silo.id ? (darkMode ? 'bg-primary-500' : 'bg-primary-500') : (darkMode ? 'bg-slate-600 invisible group-hover:visible' : 'bg-slate-400 invisible group-hover:visible') ]"></div>
                <p class="col-span-2">{{ silo.website?.name }}</p>
                <p class="col-span-2">{{ silo.name }}</p>
                <p class="col-span-2">{{ silo.industry?.name  }}</p>
                <p>{{ silo.is_active ? 'Published' : 'Not Published' }}</p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "SiloTableBody",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        silos: {
            type: Array,
            default: []
        },
    },
    data() {
        return {
            selectedSiloId: null,
        }
    },
    emits: ['selectSilo'],
    methods: {
        selectSilo(siloId) {
            this.selectedSiloId = siloId;
            this.$emit('selectSilo', siloId);
        },
    }
}
</script>

<style scoped>

</style>
