<template>
    <div class="flex flex-col group"
        :class="[
            darkMode ? 'text-slate-50' : 'text-slate-900',
        ]"
    >
        <div class="flex items-center justify-between w-full group-hover:bg-light-background px-3 rounded-lg">
            <div class="flex items-center group-hover:text-primary-500 py-2"
                 :class="[expanded === null ? 'pointer-events-none' : 'cursor-pointer', darkMode ? 'border-dark-border' : 'border-slate-300']"
                @click="expandLocation()"
            >
                <svg class="fill-current" v-if="expanded !== null && !expanded" width="21" height="19" viewBox="0 0 21 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.0732 6.00421H9.07324V9.00421H6.07324V11.0042H9.07324V14.0042H11.0732V11.0042H14.0732V9.00421H11.0732V6.00421Z" />
                    <path d="M18.0732 2.00421H9.48724L7.78024 0.297212C7.68754 0.20417 7.57734 0.130382 7.45601 0.0800968C7.33467 0.0298116 7.20459 0.00402198 7.07324 0.00421247H2.07324C0.970242 0.00421247 0.0732422 0.901212 0.0732422 2.00421V16.0042C0.0732422 17.1072 0.970242 18.0042 2.07324 18.0042H18.0732C19.1762 18.0042 20.0732 17.1072 20.0732 16.0042V4.00421C20.0732 2.90121 19.1762 2.00421 18.0732 2.00421ZM2.07324 16.0042V4.00421H18.0732L18.0752 16.0042H2.07324Z" />
                </svg>

                <svg class="fill-current" v-else-if="expanded !== null && expanded" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 2H9.414L7.707 0.293001C7.61429 0.199958 7.5041 0.126171 7.38276 0.0758854C7.26143 0.0256001 7.13134 -0.000189449 7 1.04767e-06H2C0.897 1.04767e-06 0 0.897001 0 2V16C0 17.103 0.897 18 2 18H18C19.103 18 20 17.103 20 16V4C20 2.897 19.103 2 18 2ZM2 16V4H18L18.002 16H2Z"/>
                    <path d="M5.87399 9H13.874V11H5.87399V9Z"/>
                </svg>
                <p class="text-sm font-medium" :class="[expanded !== null ? 'ml-3' : '']">{{ locationSilo.location.name }} ({{ getLocationType() }})</p>
            </div>
            <div class="cursor-pointer mr-2 flex group-hover:text-primary-500 items-center"
                @click="editLocation"
            >
                <p class="mr-2 text-sm font-medium">Edit</p>
                <svg class="w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                </svg>
            </div>
        </div>
        <div v-if="editing"
            class="p-5 border-l-2 border-b-2 mb-3 ml-2.5 border-primary-500"
        >
            <div class="flex gap-3 items-center">
                <div>
                    <p class="text-sm font-medium mb-1">Entry Slug</p>
                    <div v-if="entryOptions?.length">
                        <Dropdown
                            :dark-mode="darkMode"
                            :options="entryOptions"
                            :selected="entrySlug"
                            v-model="entrySlug"
                        />
                    </div>
                    <div v-else>
                        <CustomInput
                            :dark-mode="darkMode"
                            v-model="entrySlug"
                        />
                    </div>
                </div>
                <div>
                    <p class="text-sm font-medium mb-1">Relative Path</p>
                    <ShortcodeInputCached
                        :dark-mode="darkMode"
                        v-model="relativePath"
                        :all-shortcodes="allShortcodes"
                        :disabled-shortcodes="disabledShortcodes"
                    />
                </div>
                <div class="inline-flex items-center mt-6 gap-2">
                    <p class="text-sm font-medium mb-1">Published</p>
                    <ToggleSwitch
                        class="col-span-2"
                        :dark-mode="darkMode"
                        v-model="isActive"
                    />
                </div>
            </div>
            <div class="flex items-center mx-auto justify-self-center gap-x-3 mt-3">
                <CustomButton
                    :dark-mode="darkMode"
                    color="slate"
                    @click="cancelEditing"
                >
                    Cancel
                </CustomButton>
                <CustomButton
                    :dark-mode="darkMode"
                    @click="saveEditing"
                >
                    Save
                </CustomButton>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import CustomInput from "../Shared/components/CustomInput.vue";
import ToggleSwitch from "../Shared/components/ToggleSwitch.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import ShortcodeInputCached from "../Shared/components/ShortcodeInputCached.vue";

export default defineComponent({
    name: "LocationSiloRow",
    components: { ShortcodeInputCached, Dropdown, CustomButton, ToggleSwitch, CustomInput },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        locationSilo: {
            type: Object,
            default: {},
        },
        expanded: {
            type: Boolean,
            default: false,
        },
        entryOptions: {
            type: Array,
            default: [],
        },
        allShortcodes: {
            type: Array,
            default: [],
        },
        disabledShortcodes: {
            type: Array,
            default: [],
        },
    },
    emits: ['expandLocation', 'updateLocation'],
    beforeMount() {
        this.resetEditing();
    },
    data() {
        return {
            editing: false,
            entrySlug: "",
            relativePath: "",
            isActive: false,
        }
    },
    methods: {
        getLocationType() {
            return this.locationSilo.location_type === 1
                ? 'National'
                : this.locationSilo.location_type === 2
                    ? 'State'
                    : 'City'
        },
        expandLocation() {
            if (this.expanded === null) return;
            this.$emit('expandLocation', this.locationSilo.location?.id, this.locationSilo.location_type);
        },
        editLocation() {
            this.resetEditing();
            this.editing = true;
        },
        cancelEditing() {
            this.editing = false;
            this.resetEditing();
        },
        saveEditing() {
            this.$emit('updateLocation', {
                'id': this.locationSilo.id,
                'entry_slug': this.entrySlug,
                'relative_path': this.relativePath,
                'is_active': this.isActive,
            });
            this.editing = false;
        },
        resetEditing() {
            this.entrySlug = this.locationSilo.entry_slug;
            this.relativePath = this.locationSilo.relative_path;
            this.isActive = !!this.locationSilo.is_active;
        }
    }
})
</script>
