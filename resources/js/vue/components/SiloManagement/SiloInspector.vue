<template>
    <div>
        <div class="h-[56vh] overflow-y-auto pb-32">
            <div class="grid grid-cols-2 items-start gap-5 p-5"
                 :class="[darkMode ? 'text-slate-100 border-dark-border' : 'text-slate-900 border-light-border']"
            >
                <div class="pointer-events-none grayscale-[70%]">
                    <p class="mb-2 text-sm font-semibold">
                        Website
                    </p>
                    <Dropdown
                        :dark-mode="darkMode"
                        :selected="silo.website?.name"
                    />
                </div>
                <div class="pointer-events-none grayscale-[70%]">
                    <p class="mb-2 text-sm font-semibold">
                        Industry
                    </p>
                    <Dropdown
                        :dark-mode="darkMode"
                        :selected="silo.industry?.name"
                    />
                </div>
                <div>
                    <p class="mb-2 text-sm font-semibold">
                        Directory Root Path
                    </p>
                    <CustomInput
                        :dark-mode="darkMode"
                        v-model="siloRootPath"
                    />
                </div>
                <div>
                    <p class="mb-2 text-sm font-semibold">
                        Directory Name
                    </p>
                    <CustomInput
                        :dark-mode="darkMode"
                        v-model="siloName"
                    />
                </div>
                <div>
                    <p class="mb-2 text-sm font-semibold">
                        Flow ID
                    </p>
                    <Dropdown
                        :dark-mode="darkMode"
                        :options="flowOptions"
                        v-model="siloFlowId"
                        :selected="siloFlowId"
                        placeholder="None Selected"
                        @update:model-value="updateRevisionOptions"
                    />
                </div>
                <div>
                    <p class="mb-2 text-sm font-semibold">
                        Revision ID
                    </p>
                    <Dropdown
                        :class="[siloFlowId ? '' : 'pointer-events-none grayscale-[70%] opacity-50']"
                        :dark-mode="darkMode"
                        :options="revisionOptions"
                        placeholder="Automatic"
                        :selected="siloRevisionId"
                        v-model="siloRevisionId"
                    />
                </div>
                <div>
                    <p class="mb-2 text-sm font-semibold">
                        Collection Handle
                    </p>
                    <div v-if="collectionOptions?.length">
                        <Dropdown
                            :dark-mode="darkMode"
                            :options="collectionOptions"
                            :selected="siloCollectionHandle"
                            v-model="siloCollectionHandle"
                            @update:model-value="updateEntryOptions"
                        />
                    </div>
                    <div v-else>
                        <CustomInput
                            v-model="siloCollectionHandle"
                            :dark-mode="darkMode"
                            placeholder="Collection name..."
                        />
                    </div>
                </div>
                <div class="col-span-2">
                    <p class="mb-3 text-sm font-semibold">
                        Directory Location Pages
                    </p>
                    <div>
                        <LocationSiloRow
                            :key="mappedLocations.national.id"
                            :dark-mode="darkMode"
                            :location-silo="mappedLocations.national"
                            :expanded="Object.keys(mappedLocations.states)?.length ? expandedNational : null"
                            :entry-options="entryOptions"
                            :all-shortcodes="shortcodes"
                            :disabled-shortcodes="['city', 'state']"
                            @expand-location="handleExpandLocation"
                            @update-location="handleUpdateLocation"
                        />
                    </div>
                    <div class="border-l border-dashed ml-5" :class="[darkMode ? 'border-dark-border' : 'border-slate-400']" v-if="expandedNational">
                        <div class="ml-4" v-for="state in mappedLocations.states">
                            <LocationSiloRow
                                :key="state.id"
                                :dark-mode="darkMode"
                                :location-silo="state"
                                :expanded="mappedLocations.states[state.location.id].cities?.length ? expandedStates.includes(state.location.id) : null"
                                :entry-options="entryOptions"
                                :all-shortcodes="shortcodes"
                                :disabled-shortcodes="['city']"
                                @expand-location="handleExpandLocation"
                                @update-location="handleUpdateLocation"
                            />
                            <div class="border-l border-dashed ml-5 relative" :class="[darkMode ? 'border-dark-border' : 'border-slate-400']" v-if="expandedStates.includes(state.location.id)">
                                <div v-for="city in state.cities" class="relative pl-5"
                                >
                                    <div class="absolute top-4 left-0 w-5 border-t border-dashed" :class="[darkMode ? 'border-dark-border' : 'border-slate-400']"></div>
                                    <LocationSiloRow
                                        :key="city.id"
                                        :dark-mode="darkMode"
                                        :location-silo="city"
                                        :expanded="null"
                                        :entry-options="entryOptions"
                                        :all-shortcodes="shortcodes"
                                        @update-location="handleUpdateLocation"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="absolute inset-x-0 bottom-0 border-t z-30 p-3 inline-flex gap-3"
             :class="[darkMode ? 'text-slate-100 border-dark-border bg-dark-module' : 'text-slate-900 border-light-border bg-light-module']"
        >
            <CustomButton
                :dark-mode="darkMode"
                @click="resetSilo"
                color="slate-light"
            >
                Cancel
            </CustomButton>
            <CustomButton
                :dark-mode="darkMode"
                @click="updateSilo"
            >
                Save
            </CustomButton>
            <div class="flex items-center gap-x-2 ml-8">
                <ToggleSwitch
                    :dark-mode="darkMode"
                    v-model="siloIsActive"
                />
                <p>Published</p>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import Dropdown from "../Shared/components/Dropdown.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import LocationSiloRow from "./LocationSiloRow.vue";
import LoadingSpinner from "../LeadProcessing/components/LoadingSpinner.vue";
import ToggleSwitch from "../Shared/components/ToggleSwitch.vue";
import CustomButton from "../Shared/components/CustomButton.vue";

export default defineComponent({
    name: "SiloInspector",
    components: { CustomButton, ToggleSwitch, LoadingSpinner, LocationSiloRow, CustomInput, Dropdown },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        silo: {
            type: Object,
            default: {},
        },
        siloApiService: {
            type: Object,
            default: {},
        },
        sharedReferenceData: {
            type: Object,
            default: {},
        },
        websiteData: {
            type: Object,
            default: {},
        },
    },
    emits: ['updateSilo', 'updateLocationSiloPage'],
    data() {
        return {
            mappedLocations: {},
            expandedNational: true,
            expandedStates: [],

            flowOptions: [],
            revisionOptions: [],
            collectionOptions: [],
            entryOptions: [],
            shortcodes: [],

            siloName: "",
            siloRootPath: "",
            siloCollectionHandle: "",
            siloIsActive: true,
            siloFlowId: null,
            siloRevisionId: null,
        }
    },
    beforeMount() {
        this.sortLocationSiloPages();

        this.flowOptions = Object.entries(this.sharedReferenceData.flowRevisionMetadata ?? {}).map(([flowId, flowData]) => {
            return { id: flowId, name: flowData.name };
        });
        if (this.silo.flow_id) {
            this.updateRevisionOptions(this.silo.flow_id);
        }

        const collectionMap = this.websiteData[this.silo.website.id]?.collections ?? null;
        this.collectionOptions = collectionMap
            ? Object.keys(collectionMap).map(collection => ({ name: collection, id: collection }))
            : null;
        if (this.collectionOptions && this.siloCollectionHandle) {
            this.updateEntryOptions();
        }

        this.shortcodes = Object.entries(this.sharedReferenceData.shortcodes).map(([label, value]) => ({ label, value }));
    },
    mounted() {
        this.resetEditFields();
    },
    methods: {
        sortLocationSiloPages() {
            this.mappedLocations = this.silo.location_silos.reduce((output, location) => {
                if (location.location_type === 1) {
                    output.national = location;
                } else if (location.location_type === 2) {
                    output.states[location.location.id] = output.states[location.location.id] ?? { cities: [] };
                    Object.assign(output.states[location.location.id], location)
                } else if (location.location_type === 3) {
                    output.states[location.location.parent_id] = output.states[location.location.parent_id] ?? { cities: [] };
                    output.states[location.location.parent_id].cities.push(location);
                }

                return output;
            }, { national: null, states: {} });
        },
        handleExpandLocation(locationId, locationType) {
            if (locationType === 1) {
                this.expandedNational = !this.expandedNational;
            }
            else if (locationType === 2) {
                let deleted = false;
                for (let i = this.expandedStates.length - 1; i >= 0; i--) {
                    if (locationId === this.expandedStates[i]) {
                        this.expandedStates.splice(i, 1);
                        deleted = true;
                    }
                }
                if (!deleted) this.expandedStates.push(locationId);
            }
        },
        handleUpdateLocation(updatePayload) {
            this.$emit('updateLocationSiloPage', updatePayload);
        },
        resetEditFields() {
            this.siloName = this.silo.name;
            this.siloRootPath = this.silo.root_path;
            this.siloCollectionHandle = this.silo.collection_handle;
            this.siloIsActive = this.silo.is_active;
            this.siloFlowId = this.silo.flow_id;
            this.siloRevisionId = this.silo.revision_id;
        },
        updateSilo() {
            this.$emit('updateSilo', {
                id: this.silo.id,
                name: this.siloName,
                root_path: this.siloRootPath,
                collection_handle: this.siloCollectionHandle,
                is_active: this.siloIsActive,
                flow_id: this.siloFlowId,
                revision_id: this.siloRevisionId,
            });
        },
        resetSilo() {
            this.resetEditFields();
        },
        updateRevisionOptions(flowId = null) {
            flowId = flowId ?? this.siloFlowId;
            if (flowId) {
                const targetFlow = this.sharedReferenceData.flowRevisionMetadata?.[flowId] ?? null;
                const flowRevisionOptions = targetFlow?.revisions.map(revision => {
                    return { id: revision.id, name: revision.name }
                }) ?? [];

                this.revisionOptions = [
                    { id: null, name: 'Automatic' },
                    ...flowRevisionOptions
                ];
            }
            this.siloRevisionId = null;
        },
        updateEntryOptions() {
            const collectionData = this.websiteData[this.silo.website.id]?.collections;
            if (collectionData?.[this.siloCollectionHandle]) {
                this.entryOptions = collectionData[this.siloCollectionHandle].map(entry => ({ name: entry, id: entry }));
            }
        },
    },
    watch: {
        silo: {
            handler() {
                this.sortLocationSiloPages();
            },
            deep: true,
        }
    }
});
</script>
