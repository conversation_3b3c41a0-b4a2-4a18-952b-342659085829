<template>
    <div class="flex flex-col p-6 my-auto border rounded-sm h-full overflow-auto"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
    >
        <div class="text-center">
            <h6 class="text-lg font-bold">Directory Content Handles</h6>
        </div>
        <hr class="my-6 w-5/6 mx-auto opacity-80"
            :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
        />
        <div class="mx-auto flex justify-center items-center max-w-screen-sm">
            <div class="grid gap-y-8 h-full items-center pb-8">
                <div v-if="collectionOptions?.length">
                    <p class="mb-1 font-semibold" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">
                        Please select a Collection handle:
                    </p>
                    <Dropdown
                        :dark-mode="darkMode"
                        :options="collectionOptions"
                        v-model="collectionHandle"
                        @update:model-value="updateEntryOptions"
                    />
                </div>
                <div v-else>
                    <p class="mb-1 font-semibold" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">
                        Please select a Collection handle:
                    </p>
                    <CustomInput
                        v-model="collectionHandle"
                        :dark-mode="darkMode"
                        placeholder="Name..."
                        @keyup.enter.stop="requestNextSlide"
                    />
                </div>

                <!-- ENTRY SLUGS -->
                <div>
                    <p class="mb-1 font-semibold" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">
                        Please select an Entry Slug for each Location Type:
                    </p>
                    <div class="grid grid-cols-2 gap-4 items-end gap-x-6 mx-auto">
                        <div class="grid gap-y-2 items-center w-64">
                            <p class="text-sm font-medium" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">
                                Country
                            </p>
                            <Dropdown
                                v-if="collectionOptions?.length"
                                :dark-mode="darkMode"
                                :options="entryOptions"
                                v-model="entrySlugCountry"
                                :class="[collectionHandle ? '' : 'pointer-events-none grayscale-[50%] opacity-50']"
                                :placeholder="collectionHandle ? 'Select Entry Slug' : 'Choose Collection First'"
                            />
                            <CustomInput
                                v-else
                                v-model="entrySlugCountry"
                                :dark-mode="darkMode"
                                placeholder="entry-slug"
                                @keyup.enter.stop="requestNextSlide"
                            />
                        </div>
                        <div class="grid gap-y-2 items-center w-64"
                             v-if="availableLocationTypes.includes('state')"
                        >
                            <p class="text-sm font-medium" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">
                                State
                            </p>
                            <Dropdown
                                v-if="collectionOptions?.length"
                                :dark-mode="darkMode"
                                :options="entryOptions"
                                v-model="entrySlugState"
                                :class="[collectionHandle ? '' : 'pointer-events-none grayscale-[50%] opacity-50']"
                                :placeholder="collectionHandle ? 'Select Entry Slug' : 'Choose Collection First'"
                            />
                            <CustomInput
                                v-else
                                v-model="entrySlugState"
                                :dark-mode="darkMode"
                                placeholder="entry-slug"
                                @keyup.enter.stop="requestNextSlide"
                            />
                        </div>
                        <div class="grid gap-y-2 items-center w-64"
                             v-if="availableLocationTypes.includes('city')"
                        >
                            <p class="text-sm font-medium" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">
                                City
                            </p>
                            <Dropdown
                                v-if="collectionOptions?.length"
                                :dark-mode="darkMode"
                                :options="entryOptions"
                                v-model="entrySlugCity"
                                :class="[collectionHandle ? '' : 'pointer-events-none grayscale-[50%] opacity-50']"
                                :placeholder="collectionHandle ? 'Select Entry Slug' : 'Choose Collection First'"
                            />
                            <CustomInput
                                v-else
                                v-model="entrySlugCity"
                                :dark-mode="darkMode"
                                placeholder="entry-slug"
                                @keyup.enter.stop="requestNextSlide"
                            />
                        </div>
                    </div>
                </div>


                <!-- RELATIVE PATHS -->
                <div>
                    <p class="mb-1 font-semibold" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">
                        Please enter Relative Path(s):
                    </p>
                    <div class="grid grid-cols-2 gap-4 items-end gap-x-6 mx-auto">
                        <div class="grid gap-y-1 items-center">
                            <p class="text-sm font-medium" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">
                                Country (Optional)
                            </p>
                            <ShortcodeInputCached
                                :dark-mode="darkMode"
                                v-model="relativePathCountry"
                                :all-shortcodes="shortcodes"
                                :disabled-shortcodes="['city', 'state']"
                            />
                        </div>
                        <div class="grid gap-y-1 items-center"
                             v-if="availableLocationTypes.includes('state')"
                        >
                            <p class="text-sm font-medium" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">State</p>
                            <ShortcodeInputCached
                                :dark-mode="darkMode"
                                v-model="relativePathState"
                                :all-shortcodes="shortcodes"
                                :disabled-shortcodes="['city']"
                            />
                        </div>
                        <div class="grid gap-y-1 items-center"
                             v-if="availableLocationTypes.includes('city')"
                        >
                            <p class="text-sm font-medium" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">City</p>
                            <ShortcodeInputCached
                                :dark-mode="darkMode"
                                v-model="relativePathCity"
                                :all-shortcodes="shortcodes"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import CustomInput from "../../Shared/components/CustomInput.vue";
import { slideMap } from "../SiloWizard.vue";
import Dropdown from "../../LeadProcessing/components/Dropdown.vue";
import ShortcodeInputCached from "../../Shared/components/ShortcodeInputCached.vue";

export default {
    name: "SiloCollectionEntrySlide",
    components: {
        Dropdown,
        ShortcodeInputCached,
        CustomInput
    },
    mixins: [],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        siloData: {
            type: Object,
            default: {},
        },
        slideId: {
            type: String,
            default: null,
        },
        referenceData: {
            type: Object,
            default: {},
        },
        storedData: {
            type: Object,
            default: {},
        },
    },
    emits: ['slideError', 'slideProgress', 'siloDataUpdate'],
    expose: ['validate'],
    beforeMount() {
        this.shortcodes = Object.entries(this.referenceData.shortcodes).map(([label, value]) => ({ label, value }));
        this.availableLocationTypes = Object.values(this.referenceData.locationTypes ?? {}).reduce((keyArray, locationType) => {
            return this.siloData[slideMap.locationType.id]?.locationTypes.includes(locationType.id)
                ? [...keyArray, locationType.name]
                : keyArray;
        }, []);
        const collectionMap = this.storedData[slideMap.website.id]?.collections ?? null;
        if (collectionMap) {
            this.collectionOptions = Object.keys(collectionMap).map(collection => ({ name: collection, id: collection }));
        }
    },
    mounted() {
        if (this.siloData[this.slideId]) {
            this.collectionHandle = this.siloData[this.slideId].collectionHandle ?? '';
            this.entrySlugCountry = this.siloData[this.slideId].entrySlugCountry ?? '';
            this.entrySlugState = this.siloData[this.slideId].entrySlugState ?? '';
            this.entrySlugCity = this.siloData[this.slideId].entrySlugCity ?? '';
        }
    },
    data () {
        return {
            collectionHandle: '',
            entrySlugCountry: '',
            entrySlugState: '',
            entrySlugCity: '',
            relativePathCountry: '',
            relativePathState: '',
            relativePathCity: '',
            shortcodes: [],
            slideMap,
            availableLocationTypes: [],
            collectionOptions: [],
            entryOptions: [],
        }
    },
    methods: {
        updateEntryOptions() {
            const collectionData = this.storedData[slideMap.website.id]?.collections;
            if (collectionData?.[this.collectionHandle]) {
                this.entryOptions = collectionData[this.collectionHandle].map(entry => ({ name: entry, id: entry }));
            }
        },
        validate() {
            const errors = [];
            if (!this.collectionHandle) {
                errors.push('Please enter a Collection Handle for this Silo.');
            }
            if (!this.entrySlugCountry) {
                errors.push('Please enter a Country-level Entry Slug for this Silo.');
            }
            if (this.availableLocationTypes.includes('state') && !this.entrySlugState) {
                errors.push('Please select an Entry Slug for State-level pages in this Silo.');
            }
            if (this.availableLocationTypes.includes('city') && !this.entrySlugCity) {
                errors.push('Please select an Entry Slug for City-level pages in this Silo.');
            }

            if (this.availableLocationTypes.includes('state') && !this.relativePathState) {
                errors.push('Please enter a Relative Path for State-level pages in this Silo.');
            }
            if (this.availableLocationTypes.includes('city') && !this.relativePathCity) {
                errors.push('Please enter a Relative Path for City-level pages in this Silo.');
            }

            if (errors.length) {
                this.$emit('slideError', errors.join("\n"));
                return false;
            }
            else {
                this.$emit('siloDataUpdate', this.slideId, {
                    collectionHandle: this.collectionHandle,
                    entrySlugCountry: this.entrySlugCountry,
                    entrySlugState: this.entrySlugState,
                    entrySlugCity: this.entrySlugCity,
                    relativePathCountry: this.relativePathCountry,
                    relativePathState: this.relativePathState,
                    relativePathCity: this.relativePathCity,
                });
                return true;
            }
        },
        requestNextSlide() {
            this.$emit('slideProgress');
        }
    },
}

</script>
