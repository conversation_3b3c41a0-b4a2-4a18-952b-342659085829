<template>
    <div class="flex flex-col p-6 my-auto border rounded-sm h-full overflow-auto"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
    >
        <div class="text-center">
            <h6 class="text-lg font-bold">Directory Locations</h6>
        </div>
        <hr class="my-6 w-5/6 mx-auto opacity-80"
            :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
        />
        <div class="h-full pb-8 mt-2">
            <div v-if="!siloData[slideMap.locationType.id]"
                class="text-center"
            >
                The Location Type slide must be completed before selecting locations.
            </div>
            <div v-else class="flex flex-col gap-y-6 w-full">
                <!-- COUNTRY SELECTOR -->
                <div class="flex flex-col justify-center gap-y-2">
                    <div class="text-primary-500 cursor-pointer flex items-center justify-center"
                        @click="toggleSelector('country')"
                    >
                        <svg class="w-5 transition-all" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                            :class="[expandedSelectors.country ? 'rotate-90' : '']"
                        >
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                        </svg>
                        <p class="ml-2">Country Selection</p>
                    </div>
                    <div class="w-[18rem] mx-auto"
                        v-show="expandedSelectors.country"
                    >
                        <Dropdown
                            :options="referenceData.countries"
                            :dark-mode="darkMode"
                            :selected="selectedCountry"
                            placeholder="Select Country"
                            v-model="selectedCountry"
                            @update:model-value="handleCountrySelection"
                        />
                    </div>
                </div>
                <hr v-if="availableLocationTypes.includes(locationKeys.state)" class="my-2 w-full mx-auto"
                    :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                />

                <!-- STATE SELECTOR -->
                <div class="flex flex-col justify-center gap-y-4"
                    v-if="availableLocationTypes.includes(locationKeys.state)"
                >
                    <div class="cursor-pointer flex items-center justify-center"
                         :class="[selectedCountry === null ? 'text-slate-500 pointer-events-none' : 'text-primary-500']"
                         @click="toggleSelector('state')"
                    >
                        <svg class="w-5 transition-all" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                             :class="[expandedSelectors.state ? 'rotate-90' : '']"
                        >
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                        </svg>
                        <p class="ml-2">State Selection</p>
                    </div>
                    <div v-show="expandedSelectors.state" class="px-10 mx-auto">
                        <LoadingSpinner v-if="loadingStates" />
                        <div v-else-if="availableStates.length">
                            <div class="flex items-center gap-x-8 justify-center py-4">
                                <CustomButton
                                    color="slate-inverse"
                                    :dark-mode="darkMode"
                                    @click="selectAllStates(true)"
                                >
                                    Select All
                                </CustomButton>
                                <CustomButton
                                    color="slate-outline"
                                    :dark-mode="darkMode"
                                    @click="selectAllStates(false)"
                                >
                                    Deselect All
                                </CustomButton>
                            </div>
                            <div class="grid grid-cols-3 gap-x-12 gap-y-2 items-center justify-start my-2">
                                <div v-for="state in availableStates">
                                    <div class="flex items-center border rounded px-5 justify-center gap-x-2 gap-y-1"
                                        :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                                    >
                                        <input class="rounded-sm w-5 h-5 cursor-pointer border mr-auto"
                                               :class="[darkMode ?  'bg-dark-background hover:bg-dark-175 border-blue-400' : 'hover:bg-grey-50 border-grey-200']"
                                               type="checkbox"
                                               :checked="selectedStates.includes(state.num_id)"
                                               @change="handleStateChange(state.num_id)"
                                               :id="`state-${state.num_id}`"
                                        />
                                        <p class="text-sm uppercase p-2"
                                            :class="[availableLocationTypes.includes(locationKeys.city) ? 'text-center' : 'text-right']"
                                        >
                                            {{ state.name }}
                                        </p>
                                        <div class="w-5 ml-auto" v-if="availableLocationTypes.includes(locationKeys.city)">
                                            <div v-if="selectedStates.includes(state.num_id)"
                                                title="City Selection"
                                                 class="text-primary-500 cursor-pointer"
                                                 @click="focusState(state.stateAbbr)"
                                            >
                                                <svg class="w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <hr v-if="availableLocationTypes.includes(locationKeys.city)" class="my-2 w-full mx-auto"
                    :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                />

                <!-- CITY SELECTOR -->
                <div class="flex flex-col justify-center gap-y-4 pb-6"
                     v-if="availableLocationTypes.includes(locationKeys.city)"
                >
                    <div class="cursor-pointer flex items-center justify-center"
                         :class="[selectedStates.length ? 'text-primary-500' : 'text-slate-500 pointer-events-none']"
                         @click="toggleSelector('city')"
                    >
                        <svg class="w-5 transition-all" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                             :class="[expandedSelectors.city ? 'rotate-90' : '']"
                        >
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                        </svg>
                        <p class="ml-2">City Selection</p>
                    </div>
                    <div v-show="expandedSelectors.city" class="px-10 mx-auto">
                        <LoadingSpinner v-if="loadingCities" />
                        <div v-show="!loadingCities">
                            <div class="flex items-center gap-x-8 justify-center">
                                <div class="flex items-center justify-center py-4 gap-x-4">
                                    <p>Currently viewing Cities in: </p>
                                    <div class="w-[18rem]">
                                        <Dropdown
                                            :options="selectedStateOptions"
                                            :dark-mode="darkMode"
                                            :selected="focusedState"
                                            @update:model-value="focusState"
                                        />
                                    </div>
                                </div>
                                <CustomButton
                                    color="slate-inverse"
                                    :dark-mode="darkMode"
                                    @click="selectAllCities(true)"
                                >
                                    Select All
                                </CustomButton>
                                <CustomButton
                                    color="slate-outline"
                                    :dark-mode="darkMode"
                                    @click="selectAllCities(false)"
                                >
                                    Deselect All
                                </CustomButton>
                                <CustomInput
                                    :dark-mode="darkMode"
                                    placeholder="Search..."
                                    :search-icon="true"
                                    v-model="citySearch"
                                    @update:model-value="attemptFilter"
                                    @keyup.enter.stop="filterCities"
                                />
                            </div>
                            <div v-if="focusedState" class="grid md:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-6 gap-x-12 gap-y-2 items-center justify-start my-2">
                                <div v-for="city in filteredCities">
                                    <div class="flex items-center border rounded px-5 justify-center gap-x-2 gap-y-1"
                                         :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                                    >
                                        <input class="rounded-sm w-5 h-5 cursor-pointer border mr-auto"
                                               :class="[darkMode ?  'bg-dark-background hover:bg-dark-175 border-blue-400' : 'hover:bg-grey-50 border-grey-200']"
                                               type="checkbox"
                                               :checked="selectedCities[focusedStateId]?.includes(city.num_id)"
                                               @change="handleCityChange(city.num_id)"
                                               :id="`state-${city.num_id}`"
                                        />
                                        <p class="text-sm uppercase p-2 text-right">{{ city.name }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="py-4 flex items-center justify-center">
                                <CustomButton
                                    @click="focusSelector('state')"
                                    :dark-mode="darkMode"
                                >
                                    Back to State Selection
                                </CustomButton>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Dropdown from "../../Shared/components/Dropdown.vue";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import { nextTick } from "vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import { slideMap } from "../SiloWizard.vue"

export default {
    name: "SiloLocationSlide",
    components: {
        CustomInput,
        CustomButton,
        LoadingSpinner,
        Dropdown
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        slideId: {
            type: String,
            default: null,
        },
        siloData: {
            type: Object,
            default: {},
        },
        referenceData: {
            type: Object,
            default: {}
        },
        storedData: {
            type: Object,
            default: {},
        },
        sharedApiService: {
            type: Object,
            default: {},
        }
    },
    emits: ['slideError', 'slideProgress', 'siloDataUpdate', 'storedDataUpdate'],
    mounted() {
        this.availableLocationTypes = this.referenceData.locationTypes.reduce((output, locationType) => {
            if (!this.siloData['silo-location-type']) return output;
            return this.siloData['silo-location-type'].locationTypes?.includes(locationType.id) || locationType.required
                ? [...output, locationType.name]
                : output;
        }, []);

        if (this.siloData[this.slideId]) {
            this.selectedCountry = this.siloData[this.slideId].locations.country ?? this.selectedCountry;
            this.processStates();
            this.processCities();

            this.selectedCities = {...(this.siloData[this.slideId].locations?.cities ?? this.selectedCities)};
            this.selectedStates = [...(this.siloData[this.slideId].locations?.states ?? this.selectedStates)];

            if (this.availableLocationTypes.includes(this.locationKeys.cities) && this.selectedStates.length) {
                this.focusState(this.selectedStates[0]);
            }
        }
    },
    data () {
        return {
            availableLocationTypes: [],
            slideMap,

            loadingStates: false,
            loadingCities: false,

            selectedCountry: null,
            selectedStates: [],
            selectedCities: {},

            focusedState: null,
            focusedStateId: null,
            currentStateOption: null,

            availableStates: [],
            availableCities: [],
            filteredCities: [],
            filterActive: false,

            expandedSelectors: {
                country: true,
                state: false,
                city: false,
            },

            locationKeys: {
                country: 'national',
                state: 'state',
                city: 'city',
            },

            citySearch: "",
            citySearchDebounce: false,
        }
    },
    computed: {
        selectedStateOptions() {
            return this.availableStates.reduce((output, state) => {
                return this.selectedStates.includes(state.num_id)
                    ? [...output, { name: state.name, id: state.stateAbbr }]
                    : output;
            }, []);
        }
    },
    methods: {
        async handleCountrySelection(newVal, oldVal) {
            if (newVal === oldVal) return;

            if (oldVal !== null) {
                this.selectedStates = [];
                this.selectedCities = {};
            }

            if (this.availableLocationTypes.includes(this.locationKeys.state)) {
                this.loadingStates = true;
                this.focusSelector('state');

                if (this.storedData[this.slideId]?.[this.selectedCountry]?.states?.length) {
                    this.processStates();
                    this.loadingStates = false;
                    return;
                }

                //TODO: These routes are US-locked and will need to be replaced when Countries are available
                this.sharedApiService.getStates(this.selectedCountry).then(async (resp) => {
                    if (resp.data?.data) {
                        this.$emit('storedDataUpdate', this.slideId, [this.selectedCountry], { states: resp.data.data });
                        await nextTick();
                        this.processStates();
                    }
                }).catch(err => {
                    console.error(err);
                }).finally(() => {
                    this.loadingStates = false;
                });
            }
        },
        processStates() {
            this.availableStates = [...this.storedData[this.slideId]?.[this.selectedCountry]?.states ?? []];
        },
        processCities() {
            this.availableCities = [...this.storedData[this.slideId]?.[this.selectedCountry]?.cities?.[this.focusedState] ?? []];
            this.filteredCities = [...this.availableCities];
        },
        handleStateChange(stateId) {
            let deleted = false;
            for (let i = this.selectedStates.length; i >= 0; i--) {
                if (this.selectedStates[i] === stateId) {
                    this.selectedStates.splice(i, 1);
                    if (this.focusedStateId === stateId) this.focusedStateId = null;
                    deleted = true;
                }
            }
            if (!deleted) this.selectedStates.push(stateId);
        },
        handleCityChange(cityId) {
            this.selectedCities[this.focusedStateId] = this.selectedCities[this.focusedStateId] ?? [];
            let deleted = false;
            for (let i = this.selectedCities[this.focusedStateId].length; i >= 0; i--) {
                if (this.selectedCities[this.focusedStateId][i] === cityId) {
                    this.selectedCities[this.focusedStateId].splice(i, 1);
                    deleted = true;
                }
            }
            if (!deleted) this.selectedCities[this.focusedStateId].push(cityId);
        },
        toggleSelector(selector) {
            this.expandedSelectors[selector] = !this.expandedSelectors[selector];
        },
        focusSelector(selector) {
            for (const sel in this.expandedSelectors) {
                this.expandedSelectors[sel] = (sel === selector);
            }
        },
        setFocusedState(stateKey) {
            this.focusedState = stateKey;
            this.focusedStateId = this.availableStates.find(state => state.stateAbbr === stateKey)?.num_id;
            this.currentStateOption = this.selectedStateOptions.find(option => option.id === this.focusedState);
        },
        focusState(stateKey) {
            if (this.availableLocationTypes.includes(this.locationKeys.city)) {
                this.loadingCities = true;
                this.focusSelector('city');

                if (this.storedData[this.slideId]?.[this.selectedCountry]?.cities?.[stateKey]?.length) {
                    this.setFocusedState(stateKey);
                    this.processCities();
                    this.loadingCities = false;
                    return;
                }

                //TODO: These routes are US-locked and will need to be replaced when Countries are available
                this.sharedApiService.getCitiesByState(stateKey).then(async (resp) => {
                    if (resp.data?.data) {
                        this.setFocusedState(stateKey);
                        this.$emit('storedDataUpdate', this.slideId, [this.selectedCountry, 'cities'], { [stateKey]: resp.data.data });
                        await nextTick();
                        this.processCities();
                    }
                }).catch(err => {
                    console.error(err);
                }).finally(() => {
                    this.loadingCities = false;
                });
            }
        },
        selectAllCities(select) {
            if (!this.focusedStateId) return;
            this.selectedCities[this.focusedStateId] = select
                ? [...this.availableCities.map(city => city.num_id)]
                : [];
        },
        selectAllStates(select) {
            this.selectedStates = select
                ? [...this.availableStates.map(state => state.num_id)]
                : [];
        },
        filterCities() {
            clearInterval(this.filterDebounceInterval);
            this.filterDebounceInterval = null;

            const rx = new RegExp(this.citySearch.replace(/[^A-z]/g, ''), 'i');
            this.filteredCities = this.availableCities.filter(city => {
                this.filterActive = true;
                return rx.test(city.name);
            });
        },
        clearFilters() {
            this.filteredCities = this.availableCities;
            this.filterActive = false;
        },
        async attemptFilter() {
            await nextTick();
            if (!this.citySearch && this.filterActive) {
                this.clearFilters();
            }
            else if (this.citySearch.length < 3) {
                return;
            }

            this.filterDebounceTimer = 400;

            if (!this.filterDebounceInterval) {
                const interval = 50;
                this.filterDebounceInterval = setInterval(() => {
                    this.filterDebounceTimer -= interval;
                    if (this.filterDebounceTimer <= 0) {
                        this.filterCities();
                    }
                }, interval);
            }
        },
        validate() {
            const errors = [];

            if (this.availableLocationTypes.includes(this.locationKeys.state) && this.selectedStates.length < 1) {
                errors.push('Please select at least one State.');
            }

            if (errors.length) {
                this.$emit('slideError', errors.join("\n"));
                return false;
            }
            else {
                const payload = {
                    cities: JSON.parse(JSON.stringify(this.selectedCities)),
                    states: [...this.selectedStates],
                    country: this.selectedCountry,
                }
                this.$emit('siloDataUpdate', this.slideId, { locations: payload });

                return true;
            }
        },
    },
}

</script>
