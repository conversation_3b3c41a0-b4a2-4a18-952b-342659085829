<template>
    <div class="flex flex-col p-6 my-auto border rounded-sm h-full overflow-auto"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
    >
        <div class="text-center">
            <h6 class="text-lg font-bold">Directory Location Types</h6>
        </div>
        <hr class="my-6 w-5/6 mx-auto opacity-80"
            :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
        />
        <div class="mx-auto flex justify-center items-center max-w-screen-sm">
            <div class="grid gap-y-6 h-full items-center pb-8">
                <div :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block font-semibold mb-1">
                    Please select the location type(s) for this Silo
                </div>
                <div class="flex items-center"
                     v-for="type in locationTypeOptions"
                     :key="type.id"
                     :class="[type.required || !locationTypeDetails.selectedLocationTypes[type.id - 1] ? 'grayscale-[50%] opacity-50 pointer-events-none' : '']"
                >
                    <input class="rounded-sm w-5 h-5 cursor-pointer border"
                           :class="[darkMode ?  'bg-dark-background hover:bg-dark-175 border-blue-400' : 'hover:bg-grey-50 border-grey-200']"
                           type="checkbox"
                           :checked="type.required || locationTypeDetails.selectedLocationTypes[type.id] || false"
                           :disabled="type.required || !locationTypeDetails.selectedLocationTypes[type.id - 1]"
                           @change="handleInputChange(type.id)"
                           :id="`location-type-${type.id}`"
                    />
                    <label :for="`location-type-${type.id}`" class="ml-4 capitalize">
                        {{ type.name }}
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Dropdown from "../../LeadProcessing/components/Dropdown.vue";

export default {
    name: "SiloLocationTypeSlide",
    components: {
        Dropdown
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        slideId: {
            type: String,
            default: null,
        },
        siloData: {
            type: Object,
            default: {},
        },
        referenceData: {
            type: Object,
            default: {}
        },
    },
    emits: ['slideError', 'slideProgress', 'siloDataUpdate'],
    mounted() {
        this.locationTypeOptions = this.referenceData.locationTypes ?? [];
        this.locationTypeOptions.forEach(option => {
            this.locationTypeDetails.selectedLocationTypes[option.id] = option.required;
        });

        if (this.siloData[this.slideId]) {
            this.siloData[this.slideId].locationTypes?.forEach(id => {
                this.locationTypeDetails.selectedLocationTypes[id] = true;
            });
        }
    },
    data () {
        return {
            locationTypeOptions: [],
            locationTypeDetails: {
                selectedLocationTypes: {},
                selectedIdArray: [],
            },
        }
    },
    methods: {
        validate() {
            const errors = [];

            this.locationTypeDetails.selectedIdArray = Object.entries(this.locationTypeDetails.selectedLocationTypes).reduce((output, [id, value]) => {
                return value ? [parseInt(id), ...output] : output;
            }, []);

            if (!this.locationTypeDetails.selectedIdArray) {
                errors.push('At least the National-level Type must be selected.');
            }

            if (errors.length) {
                this.$emit('slideError', errors.join("\n"));
                return false;
            }
            else {
                this.$emit('siloDataUpdate', this.slideId, {
                    locationTypes: this.locationTypeDetails.selectedIdArray
                });

                return true;
            }
        },
        requestNextSlide() {
            this.$emit('slideProgress');
        },
        handleInputChange(typeId) {
            const targetOption = this.locationTypeOptions.find(option => option.id === typeId);
            if (!targetOption || targetOption.required) return;

            const previousOption = this.locationTypeOptions.find(option => option.id === (typeId -1));
            if (previousOption && !this.locationTypeDetails.selectedLocationTypes[previousOption.id]) {
                return;
            }

            this.locationTypeDetails.selectedLocationTypes[typeId] = !this.locationTypeDetails.selectedLocationTypes[typeId];

            if (!this.locationTypeDetails.selectedLocationTypes[typeId]) {
                for (const id in this.locationTypeDetails.selectedLocationTypes) {
                    if (id > typeId) this.locationTypeDetails.selectedLocationTypes[id] = false;
                }
            }
        }
    },
}
</script>
