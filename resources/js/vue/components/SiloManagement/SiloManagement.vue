<template>
    <div class="rounded-lg border"
        :class="[darkMode ? 'text-white border-dark-border bg-dark-module' : 'text-black border-light-border bg-light-module']"
    >
        <Transition name="tab" mode="out-in">
            <LoadingSpinner v-if="loading" class="mt-24"/>
            <SiloViewer
                v-else-if="activeTab === tabNames.siloViewer"
                :dark-mode="darkMode"
                :silo-api-service="siloApiService"
                :shared-reference-data="sharedReferenceData"
                @open-wizard="selectTab(tabNames.siloWizard)"
            />
            <SiloWizard
                v-else-if="activeTab === tabNames.siloWizard"
                :dark-mode="darkMode"
                :silo-api-service="siloApiService"
                :shared-api-service="sharedApiService"
                :shared-reference-data="sharedReferenceData"
                @close-wizard="selectTab(tabNames.siloViewer)"
            />
        </Transition>
    </div>
</template>

<script>
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import Tab from "../Shared/components/Tab.vue";
import SiloViewer from "./SiloViewer.vue";
import SiloWizard from "./SiloWizard.vue";
import SharedApiService from "../Shared/services/api.js";
import LoadingSpinner from "../LeadProcessing/components/LoadingSpinner.vue";
import SiloManagementApiFactory from "./services/factory.js";

export default {
    name: "SiloManagement",
    components: {
        LoadingSpinner,
        SiloWizard,
        SiloViewer,
        Tab,
        AlertsContainer
    },
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        tabNames: {
            type: Object,
            default: {
                siloViewer: 'Silos',
                siloWizard: 'Create Silo',
            },
        },
        apiDriver: {
            type: String,
            default: 'api',
        }
    },
    mounted() {
        this.loadAll();
    },
    data () {
        return {
            siloApiService: SiloManagementApiFactory.makeApiService(this.apiDriver),
            sharedApiService: this.apiDriver === 'api'
                ? SharedApiService.make()
                : SiloManagementApiFactory.makeApiService('dummy'),

            sharedReferenceData: {},

            loading: false,
            saving: false,
            activeTab: this.tabNames.siloViewer,
        }
    },
    methods: {
        selectTab(tabName) {
            this.activeTab = tabName;
        },
        async loadAll() {
            this.loading = true;

            const loadErrors = (await Promise.all([
                this.loadShortcodes(),
                this.loadFlowMetadata(),
                this.loadWebsites(),
            ]))
            .filter(res => res !== true);

            if (loadErrors.length) {
                this.showAlert('error', this.errors.loadError);
                // disable wizard on load errors?
            }

            this.loading = false;
        },
        handleGenericResponse(resp, responseDataKey, referenceKey = null) {
            if (resp.data?.data?.status && responseDataKey in resp.data.data) {
                this.sharedReferenceData[referenceKey ?? responseDataKey] = resp.data.data[responseDataKey];
                return true;
            }
            else {
                this.showAlert('error', resp?.response?.data?.message || resp?.message || this.errors.generic);
            }

            return false;
        },
        async loadShortcodes() {
            const resp = await this.siloApiService.getSiloShortcodes().catch(err => {
                console.error(err);
            });

            return this.handleGenericResponse(resp, 'shortcodes');
        },
        async loadFlowMetadata() {
            const resp = await this.sharedApiService.getFlowRevisionMetadata().catch(err => {
                console.error(err);
            });

            return this.handleGenericResponse(resp, 'revision_meta', 'flowRevisionMetadata');
        },
        async loadWebsites() {
            const resp = await this.sharedApiService.getWebsites().catch(err => {
                console.error(err);
            });

            return this.handleGenericResponse(resp, 'websites');
        },
    },
}

</script>

<style scoped>
.tab-enter-active,
.tab-leave-active {
    opacity: 1;
    transition: opacity 0.25s ease-in 0s;
}

.tab-enter-from,
.tab-leave-to {
    opacity: 0;
    transition: opacity 0.25s 0s;
}

</style>
