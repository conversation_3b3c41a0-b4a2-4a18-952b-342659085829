<template>
    <div class="mt-4 rounded-lg px-5 py-3 text-green-550" :class="[darkMode ? 'bg-dark-background' : 'bg-emerald-50']">
        <loading-spinner v-if="loading || saving" :size="'h-8 w-8'"></loading-spinner>
        <div v-else>
            <div class="flex gap-5 items-center">
                <p class="font-semibold text-sm">Opt In Names:</p>
                <p class="text-sm">{{ this.optInNames.map(name => name.name).join(', ') }}</p>
                <svg class="cursor-pointer text-blue-500" width="11" height="13" viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg" @click="editingNames = true">
                    <path
                        d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z"
                        fill="currentColor"></path>
                </svg>
            </div>
            <div class="flex gap-5 mt-2 items-center" v-if="!updateActive">
                <p class="font-semibold text-sm">Active:</p>
                <p class="text-sm">{{ activeOptInName }}</p>
                <div class="flex gap-3">
                    <svg class="cursor-pointer text-blue-500" width="11" height="13" viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg"
                         @click="updateActive = true">
                        <path
                            d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z"
                            fill="currentColor"></path>
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="cursor-pointer text-red-450" width="15"
                         @click="deleteActiveOptInName">
                        <path stroke-linecap="round" stroke-linejoin="round"
                              d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"/>
                    </svg>
                </div>
            </div>
            <div class="mt-3 flex gap-3" v-else>
                <div class="lg:w-[25rem]">
                    <dropdown :options="optInNames" v-model="selectedActiveOptInName" :placeholder="'Select'" :dark-mode="darkMode"></dropdown>
                </div>
                <custom-button @click="setActiveOptInName" :dark-mode="darkMode">Save</custom-button>
                <custom-button @click="updateActive = false" :color="'primary-outline'" :dark-mode="darkMode">Cancel</custom-button>
            </div>
        </div>
        <modal :dark-mode="darkMode" v-if="editingNames" @close="cancelEdit" :small="true" :close-text="'Cancel'" @confirm="updateOptInNames">
            <template v-slot:header>Add/Update Opt In Names</template>
            <template v-slot:content>
                <div class="flex justify-center items-center border-b">
                    <div class="flex gap-5 mb-5">
                        <custom-input v-model="newOptInName" :dark-mode="darkMode"></custom-input>
                        <custom-button :dark-mode="darkMode" :color="'primary-outline'" @click="addNewOptInName">Add</custom-button>
                    </div>
                </div>
                <div class="flex justify-center items-center flex-col mt-3">
                    <div class="my-2" v-for="name in updatingNames">
                        <custom-input :dark-mode="darkMode" v-model="name.name"></custom-input>
                    </div>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import SharedApiService from "../Shared/services/api.js";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import Modal from "../Shared/components/Modal.vue";
import CustomInput from "../Shared/components/CustomInput.vue";

export default {
    name: "CompanyOptInNames",
    components: {CustomInput, Modal, CustomButton, Dropdown, LoadingSpinner},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        api: {
            type: SharedApiService,
            required: true
        },
        companyId: {
            type: Number,
            required: true
        }
    },
    data() {
        return {
            optInNames: [],
            updatingNames: [],
            activeOptInName: null,
            loading: false,
            saving: false,
            updateActive: false,
            editingNames: false,
            selectedActiveOptInName: null,
            newOptInName: null
        }
    },
    created() {
        this.loadOptInNames();
    },
    methods: {
        loadOptInNames() {
            this.loading = true;
            this.api.getCompanyOptInNames(this.companyId)
                .then(resp => {
                    this.activeOptInName = resp.data.data.active;
                    this.optInNames = resp.data.data.opt_in_names;
                    this.updatingNames = [...this.optInNames];
                })
                .catch(e => console.error(e))
                .finally(() => this.loading = false)
        },
        deleteActiveOptInName() {
            this.saving = true;
            this.api.deleteActiveOptInName(this.companyId)
                .then(resp => this.loadOptInNames())
                .catch(e => console.error(e))
                .finally(() => this.saving = false)
        },
        setActiveOptInName() {
            if (!this.selectedActiveOptInName) {
                return;
            }

            this.saving = true;
            this.api.setActiveOptInName(this.companyId, this.selectedActiveOptInName)
                .then(resp => this.loadOptInNames())
                .catch(e => console.error(e))
                .finally(() => {
                    this.saving = false;
                    this.updateActive = false;
                });
        },
        addNewOptInName() {
            if (!this.newOptInName) {
                return;
            }

            this.updatingNames.unshift({
                id: null,
                name: this.newOptInName
            });
        },
        updateOptInNames() {
            this.saving = true;
            this.editingNames = false;
            this.api.updateCompanyOptInNames(this.companyId, this.updatingNames)
                .then(resp => this.loadOptInNames())
                .catch(e => console.error(e))
                .finally(() => this.saving = false);
        },
        cancelEdit() {
            this.editingNames = false;
            this.newOptInName = null;
            this.updatingNames = [...this.optInNames];
        }
    }
}
</script>
