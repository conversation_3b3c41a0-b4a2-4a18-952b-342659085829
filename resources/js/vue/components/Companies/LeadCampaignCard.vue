<template>
    <div class="border rounded p-4 relative"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="absolute right-0 top-0 mt-2 mr-2">
            <ActionsHandle
                v-if="hasEditRights"
                :dark-mode="darkMode"
                :custom-actions="['Set to Inactive']"
                @edit="$emit('edit', campaign.uuid)"
                @delete="$emit('delete', campaign)"
            />
        </div>
        <h4 class="text-lg font-semibold mb-3 truncate" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">{{ campaign.name }}</h4>
        <div class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap "
             :class="[
                 darkMode ? (campaign.lead_status ? 'text-green-550 border border-green-550' : 'text-red-350 border border-red-350')
                          : (campaign.lead_status ? 'text-green-550 bg-green-150' : 'text-red-350 bg-red-75')]"
        >
            <p v-if="campaign.lead_status" class="text-xs">{{ campaign.appointments_active ? "Leads ": "" }}Active</p>
            <p v-else class="text-xs">{{ campaign.appointments_active ? "Leads ": "" }}Inactive</p>

        </div>
        <badge
            v-if="campaign.status !== 'Active'"
            class="ml-1"
            wrap-style="rounded"
            :dark-mode="darkMode"
            :color="campaign.reactivate_at ? 'amber' :'red'"
        >
            {{campaign.status}}
        </badge>
        <div v-if="campaign.appointments_active" class="ml-2 px-4 mt-3 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
             :class="[
                                     darkMode
                                     ? (campaign.appointment_status ? 'text-green-550 border border-green-550' : 'text-red-350 border border-red-350')
                                     : (campaign.appointment_status ? 'text-green-550 bg-green-150' : 'text-red-350 bg-red-75')]">
            <p v-if="campaign.appointment_status" class="text-xs">Appointments Active</p>
            <p v-else class="text-xs">Appointments Inactive</p>
        </div>
        <div class="mt-3" v-if="campaign.industry">
            <p class="font-semibold" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                Industry & Service
            </p>
            <div :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}">
                <p class="text-sm">{{ campaign.industry }}</p>
                <p class="text-sm">{{ campaign.service }}</p>
            </div>
        </div>
        <div class="mb-3 mt-3">
            <p class="font-semibold" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                Region & Utilities
            </p>
            <div :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}">
                <div class="flex items-center gap-x-3">
                    <p class="text-sm">{{ campaign.zipcode_count }} zip codes in {{ campaign.state_count }} state</p>
                    <Tooltip v-if="campaign.zip_code_targeted" :dark-mode="darkMode">
                        <template v-slot:icon>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z" />
                            </svg>
                        </template>
                        <template v-slot:default>
                            This is a zip code targeted campaign
                        </template>
                    </Tooltip>
                </div>
                <p class="text-sm">{{ campaign.utility_count }} utilities</p>
            </div>
        </div>

        <div class="mb-3">
            <p class="font-semibold" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                Leads Budget
            </p>
            <div>
                <div :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}">
                    <p class="text-sm">Verified Leads: {{ campaign.lead_budget }}</p>
                </div>
                <ul class="text-sm list-disc pl-4" :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}">
                    <template v-for="leadCampaignSalesType in campaign.sales_types">
                        <li v-if="['Exclusive', 'Duo', 'Trio', 'Quad'].includes(leadCampaignSalesType.sales_type)">
                            {{ leadCampaignSalesType.sales_type }}: {{ leadCampaignSalesType.limit }}
                        </li>
                    </template>
                </ul>
                <template v-for="leadCampaignSalesType in campaign.sales_types">
                    <div v-if="['Email Only', 'Unverified'].includes(leadCampaignSalesType.sales_type)" :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}">
                        <p class="text-sm">{{ leadCampaignSalesType.sales_type }}: {{ leadCampaignSalesType.limit }}</p>
                    </div>
                </template>
            </div>
        </div>

        <div v-if="campaign.appointments_active" class="mb-3">
            <p class="font-semibold" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                Appointments Budget
            </p>
            <div>
                <div v-for="budget in campaign.appointment_budgets" :key="budget.type" class="my-2" :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}">
                    <p>{{ budget.type }}</p>
                    <p class="text-sm">Budget: {{ budget.status ? budget.budget : 'Off' }}</p>
                    <p class="text-sm">Usage: {{ budget.budget_usage || 'N/A' }} {{ budget.budget !== 'Unlimited' ? '/ ' + budget.budget_usage_percentage + '%' : ''}} </p>
                    <p class="text-sm">Start: {{ budget.budget_start_time }}</p>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <p class="font-semibold" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                Lead Category
            </p>
            <ul class="text-sm list-disc pl-4" :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}">
                <li v-for="category in campaign.lead_categories">{{ category[0] }}</li>
            </ul>
        </div>
        <div class="flex flex-col text-xs text-slate-500">
            <div v-if="campaign.reactivate_at">Reactivates: {{campaign.reactivate_at}}</div>
        </div>
    </div>
</template>
<script>
import ActionsHandle from "../Shared/components/ActionsHandle.vue";
import Badge from "../Shared/components/Badge.vue";
import Tooltip from "../Shared/components/Tooltip.vue";
import ApiService from "./services/api.js";

export default {
    name: "LeadCampaignCard",
    components: {Tooltip, Badge, ActionsHandle},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            required: true,
        },
        campaign: {
            type: Object,
            required: true
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['edit', 'delete'],
}
</script>
