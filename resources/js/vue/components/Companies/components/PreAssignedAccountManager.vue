<template>
    <div class="flex items-center gap-2" v-if="hasPermission">
        <p class="text-sm">
            <span class="font-semibold mr-2">Pre-assigned AM:</span> {{ companyStore.company.pre_assigned_account_manager?.name ?? 'Not Assigned' }}
        </p>
        <svg class="cursor-pointer" width="11" height="13" viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg" @click="edit">
            <path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z"
                  fill="#0081FF"></path>
        </svg>
        <modal
            :small="true"
            :dark-mode="darkMode"
            @close="close"
            v-if="editAssignment"
            :no-min-height="true"
            @confirm="save"
            :disable-confirm="saving"
        >
            <template v-slot:header>
                <p class="text-lg font-medium">Edit Pre-assignment</p>
            </template>
            <template v-slot:content>
                <div class="min-h-[10rem]">
                    <Dropdown
                        :options="accountManagerOptions"
                        :dark-mode="darkMode"
                        v-model="selectedAccountManager"
                        :disabled="saving"
                    ></Dropdown>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import Modal from "../../Shared/components/Modal.vue";
import {useCompanyStore} from "../../../../stores/company/company.store.js";
import {useCompanyManagersStore} from "../../../../stores/company/company-managers.store.js";
import Dropdown from "../../Shared/components/Dropdown.vue";

export default {
    name: "PreAssignedAccountManager",
    components: {Dropdown, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            permissionStore: useRolesPermissions(),
            companyStore: useCompanyStore(),
            companyMangersStore: useCompanyManagersStore(),
            editAssignment: false,
            selectedAccountManager: 0,
            accountManagerOptions: [{id: 0, name: 'Un Assign'}],
            saving: false
        }
    },
    computed: {
        hasPermission() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_PRE_ASSIGN_AM)
        }
    },
    watch: {
        'companyMangersStore.accountManagers': {
            handler() {
                this.accountManagerOptions = [
                    {id: 0, name: 'Un Assign'},
                    ...this.companyMangersStore.accountManagers
                ];
            },
            deep: true,
        },
    },
    methods: {
        edit() {
            this.editAssignment = true;
            this.selectedAccountManager = this.companyStore.company.pre_assigned_account_manager?.id ?? 0;
        },
        close() {
            this.editAssignment = false;
            this.selectedAccountManager = 0;
        },
        save() {
            this.saving = true;
            this.companyStore.sharedApi.updateAmPreAssignment(this.companyStore.company.id, this.selectedAccountManager || null)
                .then(resp => {
                    this.companyStore.company.pre_assigned_account_manager = resp.data.data.pre_assigned_account_manager;
                    this.close();
                })
                .catch(e => console.error(e))
                .finally(() => {this.saving = false});
        }
    }
}
</script>
