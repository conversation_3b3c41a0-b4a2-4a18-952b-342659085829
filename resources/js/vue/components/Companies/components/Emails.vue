<template>
    <div class="pb-10 h-[60vh]" :class="[darkMode ? 'text-white' : 'text-slate-900']">
        <Mailbox :dark-mode="darkMode" api-driver="api"/>
    </div>
</template>

<script>
import Mailbox from "../../Mailbox/Mailbox.vue";
import {useMailboxStore} from "../../../../stores/mailbox/mailbox";
export default {
    name: "Emails",
    components: {Mailbox},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: false,
        }
    },

    data(){
        return {
            mailboxStore: useMailboxStore()
        }
    },

    created(){
        console.log(this.companyId)
        this.mailboxStore.selectedCompanyId = this.companyId
    }
}
</script>

<style scoped>

</style>
