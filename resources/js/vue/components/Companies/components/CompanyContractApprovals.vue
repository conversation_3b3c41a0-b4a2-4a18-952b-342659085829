<template>
    <div class="border rounded-lg" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="flex items-center p-5 h-14">
            <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">
                Contract Approvals
            </h5>
        </div>

        <div class="grid grid-cols-6 gap-x-3 mb-2 px-5">
            <p class="text-slate-500 font-bold tracking-wide uppercase text-xs">
                Date
            </p>
            <p class="text-slate-500 font-bold tracking-wide uppercase text-xs text-center">
                UserId
            </p>
            <p class="text-slate-500 font-bold tracking-wide uppercase text-xs text-center">
                Ipaddress
            </p>
            <p class="text-slate-500 font-bold tracking-wide uppercase text-xs text-center">
                Activity
            </p>

            <p class="text-slate-500 font-bold tracking-wide uppercase text-xs text-center">
                Firstname
            </p>

            <p class="text-slate-500 font-bold tracking-wide uppercase text-xs text-center">
                Lastname
            </p>
        </div>

        <div class="border-t border-b overflow-y-auto">
            <div v-if="loading" class="flex items-center justify-center h-full">
                <loading-spinner></loading-spinner>
            </div>
            <div v-else class="divide-y" :class="[darkMode ? 'divide-dark-border' : 'divide-light-border']">
                <div
                  v-for="contract in paginationHelper.paginatedData"
                  :key="contract.id"
                  class="grid grid-cols-6 gap-x-3 px-5 py-3 cursor-pointer relative group items-center"
                  :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']"
                >
                    <p class="text-sm font-medium group-hover:text-primary-500 group-hover:font-semibold">
                        {{ contract.date ?? '-'}}
                    </p>
                    <p class="text-sm font-medium group-hover:text-primary-500 text-center">
                        {{ contract.user_id ?? '-'}}
                    </p>
                    <p class="text-sm font-medium group-hover:text-primary-500 text-center">
                        {{ contract.ip_address ?? '-' }}
                    </p>

                    <p class="text-sm font-medium group-hover:text-primary-500 text-center">
                        {{ contract.activity ?? '-' }}
                    </p>

                    <p class="text-sm font-medium group-hover:text-primary-500 text-center">
                        {{ contract.user_first_name ?? '-'}}
                    </p>

                    <p class="text-sm font-medium group-hover:text-primary-500 text-center">
                        {{ contract.user_last_name ?? '-'}}
                    </p>
                </div>


                <div class="p-3">
                    <simple-pagination
                        v-if="paginationHelper.paginatedData?.length > 0"
                        :current-page="paginationHelper.page"
                        :per-page="paginationHelper.perPage"
                        :total="paginationHelper.total"
                        :last-page="paginationHelper.lastPage"
                        :dark-mode="darkMode"
                        :from="paginationHelper.from"
                        :to="paginationHelper.to"
                        @change-page="({newPage}) => paginationHelper.setPage(newPage)"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Alert from "../../Shared/components/Alert.vue";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import SharedApiService from "../../Shared/services/api.js";
import SimplePagination from "../../Shared/components/SimplePagination.vue";
import {usePaginationHelper} from "../../../../composables/usePaginationHelper.js";

export default {
    name: "CompanyContractApprovals",
    components: {SimplePagination, Alert, LoadingSpinner, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            required: true
        },
    },
    data() {
        return {
            loading: false,
            error: null,
            api: SharedApiService.make(),
            paginationHelper: {}
        }
    },
    created() {
        this.getCompanyContract(this.companyId);
    },
    methods: {
        getCompanyContract(companyId) {
            this.loading = true
            this.api.getCompanyContractApprovals(companyId).then(resp => {
                if(resp.data.data.status === true) {
                    this.paginationHelper = usePaginationHelper(resp.data.data.contracts, 1, 5)
                }

            }).catch(e => this.error = e.response.data.message).finally(() => this.loading = false);
        },
    }
}
</script>
