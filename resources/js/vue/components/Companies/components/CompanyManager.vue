<template>
    <div class="flex items-center gap-2" v-if="canView">
        <p class="text-sm">
            <span class="font-semibold mr-2">{{ toTitleCase(role) }}:</span> {{ currentManager?.name ?? 'Not Assigned' }}
        </p>
        <svg class="cursor-pointer" width="11" height="13" viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg" @click="edit" v-if="canEdit">
            <path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z"
                  fill="#0081FF"></path>
        </svg>
        <modal
            :small="true"
            :dark-mode="darkMode"
            @close="close"
            v-if="editAssignment"
            :no-min-height="true"
            @confirm="save"
            :disable-confirm="saving"
        >
            <template v-slot:header>
                <p class="text-lg font-medium">Edit {{ toTitleCase(role) }}</p>
            </template>
            <template v-slot:content>
                <p class="font-semibold text-md mb-7 text-center" v-if="showCommissionableDatePicker">
                    First Lead (MST):
                    <span class="text-amber-500 ml-1">
                        {{ companyStore.company.first_lead_date ? $filters.dateFromTimestamp(companyStore.company.first_lead_date, 'usWithTime', 'America/Denver') : 'No purchase yet' }}
                    </span>
                </p>
                <div class="min-h-[10rem] md:px-5">
                    <div class="flex gap-3 items-center">
                        <p class="w-[18rem] text-sm">Company Manager</p>
                        <Dropdown
                            :options="managerOptions"
                            :dark-mode="darkMode"
                            v-model="selectedManager"
                            :disabled="saving"
                        />
                    </div>
                    <div v-if="datepickerVisible">
                        <div class="mt-5 flex items-center gap-3">
                            <p class="w-[18rem] text-sm">Commissionable From (MST)</p>
                            <Datepicker
                                :dark-mode="darkMode"
                                v-model="commissionableFrom"
                                :dark="darkMode"
                                teleport="body"
                                timezone="America/Denver"
                                :disabled="saving"
                            />
                        </div>
                        <div class="mt-5 flex items-center gap-3">
                            <p class="w-[18rem] text-sm">Commissionable To (MST)</p>
                            <Datepicker
                                :dark-mode="darkMode"
                                v-model="commissionableTo"
                                :dark="darkMode"
                                teleport="body"
                                timezone="America/Denver"
                                :disabled="saving"
                            />
                        </div>
                    </div>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import {useCompanyStore} from "../../../../stores/company/company.store.js";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Modal from "../../Shared/components/Modal.vue";
import {toTitleCase} from "../../../../modules/helpers.js";
import Datepicker from "@vuepic/vue-datepicker";

export default {
    name: "CompanyManager",
    components: {Datepicker, Modal, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        managerOptions: {
            type: Array,
            default: []
        },
        canEdit: {
            type: Boolean,
            default: false
        },
        canView: {
            type: Boolean,
            default: false
        },
        role: {
            type: String,
            required: true
        },
        currentManager: {
            type: [Object, undefined, null],
            required: true
        },
        showCommissionableDatePicker: {
            type: Boolean,
            default: true
        }
    },
    emits: ['company-refreshed'],
    data() {
        return {
            companyStore: useCompanyStore(),
            selectedManager: 0,
            editAssignment: false,
            saving: false,
            commissionableFrom: undefined,
            commissionableTo: undefined,
        }
    },
    mounted() {
        this.selectedManager = this.currentManager?.id ?? 0;
    },
    computed: {
        datepickerVisible() {
            return this.showCommissionableDatePicker && this.selectedManager && this.selectedManager !== this.currentManager?.id;
        }
    },
    methods: {
        toTitleCase,
        edit() {
            this.editAssignment = true;
            this.selectedManager = this.currentManager?.id ?? 0;
            this.commissionableFrom = new Date();
            this.commissionableTo = undefined;
        },
        close() {
            this.editAssignment = false;
            this.selectedManager = 0;
        },
        save() {
            this.saving = true;

            this.companyStore.sharedApi.updateCompanyManagerAssignment(
                this.companyStore.company.id,
                this.role,
                {
                    'manager_id': this.selectedManager === 0 ? null : this.selectedManager, //null value for un-assignment
                    'commissionable_from': this.datepickerVisible ? (this.commissionableFrom || undefined) : undefined,
                    'commissionable_to': this.datepickerVisible ? (this.commissionableTo || undefined) : undefined,
                },
            )
                .then((resp) => {
                    this.$emit('company-refreshed', resp.data.data.company);
                    this.close();
                })
                .catch(e => console.error(e))
                .finally(() => this.saving = false);
        }
    }

}
</script>
