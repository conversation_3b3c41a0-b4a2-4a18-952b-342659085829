<template>
    <div class="grid grid-cols-1 gap-4 pb-10">
        <Leads :company-id="companyId" :dark-mode="darkMode" :show-filters="true"></Leads>
        <div class="col-span-3 border rounded-lg p-5"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="flex items-center justify-between pb-3">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Test Leads</h5>
            </div>
            <test-leads :dark-mode="darkMode" :company-id="companyId"></test-leads>
        </div>
    </div>
</template>

<script>
import Leads from "../../Shared/modules/Leads.vue";
import TestLeads from "./TestLeads.vue";
export default {
    name: "LeadsPage",
    components: {
        TestLeads,
        Leads
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        }
    },
}
</script>

<style scoped>

</style>
