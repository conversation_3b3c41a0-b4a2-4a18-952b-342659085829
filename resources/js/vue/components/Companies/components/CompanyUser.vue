<template>
    <div>
        <div class="px-10 py-5">
            <a href="/company-users" class="text-base text-primary-500 font-medium pb-5 leading-none mr-5 inline-flex items-center">
                <svg class="mr-2" width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.70711 11.7071C6.31658 12.0976 5.68342 12.0976 5.29289 11.7071L0.292894 6.70711C-0.0976305 6.31658 -0.0976304 5.68342 0.292894 5.29289L5.29289 0.292893C5.68342 -0.0976316 6.31658 -0.0976315 6.70711 0.292893C7.09763 0.683417 7.09763 1.31658 6.70711 1.70711L2.41421 6L6.70711 10.2929C7.09763 10.6834 7.09763 11.3166 6.70711 11.7071Z" fill="#0081FF"/>
                </svg>
                Company Users
            </a>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="md:col-span-2 lg:col-span-2">
                    <div class="grid grid-cols-1 gap-6">
                        <CompanyUserInfo
                            :dark-mode="darkMode"
                            :company-user="companyUser"
                            :company-data="companyData"
                        />
                        <CompanyUserDetailedInfo
                            :dark-mode="darkMode"
                            :company-user="companyUser"
                            :company-data="companyData"
                        />
                    </div>
                </div>
                <div>
                    <CompanyUserActivities
                        :dark-mode="darkMode"
                        :company-user="companyUser"
                        :company-data="companyData"
                    />
                </div>
            </div>
        </div>
    </div>
</template>


<script>
import CompanyUserInfo from './CompanyUserComponents/CompanyUserInfo.vue';
import CompanyUserDetailedInfo from './CompanyUserComponents/CompanyUserDetailedInfo.vue';
import CompanyUserActivities from './CompanyUserComponents/CompanyUserActivities.vue';

export default {
    name: "CompanyUser",
    components: {
        CompanyUserInfo,
        CompanyUserDetailedInfo,
        CompanyUserActivities
    },
    mixins: [],
    data() {

    },
    props: {
        companyUser: {
            type: Object,
            default: {},
        },
        companyUserActions: {
            type: Array,
            default: [],
        },
        companyData: {
            type: Object,
            default: {},
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        task: {
            type: Object,
            default: {}
        },
        tableHeight: {
            type: String,
            default: 'h-100'
        }
    },
    created() {

    },
    computed: {

    },
    methods: {

    },
    watch: {

    }
}
</script>




