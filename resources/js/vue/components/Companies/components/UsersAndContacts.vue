<template>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 pb-10">
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>
        <div class="lg:col-span-2">
            <CompaniesUsers
                :has-edit-rights="hasEditRights"
                :dark-mode="darkMode"
                :company-id="companyId"
                :company-dashboard-type="companyDashboardType"
                :configurable-fields="configurableFields"
                @email="email"
            />
        </div>
        <CompanyContacts
            :company-name="companyName" :company-website="companyWebsite" :has-edit-rights="hasEditRights"
            :dark-mode="darkMode"
            :show-add-contact-button="true"
            :company-id="companyId"
            :configurable-fields="configurableFields"
            @email="email"
            @flash-alert="flashAlert"
        />
    </div>

    <email-modal
        :dark-mode="darkMode"
        :email-modal="showEmailModal"
        :recipient-email="toEmail"
        :recipient-id="toId"
        :email-template-type="EMAIL_TEMPLATE_TYPE.PERSONAL"
        @flash-alert="flashAlert"
        @close="showEmailModal = false"
        :company-id="companyId"
    />
</template>

<script>
import CompaniesUsers from "../../Shared/modules/CompaniesUsers.vue";
import CompanyContacts from "../../Shared/modules/Contacts.vue";
import Api from "../../IndustryManagement/ConfigurableFields/services/api";
import EmailModal from "../../Emails/components/EmailModal.vue";
import {EMAIL_TEMPLATE_TYPE} from "../../../../stores/email/email.store.js";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import AlertsMixin from '../../../mixins/alerts-mixin';

export default {
    name: "UsersAndContacts",
    computed: {
        EMAIL_TEMPLATE_TYPE() {
            return EMAIL_TEMPLATE_TYPE
        }
    },
    components: {AlertsContainer, EmailModal, CompanyContacts, CompaniesUsers},
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null
        },
        companyDashboardType: {
            type: String,
            default: null,
        },
        companyName: {
            type: String,
            default: null
        },
        companyWebsite: {
            type: String,
            default: null
        },
    },
    data() {
        return {
            api: Api.make(),
            configurableFields: null,
            showEmailModal: false,
            toEmail: '',
            toId: '',
        }
    },
    created() {
        this.getConfigurableFieldsForCompanyUsers()
    },
    methods: {
        getConfigurableFieldsForCompanyUsers() {
            this.api.getCompanyUserFields().then(res => {
                this.configurableFields = res.data.data.fields
            })
        },
        email(data) {
            this.showEmailModal = true;
            this.toEmail = data.email;
            this.toId = data.id;
        },
        flashAlert (type, message) {
            this.showAlert(type, message);
        }
    },
}
</script>

<style scoped>

</style>
