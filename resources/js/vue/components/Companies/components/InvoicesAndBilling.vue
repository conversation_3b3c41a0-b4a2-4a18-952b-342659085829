<template>
    <div class="grid grid-cols-1 gap-4 pb-10">
        <InvoiceSummary
            :dark-mode="darkMode"
            :company-id="companyId"
        />
        <invoice-management
            v-if="companyBilling.version === companyBilling.VERSIONS.V2"
            :dark-mode="darkMode"
            :company-name="companyName"
            :company-id="companyId"
        />
        <InvoiceBilling :dark-mode="darkMode" :company-id="companyId"/>
    </div>
</template>

<script>
import InvoiceManagement from "../../Shared/modules/Invoices/InvoiceManagement.vue";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import InvoiceBilling from "../../Shared/modules/InvoiceBilling.vue";
import {useCompanyBillingStore} from "../../../../stores/billing/company-billing.js";
import InvoiceSummary from "../../Shared/modules/InvoiceSummary/v1/InvoiceSummary.vue";
export default {
    name: "InvoicesAndBilling",
    components: {InvoiceSummary, InvoiceManagement, InvoiceBilling, ActionsHandle},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            required: true
        },
        companyName: {
            type: String,
            default: null,
        }
    },

    data() {
        return {
            companyBilling: useCompanyBillingStore(),
        }
    },

    mounted() {
        this.companyBilling.getBillingVersion(this.companyId)
    }
}
</script>

<style scoped>

</style>
