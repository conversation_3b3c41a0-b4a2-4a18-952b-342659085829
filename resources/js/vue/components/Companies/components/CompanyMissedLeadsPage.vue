<template>
    <div class="grid grid-cols-1 gap-4 pb-10">
        <CompanyMissedLeads :company-id="companyId" :dark-mode="darkMode" :show-filters="true"></CompanyMissedLeads>
    </div>
</template>

<script>
import CompanyMissedLeads from "../../Shared/modules/CompanyMissedLeads.vue";
export default {
    name: "CompanyMissedLeadsPage",
    components: {
        CompanyMissedLeads
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        }
    },
}
</script>

<style scoped>

</style>
