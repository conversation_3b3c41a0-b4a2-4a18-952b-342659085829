<template>
    <div class="border rounded-lg" :class="[darkMode ? 'min-h-[18rem] bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="flex items-center p-5 h-14">
            <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">
                Zip Code Targeting Exceptions
            </h5>
            <svg v-if="stateSelected" class="mx-2 w-3 flex-shrink-0" width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.293031 9.70698C0.10556 9.51945 0.000244141 9.26514 0.000244141 8.99998C0.000244141 8.73482 0.10556 8.48051 0.293031 8.29298L3.58603 4.99998L0.293031 1.70698C0.110873 1.51838 0.0100779 1.26578 0.0123563 1.00358C0.0146347 0.741382 0.119804 0.49057 0.305212 0.305162C0.49062 0.119753 0.741433 0.0145843 1.00363 0.0123059C1.26583 0.0100274 1.51843 0.110822 1.70703 0.29298L5.70703 4.29298C5.8945 4.48051 5.99982 4.73482 5.99982 4.99998C5.99982 5.26514 5.8945 5.51945 5.70703 5.70698L1.70703 9.70698C1.5195 9.89445 1.26519 9.99977 1.00003 9.99977C0.734866 9.99977 0.480558 9.89445 0.293031 9.70698Z" fill="#1E293B"/>
            </svg>
            <p v-if="stateSelected" class="font-semibold text-sm">{{stateSelected.name }}</p>
        </div>
        <div v-if="loading" class="flex items-center justify-center h-full">
            <loading-spinner></loading-spinner>
        </div>
        <div v-else>
            <div class="pb-4 px-5">
                <p class="font-semibold pb-1">Allow unrestricted zip code targeting</p>
                <ToggleSwitch
                    :model-value="unrestrictedZipCodeTargeting"
                    :dark-mode="darkMode"
                    @update:model-value="handleUnrestrictedZipCodeUpdate"
                />
            </div>
            <div class="grid grid-cols-2 gap-x-3 mb-2 px-5">
                <p class="text-slate-500 font-bold tracking-wide uppercase text-xs">
                    {{ stateSelected ? 'County' : 'State' }}
                </p>
                <p class="text-slate-500 font-bold tracking-wide uppercase text-xs text-center">
                    {{ stateSelected ? 'Allow Targeting' : 'Counties with Exceptions' }}
                </p>
            </div>
            <div class="border-t border-b overflow-y-auto relative"
                 :class="[stateSelected ? 'h-[18rem]' : 'h-[20rem]', darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                <div v-if="unrestrictedZipCodeTargeting"
                     class="absolute bg-opacity-75 z-10 select-none backdrop-blur-sm w-full h-full flex items-center justify-center"
                >
                    <p class="text-lg">Unrestricted Zip Code Targeting</p>
                </div>
                <div class="divide-y"
                     :class="[darkMode ? 'divide-dark-border' : 'divide-light-border', unrestrictedZipCodeTargeting && 'pointer-events-none opacity-50']">
                    <div
                        v-if="!stateSelected"
                        v-for="state in states"
                        :key="state.id"
                        @click="selectState(state)"
                        class="grid grid-cols-2 gap-x-3 px-5 py-3 cursor-pointer relative group items-center"
                        :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']"
                    >
                        <p class="text-sm font-medium group-hover:text-primary-500 group-hover:font-semibold">
                            {{ state.name }}
                        </p>
                        <p class="text-sm font-medium group-hover:text-primary-500 text-center">
                            {{ countiesWithException(state.stateAbbr) }}
                        </p>
                        <div class="absolute right-5">
                            <svg class="fill-current invisible group-hover:visible text-primary-500" width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L6.70711 5.29289C7.09763 5.68342 7.09763 6.31658 6.70711 6.70711L1.70711 11.7071C1.31658 12.0976 0.683417 12.0976 0.292893 11.7071C-0.0976311 11.3166 -0.0976311 10.6834 0.292893 10.2929L4.58579 6L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"/></svg>
                        </div>
                    </div>
                    <div v-else
                         v-for="county in counties"
                         :key="county.id"
                         class="grid grid-cols-2 gap-x-3 px-5 py-3 cursor-pointer relative group items-center"
                         :class="[darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module']">
                        <p class="text-sm font-medium capitalize">{{ county.name }}</p>
                        <div class="flex justify-center">
                            <input v-on:update:modelValue="detectChanges()" v-model="countyExceptionsQueue" :value="county.id" :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']" class="checked:bg-primary-500 focus:checked:bg-primary-500 block focus:ring-0 focus:outline-none rounded-sm cursor-pointer" type="checkbox">
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-3">
                <div v-if="stateSelected" class="flex items-center gap-3">
                    <CustomButton @click="save" :disabled="!changesDetected">
                        Save
                    </CustomButton>
                    <CustomButton @click="cancel" color="slate">
                        Cancel
                    </CustomButton>
                </div>
            </div>
        </div>
        <alert v-if="error !== null" :text="error" :alert-type="'error'" :dark-mode="darkMode"></alert>
    </div>
</template>

<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import SharedApiService from "../../Shared/services/api";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import Alert from "../../Shared/components/Alert.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";

export default {
    name: "ZipcodeTargetingExceptions",
    components: { ToggleSwitch, Alert, LoadingSpinner, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            required: true
        },
    },
    data() {
        return {
            loading: false,
            changesDetected: false,
            stateSelected: null,
            states: [],
            counties: [],
            exceptions: [],
            countyExceptionsQueue: [],
            error: null,
            api: SharedApiService.make(),
            unrestrictedZipCodeTargeting: false,
        }
    },
    created() {
        this.getStates();
        this.getExceptions(this.companyId);
    },
    methods: {
        getStates() {
            this.loading = true
            this.api.getStates().then(resp => {
                this.states = (resp.data.data ?? []).sort((a,b) => a.id > b.id ? 1 : -1);
            }).catch(e => this.error = e.response.data.message).finally(() => this.loading = false);

        },
        selectState(state) {
            this.stateSelected = state
            if(this.exceptions[state.stateAbbr] !== undefined) {
                this.countyExceptionsQueue = this.exceptions[state.stateAbbr]
            }
            else {
                this.countyExceptionsQueue = []
            }
            this.loading = true
            this.api.getCounties(state.id).then(resp => {
                this.counties = (resp.data.data ?? []).sort((a,b) => a.id > b.id ? 1 : -1);
            }).catch(e => this.error = e.response.data.message).finally(() => this.loading = false);
        },
        detectChanges() {
            return this.changesDetected = true
        },
        save() {
            let payload = {};
            payload[this.stateSelected.stateAbbr] = this.countyExceptionsQueue;
            this.changesDetected = false
            this.stateSelected = null
            this.loading = true;
            this.api.updateZipCodeTargetingExceptions(this.companyId ,payload).then(() => {
                this.getExceptions(this.companyId);
                this.resetCountiesAndExceptions();
            }).catch(e => this.error = e.response.data.message);
        },
        cancel() {
            this.changesDetected = false
            this.stateSelected = null
            this.resetCountiesAndExceptions();
        },
        resetCountiesAndExceptions() {
            this.countyExceptionsQueue = []
            this.counties = []
        },
        getExceptions(companyId) {
            this.api.getZipCodeTargetingExceptions(companyId).then(resp => {
                this.exceptions = resp.data.data.exceptions;
                this.unrestrictedZipCodeTargeting = resp.data.data.unrestricted_zip_code_targeting;
            }).catch(e => this.error = e.response.data.message).finally(() => this.loading = false);
        },
        handleUnrestrictedZipCodeUpdate(newValue) {
            if (this.loading)
                return;
            this.loading = true;
            this.api.toggleUnrestrictedZipCodeTargeting(this.companyId, newValue).then(resp => {
                if (resp.data.data.status)
                    this.unrestrictedZipCodeTargeting = !!newValue;
                else
                    this.error = resp.data.data.message ?? "An error occurred";
            }).catch(e => {
               this.error = e.response.data.message ?? e.message;
            }).finally(() => {
                this.loading = false;
            });
        },
    },
    computed: {
        countiesWithException() {
            return stateAbbr => {
                if(this.exceptions[stateAbbr] !== undefined) {
                    return this.exceptions[stateAbbr].length
                }
                else {
                    return 0
                }
            }
        }
    }
}
</script>

<style scoped>

</style>
