<template>
  <div class="grid grid-cols-1 gap-4 px-10 py-5">
    <CompanyContracts :company-id="companyId" :company-name="companyName" :dark-mode="darkMode"></CompanyContracts>
  </div>
</template>


<script>
import CompanyContracts from "../../Shared/modules/CompanyContracts.vue";

export default {
  name: "CompanyContractsPage",
  components: {CompanyContracts},
  props: {
    darkMode: {
      type: Boolean,
      default: false,
    },
    companyId: {
      type: Number,
      default: null
    },
    companyName: {
      type: String,
      default: null
    }
  },
}
</script>
