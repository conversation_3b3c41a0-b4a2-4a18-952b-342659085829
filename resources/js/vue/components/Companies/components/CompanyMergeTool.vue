<template>
    <div class="border rounded-lg" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="flex items-center justify-between p-5 h-14">
            <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">
                Company Merge Tool
            </h5>
            <div class="text-primary-500 hover:text-primary-300 cursor-pointer"
                @click="getPastMerges()"
            >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
                </svg>
            </div>
        </div>
        <div v-if="loading" class="flex items-center justify-center h-full">
            <LoadingSpinner />
        </div>
        <div v-else>
            <Tab
                :dark-mode="darkMode"
                :tabs="tabs"
                @selected="selectTab"
                :tabs-classes="'w-full'"
                tab-type="Normal"
                :default-tab-index="0"
            />
            <div v-if="selectedTab === 'Past Merges'"
                class="border-t border-b overflow-y-auto"
                :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']"
            >
                <div class="px-5 py-3">
                    <div v-if="pastMerges?.length">
                        <div v-for="pastMerge in pastMerges" :key="pastMerge.id"
                            class="grid grid-cols-4 gap-x-2 justify-end items-center"
                        >
                            <div class="col-span-2 justify-self-start flex items-center gap-x-2">
                                <div>{{ pastMerge.source_company_name }} ({{ pastMerge.source_company_id }})</div>
                                <Tooltip
                                    :dark-mode="darkMode"
                                >
                                    <template v-slot:icon>
                                        <div v-if="pastMerge.status === 'error'" class="text-red-600">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                                            </svg>
                                        </div>
                                        <div v-else-if="pastMerge.status === 'completed'">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                            </svg>
                                        </div>
                                        <div v-else class="text-yellow-600">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 7.5h-.75A2.25 2.25 0 0 0 4.5 9.75v7.5a2.25 2.25 0 0 0 2.25 2.25h7.5a2.25 2.25 0 0 0 2.25-2.25v-7.5a2.25 2.25 0 0 0-2.25-2.25h-.75m-6 3.75 3 3m0 0 3-3m-3 3V1.5m6 9h.75a2.25 2.25 0 0 1 2.25 2.25v7.5a2.25 2.25 0 0 1-2.25 2.25h-7.5a2.25 2.25 0 0 1-2.25-2.25v-.75" />
                                            </svg>
                                        </div>
                                    </template>
                                    <template v-slot:default>
                                        <div v-if="pastMerge.status === 'error'">
                                            It looks like something went wrong merging the Leads for this company. Undoing the merge is recommended.
                                        </div>
                                        <div v-else-if="pastMerge.status === 'completed'">
                                            Completed merge {{ (new Date(pastMerge.completed_at)).toLocaleString() }}
                                        </div>
                                        <div v-else-if="pastMerge.status === 'in_progress'" class="text-yellow-600">
                                            The merge record has been created, but Leads are still queued for processing. Refresh the merge list to check if processing has finished.
                                        </div>
                                        <div v-else-if="pastMerge.status === 'undo_in_progress'" class="text-yellow-600">
                                            This merge record has incomplete queued changes to be processed.
                                        </div>
                                    </template>
                                </Tooltip>
                            </div>
                            <div class="text-end">
                                <CustomButton
                                    :dark-mode="darkMode"
                                    @click="viewUndoPayload(pastMerge)"
                                >
                                    View Payload
                                </CustomButton>
                            </div>
                            <div class="text-end">
                                <CustomButton
                                    :disabled="/in_progress/i.test(pastMerge.status)"
                                    :class="[/in_progress/i.test(pastMerge.status) && 'pointer-events-none opacity-50']"
                                    :dark-mode="darkMode"
                                    @click="confirmUndoMerge(pastMerge)"
                                >
                                    Undo Merge
                                </CustomButton>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <p>No inbound merge records found for this company.</p>
                    </div>
                </div>
            </div>
            <div v-else-if="selectedTab === 'New Merge'"
                 class="border-t border-b overflow-y-auto min-h-48"
                 :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']"
            >
                <div class="px-5 py-3">
                    <div class="items-center grid grid-cols-4 gap-x-2 justify-end">
                        <p>Company Search:</p>
                        <div class="col-span-2">
                            <CompanySearchAutocomplete
                                :dark-mode="darkMode"
                                v-model="companySearch"
                                @update:model-value="handleSearchUpdate"
                            />
                        </div>
                        <div class="justify-self-end">
                            <CustomButton
                                :dark-mode="darkMode"
                                :disabled="!companySearch"
                                @click="confirmMergeCompany"
                            >
                                Merge
                            </CustomButton>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <AlertsContainer v-if="alertActive" :text="alertText" :alert-type="alertType" />

        <Modal
            v-if="showModal"
            :dark-mode="darkMode"
            :hide-confirm="modalHideConfirm"
            :close-text="modalCloseText"
            :confirm-text="modalConfirmText"
            @close="handleModalClose"
            @confirm="handleModalConfirm"
        >
            <template v-slot:header>
                <div class="text-primary-500 text-sm uppercase font-bold leading-tight">
                    {{ modalTitle }}
                </div>
            </template>
            <template v-slot:content>
                <LoadingSpinner v-if="loading" />
                <div v-else-if="modalDisplayReports">
                    <div v-for="[reportCategory, databases] in Object.entries(reports ?? {})"
                        class="mb-4"
                    >
                        <div class="cursor-pointer flex items-center gap-x-2 text-primary-500"
                             @click="toggleCategory(reportCategory)"
                        >
                            <div>
                                <svg v-if="categoryExpanded(reportCategory, tableName)" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="size-3">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14" />
                                </svg>

                                <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="size-3">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                </svg>
                            </div>
                            <p class="text-sm uppercase font-bold leading-tight">{{ $filters.toProperCase(reportCategory) }}</p>
                        </div>
                        <div v-if="categoryExpanded(reportCategory)"
                            class=""
                        >
                            <div v-for="[databaseName, tables] in Object.entries(databases)"
                                class="ml-4 mt-2"
                            >
                                <p class="text-sm uppercase font-bold leading-tight">
                                    {{ $filters.toProperCase(databaseName) }}
                                </p>
                                <div v-for="[tableName, tableData] in Object.entries(tables)"
                                    class="ml-4 text-sm py-2"
                                >
                                    <div class="cursor-pointer flex items-center gap-x-2"
                                        @click.stop="toggleCategory(reportCategory, tableName)"
                                    >
                                        <div>
                                            <svg v-if="categoryExpanded(reportCategory, tableName)" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="size-3">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14" />
                                            </svg>

                                            <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="size-3">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                            </svg>
                                        </div>
                                        <div>{{ $filters.toProperCase(tableName) }}</div>
                                    </div>
                                    <div v-if="categoryExpanded(reportCategory, tableName)" class="py-2 border-b border-t mb-2 mt-1"
                                         :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                                    >
                                        <div v-for="item in tableData"
                                            class="w-[92%] mx-auto"
                                        >
                                            <div v-if="/summary$/.test(reportCategory)">
                                                {{ item }}
                                            </div>
                                            <div v-else
                                                 class="flex items-center gap-x-3"
                                            >
                                                <div>{</div>
                                                <div v-for="([key, value], index) in Object.entries(item)"
                                                    class="flex items-center"
                                                >
                                                    <div v-if="index !== 0">, </div>
                                                    <p class="ml-2">{{ key }}:</p>
                                                    <p class="opacity-70 mx-2">{{ value }}</p>
                                                </div>
                                                <div>}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div>{{ modalContent }}</div>
                    <div v-if="undoMergeId || mergeFromId"
                        class="mt-4"
                    >
                        <ToggleSwitch
                            :dark-mode="darkMode"
                            v-model="dryRun"
                            label="Dry Run (report proposed changes only)"
                        />
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../../mixins/alerts-mixin.js";
import Tab from "../../Shared/components/Tab.vue";
import ApiService from "../services/api.js";
import Modal from "../../Shared/components/Modal.vue";
import Tooltip from "../../Shared/components/Tooltip.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import CompanySearchAutocomplete from "../../Shared/components/Company/CompanySearchAutocomplete.vue";

export default {
    name: "CompanyMergeTool",
    components: { CompanySearchAutocomplete, ToggleSwitch, Tooltip, Modal, Tab, AlertsContainer, LoadingSpinner, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null,
        },
    },
    mixins: [AlertsMixin],
    data() {
        return {
            loading: false,
            api: ApiService.make(),
            tabs: [
                {name: 'Past Merges', current: true},
                {name: 'New Merge', current: false},
            ],
            selectedTab: 'Past Merges',
            pastMerges: [],
            reports: {},

            showModal: false,
            modalTitle: '',
            modalContent: '',
            modalHideConfirm: false,
            modalCloseText: 'Close',
            modalConfirmText: 'Save',
            modalDisplayReports: false,

            companySearch: null,
            undoMergeId: null,
            mergeFromId: null,
            dryRun: true,

            expandedCategories: {},
        }
    },
    created() {
        this.initialise();
    },
    methods: {
        initialise() {
            this.getPastMerges();
        },
        categoryExpanded(reportCategory, databaseTable) {
            const reportExpanded = this.expandedCategories[reportCategory]?.expanded ?? false;
            if (!reportExpanded || !databaseTable)
                return reportExpanded;
            else
                return !!this.expandedCategories[reportCategory][databaseTable];
        },
        toggleCategory(reportCategory, databaseTable) {
            if (this.categoryExpanded(reportCategory, databaseTable))
                this.collapseCategory(reportCategory, databaseTable);
            else
                this.expandCategory(reportCategory, databaseTable);
        },
        expandCategory(reportCategory, databaseTable) {
            this.expandedCategories[reportCategory] = this.expandedCategories[reportCategory] ?? {};
            this.expandedCategories[reportCategory].expanded = true;
            if (databaseTable) {
                this.expandedCategories[reportCategory][databaseTable] = true;
            }
        },
        collapseCategory(reportCategory, databaseTable) {
            this.expandedCategories[reportCategory] = this.expandedCategories[reportCategory] ?? {};
            if (!databaseTable) {
                this.expandedCategories[reportCategory].expanded = false;
            }
            else {
                this.expandedCategories[reportCategory][databaseTable] = false;
            }
        },
        getPastMerges() {
            if (this.loading)
                return;

            this.loading = true;
            this.api.getPastMergesForCompany(this.companyId).then(resp => {
                if (resp?.data?.data?.status) {
                    this.pastMerges = resp.data.data.merges ?? [];
                    this.selectTab('Past Merges');
                }
                else {
                    this.showAlert('error', "Error fetching this company's previous merge records");
                }
            }).catch(e => {
                this.showAlert('error', e.response?.data?.message ?? e.message ?? "An error occurred.");
            }).finally(() => {
                this.loading = false;
            });
        },
        mergeQueuedReports(queuedPayload, targetReports) {
            if (queuedPayload.admin2 && targetReports.admin2)
                Object.assign(targetReports.admin2, queuedPayload.admin2);
            if (queuedPayload.legacy && targetReports.legacy)
                Object.assign(targetReports.legacy, queuedPayload.legacy);
        },
        viewUndoPayload(pastMerge) {
            this.reports = { undo_operations: pastMerge.undo_payload };
            if (pastMerge.queued_undo_summary)
                this.mergeQueuedReports(pastMerge.queued_undo_summary, this.reports.undo_operations);
            this.modalTitle = 'Undo Payload';
            this.modalHideConfirm = true;
            this.modalCloseText = 'Ok';
            this.modalDisplayReports = true;
            this.toggleModal(true);
        },
        handleModalConfirm() {
            if (this.undoMergeId)
                this.undoMerge();
            else if (this.mergeFromId)
                this.mergeCompany();
        },
        handleModalClose() {
            if (this.undoMergeId)
                this.cancelUndoMerge();
            else if (this.mergeFromId)
                this.cancelMergeCompany();
            else
                this.closeModal();
        },
        confirmUndoMerge(pastMerge) {
            this.modalContent = `Are you sure you wish to undo the merge of ${pastMerge.source_company_name}?`;
            this.modalTitle = 'Confirm Undo Merge';
            this.modalCloseText = 'Cancel';
            this.modalConfirmText = 'Undo';
            this.undoMergeId = pastMerge.id;
            this.toggleModal(true);
        },
        confirmMergeCompany() {
            this.mergeFromId = this.companySearch;
            this.modalContent = `Are you sure you wish to merge the company ${this.mergeFromId} into ${this.companyId}?`;
            this.modalTitle = 'Confirm Company Merge';
            this.modalCloseText = 'Cancel';
            this.modalConfirmText = 'Merge';
            this.toggleModal(true);
        },
        cancelMergeCompany() {
            this.mergeFromId = null;
            this.closeModal();
        },
        cancelUndoMerge() {
            this.undoMergeId = null;
            this.closeModal();
        },
        undoMerge() {
            if (this.loading)
                return;

            this.loading = true;
            this.api.undoMerge(this.companyId, this.undoMergeId, this.dryRun).then(resp => {
                if (resp?.data?.data?.status) {
                    this.undoMergeId = null;
                    this.pastMerges = resp.data.data.merges ?? this.pastMerges;
                    this.showUndoReport(resp.data.data);
                }
                else {
                    this.showAlert('error', `Error executing Undo operation. ${resp.data.data.message ?? ''}`);
                }
            }).catch(e => {
                this.showAlert('error', e.response?.data?.message ?? e.message ?? "An error occurred.");
            }).finally(() => {
                this.loading = false;
            });
        },
        mergeCompany() {
            if (this.loading)
                return;

            this.loading = true;
            this.api.mergeCompany(this.companyId, this.mergeFromId, this.dryRun).then(resp => {
                if (resp?.data?.data?.status) {
                    this.mergeFromId = null;
                    this.pastMerges = resp.data.data.merges ?? this.pastMerges;
                    this.showMergeReport(resp.data.data, this.dryRun);
                }
                else {
                    this.showAlert('error', `Error executing Merge operation. ${resp.data.data.message ?? ''}`);
                }
            }).catch(e => {
                this.showAlert('error', e.response?.data?.message ?? e.message ?? "An error occurred.");
            }).finally(() => {
                this.loading = false;
            });
        },
        handleSearchUpdate(newValue) {
            this.companySearch = newValue;
        },
        showMergeReport(responseData, dryRun) {
            this.modalTitle = 'Merge Report';
            this.modalHideConfirm = true;
            this.modalCloseText = 'Ok';
            this.reports = dryRun
                ? {
                    summary: responseData.messages,
                    proposed_merge_operations: responseData.changes,
                    undo_operations: responseData.undo,
                } : {
                    merge_completed_summary: responseData.messages,
                };
            this.modalDisplayReports = true;
            this.undoMergeId = null;
        },
        showUndoReport(responseData) {
            this.modalTitle = 'Undo Report';
            this.reports = {
                undo_operations: responseData.undo,
            };
            this.modalDisplayReports = true;
            this.modalHideConfirm = true;
            this.modalCloseText = 'Ok';
            this.undoMergeId = null;
        },
        toggleModal(show) {
            this.showModal = !!show;
        },
        closeModal() {
            this.toggleModal(false);
            this.modalContent = '';
            this.modalTitle = '';
            this.modalCloseText = 'Close';
            this.modalHideConfirm = false;
            this.modalConfirmText = 'Save';
            this.modalDisplayReports = false;
            this.reports = {};
        },
        selectTab(newTab) {
            for (const tab in this.tabs) {
                this.tabs[tab].current = (this.tabs[tab].name === newTab);
            }
            this.selectedTab = newTab;
        },
    },
}
</script>
