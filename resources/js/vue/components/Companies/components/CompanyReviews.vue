<template>
    <ReviewsModule
        :dark-mode="darkMode"
        :company-id="companyId"
        :company-name="companyName"
    />
</template>

<script>
import ReviewsModule from "../../Reviews/ReviewsModule.vue";

export default {
    name: "CompanyReviews.vue",
    components: { ReviewsModule },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null,
        },
        companyName: {
            type: String,
            default: null,
        },
    }
}
</script>