<template>
    <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>

    <div class="pb-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
        <div class="border rounded-lg grid grid-cols-2"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}"
        >
            <div class="border-r" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
                <div class="flex items-center justify-between p-5">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Best Revenue Scenario Logs</h5>
                    <button class="bg-slate-600 hover:bg-slate-700 transition duration-200 mt-5 sm:mt-0 px-5 h-9 py-2 font-medium text-sm rounded-md text-white inline-flex items-center justify-center"
                            @click="toggleInvestigationModal(true)">
                        Investigate Allocation Failure
                    </button>
                </div>
                <div>
                    <div class="grid grid-cols-2 gap-3 mb-2 px-5">
                        <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Consumer Product ID / Email</p>
                        <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs">Created by / Phone</p>
                    </div>
                    <div v-if="loading" class="border-t border-b h-[32rem] overflow-y-auto divide-y flex items-center justify-center"
                         :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                        <loading-spinner></loading-spinner>
                    </div>
                    <div v-else class="border-t border-b h-[32rem] overflow-y-auto divide-y" :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                        <div @click="selectLog(consumerProductLogs.consumer_product_id)"
                             v-for="consumerProductLogs in sortedBrsLogs"
                             :key="consumerProductLogs.consumer_product_id"
                             class="cursor-pointer grid grid-cols-2 gap-x-3 py-3 px-5 group relative transition duration-100 items-center"
                             :class="[selectedId === consumerProductLogs.consumer_product_id ? (darkMode ? 'bg-slate-800 bg-opacity-50 text-primary-500 font-medium' : 'bg-primary-50 text-primary-500 font-medium') : (darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module')]">
                            <div class="absolute left-0 h-full w-1"
                                 :class="[selectedId === consumerProductLogs.consumer_product_id ? (darkMode ? 'bg-primary-500' : 'bg-primary-500') : (darkMode ? 'bg-slate-600 invisible group-hover:visible' : 'bg-slate-400 invisible group-hover:visible') ]">
                            </div>
                            <p class="text-sm">
                                {{ consumerProductLogs.consumer_product_id }}
                            </p>
                            <p class="text-sm">
                                {{ consumerProductLogs.consumer.name }}
                            </p>
                            <p class="text-sm">
                                {{ consumerProductLogs.consumer.email }}
                            </p>
                            <p class="text-sm">
                                {{ consumerProductLogs.consumer.phone }}
                            </p>
                            <div class="absolute right-5">
                                <svg class="fill-current" :class="[selectedId === consumerProductLogs.consumer_product_id ? (darkMode ? 'text-primary-500' : 'text-primary-500') : (darkMode ? 'text-dark-border' : 'text-light-border')]"
                                     width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L6.70711 5.29289C7.09763 5.68342 7.09763 6.31658 6.70711 6.70711L1.70711 11.7071C1.31658 12.0976 0.683417 12.0976 0.292893 11.7071C-0.0976311 11.3166 -0.0976311 10.6834 0.292893 10.2929L4.58579 6L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="p-3">
                        <simple-pagination
                            v-if="paginationData.lastPage > 1"
                            :dark-mode="darkMode"
                            :current-page="paginationData.currentPage"
                            :from="paginationData.from"
                            :to="paginationData.to"
                            :last-page="paginationData.lastPage"
                            :per-page="5"
                            :total="paginationData.total"
                            @change-page="getBRSLogs($event.newPage)"
                        />
                    </div>
                </div>
            </div>
            <div>
                <div class="flex items-center p-5">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Log Details</h5>
                </div>
                <div class="border-t border-b overflow-y-auto h-[33.5rem] relative"
                     :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-175': darkMode, 'flex flex-col items-center justify-center': loading}">
                    <div v-if="selectedLog" v-for="(runLogs, runId) in selectedLog.run_logs" :key="selectedId + runId">
                        <div class="sticky top-0 z-30 px-5 py-3" :class="[darkMode ? 'bg-primary-900' : 'bg-primary-50']">
                            <div class="font-semibold text-sm leading-none">
                                {{ $filters.dateFromTimestamp(runId, 'weekdayWithDateTimeShort') }}
                            </div>
                        </div>
                        <div v-for="(saleTypeLogs, saleTypeId) in runLogs" :key="selectedId + runId + saleTypeId">
                            <div class="items-center gap-x-2">
                                <div class="sticky top-[2.3rem] py-3 px-8 text-xs flex items-center justify-between" :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']">
                                    <p class="font-bold">{{ saleTypes[saleTypeId] }}</p>
                                </div>
                                <div class="px-10 py-2 border-b"
                                     :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                                     v-for="(productCampaignLogs, productCampaignId) in saleTypeLogs" :key="selectedId + runId + saleTypeId + productCampaignId">
                                    <div class="font-semibold text-sm">
                                        {{ productCampaignLogs.product_campaign_name }}
                                    </div>

                                    <div v-for="(productCampaignLog, idx) in productCampaignLogs.product_campaign_logs"
                                         :key="selectedId + runId + saleTypeId + productCampaignId + idx"
                                         class="pl-3 text-sm" :class="[darkMode ? 'text-slate-300' : 'text-slate-700']">
                                        <div>- {{ productCampaignLog.message }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center">
                            <div class="flex justify-center">
                                <svg width="23" height="19" viewBox="0 0 23 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M14.8571 -4.99559e-08C15.1602 -3.67067e-08 15.4509 0.0790176 15.6653 0.21967C15.8796 0.360322 16 0.551088 16 0.75L16 2.25C16 2.44891 15.8796 2.63968 15.6653 2.78033C15.4509 2.92098 15.1602 3 14.8571 3L1.14286 3C0.839753 3 0.549062 2.92098 0.334735 2.78033C0.120408 2.63968 8.46629e-07 2.44891 8.55324e-07 2.25L9.20891e-07 0.749999C9.29586e-07 0.551087 0.120408 0.360321 0.334735 0.219669C0.549062 0.0790169 0.839753 -6.62675e-07 1.14286 -6.49426e-07L14.8571 -4.99559e-08ZM14.8571 4.5C15.1602 4.5 15.4509 4.57902 15.6653 4.71967C15.8796 4.86032 16 5.05109 16 5.25L16 6.75C16 6.94891 15.8796 7.13968 15.6653 7.28033C15.4509 7.42098 15.1602 7.5 14.8571 7.5L1.14286 7.5C0.839753 7.5 0.549062 7.42098 0.334735 7.28033C0.120408 7.13968 6.49928e-07 6.94891 6.58622e-07 6.75L7.2419e-07 5.25C7.32884e-07 5.05109 0.120408 4.86032 0.334735 4.71967C0.549062 4.57902 0.839753 4.5 1.14286 4.5L14.8571 4.5ZM16 9.75C16 9.55109 15.8796 9.36032 15.6653 9.21967C15.4509 9.07902 15.1602 9 14.8571 9L1.14286 9C0.839753 9 0.549062 9.07902 0.334735 9.21967C0.120407 9.36032 5.36183e-07 9.55109 5.27488e-07 9.75L4.61921e-07 11.25C4.53226e-07 11.4489 0.120407 11.6397 0.334735 11.7803C0.549062 11.921 0.839753 12 1.14286 12L14.8571 12C15.1602 12 15.4509 11.921 15.6653 11.7803C15.8796 11.6397 16 11.4489 16 11.25L16 9.75Z" fill="#CBD5E1"/><path fill-rule="evenodd" clip-rule="evenodd" d="M11.672 2.91098C11.6033 2.65478 11.4357 2.43636 11.2059 2.30378C10.9762 2.17119 10.7032 2.13529 10.447 2.20398C10.1908 2.27267 9.9724 2.44033 9.83981 2.67006C9.70723 2.89979 9.67133 3.17278 9.74002 3.42898L9.99902 4.39498C10.0677 4.65118 10.2354 4.8696 10.4651 5.00219C10.6948 5.13478 10.9678 5.17067 11.224 5.10198C11.4802 5.03329 11.6986 4.86564 11.8312 4.63591C11.9638 4.40617 11.9997 4.13318 11.931 3.87698L11.672 2.91098ZM7.42902 5.73998C7.30194 5.70534 7.16927 5.69614 7.03863 5.71292C6.90799 5.7297 6.78195 5.77212 6.66775 5.83775C6.55355 5.90338 6.45344 5.99092 6.37317 6.09535C6.2929 6.19978 6.23405 6.31904 6.20001 6.44627C6.16596 6.57351 6.15738 6.70622 6.17477 6.83678C6.19215 6.96734 6.23516 7.09319 6.30132 7.20708C6.36748 7.32097 6.45549 7.42067 6.56029 7.50045C6.6651 7.58023 6.78463 7.63853 6.91202 7.67198L7.87802 7.93098C8.13376 7.99814 8.40568 7.96128 8.63432 7.82847C8.86295 7.69566 9.02968 7.4777 9.09803 7.22228C9.16638 6.96686 9.13079 6.69476 8.99904 6.46551C8.8673 6.23626 8.65012 6.06852 8.39502 5.99898L7.42902 5.73998ZM16.243 5.17098C16.3359 5.07807 16.4095 4.96778 16.4597 4.84642C16.5099 4.72505 16.5358 4.59498 16.5357 4.46363C16.5357 4.33228 16.5098 4.20223 16.4595 4.08089C16.4091 3.95956 16.3354 3.84933 16.2425 3.75648C16.1496 3.66364 16.0393 3.59 15.918 3.53978C15.7966 3.48956 15.6665 3.46373 15.5352 3.46378C15.4038 3.46383 15.2738 3.48974 15.1524 3.54005C15.0311 3.59036 14.9209 3.66407 14.828 3.75698L14.121 4.46398C14.0281 4.55689 13.9544 4.66719 13.9041 4.78859C13.8538 4.90998 13.828 5.04009 13.828 5.17148C13.828 5.30288 13.8538 5.43298 13.9041 5.55438C13.9544 5.67577 14.0281 5.78607 14.121 5.87898C14.2139 5.97189 14.3242 6.04559 14.4456 6.09588C14.567 6.14616 14.6971 6.17204 14.8285 6.17204C14.9599 6.17204 15.09 6.14616 15.2114 6.09588C15.3328 6.04559 15.4431 5.97189 15.536 5.87898L16.243 5.17098ZM9.17202 12.243L9.87902 11.536C10.0668 11.3485 10.1724 11.0941 10.1726 10.8287C10.1728 10.5633 10.0675 10.3088 9.88002 10.121C9.69251 9.93321 9.43809 9.82761 9.17273 9.82743C8.90736 9.82724 8.65279 9.93247 8.46502 10.12L7.75702 10.827C7.56938 11.0146 7.46396 11.2691 7.46396 11.5345C7.46396 11.7998 7.56938 12.0543 7.75702 12.242C7.94466 12.4296 8.19916 12.535 8.46452 12.535C8.72988 12.535 8.98438 12.4296 9.17202 12.242V12.243ZM12.372 7.07198C12.1903 6.99924 11.9912 6.98143 11.7995 7.02077C11.6077 7.0601 11.4317 7.15485 11.2933 7.29326C11.1549 7.43168 11.0601 7.60767 11.0208 7.79942C10.9815 7.99117 10.9993 8.19025 11.072 8.37198L15.072 18.372C15.1437 18.551 15.2656 18.7054 15.423 18.8167C15.5804 18.928 15.7666 18.9915 15.9593 18.9994C16.1519 19.0073 16.3427 18.9594 16.5087 18.8614C16.6747 18.7634 16.8089 18.6195 16.895 18.447L18.275 15.688L21.293 18.708C21.4807 18.8955 21.7351 19.0008 22.0004 19.0007C22.2656 19.0006 22.52 18.8951 22.7075 18.7075C22.895 18.5198 23.0003 18.2654 23.0002 18.0001C23.0001 17.7349 22.8947 17.4805 22.707 17.293L19.688 14.273L22.448 12.894C22.6202 12.8076 22.7637 12.6734 22.8615 12.5075C22.9592 12.3415 23.0069 12.1508 22.9989 11.9584C22.9908 11.766 22.9274 11.58 22.8162 11.4227C22.705 11.2655 22.5508 11.1437 22.372 11.072L12.372 7.07198Z" fill="#64748B"/>
                                </svg>
                            </div>
                            <p class="text-slate-500">Select a Log to show details.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <modal v-if="showInvestigationModal" :no-buttons="true" :small="true" :dark-mode="darkMode" @close="toggleInvestigationModal(false)">
        <template v-slot:header>
            <div class="font-medium">
                Investigate Allocation Failure
            </div>
        </template>
        <template v-slot:content>
            <div class="flex flex-col gap-5">
                <div class="grid grid-cols-8 gap-2">
                    <div class="font-medium flex flex-col gap-2" :class="consumerProductSearchLoading ? 'col-span-7' : 'col-span-8'">
                        Consumer Product ID
                        <autocomplete v-model="investigateConsumerProductId"
                                      @search="searchConsumerProducts"
                                      :min-search-input-len="1"
                                      :dark-mode="darkMode"
                                      :options="consumerProductOptions"
                                      :placeholder="'Search by ID/name/email'"
                                      :debounce-delay="1500"
                                      :create-user-input-option="true"/>
                    </div>
                    <div v-if="consumerProductSearchLoading" class="flex flex-row h-full items-end">
                        <loading-spinner :dark-mode="darkMode" :size="'w-8 h-8'" :margin="''" />
                    </div>
                </div>

                <div class="grid grid-cols-8 gap-2">
                    <div class="font-medium flex flex-col gap-2" :class="productCampaignSearchLoading ? 'col-span-7' : 'col-span-8'">
                        Campaign ID (optional)
                        <autocomplete v-model="investigateCampaignId"
                                      @search="searchProductCampaigns"
                                      :min-search-input-len="1"
                                      :dark-mode="darkMode"
                                      :options="productCampaignOptions"
                                      :placeholder="'Search by campaign name'"
                                      :debounce-delay="1500"
                                      :create-user-input-option="true" />
                    </div>
                    <div v-if="productCampaignSearchLoading" class="flex flex-row h-full items-end">
                        <loading-spinner :dark-mode="darkMode" :size="'w-8 h-8'" :margin="''" />
                    </div>
                </div>

                <div class="justify-center items-center flex flex-row">
                    <button class="bg-slate-600 hover:bg-slate-700 transition duration-200 mt-5 sm:mt-0 px-5 h-9 py-2 font-medium text-sm rounded-md text-white inline-flex items-center justify-center"
                            @click="investigateAllocationFailure">
                        Submit
                    </button>
                </div>
            </div>

            <div v-if="investigationLoading">
                <loading-spinner/>
            </div>
            <div v-else-if="investigationResult.length > 0" class="mt-5 text-left w-full whitespace-pre-wrap">
                <div v-for="result in investigationResult">
                    {{ result }}
                </div>
            </div>
        </template>
    </modal>
</template>

<script>
    import ApiService from "../services/api";
    import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
    import HasAlertsMixin from "../../Shared/mixins/has-alerts-mixin";
    import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
    import SimplePagination from "../../Shared/components/SimplePagination.vue";
    import Modal from "../../Shared/components/Modal.vue";
    import Autocomplete from "../../Shared/components/Autocomplete.vue";
    import SharedApiService from "../../Shared/services/api";

    export default {
        name: "BestRevenueScenarioLogs",
        components: {
            Autocomplete,
            Modal,
            SimplePagination,
            AlertsContainer,
            LoadingSpinner
        },
        mixins: [
            HasAlertsMixin
        ],
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            companyId: {
                type: Number,
                default: 0
            }
        },
        created: function() {
            this.getBRSLogs();
        },
        data() {
           return {
               api: ApiService.make(),
               sharedApi: SharedApiService.make(),

               brsLogs: {},
               saleTypes: {
                   1: 'Exclusive',
                   2: 'Duo',
                   3: 'Trio',
                   4: 'Quad'
               },
               selectedLog: null,
               selectedId: null,

               paginationData: {
                   currentPage: 1,
                   from: 0,
                   to: 0,
                   lastPage: 1,
                   total: 0
               },

               showInvestigationModal: false,
               investigateConsumerProductId: 0,
               investigateCampaignId: 0,
               investigationResult: [],

               consumerProductOptions: [],
               productCampaignOptions: [],

               loading: true,
               investigationLoading: false,
               consumerProductSearchLoading: false,
               productCampaignSearchLoading: false
           };
        },
        computed: {
            sortedBrsLogs() {
                return Object.values(this.brsLogs).sort((a, b) => {
                    return b.consumer_product_id - a.consumer_product_id;
                });
            }
        },
        methods: {
            getBRSLogs(page = 1) {
                this.resetLog();

                this.loading = true;

                this.api.getBestRevenueScenarioLogs(this.companyId, page).then(res => {
                    if(res.data.data.status === true) {
                        this.brsLogs = res.data.data.brs_logs.brs_logs;

                        this.paginationData.currentPage = res.data.data.brs_logs.pagination_data.current_page;
                        this.paginationData.from = res.data.data.brs_logs.pagination_data.from;
                        this.paginationData.to = res.data.data.brs_logs.pagination_data.to;
                        this.paginationData.lastPage = res.data.data.brs_logs.pagination_data.last_page;
                        this.paginationData.total = res.data.data.brs_logs.pagination_data.total;
                    }
                    else {
                        this.showAlert('error', 'Problem retrieving logs');
                    }
                }).catch(() => {
                    this.showAlert('error', 'Error retrieving logs');
                }).finally(() => {
                    this.loading = false;
                })
            },
            selectLog(consumerProductId) {
                if(this.selectedLog === this.brsLogs[consumerProductId]) {
                    this.resetLog();
                }
                else {
                    this.selectedLog = this.brsLogs[consumerProductId]
                    this.selectedId = consumerProductId;
                }
            },
            resetLog() {
                this.selectedLog = null;
                this.selectedId = null;
            },
            toggleInvestigationModal(show) {
                this.investigateConsumerProductId = 0;
                this.investigateCampaignId = 0;
                this.investigationResult = [];
                this.consumerProductOptions = [];
                this.productCampaignOptions = [];
                this.showInvestigationModal = !!show;
            },
            investigateAllocationFailure() {
                if(this.investigateConsumerProductId <= 0) {
                    this.investigationResult = ["Missing consumer product ID"];

                    return;
                }

                this.investigationResult = [];
                this.investigationLoading = true;

                this.api.investigateAllocationFailure(this.companyId, this.investigateConsumerProductId, this.investigateCampaignId).then(res => {
                    this.investigationResult = res.data.data.allocation_failure_reason.split(';');
                }).catch(err => {
                    this.investigationResult = [err.response.data.data.error || "Error investigating"];
                }).finally(() => {
                    this.investigationLoading = false;
                });
            },
            searchConsumerProducts(query) {
                this.consumerProductSearchLoading = true;

                this.sharedApi.searchConsumerProducts(query).then(res => {
                    this.consumerProductOptions = res.data.data.status === true ? res.data.data.results : [];
                }).catch(err => {
                    this.showAlert('error', 'Error retrieving consumer products');
                }).finally(() => {
                    this.consumerProductSearchLoading = false;
                })
            },
            searchProductCampaigns(query) {
                this.productCampaignSearchLoading = true;

                this.sharedApi.searchCompanyCampaigns(this.companyId, query).then(res => {
                    this.productCampaignOptions = res.data.data.status === true ? res.data.data.results : [];
                }).catch(err => {
                    this.showAlert('error', 'Error retrieving campaigns');
                }).finally(() => {
                    this.productCampaignSearchLoading = false;
                })
            }
        }
    }
</script>
