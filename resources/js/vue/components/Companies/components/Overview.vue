<template>
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-4 pb-10">
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>
        <DuplicateCompaniesModule :dark-mode="darkMode" :company-id="companyId" class="col-span-2"/>
        <RevenueOverview @send-to-tab="sendToTab" :dark-mode="darkMode" :company-id="companyId"></RevenueOverview>
        <LeadsOverview :dark-mode="darkMode" :company-id="companyId"></LeadsOverview>
        <CompanyContacts
            :has-edit-rights="hasEditRights"
            :dark-mode="darkMode"
            :show-add-contact-button="true"
            :company-id="companyId"
            @email="email"
        />
        <Activity :dark-mode="darkMode" :company-id="companyId" :company-name="companyName" show-search show-category-filter :category-options="actionCategories"></Activity>
        <div class="xl:col-span-2">
            <CampaignsOverview :table-height="'h-88'" :dark-mode="darkMode" :company-id="companyId" :legacy-company-id="legacyCompanyId"></CampaignsOverview>
        </div>
        <RoutineOverview class="xl:col-span-2" :company-id="companyId" :dark-mode="darkMode" :current-user-id="currentUserId"></RoutineOverview>
        <div class="xl:col-span-2 space-y-4">
<!--            Temporarily disabled to investigate high CPU usage  -->
<!--            <similar-companies :company-id="companyId" :dark-mode="darkMode"></similar-companies>-->
            <CompanyLinks :company-id="companyId" :dark-mode="darkMode" :current-user-id="currentUserId"></CompanyLinks>
        </div>
        <email-modal
            :dark-mode="darkMode"
            :email-modal="showEmailModal"
            :recipient-email="toEmail"
            :recipient-id="toId"
            :email-template-type="EMAIL_TEMPLATE_TYPE.PERSONAL"
            @flash-alert="flashAlert"
            @close="showEmailModal = false"
            :company-id="companyId"
        />
    </div>
</template>

<script>
import RevenueOverview from "../../Shared/modules/RevenueOverview.vue";
import LeadsOverview from "../../Shared/modules/LeadsOverview.vue";
import CampaignsOverview from "../../Shared/modules/CampaignsOverview.vue";
import Activity from "../../Shared/modules/Activity.vue";
import CompanyContacts from "../../Shared/modules/Contacts.vue";
import RoutineOverview from "../../Shared/modules/RoutineOverview.vue";
import CompanyLinks from "../../Shared/modules/CompanyLinks.vue";
import EmailModal from "../../Emails/components/EmailModal.vue";
import {EMAIL_TEMPLATE_TYPE} from "../../../../stores/email/email.store.js";
import AlertsMixin from "../../../mixins/alerts-mixin.js";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import DuplicateCompaniesModule from "../../BDMDashboard/components/DuplicateCompaniesModule.vue";

export default {
    name: "Overview",
    components: {
        DuplicateCompaniesModule,
        AlertsContainer,
        EmailModal,
        RoutineOverview, CompanyContacts, Activity, CampaignsOverview, LeadsOverview, RevenueOverview, CompanyLinks
    },
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null
        },
        legacyCompanyId: {
            type: Number,
            default: null,
        },
        currentUserId: {
            type: Number,
            default: null,
        },
        companyName: {
            type: String,
            default: ''
        },
        actionCategories: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            showEmailModal: false,
            toEmail: '',
            toId: '',

        }
    },
    computed: {
        EMAIL_TEMPLATE_TYPE() {
            return EMAIL_TEMPLATE_TYPE
        }
    },
    methods: {
        // TODO: This function does update the variable for the Tab selected, but it does not visually change the Tab. May need a global event here. Will come back to this functionality later.
        sendToTab(value) {
            this.$emit('send-to-tab', value)
        },
        flashAlert (type, message) {
            this.showAlert(type, message);
        },
        email(data) {
            this.showEmailModal = true;
            this.toEmail = data.email;
            this.toId = data.id;
        },
    },

}
</script>

<style scoped>

</style>
