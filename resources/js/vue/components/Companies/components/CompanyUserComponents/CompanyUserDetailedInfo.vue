<template>
    <div>
        <div :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-background border-dark-border text-slate-100': darkMode}"
             class="border rounded-lg p-4">
                <Tab
                    :tabs="tabs"
                    :dark-mode="darkMode"
                    @selected="tabSelected"
                    :default-tab-index="tabIndex"
                    :showTotal=false
                    tab-type="Normal"
                    background-color="light"
                />
            <div class="h-[12rem] overflow-y-auto">
                <div v-if="selectedTabName === 'Details'" class="grid">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 pt-4">
                        <div>
                            <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">ID</p>
                            <p class="text-sm">{{ companyUser.id }}</p>
                            <hr class="w-3/4">
                        </div>
                        <div>
                            <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">User Type</p>
                            <p class="text-sm">{{ companyUser.is_contact === 1 ? "Company contact" : "Company user" }}</p>
                            <hr class="w-3/4">
                        </div>
                        <div>
                            <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Registered date</p>
                            <p class="text-sm">{{ $filters.dateFromTimestamp(companyUser.date_registered) }}</p>
                            <hr class="w-3/4">
                        </div>
                    </div>
                    <div class="p-3"></div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        <div>
                            <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Company</p>
                            <p class="text-sm">
                                <a :href="`/companies/${companyData.id}`"
                                   class="text-primary-500 font-bold flex items-center"
                                   target="_blank">
                                    {{ companyData.name }}
                                </a>
                            </p>
                            <hr class="w-3/4">
                        </div>
                        <div>
                            <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Company website</p>
                            <p class="text-sm">{{ companyData.website }}</p>
                            <hr class="w-3/4">
                        </div>
                        <div>
                            <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Title</p>
                            <p class="text-sm">{{ companyUser.title }}</p>
                            <hr v-if="companyUser.title" class="w-3/4">
                        </div>
                    </div>
                </div>
                <div v-if="selectedTabName === 'Related'" class="grid">
                    <div class="pt-4">
                        <div v-if="companyUser.is_contact === 1" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                            <div>
                                <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Total calls: </p>
                                <p class="text-sm">{{ companyUser?.total_calls_count ?? "N/A" }}</p>
                            </div>
                            <div>
                                <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Total calls over a minute: </p>
                                <p class="text-sm">{{ companyUser?.total_calls_over_one_minute_count ?? "N/A" }}</p>
                            </div>
                            <div>
                                <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Last called on: </p>
                                <p v-if="typeof companyUser?.latest_call_timestamp == 'number'" class="text-sm">
                                    {{
                                        DateTime.fromSeconds(companyUser.latest_call_timestamp).toLocaleString(DateTime.DATETIME_FULL)
                                    }}
                                </p>
                                <p v-else class="text-sm">
                                    This contact was not called yet.
                                </p>
                            </div>
                        </div>
                        <div class="p-3"></div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                            <div>
                                <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Notes: </p>
                                <p class="text-sm">{{ companyUser?.notes }}</p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Tab from "../../../Shared/components/Tab.vue";
import {PencilIcon} from "@heroicons/vue/solid";
import {DateTime} from "luxon";

export default {
    name: "CompanyUserDetailedInfo",
    components: {PencilIcon, Tab},
    mixins: [],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyUser: {
            type: Object,
            default: {},
        },
        companyData: {
            type: Object,
            default: {},
        },
    },
    data() {
       return {
           tabs: [
               {name: 'Details', current: true},
               {name: 'Related', current: false}
           ],
           selectedTabName: 'Details',
           tabIndex: 0,
       }
    },
    created() {},
    watch: {},
    computed: {
        DateTime() {
            return DateTime
        }
    },
    methods: {
        tabSelected(tabName) {
            this.selectedTabName = tabName;
        }
    }
}
</script>
