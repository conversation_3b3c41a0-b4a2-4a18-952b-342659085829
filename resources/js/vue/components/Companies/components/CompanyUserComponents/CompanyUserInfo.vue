<template>
    <div>
        <div :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-background border-dark-border text-slate-100': darkMode}"
             class="border rounded-lg p-4">
            <template v-if="!savingUser">
              <div class="flex flex-row gap-3 items-center justify-between w-full">
                <div class="flex-grow"></div>
                <div>
                  <ActionsHandle :dark-mode="darkMode" :no-delete-button="true" @edit="openEditUserModal(companyUser)"/>
                </div>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                <div>
                  <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Name</p>
                  <p class="text-sm">{{ companyUser.name }}</p>
                </div>
                <div>
                  <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Email</p>
                  <p v-if="companyUser.email" class="text-sm truncate">
                    <a :href="`mailto:${companyUser.email}`"
                       class="text-primary-500 font-bold">
                      {{ companyUser.email }}
                    </a>
                  </p>
                    <p v-else>
                        --
                    </p>
                </div>
                <div>
                  <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Phone</p>
                  <div class="flex items-center gap-3">
                    <ButtonDropdown
                        v-if="companyUser.is_contact === 1 && phoneNumberOptionsHasAtLeastOneOption"
                        :options="phoneNumberOptions"
                        class="z-10" @selected="call(companyUser, $event['type'])">
                      <svg v-if="contactIsValid(companyUser)"
                           class="w-4 cursor-pointer" fill="none" height="16" viewBox="0 0 16 16"
                           width="16" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M15.7061 12.5332L12.0551 9.21369C11.8825 9.05684 11.6558 8.97318 11.4227 8.98038C11.1895 8.98759 10.9684 9.0851 10.8058 9.25231L8.65655 11.4626C8.13922 11.3638 7.09917 11.0396 6.02859 9.97172C4.958 8.90024 4.63377 7.85751 4.53767 7.34378L6.7462 5.19364C6.91363 5.03119 7.01128 4.80998 7.01848 4.57681C7.02569 4.34364 6.94189 4.11681 6.78482 3.94433L3.46619 0.294312C3.30905 0.121292 3.09065 0.0163428 2.85738 0.00175297C2.62411 -0.0128368 2.39434 0.0640823 2.21687 0.216174L0.2679 1.8876C0.112621 2.04344 0.0199401 2.25085 0.00743837 2.47049C-0.00603376 2.69503 -0.262902 8.01378 3.86137 12.1398C7.45933 15.7368 11.9662 16 13.2074 16C13.3889 16 13.5002 15.9946 13.5299 15.9928C13.7495 15.9805 13.9568 15.8874 14.1119 15.7315L15.7824 13.7816C15.9351 13.6047 16.0126 13.3751 15.9983 13.1419C15.9841 12.9086 15.8792 12.6902 15.7061 12.5332V12.5332Z"
                            fill="#0081FF"/>
                      </svg>
                    </ButtonDropdown>
                    <ButtonDropdown
                        v-if="companyUser.is_contact === 1 && phoneNumberOptionsHasAtLeastOneOption"
                        :options="phoneNumberOptions"
                        class="z-10" @selected="sms(companyUser, $event['type'])">
                      <svg v-if="contactIsValid(companyUser)"
                           class="w-4 cursor-pointer" fill="none" height="16" viewBox="0 0 16 16"
                           width="16" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M1.66667 12.9547H3.33333V16L7.58417 12.9547H11.6667C12.5858 12.9547 13.3333 12.2853 13.3333 11.4622V5.49244C13.3333 4.66936 12.5858 4 11.6667 4H1.66667C0.7475 4 0 4.66936 0 5.49244V11.4622C0 12.2853 0.7475 12.9547 1.66667 12.9547Z"
                            fill="#0081FF"/>
                        <path
                            d="M14.3332 0H4.33317C3.414 0 2.6665 0.797333 2.6665 1.77778H12.6665C13.5857 1.77778 14.3332 2.57511 14.3332 3.55556V10.6667C15.2523 10.6667 15.9998 9.86933 15.9998 8.88889V1.77778C15.9998 0.797333 15.2523 0 14.3332 0Z"
                            fill="#0081FF"/>
                      </svg>
                    </ButtonDropdown>
                    <div v-if="phoneNumberOptions.length > 0">
                      <p v-for="option in phoneNumberOptions" :key="option.value" class="text-sm">
                        {{ option.name }}
                      </p>
                    </div>
                      <div v-else>
                          --
                      </div>
                  </div>
                </div>
                <div>
                  <p class="text-slate-500 text-sm uppercase font-semibold leading-tight">Status</p>
                  <p class="text-sm">{{ (companyUser.status === 1) ? 'Active' : 'Inactive' }}</p>
                </div>
              </div>
            </template>
            <div v-else class="flex items-center justify-center h-full">
              <loading-spinner :dark-mode="darkMode"></loading-spinner>
            </div>
        </div>

        <!-- Edit Company user/contact modal -->
        <Modal
            v-if="editUserModal"
            :close-text="'Cancel'"
            :confirm-text="confirmText"
            :dark-mode="darkMode"
            :disable-confirm="disableConfirm"
            @close="closeEditUserModal"
            @confirm="updateUser()"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium">Edit {{ isCompanyContact ? 'Contact' : 'User' }}</h4>
            </template>

            <template v-slot:content>
                <div v-if="isCompanyContact">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div class="col-span-2">
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Status
                                <span style="font-size: 0.65rem;" class="block"
                                      :class="[darkMode ? 'text-slate-400' : 'text-slate-500']"></span>
                            </p>
                            <toggle-switch
                                v-model="user.status"
                                :dark-mode="darkMode" />
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                First Name
                            </p>
                            <input
                                class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                placeholder="First Name" v-model="user.first_name"
                                :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Last Name
                            </p>
                            <input
                                class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                placeholder="Last Name" v-model="user.last_name"
                                :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Title
                            </p>
                            <input
                                class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                placeholder="Title" v-model="user.title"
                                :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Email
                            </p>
                            <input
                                class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                placeholder="Email" v-model="user.email"
                                :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Office Phone
                            </p>
                            <input
                                class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                placeholder="Office Phone" v-model="user.office_phone"
                                :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Mobile
                            </p>
                            <input
                                class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                placeholder="Mobile" v-model="user.cell_phone"
                                :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                        </div>
                        <div class="col-span-2">
                            <label
                                :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                Notes
                            </label>
                            <textarea
                                v-model="user.notes"
                                :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"
                                class="min-h-88 w-full border rounded pl-4 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                placeholder="Enter notes..."
                                type="text"/>
                        </div>
                        <div>
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Decision Maker?
                            </p>
                            <toggle-switch
                                v-model="user.is_decision_maker"
                                :dark-mode="darkMode" />
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Can Receive Promotions
                                <span style="font-size: 0.65rem;" class="block"
                                      :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Default is on. Check with this contact before enabling.</span>
                            </p>
                            <toggle-switch
                                v-model="user.can_receive_promotions"
                                :dark-mode="darkMode" />
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                User Type
                            </p>
                            <Dropdown
                                v-model="user.is_contact"
                                :dark-mode="darkMode"
                                :options="isContactOptions"
                                :placeholder="'Contact'"
                            />
                        </div>
                        <div class="col-span-2">
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Configurable Fields
                            </p>
                        </div>
                        <div v-for="(field, id) in user.user_fields['global']">
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                {{ field.name }}
                            </p>

                            <custom-input
                                v-if="field.type !== 'Boolean'"
                                v-model="user.user_fields['global'][id].value"
                                :dark-mode="darkMode"
                                :type="field.type"
                            />
                            <toggle-switch
                                v-if="field.type ==='Boolean'"
                                v-model="user.user_fields['global'][id].value"
                                :dark-mode="darkMode" />
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <div>
                            <custom-input
                                v-model="user.first_name"
                                :dark-mode="darkMode"
                                label="*First Name"
                            />
                        </div>
                        <div>
                            <custom-input
                                v-model="user.last_name"
                                :dark-mode="darkMode"
                                label="Last Name"
                            />
                        </div>
                        <div>
                            <custom-input
                                v-model="user.cell_phone"
                                :dark-mode="darkMode"
                                label="Cell Phone"
                            />
                        </div>
                        <div>
                            <custom-input
                                v-model="user.office_phone"
                                :dark-mode="darkMode"
                                label="Office Phone"
                            />
                        </div>
                        <div>
                            <label
                                :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                Notes
                            </label>
                            <textarea
                                v-model="user.notes"
                                :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"
                                class="min-h-88 w-full border rounded pl-4 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                placeholder="Enter notes..."
                                type="text"/>
                        </div>
                        <div>
                            <label class="font-medium mb-1 block">Status</label>
                            <Dropdown v-model="user.status" :dark-mode="darkMode" :options="statusOptions"
                                      :selected="user.status"/>
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                User Type
                            </p>
                            <Dropdown
                                v-model="user.is_contact"
                                :dark-mode="darkMode"
                                :options="isContactOptions"
                                :placeholder="'User'"
                            />
                        </div>
                        <div class="flex items-center justify-center gap-x-4">
                            <custom-button
                                color="primary-outline"
                                @click="openResetPasswordModal(user.userid)"
                            >
                                Reset Password
                            </custom-button>
                            <custom-button
                                color="primary-outline"
                                @click="openUpdatePasswordModal(user.userid)"
                            >
                                Update Password
                            </custom-button>
                        </div>
                        <div class="col-span-2 mt-4">
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Configurable Fields
                            </p>
                        </div>
                        <div v-for="(field, id) in user.user_fields['global']">
                            <p class="uppercase font-semibold text-xs mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                {{ field.name }}
                            </p>
                            <custom-input
                                v-if="field.type !== 'Boolean'"
                                v-model="user.user_fields['global'][id].value"
                                :dark-mode="darkMode"
                                :type="field.type"
                            />
                            <toggle-switch
                                v-if="field.type ==='Boolean'"
                                v-model="user.user_fields['global'][id].value"
                                :dark-mode="darkMode" />
                        </div>
                    </div>
                </div>
            </template>
        </Modal>

        <!-- Reset company user password modal -->
        <Modal
            v-if="resetUserPasswordModal"
            :close-text="'Cancel'"
            :confirm-text="passwordResetResponse ? 'OK' : resettingPassword ? 'Resetting...' : 'Reset'"
            :dark-mode="darkMode"
            :disable-confirm="resettingPassword"
            :small="true"
            @close="closeResetPasswordModal"
            @confirm="passwordResetResponse ? closeResetPasswordModal() : resetCompanyUserPassword()"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium inline-flex items-center">
                    <svg class="mr-2 fill-current text-red-500" fill="none" height="19" viewBox="0 0 20 19" width="20"
                         xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M10.8849 0.4905C10.5389 -0.1635 9.46294 -0.1635 9.11694 0.4905L0.116935 17.4905C0.0359187 17.6429 -0.00424573 17.8136 0.00035519 17.9861C0.00495611 18.1586 0.0541655 18.327 0.143189 18.4749C0.232212 18.6227 0.358012 18.7449 0.508333 18.8297C0.658653 18.9145 0.828366 18.9588 1.00094 18.9585H19.0009C19.1734 18.9589 19.343 18.9145 19.4932 18.8298C19.6434 18.7451 19.7691 18.6229 19.8581 18.4752C19.947 18.3274 19.9961 18.1591 20.0007 17.9867C20.0052 17.8144 19.965 17.6437 19.8839 17.4915L10.8849 0.4905ZM11.0009 15.9585H9.00094V13.9585H11.0009V15.9585ZM9.00094 11.9585V6.9585H11.0009L11.0019 11.9585H9.00094Z"/>
                    </svg>
                    Reset User Password
                </h4>
            </template>
            <template v-slot:content>
                {{
                    passwordResetResponse || `Are you sure you want to reset this User's password? An email will be sent to ${user.email}`
                }}
            </template>
        </Modal>

        <!-- Update company user password modal -->
        <Modal
            v-if="updateUserPasswordModal"
            :close-text="'Cancel'"
            :confirm-text="savingUser ? 'Saving...' : 'Update'"
            :dark-mode="darkMode"
            :disable-confirm="savingUser"
            :small="true"
            @close="closeUpdatePasswordModal"
            @confirm="updateCompanyUserPassword()"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium inline-flex items-center">
                    <svg class="mr-2 fill-current text-red-500" fill="none" height="19" viewBox="0 0 20 19" width="20"
                         xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M10.8849 0.4905C10.5389 -0.1635 9.46294 -0.1635 9.11694 0.4905L0.116935 17.4905C0.0359187 17.6429 -0.00424573 17.8136 0.00035519 17.9861C0.00495611 18.1586 0.0541655 18.327 0.143189 18.4749C0.232212 18.6227 0.358012 18.7449 0.508333 18.8297C0.658653 18.9145 0.828366 18.9588 1.00094 18.9585H19.0009C19.1734 18.9589 19.343 18.9145 19.4932 18.8298C19.6434 18.7451 19.7691 18.6229 19.8581 18.4752C19.947 18.3274 19.9961 18.1591 20.0007 17.9867C20.0052 17.8144 19.965 17.6437 19.8839 17.4915L10.8849 0.4905ZM11.0009 15.9585H9.00094V13.9585H11.0009V15.9585ZM9.00094 11.9585V6.9585H11.0009L11.0019 11.9585H9.00094Z"/>
                    </svg>
                    Update User Password
                </h4>
            </template>
            <template v-slot:content>
                <custom-input-password
                    v-model:confirm-password-value="user.confirmPassword"
                    v-model:password-value="user.password"
                    :dark-mode="darkMode"
                    :new-password="true"
                />
            </template>
        </Modal>


    </div>
</template>

<script>

import CustomButton from "../../../Shared/components/CustomButton.vue";
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import Modal from "../../../Shared/components/Modal.vue";
import CustomInputPassword from "../../../Shared/components/CustomInputPassword.vue";
import {
    contactIsValid,
    formatPhoneNumber,
    getPhoneNumberByType,
    phoneIsValid
} from "../../../../../modules/contacts/helpers";
import {DateTime} from "luxon";
import {mapState} from "pinia";
import {useCallStore} from "../../../../../stores/call.store";
import ButtonDropdown from "../../../Shared/components/ButtonDropdown.vue";
import {CommunicationRelationTypes} from "../../../Communications/enums/communication";
import AlertsMixin from "../../../../mixins/alerts-mixin";
import DispatchesGlobalEventsMixin from "../../../../mixins/dispatches-global-events-mixin";
import SharedApiService from "../../../Shared/services/api";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";

export default {
    name: "CompanyUserInfo",
    components: {
      LoadingSpinner,
        ButtonDropdown,
        CustomInputPassword,
        Modal,
        Dropdown,
        ToggleSwitch,
        CustomInput,
        ActionsHandle,
        CustomButton

    },
    mixins: [DispatchesGlobalEventsMixin, AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyUser: {
            type: Object,
            default: {},
        },
        companyData: {
          type: Object,
          default: {},
        },
    },
    data() {
        return {
            api: SharedApiService.make(),
            isCompanyContact: false,
            isContactOptions: [ {id: 1, name: 'Contact'}, {id: 0, name: 'User'}],
            users: [],
            user: {
                id: undefined,
                first_name: undefined,
                last_name: '',
                email: '',
                status: 1,
                cell_phone: undefined,
                office_phone: undefined,
                notes: null,
                password: '',
                confirmPassword: '',
                is_contact: false,
                user_fields: {global: []},
            },

            modifyUserPasswordId: null,
            editUserModal: false,
            resetUserPasswordModal: false,
            resettingPassword: false,
            passwordResetResponse: null,
            updateUserPasswordModal: false,
            statusOptions: [
                {id: 1, name: 'Active'},
                {id: 0, name: 'Inactive'}
            ],

            savingUser: false,
            error: null,

        }
    },
    created() {
      this.getUsers();
    },
    watch: {
        configurableFields(newVal) {
            this.user.user_fields.global = newVal;
        }
    },
    computed: {
        DateTime() {
            return DateTime
        },
        confirmText: function () {
            return this.savingUser ? 'Saving...' : 'Update';
        },
        disableConfirm: function () {
            return this.savingUser || !this.companyUser.first_name || (!this.companyUser.cell_phone && !this.companyUser.email);
        },
        phoneNumberOptions() {
            let result = [];
            if (this.companyUser.hasOwnProperty('office_phone') && this.companyUser.office_phone && phoneIsValid(this.companyUser.office_phone)) {
                result.push({
                    name: formatPhoneNumber(this.companyUser.office_phone) + ' (Office)',
                    value: this.companyUser.office_phone,
                    type: "office_phone"
                });
            }

            if (this.companyUser.hasOwnProperty('cell_phone') && this.companyUser.cell_phone && phoneIsValid(this.companyUser.cell_phone)) {
                result.push({
                    name: formatPhoneNumber(this.companyUser.cell_phone) + ' (Mobile)',
                    value: this.companyUser.cell_phone,
                    type: "cell_phone"
                });
            }

            return result;
        },
        phoneNumberOptionsHasAtLeastOneOption() {
            return this.phoneNumberOptions.length > 0;
        },
        ...mapState(useCallStore, [
            'callActive'
        ])
    },
    methods: {
        formatPhoneNumber,
        contactIsValid,
        getUsers() {
          this.api.getUsers().then(resp => {
            resp.data.data.users.forEach((user) => {
              this.users.push({
                id: user.id,
                name: user.name
              });
            });
          });
        },
        openEditUserModal(user) {
            this.isCompanyContact = this.companyUser.is_contact === 1;

            this.user.id = this.companyUser.id;
            this.user.title = this.companyUser.title;
            this.user.first_name = this.companyUser.first_name;
            this.user.last_name = this.companyUser.last_name;
            this.user.status = this.companyUser.status;
            this.user.cell_phone = this.companyUser.cell_phone;
            this.user.office_phone = this.companyUser.office_phone;
            this.user.email = this.companyUser.email;
            this.user.notes = this.companyUser.notes;
            this.user.is_contact = this.companyUser.is_contact;
            this.user.user_fields = this.companyUser.user_fields;
            this.editUserModal = true;
        },
        closeEditUserModal() {
            this.editUserModal = false;
            this.user = {
                id: undefined,
                first_name: undefined,
                last_name: undefined,
                status: 1,
                cell_phone: undefined,
                office_phone: undefined,
                notes: null,
                is_contact: false,
                user_fields: {global: this.configurableFields}
            };
            this.clearError();
        },
        clearError() {
            this.error = null;
        },
        updateUser() {
            this.savingUser = true;
            this.clearError();

            let method = '';
            if (this.companyUser.is_contact === 1) {
                method = this.api.updateCompanyContact(this.companyData.id, this.user);
            } else {
                method = this.api.updateCompanyUser(this.companyData.id, this.companyUser.id, this.user);
            }

            method.then(() => {
                this.companyUser.first_name = this.user.first_name;
                this.companyUser.last_name = this.user.last_name;
                this.companyUser.status = this.user.status;
                this.companyUser.cell_phone = this.user.cell_phone;
                this.companyUser.office_phone = this.user.office_phone;
                this.companyUser.email = this.user.email;
                this.companyUser.notes = this.user.notes;
                this.companyUser.title = this.user.title;

                this.closeEditUserModal();
            }).catch(e => this.error = e.response.data.message)
                .finally(() => {
                    this.savingUser = false;
                });
        },
        openResetPasswordModal(userId) {
            this.modifyUserPasswordId = userId;
            this.resetUserPasswordModal = true;
            this.passwordResetResponse = null;
            this.clearError();
        },
        closeResetPasswordModal() {
            this.resetUserPasswordModal = false;
            this.modifyUserPasswordId = null;
            this.passwordResetResponse = null;
            this.clearError();
        },
        resetCompanyUserPassword() {
            this.resettingPassword = true;
            this.api.resetCompanyUserPassword(this.companyId, this.modifyUserPasswordId).then(response => {
                if (response.data.data.status != null) {
                    if (response.data.data.message) {
                        this.passwordResetResponse = response.data.data.message;
                    } else {
                        this.closeResetPasswordModal();
                    }
                } else this.error = `An error occurred resetting the User's password.`;
            }).catch(err => {
                this.error = err.response?.data?.message || err.message || `An unknown error occurred resetting the User's password.`;
            }).finally(() => {
                this.resettingPassword = false;
            });
        },
        openUpdatePasswordModal(userId) {
            this.modifyUserPasswordId = userId;
            this.updateUserPasswordModal = true;
            this.clearError();
        },
        closeUpdatePasswordModal() {
            this.modifyUserPasswordId = null;
            this.updateUserPasswordModal = false;
            this.user.password = '';
            this.user.confirmPassword = '';
            this.clearError();
        },
        updateCompanyUserPassword() {
            if (!this.user.password || this.user.password !== this.user.confirmPassword) {
                this.error = "Password is required, and must match confirm password.";
                return;
            }
            this.savingUser = true;
            this.api.updateCompanyUserPassword(this.companyId, this.modifyUserPasswordId, {
                'password': this.user.password,
            }).then(response => {
                if (response.data.data.status) {
                    this.closeUpdatePasswordModal();
                } else this.error = `An error occurred updating the User's password.`;
            }).catch(err => {
                this.error = err.response?.data?.message || err.message || `An unknown error occurred resetting the User's password.`;
            }).finally(() => {
                this.savingUser = false;
            });
        },
        call(contact, phoneType) {
            if (!this.callActive) {
                const targetNumber = getPhoneNumberByType(contact, phoneType);
                this.dispatchGlobalEvent('call', {
                    phone: targetNumber,
                    name: contact.name,
                    id: contact.id,
                    relId: contact.id,
                    relType: CommunicationRelationTypes.COMPANY_USER,
                    relData: this.companyData
                });
            } else {
                this.showAlert('warning', 'Please end your current call before making another one.')
            }
        },
        sms(contact, phoneType) {
            const targetNumber = getPhoneNumberByType(contact, phoneType);
            this.dispatchGlobalEvent('sms', {
                phone: targetNumber,
                name: contact.name,
                id: contact.id,
                relId: contact.id,
                relType: CommunicationRelationTypes.COMPANY_USER
            });
        },
    }
}

</script>
