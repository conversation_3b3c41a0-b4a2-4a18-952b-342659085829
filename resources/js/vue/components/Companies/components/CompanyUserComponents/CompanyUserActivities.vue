<template>
  <AlertsContainer v-if="alertActive" :alert-type="alertType" :dark-mode="darkMode" :text="alertText"/>
  <!-- Activity Feeds -->
  <div
      :class="{'bg-light-module bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}"
      class="border rounded-lg">
    <div class="px-5 pt-5 pb-4">
      <div class="flex items-center justify-between">
        <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Activity Feed</h5>
        <button
            class="ml-2 my-1 transition duration-200 inline-flex items-center font-semibold bg-grey-475 hover:bg-grey-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
            @click="toggleActionModal(true)">
          Add Action
        </button>
      </div>
    </div>
    <div
        :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-module bg-light-background  border-light-border', tableHeight]"
        class="border-t border-b overflow-y-auto">
      <template v-if="!loading">
        <AlertsContainer v-if="alertActive" :alert-type="alertType" :dark-mode="darkMode"
                         :text="alertText"/>
        <div v-if="actions.length">
          <template v-for="(action, idx) in actions" :key="action.id">
            <div class="relative">
              <div :class="{
                                        'text-slate-900 border-light-border': !darkMode,
                                        'text-slate-100 hover:bg-dark-module border-dark-border': darkMode
                                    }"
                   class="grid grid-cols-1 gap-x-3 border-b px-5"
                   @click="toggleExpandActivity(action.id)">
                <div class="py-4 cursor-pointer">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center w-full">
                      <svg
                          :class="{'transform transition duration-200 rotate-90' : openedActivities.includes(action.id) }"
                          class="mr-4" fill="none" height="10" viewBox="0 0 6 10"
                          width="6"
                          xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 9L5 5L1 1" stroke="#0081FF" stroke-linecap="round"
                              stroke-linejoin="round" stroke-width="2"/>
                      </svg>
                      <div class="w-full">
                        <div class="flex justify-start items-center">
                          <p v-if="action.display_date && action.display_date.length > 0"
                             class="text-xs text-slate-500 mr-2">
                            For: {{
                              action.display_date ? $filters.dateFromTimestamp(action.display_date) : 'No Date Set'
                            }}
                          </p>
                          <p class="text-xs text-slate-500">
                            Created: {{
                              $filters.dateFromTimestamp(action.created_timestamp, 'usWithTime')
                            }}
                          </p>
                          <p v-if="action.updated_timestamp && action.updated_timestamp > action.created_timestamp"
                             class="text-xs text-slate-500 ml-2">
                            Last Updated: {{
                              $filters.dateFromTimestamp(action.updated_timestamp, 'usWithTime')
                            }}
                          </p>
                        </div>

                        <div>
                          <p class="pb-0 text-sm font-semibold">
                            {{ action.subject }}
                          </p>
                        </div>
                      </div>
                      <div>
                        <actions-handle
                            :dark-mode="darkMode"
                            :no-delete-button="true"
                            @edit="toggleActionModal(true, action)"
                            @click.stop
                        />
                      </div>
                    </div>
                  </div>
                  <div v-if="openedActivities.includes(action.id)" class="px-5 pt-2">
                    <p :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}"
                       class="text-sm border-l border-primary-500 pl-3"
                       v-html="$filters.scrubHtml(action.message)">
                    </p>

                    <p v-if="action && action.tags && Array.isArray(action.tags) && action.tags.length > 0"
                       class="text-sm mt-4">
                      <span class="font-semibold">Tagged:</span>
                      <span v-for="(tagId, index) in action.tags" :key="index"
                            class="text-slate-500">
                                                    @{{ getTaggedUserById(tagId) }}
                                                    <span v-if="index < action.tags.length - 1">, </span>
                                                </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div v-else
             :class="{'text-grey-800': !darkMode, 'text-grey-120': darkMode}"
             class="h-100 flex items-center justify-center text-center pb-4">
          <p>No Activities Found</p>
        </div>
      </template>
      <div v-else class="flex items-center justify-center h-full">
        <loading-spinner :dark-mode="darkMode"></loading-spinner>
      </div>
    </div>
    <!-- Pagination -->
    <div v-if="paginationData && paginationData.to" class="p-3 mt-auto">
      <Pagination :dark-mode="darkMode" :pagination-data="paginationData" :show-pagination="true"
                  :show-total-records-detail="false" truncated
                  @change-page="handlePaginationEvent"></Pagination>
    </div>
    <!-- Action modal -->
    <company-user-action-modal
        v-if="showActionModal"
        :action="editAction"
        :darkMode="darkMode"
        :task-id="task?.id || 0"
        :user-id="companyUser.id"
        @close="toggleActionModal(false)"
        @reload-actions="getActions"
    />
  </div>


</template>
<script>

import ConfigurableFieldsApiService from "../../services/api";
import SharedApiService from "../../../Shared/services/api";
import AlertsMixin from "../../../../mixins/alerts-mixin";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import Pagination from "../../../Shared/components/Pagination.vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import CompanyUserActionModal from "../CompanyUserActionModal.vue";
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";

export default {
  components: {
    LoadingSpinner,
    Pagination, AlertsContainer, CompanyUserActionModal, ActionsHandle
  },
  mixins: [AlertsMixin],
  data() {
    return {
      api: SharedApiService.make(),
      configurableFieldsApi: ConfigurableFieldsApiService.make(),
      statusOptions: [
        {id: 1, name: 'Active'},
        {id: 0, name: 'Inactive'}
      ],
      selectedStatusOption: 1,
      expandActivity: false,
      openedActivities: [],
      users: [],
      user: {
        id: undefined,
        first_name: undefined,
        last_name: '',
        email: '',
        status: 1,
        cell_phone: undefined,
        office_phone: undefined,
        notes: null,
        password: '',
        confirmPassword: '',
        is_contact: false,
        user_fields: {global: []},
      },

      savingUser: false,
      error: null,
      editAction: null,
      showActionModal: false,

      loading: false,
      actions: [],
      paginationData: null,

      isCompanyContact: false,
      isContactOptions: [{id: 1, name: 'Contact'}, {id: 0, name: 'User'}],
    }
  },
  props: {
    companyUser: {
      type: Object,
      default: {},
    },
    companyUserActions: {
      type: Array,
      default: [],
    },
    companyData: {
      type: Object,
      default: {},
    },
    darkMode: {
      type: Boolean,
      default: false,
    },
    task: {
      type: Object,
      default: {}
    },
    tableHeight: {
      type: String,
      default: 'h-100'
    }
  },
  created() {
    this.getUsers();
    this.getActions();
  },
  computed: {},
  methods: {
    toggleExpandActivity(id) {
      if (this.openedActivities.includes(id))
        this.openedActivities.splice(this.openedActivities.indexOf(id), 1);
      else
        this.openedActivities.push(id);
    },
    getUsers() {
      this.api.getUsers().then(resp => {
        resp.data.data.users.forEach((user) => {
          this.users.push({
            id: user.id,
            name: user.name
          });
        });
      });
    },
    getTaggedUserById(userId) {
      const user = this.users.find(user => user.id === userId);
      return user ? user.name : "Unknown User";
    },
    clearError() {
      this.error = null;
    },
    toggleActionModal(show, action = null) {
      this.editAction = show && action ? action : null;
      this.showActionModal = show;
    },
    getActions() {
      this.loading = true;
      this.api.getCompanyUserActions(this.companyData.id, this.companyUser.id).then(resp => this.addPaginatedData(resp))
          .finally(() => this.loading = false);
    },
    addPaginatedData(resp) {
      if (resp.data.data.status === true) {
        let {data, ...paginationData} = resp.data.data.actions;
        this.actions = data;
        this.paginationData = paginationData;
      }
    },
    async handlePaginationEvent(newPageUrl) {
      this.loading = true;
      await axios.get(newPageUrl.link).then(resp => this.addPaginatedData(resp))
          .finally(() => this.loading = false);
    },
  },
  watch: {}
}
</script>
