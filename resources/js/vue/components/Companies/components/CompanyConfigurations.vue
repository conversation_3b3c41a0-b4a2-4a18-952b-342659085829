<template>
    <div class="h-80 flex items-center justify-center" v-if="loading">
        <loading-spinner></loading-spinner>
    </div>
    <div v-else class="grid md:grid-cols-2 grid-cols-1 gap-4 pb-10" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
        <div>
            <div class="border rounded-lg p-5"  :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                <div class="flex flex-col gap-5">
                    <div class="flex items-center justify-between">
                        <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Configurations</h5>
                    </div>
                    <div v-if="alert.show" class="flex gap-2 items-center p-4 text-sm rounded-lg text-white" :class="[alert.type === 'error' ? 'bg-red-400' : 'bg-green-400']" role="alert">
                        <svg v-if="alert.type === 'error'" class="w-6" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" style="enable-background:new 0 0 1024 1024" xml:space="preserve" fill="currentColor"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M928.99 755.83 574.6 203.25c-12.89-20.16-36.76-32.58-62.6-32.58s-49.71 12.43-62.6 32.58L95.01 755.83c-12.91 20.12-12.9 44.91.01 65.03 12.92 20.12 36.78 32.51 62.59 32.49h708.78c25.82.01 49.68-12.37 62.59-32.49 12.91-20.12 12.92-44.91.01-65.03zM554.67 768h-85.33v-85.33h85.33V768zm0-426.67v298.66h-85.33V341.32l85.33.01z" fill="currentColor"></path></g></svg>
                        <svg v-else class="w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M12 17.75C12.4142 17.75 12.75 17.4142 12.75 17V11C12.75 10.5858 12.4142 10.25 12 10.25C11.5858 10.25 11.25 10.5858 11.25 11V17C11.25 17.4142 11.5858 17.75 12 17.75Z" fill="currentColor"></path> <path d="M12 7C12.5523 7 13 7.44772 13 8C13 8.55228 12.5523 9 12 9C11.4477 9 11 8.55228 11 8C11 7.44772 11.4477 7 12 7Z" fill="currentColor"></path> <path fill-rule="evenodd" clip-rule="evenodd" d="M1.25 12C1.25 6.06294 6.06294 1.25 12 1.25C17.9371 1.25 22.75 6.06294 22.75 12C22.75 17.9371 17.9371 22.75 12 22.75C6.06294 22.75 1.25 17.9371 1.25 12ZM12 2.75C6.89137 2.75 2.75 6.89137 2.75 12C2.75 17.1086 6.89137 21.25 12 21.25C17.1086 21.25 21.25 17.1086 21.25 12C21.25 6.89137 17.1086 2.75 12 2.75Z" fill="currentColor"></path> </g></svg>
                        <p>{{ alert.message }}</p>
                    </div>

                    <div class="grid md:grid-cols-2 xl:grid-cols-3 gap-5">
                        <div v-for="configuration in companyConfigurations">
                            <p class="font-semibold pb-1">{{ configuration.description }}</p>
                            <toggle-switch v-model="editConfiguration[configuration.key]" :dark-mode="darkMode"></toggle-switch>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <custom-button class="mt-2 text-sm" :dark-mode="darkMode" @click="saveCompanyConfiguration" :disabled="loadingSave">
                            <div class="relative">
                                <span class="min-w-[6rem]" :class="loadingSave ? 'invisible' : ''">Save</span>
                                <div v-if="loadingSave" role="status" class="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2">
                                    <svg aria-hidden="true"  class="w-6 h-6 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
                                </div>
                            </div>
                        </custom-button>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <ZipcodeTargetingExceptions v-if="showZipCodeTargetingExceptions" :company-id="companyId" :dark-mode="darkMode"></ZipcodeTargetingExceptions>
        </div>
        <div>
            <CompanyContractApprovals v-if="showCompanyContractsApproval" :company-id="companyId" :dark-mode="darkMode"></CompanyContractApprovals>
        </div>
        <div v-if="showCompanyMergeTool">
            <CompanyMergeTool
                :dark-mode="darkMode"
                :company-id="companyId"
            />
        </div>
    </div>
</template>

<script>
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import SharedApiService from "../../Shared/services/api";
import CustomInput from "../../Shared/components/CustomInput.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import Alert from "../../Shared/components/Alert.vue";
import ZipcodeTargetingExceptions from "./ZipcodeTargetingExceptions.vue";
import { PERMISSIONS, ROLES, useRolesPermissions } from "../../../../stores/roles-permissions.store";
import CompanyContractApprovals from "./CompanyContractApprovals.vue";
import CompanyMergeTool from "./CompanyMergeTool.vue";

export default {
    name: "CompanyConfigurations",
    components: {
        CompanyMergeTool,
        ZipcodeTargetingExceptions,
        CompanyContractApprovals,
        Alert,
        ToggleSwitch,
        CustomButton,
        CustomInput,
        LoadingSpinner,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            required: true
        },
    },
    data() {
        return {
            permissionStore: useRolesPermissions(),
            sharedApiService: SharedApiService.make(),
            companyConfigurations: [],
            editConfiguration: {},
            loading: false,
            loadingSave: false,
            alert: {},
            PERMISSIONS,
        }
    },
    mounted() {
        this.getCompanyConfigurations()
    },
    methods: {
        async getCompanyConfigurations(){
            this.loading = true

            try {
                const { data } = await this.sharedApiService.getCompanyConfigurations(this.companyId)
                if (data.data.status && data.data.company_configurations) {
                    this.companyConfigurations = data.data.company_configurations
                    this.editConfiguration = data.data.company_configurations
                        .reduce((prev, curr) => Object.assign(prev, {[curr.key]: curr.value}), {})
                }
            } catch (err) {
                this.showAlert('Error retrieving configuration', 'error')
                console.error(err)
            }

            this.loading = false
        },

        async saveCompanyConfiguration(){
            this.loadingSave = true

            try {
                await this.sharedApiService.saveCompanyConfiguration(this.companyId, this.editConfiguration)
                this.showAlert('Configuration saved successfully', 'success')
            } catch (err) {
                this.showAlert('Error saving configuration', 'error')
                console.error(err)
            }

            this.loadingSave = false
        },

        showAlert(message, type){
            this.alert = {
                message,
                type,
                show: true
            }

            setTimeout(() => {
                this.alert = { show: false }
            }, 3000)
        }
    },
    computed: {
        showZipCodeTargetingExceptions(){
            return useRolesPermissions().hasPermission('can-enable-zip-code-targeted-campaigns');
        },
        showCompanyContractsApproval() {
            return this.permissionStore.hasRole(ROLES.ADMIN);
        },
        showCompanyMergeTool() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_MERGE);
        }
    }
}
</script>

<style scoped>

</style>
