<template>
    <AlertsContainer :alert-type="alertType" :v-if="alertActive" :text="alertText" />
    <Modal
        :dark-mode="darkMode"
        @close="cancelCustomFloorPricing"
        @confirm="updateCustomFloorPricing"
    >
        <template v-slot:header>
            <div>
                <p class="text-lg text-primary-500">Custom Campaign Floor Prices</p>
            </div>
        </template>
        <template v-slot:content>
            <div class="relative w-full">
                <div class="absolute flex flex-col items-center w-full"
                     v-if="loading || saving"
                >
                    <LoadingSpinner />
                </div>
                <div v-if="!loading" class="flex flex-col gap-y-6"
                    :class="[saving && 'pointer-events-none grayscale-[50%] opacity-[50%]']"
                >
                    <div class="flex items-center uppercase font-bold font-sm">
                        Editing floor pricing for - {{ campaignStore.editingCampaign.name }}
                    </div>
                    <div class="flex flex-col items-start px-5 gap-y-4">
                        <div class="flex items-center gap-x-3 justify-between">
                            <p class="text-sm min-w-[16rem]">Enable Custom Floor Pricing:</p>
                            <Toggle
                                :dark-mode="darkMode"
                                :true-value="1"
                                :false-value="0"
                                v-model="campaignStore.editingCampaign.uses_custom_floor_prices"
                            />
                        </div>
                        <div v-if="campaignStore.editingCampaign.uses_custom_floor_prices && editingIndustrySlug"
                             class="flex items-center gap-x-6"
                        >
                            <div class="flex items-center gap-x-3 justify-between">
                                <p class="text-sm capitalize min-w-[16rem]">Bulk-apply to campaigns:</p>
                                <Toggle
                                    :dark-mode="darkMode"
                                    :true-value="1"
                                    :false-value="0"
                                    v-model="applyToAllScopedCampaigns"
                                />
                            </div>
                            <div class="flex items-center gap-x-3">
                                <Tooltip
                                    :dark-mode="darkMode"
                                    :large="false"
                                >
                                    <div>Apply the price changes to all of this Company's campaigns within the current industry</div>
                                </Tooltip>
                                <div v-if="applyToAllScopedCampaigns">
                                    <div class="italic">
                                        <div class="inline">This will apply to campaigns in the
                                            "<span class="capitalize">{{ editingIndustrySlug }}</span>"
                                            industry. Any price modified below will be marked with the
                                            <div class="inline-block mx-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941" />
                                                </svg>
                                            </div>
                                            icon, and will be updated on all campaigns with matching locations upon saving.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center gap-x-6"
                             v-if="campaignStore.editingCampaign.uses_custom_floor_prices"
                        >
                            <div class="flex items-center gap-x-3 justify-between">
                                <p class="text-sm min-w-[16rem]">Modify Campaign Bids:</p>
                                <div class="min-w-[14rem]">
                                    <Dropdown
                                        :dark-mode="darkMode"
                                        :options="bidModificationOptions"
                                        v-model="bidModificationType"
                                    />
                                </div>
                                <Tooltip
                                    class="ml-4"
                                    :dark-mode="darkMode"
                                    :large="false"
                                >
                                    <div>{{ bidModificationTooltips[bidModificationType] ?? '-' }}</div>
                                </Tooltip>
                            </div>
                        </div>
                    </div>
                    <div v-show="campaignStore.editingCampaign.uses_custom_floor_prices" class="mt-4">
                        <BiddingTable
                            ref="biddingTable"
                            :dark-mode="darkMode"
                            :initial-data="customFloorPrices"
                            :custom-floor-pricing="true"
                        />
                    </div>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>
import { useFutureCampaignStore } from "../../Campaigns/Wizard/stores/future-campaigns.js";
import Modal from "../../Shared/components/Modal.vue";
import Toggle from "../../Inputs/Toggle/Toggle.vue";
import BiddingTable from "../../Campaigns/Wizard/components/BiddingTable.vue";
import { useBiddingStore } from "../../Campaigns/Wizard/stores/bidding.js";
import { reactive } from "vue";
import AlertsMixin from "../../../mixins/alerts-mixin.js";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import { useLocalityDataStore } from "../../Campaigns/Wizard/stores/locality-data.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Tooltip from "../../Shared/components/Tooltip.vue";

export default {
    name: "CustomCampaignFloorPriceModal",
    components: { Tooltip, Dropdown, LoadingSpinner, AlertsContainer, BiddingTable, Toggle, Modal },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        campaignReference: {
            type: String,
            default: "",
        },
    },
    mixins: [AlertsMixin],
    data() {
        return {
            campaignStore: useFutureCampaignStore(),
            biddingStore: useBiddingStore(),
            localityStore: useLocalityDataStore(),
            customFloorPrices: reactive({}),
            campaignRef: null,
            loading: true,
            saving: false,

            applyToAllScopedCampaigns: false,
            editingIndustrySlug: null,
            modifyCampaignBids: false,
            bidModificationType: "1",
            bidModificationOptions: [],
            bidModificationTooltips: {},
        }
    },
    emits: ['cancel:modal'],
    computed: {},
    mounted() {
        this.campaignRef = this.campaignReference;
        this.initialise();
    },
    methods: {
        async initialise() {
            this.loading = true;
            this.selectedIndustry = this.scopedIndustry ?? this.selectedIndustry;

            await Promise.all([
                this.campaignStore.editCampaign(this.campaignRef),
                this.localityStore.initialize(),
                this.getBidModificationOptions(),
            ]).catch(e => e);

            this.editingIndustrySlug = this.campaignStore.editingCampaign.industry;

            const locationData = this.campaignStore.fetchModuleInputValue('location', 'zip_codes');
            this.campaignStore.updateActiveZipCodes(locationData);

            this.loading = false;
        },
        async updateCustomFloorPricing() {
            this.saving = true;
            const prices = this.$refs.biddingTable?.customFloorPrices;
            const payload = Object.entries(prices ?? {}).reduce((output, [saleTypeKey, priceArray]) => {
                output.push(...priceArray.map(price => ({
                    sale_type: saleTypeKey,
                    property_type: price.property_type,
                    quality_tier: price.quality_tier,
                    state_location_id: price.state_location_id,
                    county_location_id: price.is_state_price ? null : price.location_id,
                    price: price.custom_floor_price,
                })));
                return output;
            }, []);

            const usesCustomFloorPrices = this.campaignStore.editingCampaign.uses_custom_floor_prices;
            const bulkApply = !!(this.applyToAllScopedCampaigns && this.editingIndustrySlug);
            const bidModification = parseInt(this.bidModificationType);

            const resp = await this.campaignStore.apiService.saveCustomFloorPrices(this.campaignRef, usesCustomFloorPrices, payload, bulkApply, bidModification).catch(e => e);
            if (resp.data?.data?.status) {
                this.showAlert('success', 'The custom prices are being updated. You will receive a notification email once the update is complete.');
                setTimeout(() => {
                    this.saving = false;
                    this.cancelCustomFloorPricing()
                }, 3000);
            }
            else {
                this.showAlert('error', resp?.response?.data?.message || resp.data?.data?.message || resp.message || 'An unknown error occurred.');
                this.saving = false;
            }
        },
        cancelCustomFloorPricing() {
            this.$emit('cancel:modal');
        },
        async getBidModificationOptions() {
            const resp = await this.campaignStore.apiService.getCustomPricingBidModificationOptions().catch(e => e);
            if (resp.data?.data?.status) {
                this.bidModificationOptions = Object.entries(resp.data.data.options ?? {}).map(([key, value]) => ({ id: key, name: value }));
                this.bidModificationType = this.bidModificationOptions[0]?.id;
                this.bidModificationTooltips = resp.data.data.tooltips;
            }
            else
                this.showAlert('error', resp?.response?.data?.message || resp.data?.data?.message || resp.message || 'An unknown error occurred.');
        }
    },

}
</script>

