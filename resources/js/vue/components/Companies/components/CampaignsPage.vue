<template>
    <div class="grid grid-cols-1 gap-4 pb-10">
        <Campaigns
            :has-edit-rights="hasEditRights"
            :dark-mode="darkMode"
            :company-id="company.id"
            :legacy-company-id="legacyCompanyId"
            :unrestricted-zip-code-targeting="company.unrestricted_zip_code_targeting ?? false"
            :show-filters="true"
            :campaign-activity-log-relation-class="campaignActivityLogRelationClass"
        />
    </div>
</template>

<script>
import Campaigns from "../../Companies/Campaigns.vue";
export default {
    name: "CampaignsPage",
    components: {Campaigns},
    props: {
        darkMode: {
            type: <PERSON>olean,
            default: false
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        company: {
            type: Object,
            default: null
        },
        legacyCompanyId: {
            type: Number,
        },
        campaignActivityLogRelationClass: {
            type: String,
            default: null,
        }
    },
    data() {
       return {}
    }
}
</script>

<style scoped>

</style>
