<template>
    <div class="grid grid-cols-5 gap-2">
        <div class="col-span-1 font-medium">
            Review:
        </div>
        <div class="col-span-4">
            <comment-content :content="companyExpertReview.body" />
        </div>
        <div class="col-span-1 font-medium">
            Added By:
        </div>
        <div class="col-span-4">
            {{ companyExpertReview.user?.name }}
        </div>
        <div class="col-span-1 font-medium">
            Date:
        </div>
        <div class="col-span-4">
            {{ new Date(companyExpertReview.created_at).toDateString() }}
        </div>
    </div>
</template>

<script>

import CommentContent from "../activity/comments/CommentContent.vue";

export default {
    name: "ExpertReview",
    components: {CommentContent},
    props: {
        companyExpertReview: {
            type: Object,
            default: {}
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },
}
</script>