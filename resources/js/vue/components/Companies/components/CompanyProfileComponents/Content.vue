<template>
    <div class="m-6 gap-2">

        <alert v-if="error !== null" :text="error" :alert-type="'error'" :dark-mode="darkMode" class="mb-4"></alert>

        <p class="text-sm font-medium mb-1">Expert Rating</p>
        <CustomInput v-on:keydown="caneSave = true" class="flex-grow mb-4" :dark-mode="darkMode" v-model="expertRating" type="number"></CustomInput>

        <p class="text-sm font-medium mb-1">Introduction</p>
        <CustomInput v-on:keydown="caneSave = true" class="flex-grow mb-4" :dark-mode="darkMode" v-model="introduction" type="textarea"></CustomInput>

        <p class="text-sm font-medium mb-1">What Customers Like</p>
        <CustomInput v-on:keydown="caneSave = true" class="flex-grow mb-4" :dark-mode="darkMode" v-model="customersLike" type="textarea"></CustomInput>

        <p class="text-sm font-medium mb-1">What Customers Don't Like</p>
        <CustomInput v-on:keydown="caneSave = true" class="flex-grow mb-4" :dark-mode="darkMode" v-model="customersDislike" type="textarea"></CustomInput>

        <custom-button :dark-mode="darkMode" :disabled="!caneSave" class="my-6" v-on:click="save">Save Changes
        </custom-button>
    </div>
</template>

<script>

import ApiService from "../../services/api";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import Alert from "../../../Shared/components/Alert.vue";

export default {
    name: 'Content',
    components: {
        Alert,
        CustomInput,
        CustomButton,
        Dropdown
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null
        },
    },
    data() {
        return {
            api: ApiService.make(),
            error: null,
            caneSave: false,
            expertRating: null,
            introduction: null,
            customersLike: null,
            customersDislike: null,
        }
    },
    created() {
        if (this.companyId) {
            this.getContent()
        }
    },
    methods: {
        getContent() {
            this.api.getContractorProfileContent(this.companyId).then(resp => {
                this.expertRating = resp.data.data.expert_rating
                this.introduction = resp.data.data.introduction
                this.customersLike = resp.data.data.customers_like
                this.customersDislike = resp.data.data.customers_dislike
            })
        },
        save() {
            if (this.caneSave)
                this.api.updateContractorProfileContent(
                    this.companyId,
                    this.expertRating,
                    this.introduction,
                    this.customersLike,
                    this.customersDislike
                )
                    .then(() => this.caneSave = false)
                    .catch(err => {
                        this.error = err.response?.data?.message;
                        setTimeout(() => this.error = null, 2000)
                    })
        }
    }
}
</script>
