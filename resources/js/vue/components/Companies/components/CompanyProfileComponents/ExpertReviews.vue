<template>
    <div :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="overflow-y-auto 'h-[32rem]'">
            <div class="h-full flex items-center justify-center" v-if="loading">
                <loading-spinner></loading-spinner>
            </div>
            <div v-else>
                <div class="grid lg:grid-cols-2 gap-5 p-5 pb-4">
                    <div class="grid grid-cols-1 gap-x-3 rounded-md" v-for="expertReview in expertReviews"
                         :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
                        <div class="p-5">
                            <div class="flex items-center justify-between ">
                                <expert-review :company-expert-review="expertReview" :dark-mode="darkMode"></expert-review>
                                <div class="inline-flex items-center gap-3">
                                    <ActionsHandle :dark-mode="darkMode" @edit="openModal(expertReview)" @delete="deleteExpertReview(expertReview.id)"></ActionsHandle>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="expertReviews?.length === 0">
                        <button
                            class="text-primary-500 text-sm font-semibold rounded-md px-5 py-2.5" :class="[darkMode ? 'bg-dark-background' : 'bg-primary-50 hover:bg-primary-100']"
                            @click="openModal(null)">
                            + Create Expert Company Review
                        </button>
                    </div>
                </div>
                <div class="p-5 pt-0">
                    <div class="pb-4">
                        <button
                            class="text-primary-500 text-sm font-semibold rounded-md p-5" :class="[darkMode ? 'bg-dark-background' : 'bg-primary-50 hover:bg-primary-100']"
                            @click="showHistory = ! showHistory">
                            {{ showHistory ? "Hide History" : "Show History" }}
                        </button>
                    </div>
                    <div v-if="showHistory" class="grid lg:grid-cols-2 gap-5 pb-4">
                        <div class="grid grid-cols-1 gap-x-3 rounded-md" v-for="expertReview in expertReviewsHistory"
                             :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
                            <div class="p-5">
                                <div class="flex items-center justify-between ">
                                    <expert-review :company-expert-review="expertReview" :dark-mode="darkMode"></expert-review>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <Modal
            v-if="modal"
            :small="true"
            @confirm="saveExpertReview"
            @close="closeModal"
            :dark-mode="darkMode"
            :close-text="'Cancel'"
            :confirm-text="saving ? 'Saving...' : addingNewExpertReview ? 'Create' : 'Update'"
        >
            <template v-slot:header>
                <p class="font-medium">
                    {{ addingNewExpertReview ? 'Add' : 'Edit'}} Expert Review
                </p>
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-1 gap-3">
                    <wysiwyg-editor v-model="editingExpertReview.body" :dark-mode="darkMode" auto-width="100%"></wysiwyg-editor>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>

import SharedApiService from "../../../Shared/services/api";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import CommentEditor from "../activity/comments/CommentEditor.vue";
import CommentContent from "../activity/comments/CommentContent.vue";
import ExpertReview from "./ExpertReview.vue";
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import Modal from "../../../Shared/components/Modal.vue";
import WysiwygEditor from "../../../Shared/components/WysiwygEditor.vue";

export default {
    name: "ExpertReviews",
    components: {
        WysiwygEditor,
        Modal,
        Dropdown,
        ActionsHandle, CommentContent, CommentEditor, CustomButton, LoadingSpinner, ExpertReview},
    props: {
        companyId: {
            type: Number,
            default: null
        },
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            api: SharedApiService.make(),
            loading: false,
            expertReviewModal: false,
            showHistory: false,
            expertReviews: null,
            editingExpertReview: {
                id: undefined,
                body: undefined,
            },
            saving: false,
            modal: false,
            addingNewExpertReview: false,
            expertReviewsHistory: null,
        }
    },
    created() {
        this.getExpertReview()
        this.getExpertReviewHistory()
    },
    methods: {
        getExpertReview() {
            this.loading = true;
            this.api.getExpertReviews(this.companyId).then(resp => {
                this.expertReviews = resp.data.data.reviews;
            }).catch(e => console.error(e)).finally(() => this.loading = false);
        },
        getExpertReviewHistory() {
            this.loading = true;
            this.api.getExpertReviewHistory(this.companyId).then(resp => {
                this.expertReviewsHistory = resp.data.data.history;
            }).catch(e => console.error(e)).finally(() => this.loading = false);
        },
        saveExpertReview() {
            this.saving = true;
            const payload = { ...this.editingExpertReview };
            if (payload.id) {
                this.api.updateExpertReview(this.companyId, this.editingExpertReview.id, payload).then(() => {

                }).catch(e => console.error(e)).finally(() => {
                    this.loading = false
                    this.getExpertReview()
                    this.getExpertReviewHistory()
                });
            } else {
                this.api.addExpertReview(this.companyId, payload).then(() => {
                    this.closeModal();
                }).catch(e => console.error(e)).finally(() => {
                    this.loading = false
                    this.addingNewExpertReview = false
                    this.getExpertReview()
                    this.getExpertReviewHistory()
                });
            }
        },
        deleteExpertReview(reviewId) {
            this.api.deleteExpertReview(this.companyId, reviewId).then(() => {
                this.getExpertReview()
                this.getExpertReviewHistory()
            })
        },
        openModal(expertReview) {
            if(expertReview) {
                this.editingExpertReview.id = expertReview.id
                this.editingExpertReview.body= expertReview.body
            } else {
                this.editingExpertReview.id = null
                this.addingNewExpertReview = true
            }
            this.modal = true
        },
        closeModal() {
            this.editingExpertReview = {
                id: undefined,
                body: undefined,
            };
            this.modal = false
        },
    },
}
</script>

<style scoped>

</style>