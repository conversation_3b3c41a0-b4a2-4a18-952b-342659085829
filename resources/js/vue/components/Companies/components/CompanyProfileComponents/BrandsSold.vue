<template>
    <div class="m-6">
        <div class="grid lg:grid-cols-2 gap-8">
            <div>

                <p class="font-semibold">Services Offered</p>
                <MultiSelect :options="services" :dark-mode="darkMode"
                             :selected-ids="servicesSelected"
                             text-place-holder="Choose Services"
                             class="mb-8" v-on:input="caneSave = true"></MultiSelect>

                <p class="font-semibold">Brands sold</p>
                <div class="flex gap-4 mb-3">
                    <div class="flex-grow">
                        <p class="text-sm font-medium mb-1">Add a brand</p>
                        <div class="flex items-center gap-1">
                            <CustomInput class="flex-grow" :dark-mode="darkMode" v-model="newBrand" type="text"></CustomInput>
                            <custom-button :dark-mode="darkMode" color="primary-outline" class="w-8 justify-center" v-on:click="addNewBrand" v-on:keyup.enter="addNewBrand">+</custom-button>
                        </div>
                    </div>
                </div>
                <div class="w-full inline-flex gap-2">
                    <p class="text-sm font-medium mb-1">Brands sold</p>
                    <p class="text-sm font-medium mb-1 text-slate-500">({{brands.length}})</p>
                </div>
                <div class="w-full border rounded-lg p-2 h-64 overflow-y-auto" :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']">
                    <div class="relative flex items-center p-2 rounded-md group cursor-pointer border border-transparent" v-on:click="remove(brand)" :class="[darkMode ? 'hover:bg-dark-module hover:border-dark-border' : 'hover:bg-light-module hover:border-light-border']" v-for="brand in brands">
                        {{brand}}
                        <svg class="absolute fill-current w-3 right-4 invisible group-hover:visible text-rose-400" width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 5H2V18C2 18.5304 2.21071 19.0391 2.58579 19.4142C2.96086 19.7893 3.46957 20 4 20H14C14.5304 20 15.0391 19.7893 15.4142 19.4142C15.7893 19.0391 16 18.5304 16 18V5H3ZM13.618 2L12 0H6L4.382 2H0V4H18V2H13.618Z"/>
                        </svg>
                    </div>
                </div>

            </div>
        </div>
        <custom-button :dark-mode="darkMode" :disabled="!caneSave" class="my-6" v-on:click="save">Save Changes</custom-button>
    </div>
</template>

<script>

import ApiService from "../../services/api";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";

export default {
    name: 'BrandsSold',
    components: {
        MultiSelect,
        CustomInput,
        CustomButton,
        Dropdown
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null
        },
    },
    data() {
        return {
            api: ApiService.make(),
            caneSave: false,
            brands: [],
            newBrand: null,
            services: [
                {id: 'new-installation', name: 'New Installation', selected: false},
                {id: 'removal-services', name: 'Removal Services', selected: false},
                {id: 'replacement-services', name: 'Replacement Services', selected: false},
                {id: 'maintenance', name: 'Maintenance', selected: false},
                {id: 'cleaning', name: 'Cleaning', selected: false},
                {id: 'emergency-services', name: 'Emergency Services', selected: false},
                {id: 'weatherproofing', name: 'Weatherproofing', selected: false},
                {id: 'repair-service', name: 'Repair Services', selected: false},
            ],
            servicesSelected: [],
        }
    },
    created() {
        if (this.companyId) {
            this.getBrandsSold()
        }
    },
    methods: {
        getBrandsSold(){
            this.api.getBrandsAndServices(this.companyId).then(resp => {
                this.brands = resp.data.data.brands_sold
                this.servicesSelected = resp.data.data.services
            })
        },
        remove(brand){
            const index = this.brands.indexOf(brand)
            if (index > -1)
                this.brands.splice(index, 1)
            this.caneSave = true
        },
        addNewBrand(){
            if(this.newBrand && this.brands.indexOf(this.newBrand) < 0){
                this.brands.push(this.newBrand)
                this.newBrand = null
                this.caneSave = true
            }
        },
        save(){
            if(this.caneSave)
                this.api.updateBrandsAndServices(this.companyId, this.brands, this.servicesSelected).then(() => {
                    this.caneSave = false
                    this.newBrand = null
                })
        }
    }
}
</script>
