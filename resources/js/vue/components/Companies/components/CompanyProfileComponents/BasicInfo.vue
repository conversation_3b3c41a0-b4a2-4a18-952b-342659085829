<template>
    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4 p-5 relative"
         :class="[savingBasicInfo && 'pointer-events-none opacity-50', darkMode ? 'border-slate-200 text-slate-200' : 'border-slate-800 text-slate-800']"
    >
        <div>
            <div v-if="!editingBasicInfo" @click="editBasicInfo" class="cursor-pointer inline-flex items-center text-primary-500">
                <svg class="w-4 mr-2" viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z" fill="#0081FF"/>
                </svg>
                Edit basic info
            </div>
            <div v-else class="flex items-center gap-x-4">
                <CustomButton v-if="editingBasicInfo"
                              @click="saveBasicInfo"
                              color="green"
                              :disabled="savingBasicInfo || !validBasicInfo"
                >
                    Save
                </CustomButton>
                <CustomButton v-if="editingBasicInfo"
                              @click="cancelBasicInfo"
                              color="slate"
                              :disabled="savingBasicInfo"
                >
                    Cancel
                </CustomButton>
            </div>
        </div>
        <div>
            <p class="font-semibold pb-1 mr-1" v-html="highlight('Status')"></p>
            <div>
                <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{ company.status || placeholder.generic }}</p>
            </div>
        </div>

        <div>
            <p class="font-semibold pb-1 mr-1" v-html="highlight('Date Registered')"></p>
            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{ `${$filters.dateFromTimestamp(company.dateRegistered)}` || placeholder.generic }}</p>
        </div>
        <div>
            <p class="font-semibold pb-1 mr-1" v-html="highlight('Trading Name')"></p>
            <p v-if="!editingBasicInfo" :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{company.name || placeholder.generic}}</p>
            <custom-input :dark-mode="darkMode" v-else type="text" v-model="company.name" />
            <span v-if="errorHandler.errors?.name" class="text-red-400 text-sm">{{ errorHandler.errors?.name }}</span>
        </div>
        <div>
            <p class="font-semibold pb-1 mr-1" v-html="highlight('Entity Name')"></p>
            <p v-if="!editingBasicInfo" :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{company.entity_name || placeholder.generic}}</p>
            <custom-input :dark-mode="darkMode" v-else type="text" v-model="company.entity_name" />
            <span v-if="errorHandler.errors?.entity_name" class="text-red-400 text-sm">{{ errorHandler.errors?.entity_name }}</span>
        </div>
        <div>
            <p class="font-semibold pb-1 mr-1" v-html="highlight('Company ID')"></p>
            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{company.id || placeholder.generic}}</p>
        </div>
        <div>
            <p class="font-semibold pb-1 mr-1" v-html="highlight('Legacy Company ID')"></p>
            <a :href="legacyHref('companyProfile')" class="text-primary-500" target="_blank">
                {{company.legacy_id}}
            </a>
        </div>
        <div>
            <p class="font-semibold pb-1 mr-1" v-html="highlight('Buying Leads')"></p>
            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{company.status}}</p>
            <!--todo enable updating buying leads-->
            <!--                        <input v-else type="text" :name="company.buyingLeads" v-model="company.buyingLeads" class="rounded border font-medium text-sm h-9 w-full" :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-200' : 'bg-light-background border-light-border text-slate-900']" />-->
            <!--                        <Dropdown :dark-mode="darkMode" v-else :placeholder="company.buyingLeads ? 'Yes' : 'No'" :options="['Yes', 'No']" :value="company.buyingLeads"></Dropdown>-->
        </div>
        <div>
            <p class="font-semibold pb-1 mr-1" v-html="highlight('License Status')"></p>
            <div>
                <p v-if="!company.licenses?.length">{{ placeholder.generic }}</p>
                <p v-else :class="[darkMode ? 'text-slate-400' : 'text-slate-700']" class="mb-2" v-for="license in company.licenses">
                    - {{ license.license }}
                </p>
            </div>
            <!--todo enable saving licence information -->
            <!--                        <Dropdown :dark-mode="darkMode" v-else :placeholder="company.licenseStatus === true ? 'Active' : 'Inactive'" :options="['Active', 'Inactive']"></Dropdown>-->
        </div>
        <div>
            <div class="font-semibold pb-1">Company Type (Legacy): </div>
            <div v-if="!editingBasicInfo">
                {{ company.type }}
            </div>
            <Dropdown
                v-else
                :dark-mode="darkMode"
                :options="companyTypes"
                v-model="selectedCompanyType"
                :selected="companyTypes.find(i => i.id === selectedCompanyType)"
            ></Dropdown>
            <span v-if="errorHandler.errors?.type" class="text-red-400 text-sm">{{ errorHandler.errors?.type }}</span>
        </div>
        <div>
            <p class="font-semibold pb-1 mr-1">Company Industries</p>
            <div v-if="!editingBasicInfo">
                <p v-if="!company.services?.length">{{ placeholder.generic }}</p>
                <p v-else :class="[darkMode ? 'text-slate-400' : 'text-slate-700']" class="mb-2" v-for="industry in company.industries.map(a => a.name)">
                    {{ industry }}
                </p>
            </div>
            <div v-else>
                <multi-select
                    :options="allIndustryOptions"
                    :dark-mode="darkMode"
                    :text-place-holder="'Select Industries'"
                    :show-search-box="false"
                    :selected-ids="selectedIndustries"
                    @selection-completed="handleIndustryChange"
                    :classes="'w-full'"
                />
            </div>
            <span v-if="errorHandler.errors?.industries" class="text-red-400 text-sm">{{ errorHandler.errors?.industries }}</span>
        </div>
        <div>
            <p class="font-semibold pb-1 mr-1">Company Services</p>
            <div v-if="!editingBasicInfo">
                <p v-if="!company.services?.length">{{ placeholder.generic }}</p>
                <p v-else :class="[darkMode ? 'text-slate-400' : 'text-slate-700']" class="mb-2" v-for="service in company.services.map(a => a.name)">
                    {{ service }}
                </p>
            </div>
            <div v-else>
                <multi-select
                    :options="filteredIndustryServiceOptions"
                    :dark-mode="darkMode"
                    :text-place-holder="'Select Services'"
                    :show-search-box="false"
                    :selected-ids="selectedIndustryServices"
                    :classes="'w-full'"
                />
                <span v-if="errorHandler.errors?.services" class="text-red-400 text-sm">{{ errorHandler.errors?.services }}</span>
            </div>
        </div>
        <div>
            <p class="font-semibold pb-1">Reviews</p>
            <a :href="legacyHref('reviews')" class="text-primary-500 text-sm">Link to reviews</a>
        </div>
        <div>
            <p class="font-semibold pb-1">Tickets</p>
            <a :href="legacyHref('tickets')" class="text-primary-500 text-sm">Link to tickets</a>
        </div>
        <div>
            <p class="font-semibold pb-1">Contract</p>
            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']" v-if="company.contractApproved">
                Approved: {{ $filters.dateFromTimestamp(company.contractApproved) }}
            </p>
            <p v-else>{{ placeholder.generic }}</p>
        </div>
        <div>
            <p class="font-semibold pb-1">Purchased Leads</p>
            <a :href="legacyHref('leads')" class="text-primary-500 text-sm">Link to leads</a>
        </div>
        <div>
            <p class="font-semibold pb-1">Expert Review</p>
            <a :href="legacyHref('expertReviews')" class="text-primary-500 text-sm">Link to expert review</a>
        </div>
        <div>
            <p class="font-semibold pb-1">Prescreened</p>
            <div v-if="!editingBasicInfo" :title="prescreenedAt">
                <svg v-if="company?.prescreened" width="12" height="10" viewBox="0 0 12 10" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M4.27411 6.5197L1.62626 3.69335L0 5.43719L4.27642 10L12 1.74138L10.3714 0L4.27411 6.5197Z"
                        fill="#00AE07"/>
                </svg>
                <svg v-else width="9" height="9" viewBox="0 0 9 9" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M0.219369 0.228966C0.359874 0.088363 0.550413 0.00937635 0.749086 0.00937635C0.947759 0.00937635 1.1383 0.088363 1.2788 0.228966L4.49532 3.44872L7.71183 0.228966C7.78095 0.157334 7.86362 0.100197 7.95503 0.0608901C8.04644 0.0215834 8.14476 0.00089368 8.24424 2.83178e-05C8.34373 -0.********* 8.44239 0.0181391 8.53447 0.0558497C8.62655 0.0935604 8.7102 0.14925 8.78055 0.21967C8.8509 0.290089 8.90653 0.373828 8.94421 0.466001C8.98188 0.558173 9.00084 0.656933 8.99997 0.756517C8.99911 0.856102 8.97844 0.954516 8.93917 1.04602C8.8999 1.13752 8.84282 1.22028 8.77126 1.28947L5.55475 4.50922L8.77126 7.72897C8.90774 7.87042 8.98326 8.05987 8.98156 8.25652C8.97985 8.45316 8.90105 8.64127 8.76214 8.78033C8.62322 8.91939 8.4353 8.99826 8.23885 8.99997C8.0424 9.00168 7.85314 8.92608 7.71183 8.78947L4.49532 5.56972L1.2788 8.78947C1.13749 8.92608 0.948232 9.00168 0.751782 8.99997C0.555332 8.99826 0.367412 8.91939 0.228496 8.78033C0.0895797 8.64127 0.0107821 8.45316 0.00907497 8.25652C0.00736788 8.05987 0.082888 7.87042 0.219369 7.72897L3.43588 4.50922L0.219369 1.28947C0.0789073 1.14882 0 0.958089 0 0.759216C0 0.560343 0.0789073 0.369612 0.219369 0.228966V0.228966Z"
                          fill="#E13131"/>
                </svg>
            </div>
            <Dropdown v-if="editingBasicInfo" placement="top" :dark-mode="darkMode"
                      :options="prescreenedOptions" v-model="selectedPrescreenedOption"
                      :selected="selectedPrescreenedOption"></Dropdown>
            <span v-if="errorHandler.errors?.prescreened" class="text-red-400 text-sm">{{ errorHandler.errors?.prescreened }}</span>
        </div>
        <div>
            <p class="font-semibold pb-1 mr-1" v-html="highlight('Active Slug')"></p>
            <p v-if="!editingBasicInfo" :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{company.activeSlug || placeholder.generic}}</p>
            <custom-input :dark-mode="darkMode" v-else type="text" v-model="company.activeSlug" />
            <span v-if="errorHandler.errors?.slug" class="text-red-400 text-sm">{{ errorHandler.errors?.slug }}</span>
        </div>
        <div v-if="company.activeSlug && displayProfile">
            <p class="font-semibold pb-1 mr-1" v-html="highlight('Contractor Profile')"></p>
            <a class="text-primary-500" target="_blank" :href="`https://www.fixr.com/contractors/` + company.activeSlug">Link to Fixr Profile</a>
        </div>
        <!-- Disabling the edit option until the backend logic to sync this data with Legacy is finalized -->
        <div v-if="false">
            <div v-if="!editingAdditionalInfo" @click="editAdditionalInfo" class="w-4 p-1 cursor-pointer mr-5" >
                <svg class="w-4"  viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z" fill="#0081FF"/>
                </svg>
            </div>
            <div v-else class="flex items-center">
                <button v-if="editingAdditionalInfo" @click="editAdditionalInfo" class="mr-3 text-white h-9 bg-emerald-500  hover:bg-emerald-600 mt-5 sm:mt-0 px-5 py-2 font-medium text-sm rounded-md inline-flex items-center justify-center">
                    Save
                </button>
                <button v-if="editingAdditionalInfo" @click="editAdditionalInfo" class="text-white h-9 bg-slate-400 hover:bg-slate-500 mt-5 sm:mt-0 px-5 py-2 font-medium text-sm rounded-md inline-flex items-center justify-center">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import Dropdown from "../../../Shared/components/Dropdown.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import EditField from "../../../Shared/components/EditField.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import {useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import AlertsMixin from "../../../../mixins/alerts-mixin";
import ApiService from "../../services/api";
import SharedApiService from "../../../Shared/services/api";
import Addresses from "../../../Shared/modules/Addresses.vue";
import Alert from "../../../Shared/components/Alert.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import useErrorHandler from "../../../../../composables/useErrorHandler";

export default {
    name: 'BasicInfo',
    components: {
        CustomButton,
        Addresses,
        Dropdown,
        LoadingSpinner,
        EditField,
        Alert,
        MultiSelect,
        CustomInput,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null
        },
        legacyCompanyId: {
            type: Number,
            default: null
        },
        searchString: {
            type: String,
            default: ''
        },
    },
    mixins: [
        AlertsMixin
    ],
    data() {
        return {
            api: ApiService.make(),
            sharedApiService: SharedApiService.make(),
            allIndustryOptions: [],
            allIndustryServiceOptions: [],
            filteredIndustryServiceOptions: [],
            selectedCompanyType: null,
            selectedIndustries: [],
            selectedIndustryServices: [],
            companyTypes: [],
            company: {},
            editingBasicInfo: false,
            adminBaseUrl: '',
            loading: false,
            basicInfoUpdateData: {},
            savingBasicInfo: false,
            userIsAdmin: false,
            placeholder: {
                generic: '-',
                notSet: 'Not set',
            },
            editingAdditionalInfo: false,
            prescreenedOptions: [
                { id: 'yes', name: 'Yes' },
                { id: 'no', name: 'No' },
            ],
            selectedPrescreenedOption: null,
            errorHandler: useErrorHandler()
        }
    },
    created() {
        if (this.companyId) {
            this.getCompanyProfileData();
        }
        this.userIsAdmin = useRolesPermissions().hasRole('admin');
        this.getLegacyCompanyTypes();
    },
    computed: {
        validBasicInfo() {
            return this.company.name.length && this.selectedIndustries.length && this.selectedIndustryServices.length;
        },
        prescreenedAt() {
            return this.company?.prescreened_at ?? 0 > 1
                ? `Last prescreened at: ${this.$filters.dateFromTimestamp(this.company.prescreened_at, 'usWithTime')}`
                : null;
        },
    },
    methods: {
        // todo these are pulled from the parent class: consolidate
        highlight(string) {
            if(!this.searchString) {
                return string;
            }
            return string.replace(new RegExp(this.searchString, "gi"), match => {
                if(this.darkMode) {
                    return '<span class="bg-yellow-800">' + match + '</span>';
                }
                else {
                    return '<span class="bg-yellow-500">' + match + '</span>';
                }
            });
        },
        legacyHref(section) {
            switch (section) {
                case 'reviews': return `${this.adminBaseUrl}/review/review-search?company_name=${encodeURIComponent(this.company.tradingName)}&review_status=any&email_validation=any&limit=10`;
                case 'tickets': return `${this.adminBaseUrl}/tickets.php?ticketcompanyid=${this.legacyCompanyId}`;
                case 'expertReviews': return `${this.adminBaseUrl}/companies/${this.legacyCompanyId}/expert-reviews`;
                case 'generateInvoice': return `${this.adminBaseUrl}/company.php?getcompanyid=${this.legacyCompanyId}&action=generateinvoice`;
                case 'attachments': return `${this.adminBaseUrl}/companies/${this.legacyCompanyId}/attachments`;
                case 'media': return `${this.adminBaseUrl}/companymedia.php?getcompanyid=${this.legacyCompanyId}`;
                case 'logoRectangle': return `${this.adminBaseUrl}/companies/${this.legacyCompanyId}/logo-rectangle`;
                case 'paymentMethods': return `${this.adminBaseUrl}/company.php?getcompanyid=${this.legacyCompanyId}`;
                case 'logoSquare': return `${this.adminBaseUrl}/company.php?getcompanyid=${this.legacyCompanyId}&action=logo`;
                case 'profileMedia': return `${this.adminBaseUrl}/companymedia.php?getcompanyid=${this.legacyCompanyId}`;
                case 'companyProfile': return `${this.adminBaseUrl}/company.php?getcompanyid=${this.legacyCompanyId}`;
                case 'leads': return `${this.adminBaseUrl}/quotes.php?action=Search&delivery=&fromdate=&getcompanyid=${this.legacyCompanyId}&&hours=24&itemsperpage=10&lead_category=&lead_industries%5B%5D=multi&lead_industries%5B%5D=roofing&lead_industries%5B%5D=solar&searchstatus=all`
                default: return ``;
            }
        },
        saveBasicInfo() {
            this.savingBasicInfo = true;
            this.basicInfoUpdateData = {
                name: this.company.name,
                entity_name: this.company.entity_name,
                services: this.selectedIndustryServices,
                type: this.selectedCompanyType,
                prescreened: this.selectedPrescreenedOption === 'yes',
                slug: this.company.activeSlug,
            };
            this.api.updateCompanyDetails(this.companyId, this.basicInfoUpdateData)
                .then(resp => {
                    if (!!resp?.data?.data.status) {
                        this.$emit('activate-alert', {
                            type: 'success',
                            text: 'Company profile updated'
                        });
                        this.getCompanyProfileData().then(() => {
                            this.basicInfoUpdateData = {}
                            this.savingBasicInfo = false
                            this.editBasicInfo()
                            this.errorHandler.resetError()
                        })
                    } else {
                        this.$emit('activate-alert', {
                            type: 'error',
                            text: resp?.data?.data?.msg || 'Could not update company information'
                        });
                    }
                })
                .catch(e => {
                    this.errorHandler.handleError(e, e.response?.data?.message || 'Could not update company information')

                    this.$emit('activate-alert', {
                        type: 'error',
                        text: this.errorHandler.message
                    });
                }).finally(() => {
                    this.savingBasicInfo = false
                })

        },
        editBasicInfo() {
            this.editingBasicInfo = !this.editingBasicInfo;
        },
        cancelBasicInfo() {
            this.errorHandler.resetError()
            this.resetIndustriesAndServices();
            this.editingBasicInfo = false;
        },
        editAdditionalInfo() {
            this.editingAdditionalInfo = !this.editingAdditionalInfo;
        },
        // todo this call is made in the parent also: consolidate
        async getCompanyProfileData() {
            this.loading = true;
            await this.api.getCompanyProfileData(this.companyId).then(resp => {
                this.company = resp.data.data.company;
                this.adminBaseUrl = resp.data.data.adminUrl;
                this.selectedCompanyType = this.company.type;
                this.selectedPrescreenedOption = this.company?.prescreened ? this.prescreenedOptions[0]?.id : this.prescreenedOptions[1]?.id;
                this.displayProfile = resp.data.data.display_profile;
            }).finally(() => {
                this.loading = false;
                this.getIndustryServiceData();
                this.resetIndustriesAndServices();
            });
        },
        async getIndustryServiceData() {
            await this.sharedApiService.allServicesByIndustry().then(resp => {
                if (resp.data.data.status) {
                    this.allIndustryOptions = Object.values(resp.data.data.industry_services).map(industryGroup => {
                        return { id: industryGroup[0].industry_id, name: industryGroup[0].industry_name }
                    });
                    this.allIndustryServiceOptions = resp.data.data.industry_services;
                }
            }).finally(() => {
                this.filterIndustryServices();
            });
        },
        filterIndustryServices() {
            this.filteredIndustryServiceOptions = Object.entries(this.allIndustryServiceOptions).reduce((output, [ industryId, services ]) => {
                return this.selectedIndustries.includes(parseInt(industryId))
                    ? [ ...output, ...services ]
                    : output;
            }, []);
        },
        resetIndustriesAndServices() {
            this.selectedCompanyType = this.company.type;
            this.selectedIndustries = this.company.industries.map(industry => industry.id);
            this.selectedIndustryServices = this.company.services.map(service => service.id);
            this.filterIndustryServices();
        },
        cancelEdit() {
            this.editingField = null;
            this.resetIndustriesAndServices();
        },
        //todo there used to be filters based on the company type (solar, roofing)
        // todo investigate the repercussions of removing these and allowing all combinations of types and services
        // todo warn about needing legacy users for solar and roofing dashboard
        handleIndustryChange(selectedIndustries) {
            this.selectedIndustries = selectedIndustries;
            this.filterIndustryServices();

            // Update selected services based on filtered options
            this.selectedIndustryServices = this.selectedIndustryServices.filter(serviceId => {
                // Check if the service belongs to any of the selected industries
                return this.filteredIndustryServiceOptions.some(option => option.id === serviceId);
            });
        },
        selectProfileTab(profileTabId) {
            this.selectedProfileTab = profileTabId
        },
        getLegacyCompanyTypes() {
            this.api.getLegacyCompanyTypes().then(resp => {
                this.companyTypes = resp.data.data.types
            });
        }
    }
}
</script>
