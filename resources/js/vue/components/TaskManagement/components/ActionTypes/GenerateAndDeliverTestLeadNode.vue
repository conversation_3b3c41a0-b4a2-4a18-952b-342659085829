<template>
    <div class="mt-5 flex items-center">
        <p>Generate and deliver a test lead.</p>
    </div>
</template>

<script>

import CustomInput from "../../../Shared/components/CustomInput.vue";

export default {
    name: "GenerateAndDeliverTestLeadNode",
    components: {CustomInput},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        action: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            nthLead: 10
        }
    },
    created() {
        if (this.action) {
            this.nthLead = this.action.payload?.nth_lead ?? 10
        }
    },
    watch: {
        action: function () {
            if (this.action) {} else {}

            this.loadTemplates();
        }
    },
    methods: {
        getDisplayName() {
            return "Generate and Deliver Test Lead";
        },
        getPayload() {
            return {
                nth_lead: this.nthLead
            }
        },
    }
}
</script>

<style lang="scss">

</style>
