<template>
    <div class="mt-5">
        <div class="rounded-lg bg-red-100 p-4 mb-5" v-if="errorMessage">
            <div class="text-sm text-red-700">{{ errorMessage }}</div>
        </div>
        <p class="uppercase font-semibold text-xs mb-1"
           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
            Subject
        </p>
        <shortcode-input :dark-mode="darkMode" v-model="subject"></shortcode-input>
        <p class="uppercase font-semibold text-xs mb-1 mt-5"
           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
            Notification Text
        </p>
        <shortcode-input :dark-mode="darkMode" v-model="notificationText" type="textarea"></shortcode-input>
        <p class="uppercase font-semibold text-xs mb-1 mt-5"
           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
            To
        </p>
        <div v-for="(email, index) in toEmails" class="flex justify-between gap-2 items-center mb-2">
            <div class="flex-grow">
                <shortcode-input :dark-mode="darkMode" v-model="toEmails[index]"></shortcode-input>
            </div>
            <span class="text-rose-500 cursor-pointer font-semibold" @click="removeToEmail(index)" v-if="toEmails.length > 1">X</span>
        </div>
        <div class="mt-3">
            <CustomButton height="h-6" color="primary-outline" @click="addToEmail">+</CustomButton>
        </div>
        <p class="uppercase font-semibold text-xs mb-1 mt-5"
           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
            From
        </p>
        <div>
            <custom-input
                placeholder="From email"
                :dark-mode="darkMode"
                type="email"
                v-model="fromEmail"
            />
        </div>
    </div>
</template>

<script>
import Dropdown from "../../../Shared/components/Dropdown.vue";
import ShortcodeInput from "../../../Shared/components/ShortcodeInput.vue";
import Validator from "../../../../../services/validator";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";

export default {
    name: "EmailNotification",
    components: {CustomButton, CustomInput, ShortcodeInput, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        action: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            notificationText: null,
            subject: null,
            fromEmail: null,
            errorMessage: '',
            toEmails: [''],
        }
    },
    created() {
        if (this.action) {
            this.subject = this.action.payload?.subject;
            this.notificationText = this.action.payload?.message;
            this.fromEmail = this.action.payload?.targets[0]?.from;
            this.toEmails = this.action.payload?.targets?.map(t => t.to) ?? [''];
        }
    },
    methods: {
        validate() {
            if (!this.subject || !this.notificationText || !this.fromEmail || this.toEmails.some(email => !email)) {
                this.errorMessage = 'All the fields are required';
                return false;
            }

            if (!Validator.validateEmail(this.fromEmail)) {
                this.errorMessage = 'The from email addresses must be valid';
                return false;
            }

            this.errorMessage = '';
            return true;
        },
        getDisplayName() {
            return 'Email Notification';
        },
        getPayload() {
            return {
                subject: this.subject,
                message: this.notificationText,
                targets: this.toEmails.map(email => ({
                    to: email,
                    from: this.fromEmail
                }))
            }
        },
        addToEmail() {
            this.toEmails.push('');
        },
        removeToEmail(index) {
            this.toEmails.splice(index, 1);
        },
    }
}
</script>

<style scoped>

</style>
