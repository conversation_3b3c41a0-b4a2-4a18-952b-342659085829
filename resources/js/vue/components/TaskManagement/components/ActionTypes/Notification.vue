<template>
    <div class="mt-5">
        <p class="uppercase font-semibold text-xs mb-1"
           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
            Subject
        </p>
        <shortcode-input :dark-mode="darkMode" v-model="subject"></shortcode-input>
        <p class="uppercase font-semibold text-xs mb-1 mt-5"
           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
            Notification Text
        </p>
        <shortcode-input :dark-mode="darkMode" v-model="notificationText"></shortcode-input>
        <p class="uppercase font-semibold text-xs mb-1 mt-5"
           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
            Target
        </p>
        <!--todo: target relation multiselect-->
        <Dropdown v-model="targetRelation" :options="targetRelations" :dark-mode="darkMode" :selected="targetRelation"
                  :key="targetRelation"></Dropdown>
    </div>
</template>

<script>
import Dropdown from "../../../Shared/components/Dropdown.vue";
import ShortcodeInput from "../../../Shared/components/ShortcodeInput.vue";
import HasTargetRelations from "../../mixins/has-target-relations";

export default {
    name: "Notification",
    components: {ShortcodeInput, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        action: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            notificationText: '',
            subject: '',
            targetRelation: 'sales-manager',
            targetType: 'staff'
        }
    },
    mixins: [HasTargetRelations],
    created() {
        if (this.action) {
            this.subject = this.action.payload.subject;
            this.notificationText = this.action.payload.message;
            this.targetRelation = this.action.payload.targets[0]?.target_relation;
        }
    },
    watch: {
        action: function () {
            if (this.action) {
                this.subject = this.action.payload.subject;
                this.notificationText = this.action.payload.message;
                this.targetRelation = this.action.payload.targets[0]?.target_relation;
            } else {
                this.subject = '';
                this.notificationText = '';
                this.targetRelation = 'sales-manager';
            }
        }
    },
    methods: {
        getDisplayName() {
            return 'Notification';
        },
        getPayload() {
            return {
                subject: this.subject,
                message: this.notificationText,
                targets: [{
                    target_type: this.targetType,
                    target_relation: this.targetRelation
                }]
            }
        }
    }
}
</script>

<style scoped>

</style>
