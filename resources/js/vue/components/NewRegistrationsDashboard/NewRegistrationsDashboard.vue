<template>
  <div class="new-registrations-dashboard">
      <div class="relative z-30">
          <div class="px-4 md:px-10 pt-6">
              <h1 class="text-lg font-semibold pb-0 leading-none mb-6" :class="[darkMode ? 'text-slate-50' : 'text-slate-900']">
                  New Registrations Dashboard
              </h1>
          </div>
      </div>
      <div class="mx-4 mt-4 pb-16 md:mx-10 md:mt-8">
          <div class="border rounded-lg" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
              <div class="p-5">
                  <h3 class="font-bold text-sm uppercase text-primary-500">
                      Registrations
                  </h3>
              </div>

              <div class="flex justify-between items-center px-5 pb-5">
                  <div class="inline-flex items-center gap-3">
                      <Filterable
                          :dark-mode="darkMode"
                          :filters="filters"
                          v-model="filterInputs"
                      />
                      <FilterableActivePills
                          v-if="filters.length"
                          :filters="filters"
                          :active-filters="filterInputs"
                          :dark-mode="darkMode"
                          @reset-filter="clearFilter"
                      />
                  </div>
              </div>

              <BaseTable
                  body-height="h-100"
                  :loading="loading"
                  :dark-mode="darkMode">
                  <template #head>
                      <tr>
                          <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('created_at')">
                              Created At
                              <span v-if="lastSortBy === 'created_at'">
                                    <span>
                                        <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'desc' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                        </svg>
                                    </span>
                              </span>
                          </th>
                          <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('company_name')">
                              Company Name
                              <span v-if="lastSortBy === 'company_name'">
                                    <span>
                                        <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'desc' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                        </svg>
                                    </span>
                              </span>
                          </th>
                          <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('manager_assigned')">
                              Manager Assigned
                              <span v-if="lastSortBy === 'manager_assigned'">
                                    <span>
                                        <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'desc' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                        </svg>
                                    </span>
                              </span>
                          </th>
                          <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('status')">
                              Status
                              <span v-if="lastSortBy === 'status'">
                                    <span>
                                        <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'desc' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                        </svg>
                                    </span>
                              </span>
                          </th>
                          <th>
                              Contacted On
                          </th>
                          <th>
                              Resolution
                          </th>
                          <th>
                              Industry Services
                          </th>
                      </tr>
                  </template>
                  <template #body>
                      <tr v-for="prospect in prospects" :key="prospect.id">
                          <td>
                              {{formatTime(prospect.created_at)}}
                          </td>
                          <td>
                              <a v-if="prospect.company_id === 0" :href="'/bdm-dashboard?prospect_id=' + prospect.reference" target="_blank" class="hover:text-primary-500 font-medium truncate cursor-pointer py-3">{{prospect.company_name ?? "N/A"}}
                                  <svg class="inline w-3.5 flex-shrink-0 ml-1 mb-1" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                              </a>

                              <a v-else :href="'/companies/' + prospect.company_id" target="_blank" class="hover:text-primary-500 font-medium truncate cursor-pointer py-3">{{prospect.company_name ?? "N/A"}}
                                  <svg class="inline w-3.5 flex-shrink-0 ml-1 mb-1" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                              </a>

                          </td>
                          <td>
                              <p v-if="prospect.company_id === 0">
                                  {{prospect.final_user_name ?? 'None'}}
                              </p>
                              <p v-else>
                                  {{prospect?.final_user_name ?? 'None'}}
                              </p>
                          </td>
                          <td>
                              <p v-if="prospect.status">
                                  <Badge v-if="prospect.status === 'initial'" color="primary" class="capitalize" :dark-mode="darkMode">{{prospect.status}}</Badge>
                                  <Badge v-if="prospect.status === 'closed'" color="green" class="capitalize" :dark-mode="darkMode">{{prospect.status}}</Badge>
                              </p>
                              <p v-else>N/A</p>
                          </td>
                          <td>
                              {{formatDate(prospect?.last_contact_date)}}
                          </td>
                          <td>
                              <p v-if="prospect.resolution">
                                  <Badge v-if="prospect.resolution === 'registration_complete'" color="green" class="capitalize" :dark-mode="darkMode">{{prospect.resolution.replaceAll('_', ' ')}}</Badge>
                                  <Badge v-else color="red" class="capitalize" :dark-mode="darkMode">{{prospect.resolution.replaceAll('_', ' ')}}</Badge>
                              </p>
                              <p v-else>N/A</p>
                          </td>
                          <td>
                              <p v-if="prospect.industry_services">
                                  <badge v-if="prospect.industry_services.length > 1" class="group relative" :dark-mode="darkMode" color="green" >
                                      <svg class="inline mr-1 w-3.5" viewBox="0 0 22 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.3994 6C10.869 6 10.3603 6.21071 9.9852 6.58579C9.61013 6.96086 9.39941 7.46957 9.39941 8C9.39941 8.53043 9.61013 9.03914 9.9852 9.41421C10.3603 9.78929 10.869 10 11.3994 10C11.9298 10 12.4386 9.78929 12.8136 9.41421C13.1887 9.03914 13.3994 8.53043 13.3994 8C13.3994 7.46957 13.1887 6.96086 12.8136 6.58579C12.4386 6.21071 11.9298 6 11.3994 6ZM8.57099 5.17157C9.32113 4.42143 10.3385 4 11.3994 4C12.4603 4 13.4777 4.42143 14.2278 5.17157C14.978 5.92172 15.3994 6.93913 15.3994 8C15.3994 9.06087 14.978 10.0783 14.2278 10.8284C13.4777 11.5786 12.4603 12 11.3994 12C10.3385 12 9.32113 11.5786 8.57099 10.8284C7.82084 10.0783 7.39941 9.06087 7.39941 8C7.39941 6.93913 7.82084 5.92172 8.57099 5.17157Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M2.91143 8C4.14693 11.4964 7.48265 14 11.3994 14C15.3171 14 18.6519 11.4965 19.8874 8C18.6519 4.50354 15.3171 2 11.3994 2C7.48265 2 4.14693 4.50359 2.91143 8ZM0.903357 7.7004C2.3045 3.23851 6.47319 0 11.3994 0C16.3267 0 20.4944 3.23856 21.8955 7.7004C21.9567 7.89544 21.9567 8.10456 21.8955 8.2996C20.4944 12.7614 16.3267 16 11.3994 16C6.47319 16 2.3045 12.7615 0.903357 8.2996C0.84211 8.10456 0.84211 7.89544 0.903357 7.7004Z" fill="currentColor"/></svg>
                                      Multi-Industry
                                      <p class="absolute z-50 top-0 right-0 group-hover:visible invisible rounded-md px-3 border shadow-md py-2 w-64" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                                            <span class="block" v-for="industry in prospect.industry_services" >
                                                <span>- {{ industry.name}}</span>
                                            </span>
                                        </p>
                                  </badge>
                                  <badge v-else :dark-mode="darkMode" color="primary" class="mr-1" v-for="industry in prospect.industry_services">
                                      {{ industry.name}}
                                  </badge>
                              </p>
                              <p v-else>N/A</p>
                          </td>
                      </tr>
                  </template>
              </BaseTable>
              <nav class="pb-3 px-5 flex  flex-grow items-center justify-end gap-8">
                  <CustomButton
                      color="slate-inverse"
                      :disabled="currentPage === 1"
                      @click="fetchProspects(currentPage - 1)"
                      class="disabled:opacity-50"
                  >
                      Previous
                  </CustomButton>
                  <div class="inline-flex items-center gap-3">
                      <p class="whitespace-nowrap mr-2 text-sm text-slate-500">Page <LoadingSpinner wrapper-style="inline-flex" margin="m-0" v-if="loading" size="w-3 h-3"></LoadingSpinner> <span v-else>{{ currentPage }}</span> of {{ Math.ceil(total / perPage) }}</p>
                      <p class="whitespace-nowrap text-sm text-slate-500">Per Page:</p>
                      <Dropdown v-model="perPage" :options="[10, 25, 50]" @update:modelValue="fetchProspects"></Dropdown>
                  </div>
                  <CustomButton
                      color="slate-inverse"
                      :disabled="currentPage >= Math.ceil(total / perPage)"
                      @click="fetchProspects(currentPage + 1)"
                      class="disabled:opacity-50"
                  >
                      Next
                  </CustomButton>
              </nav>
          </div>
      </div>
  </div>
</template>

<script setup>
import {ref, onMounted, watch} from 'vue'
import axios from 'axios'
import BaseTable from "../Shared/components/BaseTable.vue";
import Badge from "../Shared/components/Badge.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import Filterable from "../Shared/components/Filterables/Filterable.vue";
import FilterableActivePills from "../Shared/components/Filterables/FilterableActivePills.vue";

const prospects = ref([])
const managerOptions = ref({})
const loading = ref(false)
const total = ref(0)
const perPage = ref(25)
const currentPage = ref(1)
const props = defineProps({
    darkMode: false,
})
const fetchProspects = async (page = 1) => {
    loading.value = true
    try {
        const response = await axios.get('/internal-api/v2/new-registrations/prospects', {
            params: {
                page: page,
                per_page: perPage.value,
                sort_field: lastSortBy.value,
                sort_direction: orderBy.value,
                filter_inputs: filterInputs.value,
            }
        })
        prospects.value = response.data.results.data
        total.value = response.data.results.total
        perPage.value = response.data.results.per_page
        currentPage.value = response.data.results.current_page
        managerOptions.value = response.data.filterOptions.managers
    } catch (error) {
        console.error('Error fetching prospects:', error)
    } finally {
        loading.value = false
    }
}

function formatTime(ts) {
    if (!ts) return '';
    const date = new Date(ts);
    return date.toLocaleString();
}

onMounted(() => {
    fetchProspects(currentPage.value)
})

const orderBy = ref('desc')
const lastSortBy = ref('created_at')

const sortDataBy = (sortBy) => {
    if(lastSortBy.value !== sortBy) {
        orderBy.value = "asc"
    }
    if(orderBy.value === "asc") {
        orderBy.value = "desc"
    }
    else {
        orderBy.value = "asc"
    }
    lastSortBy.value = sortBy
    fetchProspects(currentPage.value)
};

const formatDate = (dateStr) => {
    if(!dateStr) return ""
    const date = new Date(dateStr);
    const currentYear = new Date().getFullYear();
    const dateYear = date.getFullYear();

    const month = date.toLocaleString('en-US', { month: 'long' });
    const day = date.getDate();
    const time = date.toLocaleString('en-US', {
        hour: 'numeric',
        minute: 'numeric',
        hour12: true
    });

    const yearSuffix = dateYear < currentYear ? ` (${dateYear})` : '';
    return `${month} ${day}, ${time}${yearSuffix}`;
};

const filters = ref([
    { "type": "select", "name": "Manager Assigned", "id": "manager", "clearable": true, "options": managerOptions, "show": true },
    { "type": "select", "name": "Resolution", "id": "resolution", "clearable": true, "options": {
             'Decision Maker Identified': 'decision_maker_identified',
             'Demo Booked': 'demo_booked',
             'Cancelled': 'cancelled',
             'Registration Complete': 'registration_complete',
             'Duplicate Company': 'duplicate_company',
             'No Available Contacts': 'no_available_contacts'
        }, "show": true },
])
const filterInputs = ref({})
watch(filterInputs, () => fetchProspects(), {deep: true})

function clearFilter (filterId) {
    delete filterInputs.value[filterId]
}


</script>
