import { PusherService } from "../../../../services/pusher";

/**
 * Bind and trigger all events that belongs to communication
 *
 *  - onNewSMSMessage // Server side
 */
export class CommunicationPusherService {
    static CHANNEL_USERS_COMMUNICATION = (userId) => `users.${userId}.communication`

    static EVENT_ON_OUTGOING_CALL_ATTEMPT = 'onOutgoingCallAttempt'
    static EVENT_ON_OUTGOING_CALL_RINGING = 'onOutgoingCallRinging'
    static EVENT_ON_OUTGOING_CALL_ACCEPT = 'onOutgoingCallAccept'

    static EVENT_ON_CALL_END = 'onCallEnd'

    static EVENT_ON_CALL_HOLD = 'onCallHold'
    static EVENT_ON_CALL_MUTE = 'onCallMute'

    static EVENT_ON_NEW_SMS_MESSAGE = 'on-new-sms-message'

    static EVENT_ON_INCOMING_CALL_ATTEMPT = 'onIncomingCallAttempt'
    static EVENT_ON_INCOMING_CALL_ACCEPT = 'onIncomingCallAccept'
    static EVENT_ON_INCOMING_CALL_DECLINE = 'onIncomingCallDecline'

    static EVENT_ON_GLOBAL_CALL = 'onGlobalCall'
    static EVENT_ON_GLOBAL_SMS = 'onGlobalSMS'

    static EVENT_ON_OPEN_RELATION_PAGE = 'onOpenRelationPage'
    static EVENT_ON_CONTACT_IDENTIFICATION_CLOSE = 'onContactIdentificationClose'

    _pusher = null

    static _instance = null

    /**
     * Please use static generate
     * Pusher Service constructor
     * @param {string} appKey
     * @param {string} appCluster
     */
    constructor(appKey, appCluster) {
        this._pusher = new PusherService(appKey, appCluster)
    }

    static generate(appKey, appCluster){
        if (this._instance) return this._instance

        this._instance = new CommunicationPusherService(appKey, appCluster)

        return this._instance
    }

    subscribe(userId) {
        if (!userId) throw new Error('userId is required')

        this._pusher.subscribe(CommunicationPusherService.CHANNEL_USERS_COMMUNICATION(userId));
    }

    /**
     * Bindings - Remember to pass clientSide false when it's a server side event
     */
    bindOnNewSMSMessage(cb) {
        this._pusher.bind(CommunicationPusherService.EVENT_ON_NEW_SMS_MESSAGE, cb, { clientSide: false })
    }

    bindOnIncomingCallAccept(cb) {
        this._pusher.bind(CommunicationPusherService.EVENT_ON_INCOMING_CALL_ACCEPT, cb)
    }

    bindOnGlobalCall(cb) {
        this._pusher.bind(CommunicationPusherService.EVENT_ON_GLOBAL_CALL, cb)
    }

    bindOnGlobalSMS(cb) {
        this._pusher.bind(CommunicationPusherService.EVENT_ON_GLOBAL_SMS, cb)
    }

    bindOnCallEnd(cb) {
        this._pusher.bind(CommunicationPusherService.EVENT_ON_CALL_END, cb)
    }

    bindOnOpenRelationPage(cb) {
        this._pusher.bind(CommunicationPusherService.EVENT_ON_OPEN_RELATION_PAGE, cb)
    }

    bindOnContactIdentificationClose(cb) {
        this._pusher.bind(CommunicationPusherService.EVENT_ON_CONTACT_IDENTIFICATION_CLOSE, cb)
    }


    /**
     * Unbinds
     */
    unbindOnNewSMSMessage() {
        this._pusher.unbind(CommunicationPusherService.EVENT_ON_NEW_SMS_MESSAGE, { clientSide: false })
    }

    /**
     * Triggers
     */

    triggerOnIncomingCallAttempt(data) {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_INCOMING_CALL_ATTEMPT, data)
    }

    triggerOnIncomingCallDecline(data) {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_INCOMING_CALL_DECLINE, data)
    }

    triggerOnIncomingCallAccept(data) {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_INCOMING_CALL_ACCEPT, data)
    }

    triggerOnOutgoingCallAttempt(data) {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_OUTGOING_CALL_ATTEMPT, data);
    }

    triggerOnOutgoingCallRinging() {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_OUTGOING_CALL_RINGING);
    }

    triggerOnOutgoingCallAccept() {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_OUTGOING_CALL_ACCEPT);
    }

    triggerOnCallEnd(payload) {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_CALL_END, payload);
    }

    triggerOnCallHold({ held }) {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_CALL_HOLD, { held });
    }

    triggerOnCallMute({ muted }) {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_CALL_MUTE, { muted });
    }

    triggerOnGlobalCall(data) {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_GLOBAL_CALL, data)
    }

    triggerOnGlobalSMS(data) {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_GLOBAL_SMS, data)
    }

    triggerOnOpenRelationPage({ url }) {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_OPEN_RELATION_PAGE, { url })
    }

    triggerOnContactIdentificationClose() {
        this._pusher.trigger(CommunicationPusherService.EVENT_ON_CONTACT_IDENTIFICATION_CLOSE)
    }
}
