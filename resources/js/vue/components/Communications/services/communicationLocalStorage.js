import { LocalStorageService } from "../../../../services/localStorage";

/**
 * Controls Communication Portal page visibility
 *
 */
export class CommunicationPortalLocalStorageService extends LocalStorageService {
    _namespace = 'communication'

    static COMMUNICATION_PAGE_FLAG_LOCAL_STORAGE_KEY = 'is_visible'

    /**
     * Set whether page is visible or not
     *
     * @param {boolean} flag
     */
    setCommunicationPortalVisibility(flag){
        this.setItem(CommunicationPortalLocalStorageService.COMMUNICATION_PAGE_FLAG_LOCAL_STORAGE_KEY, flag)
    }

    /**
     * Return value that represents whether page is opened or not
     *
     * @returns {boolean}
     */
    getCommunicationPortalVisibility(){
        return this.getItem(CommunicationPortalLocalStorageService.COMMUNICATION_PAGE_FLAG_LOCAL_STORAGE_KEY)
    }
}

