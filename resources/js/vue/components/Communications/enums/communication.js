export const CallStatusesEnum = Object.freeze(({
    SETUP: 'Setup',
    DIALING: 'Dialing',
    CONNECTING: 'Connecting',
    CONNECTED: 'Connected',
    ANSWERED: 'Answered',
    ENDED: 'Ended'
}))


export const CallModeEnum = Object.freeze(({
    OUTBOUND: 'outbound',
    INBOUND: 'inbound'
}))


export const CommunicationRelationTypes = Object.freeze(({
    LEAD: 'lead',
    COMPANY_USER: 'company_user',
    COMPANY_LOCATIONS: 'company_locations',
    COMPANY: 'company',
    CONSUMER: 'consumers',
    CONSUMER_PRODUCT: 'consumer_product',

    isCompanyRelated(relationType){
        return [this.COMPANY_LOCATIONS, 'company_location', this.COMPANY_USER, this.COMPANY, 'legacy-company', ].includes(relationType)
    },

    isLeadRelated(relationType){
        return [this.LEAD, 'consumer_product'].includes(relationType)
    },

}))
