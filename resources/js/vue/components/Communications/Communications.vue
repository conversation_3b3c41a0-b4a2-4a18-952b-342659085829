<template>
    <div>
        <div class="inline-flex gap-1" v-if="!alerts.showUserNoPhoneAvailable">
            <div v-if="checkUserHasAlerts()">
                <microphone-alert v-if="alerts.showMicrophoneAccess"></microphone-alert>
                <user-no-phone-available-alert v-if="alerts.showUserNoPhoneAvailable"></user-no-phone-available-alert>
            </div>
            <contact-selection-modal
                v-if="isContactIdentificationModalVisible"
                :dark-mode="darkMode"
                :pusher-app-key="pusherAppKey"
                :pusher-app-cluster="pusherAppCluster"
            />

            <dialer-wrapper :dark-mode="darkMode" @toggle-dialer-modal="openCommunicationPortal"/>

            <dialer
                v-if="showDialer"
                :dialer-modal="showDialer"
                @toggle-dialer-modal="showDialer = !showDialer"
                @sms="showMessenger"
                @call="handleDialerCall"
                :dark-mode="darkMode"
                show-close-button
            />

            <recordings-wrapper
                @toggle-recording-list="toggleActiveList('recording')"
                @clear-recordings="clearCallCallRecordings"
                @get-call-recordings-for-lead="getCallRecordingsForLeadId"
                :recording-list="this.activeMenu === 'recording'"
                :dark-mode="darkMode"
                :recordings="recordings"
            />

            <voicemail-wrapper
                @toggle-voicemail-list="toggleActiveList('voicemail')"
                @voicemail-played="markVoicemailHeard"
                :voicemail-list="this.activeMenu === 'voicemail'"
                :dark-mode="darkMode"
                :voicemails="voicemails"
            />

            <outgoing-call
                :minimize-call="callState.minimized"
                :call-active="callActive"
                :muted="callState.muted"
                :on-hold="callState.held"
                :dark-mode="darkMode"
                :comment="callInfo.comment"
                :contact-name="callInfo.contactName"
                :contact-phone="callInfo.contactPhone"
                :status="callInfo.status"
                :session="null"
                :is-identifying-contact="callState.isIdentifyingContact"
                :relation-type="callInfo.relationType"
                :relation-id="callInfo.relationId"
                :pusher-app-key="pusherAppKey"
                :pusher-app-cluster="pusherAppCluster"
                :has-relation-page="callInfo?.identifiedContact?.relationSubtype"
                @minimize-call="toggleMinimizeCall"
                @end-call="handleEndCall"
                @on-hold="toggleHold"
                @muted="toggleMute"
                @key-pressed="onDialerKeyPressed"
                @open-relation-page="handleOpenRelationPage"
            />

            <incoming-call
                :is-identifying-contact="callState.isIdentifyingContact"
                @answer-call="answerCall"
                @end-call="declineCall"
                :call-active="dialogsVisibility.isIncomingDialogVisible"
                :name="callInfo.contactName"
                :phone="callInfo.contactPhone"
                :comment="callInfo.comment"
                :id="callInfo.relationId"
                :type="callInfo.relationType"
            />

            <messenger
                :messenger-active="messengerActive"
                :messages="messages"
                :dark-mode="darkMode"
                :loading="messagesLoading"
                :name="callInfo?.contactName"
                ref="messenger"
                :contact-url="callInfo?.contactUrl"
                @close-messenger="handleCloseMessenger"
                @send-message="handleSendMessage"
            />

            <notifications-wrapper
                :notifications-list="this.activeMenu === 'notification'"
                @toggle-notifications-list="toggleActiveList('notification')"
                @get-next-lead="requestLead"
                :api="api"
                ref="notificationWrapper"
                :dark-mode="darkMode"
            />
            <sentry-report-button v-if="sentryPermission" :dark-mode="darkMode"></sentry-report-button>
        </div>
        <compose-email v-show="composeEmailStore.showComposeFixed()" :dark-mode="darkMode"></compose-email>
    </div>
</template>

<script>

import {ApiFactory} from "../LeadProcessing/services/api/factory";
import OutgoingCall from "./components/OutgoingCall.vue";
import IncomingCall from "./components/IncomingCall.vue";
import DialerWrapper from "./components/DialerWrapper.vue";
import VoicemailWrapper from "./components/VoicemailWrapper.vue";
import RecordingsWrapper from "./components/RecordingsWrapper.vue";
import DispatchesGlobalEventsMixin from "../../mixins/dispatches-global-events-mixin";
import CommunicationMixin from "../../mixins/communcation/communication-mixin";
import {WindowService} from "../../../services/window";
import {CommunicationPortalLocalStorageService} from "./services/communicationLocalStorage";
import ContactSelectionModal from "./components/ContactIdentificationModal/ContactIdentificationModal.vue";
import Dialer from "./components/Dialer.vue";
import Messenger from "./components/Messenger.vue";
import UserNoPhoneAvailableAlert from "./components/UserNoPhoneAvailableAlert.vue";
import MicrophoneAlert from "./components/MicrophoneNotAllowedAlert.vue";
import {CallModeEnum, CommunicationRelationTypes} from "./enums/communication";
import NotificationsWrapper from "../LeadProcessingNotifications/components/NotificationsWrapper.vue";
import ComposeEmail from "../Mailbox/components/ComposeEmail.vue";
import {useMailboxComposeEmailStore} from "../../../stores/mailbox/composeEmail";
import SentryReportButton from "../Shared/components/SentryReportButton.vue";
import {useRolesPermissions} from "../../../stores/roles-permissions.store";

export default {
    name: "Communications",
    props: {
        apiDriver: {
            type: String,
            default: 'dummy'
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        userId: {
            type: [String, Number],
            required: true
        },
        pusherAppKey: {
            type: String,
            required: true
        },
        pusherAppCluster: {
            type: String,
            required: true
        },
        dialerIsAttachedToParent: {
            type: Boolean,
            default: true
        },
        activeMenu: {
            type: String,
            default: null
        },
    },
    data: function() {
        return {
            api: null,
            messengerActive: false,
            messages: [],
            messagesLoading: true,
            dialerModal: false,
            windowService: null,
            showWhoTheySpokeWithModal: false,
            composeEmailStore: useMailboxComposeEmailStore(),

            communicationWindow: null,
            communicationWindowName: 'Communication',
            permissionStore: useRolesPermissions(),
        }
    },
    components: {
        useRolesPermissions,
        SentryReportButton,
        NotificationsWrapper,
        ComposeEmail,
        UserNoPhoneAvailableAlert,
        ContactSelectionModal,
        RecordingsWrapper,
        VoicemailWrapper,
        MicrophoneAlert,
        DialerWrapper,
        OutgoingCall,
        IncomingCall,
        Messenger,
        Dialer,
    },
    created() {
        this.api = ApiFactory.makeApiService(this.apiDriver);
        this.windowService = new WindowService()
        this.communicationPortalLocalStorageService = new CommunicationPortalLocalStorageService()
    },
    mounted() {
        this.initializeEventListeners();
    },
    computed: {
        sentryPermission() {
            return this.permissionStore.hasPermission('sentry-feedback')
        },
    },
    emits: ['update-active-menu'],

    mixins: [DispatchesGlobalEventsMixin, CommunicationMixin],

    watch: {
        messengerActive(isActive){
            if (isActive) this.bindCommunicationListener()
            else this.unbindCommunicationListener()
        },
    },

    methods: {
        /**
         * Register communication listeners
         */
        bindCommunicationListener(){
            this.communicationPusherService.bindOnNewSMSMessage((message) => this.addNewSMSMessage(message))
        },

        /**
         * Add new sms message and scroll messages div down
         *
         * @param message
         */
        addNewSMSMessage(message){
            this.messages.push(message)
            this.scrollMessagesDivDown()
            
            // Play ding sound for new SMS
            const audio = new Audio('/audio/ding.mp3')
            audio.play().catch(e => console.warn('Could not play SMS notification sound:', e))
        },

        /**
         * Unbind communication listeners
         */
        unbindCommunicationListener(){
            this.communicationPusherService.unbindOnNewSMSMessage()
        },
        /**
         * Parse query object to string
         * @param params
         * @returns {string}
         */
        parseQuery(params){
            return '?' + Object.keys(params).map(key => key + '=' + params[key]).join('&');
        },

        openCommunicationPortal(params = {}) {
            if (this.dialerIsAttachedToParent){
                this.showDialer = !this.showDialer
                return
            }

            const dialerWindowDimensions = {
                height: 575,
                width: 350,
            }

            const options = { centered: true }
            const features = { ...dialerWindowDimensions, popup: true }
            const url = `/portal/communication` + this.parseQuery(params)

            const page = { url, name: this.communicationWindowName }

            const isCommunicationVisible = this.communicationPortalLocalStorageService.getCommunicationPortalVisibility()

            if (!isCommunicationVisible) this.communicationWindow = this.windowService.openNewWindow(page, features,options)
            else if (this.communicationWindow) this.communicationWindow.focus()
        },

        onDialerKeyPressed(key) {
            this.generateKeyTone(key);
        },

        /**
         * Listen for call global event and open/send payload to communication portal
         *
         * @param phone
         * @param name
         * @param comment
         * @param relType
         * @param relId
         * @param relData
         */
        onAttemptCall({phone, name, comment = '', relType = null, relId = null, relData = {}}) {
            // Ensure that it does not call empty numbers
            if (!phone) return;

            const payload = { phone, name, comment, relType, relId, relData }

            if (!this.dialerIsAttachedToParent) return this.handleGlobalEvent({ ...payload, eventName: 'call' }, 'triggerOnGlobalCall')

            this.callInfo.relationType = relType
            this.callInfo.relationId = relId
            this.callInfo.contactName = name
            this.callInfo.contactPhone = phone
            this.callInfo.comment = comment
            this.callActive = true
            this.callState.active = true
            this.callInfo.mode = CallModeEnum.OUTBOUND

            if (Object.keys(relData).length !== 0) {
                this.callInfo.identifiedContact = {}
                this.callInfo.identifiedContact.relationData = {}
                this.callInfo.identifiedContact.relationSubtype = relType
                this.callInfo.identifiedContact.relationData.name = relData.name
                this.callInfo.identifiedContact.relationData.id = relData.id
                this.callInfo.comment = relData.name + " #" + relData.id
            }


            this.shouldShowWhoTheySpokeWithModal = false

            this.handleAttemptCall();
        },

        /**
         * Listen for sms global event and open/send payload to communication portal
         *
         * @param phone
         * @param name
         * @param id
         * @param relType
         * @param relId
         *
         */
        onAttemptSMS({ phone, name, id, relType = null, relId = null}) {
            const payload = { phone, name, id }

            if (!this.dialerIsAttachedToParent) return this.handleGlobalEvent({ ...payload, eventName: 'sms' }, 'triggerOnGlobalSMS')

            this.callInfo.contactName = name
            this.callInfo.contactPhone = phone
            this.callState.active = true
            this.showDialer = false
            this.messengerActive = true;

            this.getSMSMessages(this.callInfo.contactPhone)
            if (relType && relId) {
                if (relType === CommunicationRelationTypes.COMPANY) {
                    this.callInfo.contactUrl = "/companies/" + relId
                }
                else if (relType === CommunicationRelationTypes.CONSUMER_PRODUCT) {
                    this.callInfo.relationType = relType;
                    this.callInfo.relationId = relId;
                }
            } else {
                this.identifyNumber(phone)
            }
        },

        /**
         * Handle global event
         * @param payload
         * @param triggerMethod
         */
        handleGlobalEvent(payload, triggerMethod){
            if (this.communicationPortalLocalStorageService.getCommunicationPortalVisibility()){
                // Focus already opened popup does not work from others page than the page that opened the dialer.
                // this.windowService.focusWindow(this.communicationWindowName)
                this.communicationPusherService[triggerMethod](payload)
            }else {
                this.openCommunicationPortal(payload)
            }
        },

        requestLead(lead_id) {
            this.dispatchGlobalEvent('next-lead', {lead_id})
        },

        /**
         * Init global Event Listeners
         */
        initializeEventListeners() {
            this.listenForGlobalEvent('call', this.onAttemptCall.bind(this));
            this.listenForGlobalEvent('sms', this.onAttemptSMS.bind(this));
        },

        toggleActiveList(activeMenu) {
            this.$emit('update-active-menu', activeMenu)
        },
    }
}
</script>
