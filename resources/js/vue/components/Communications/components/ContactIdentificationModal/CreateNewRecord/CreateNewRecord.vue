<template>
    <div class="mb-5 flex ">
        <a @click="handleBack" class="text-base text-primary-500 font-medium pb-0 leading-none inline-flex items-center cursor-pointer">
            <svg class="mr-2" width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.70711 11.7071C6.31658 12.0976 5.68342 12.0976 5.29289 11.7071L0.292894 6.70711C-0.0976305 6.31658 -0.0976304 5.68342 0.292894 5.29289L5.29289 0.292893C5.68342 -0.0976316 6.31658 -0.0976315 6.70711 0.292893C7.09763 0.683417 7.09763 1.31658 6.70711 1.70711L2.41421 6L6.70711 10.2929C7.09763 10.6834 7.09763 11.3166 6.70711 11.7071Z" fill="#0081FF"/>
            </svg>
            Back
        </a>
        <p class="flex-1 text-center">
            {{createNewRecordComponentShown.title}}
        </p>
    </div>
    <component :is="createNewRecordComponentShown.componentName"
               :darkMode="darkMode"
               :pusher-app-key="pusherAppKey"
               :pusher-app-cluster="pusherAppCluster"
               @contact-saved="handleContactCreated"
    ></component>
</template>

<script>
import CreateCompanyUser from './CreateCompanyUser/CreateCompanyUser.vue'
import CreateOther from "./CreateOther/CreateOther.vue";

import {
    CONTACT_IDENTIFICATION_STORE_METHODS,
    useContactIdentificationStore
} from "../../../../../../stores/communication/contactIdentification";

import { mapActions, mapState } from "pinia";
import {CommunicationRelationTypes} from "../../../enums/communication";

export default {
    name: "CreateNewRecord",
    components: { CreateCompanyUser, CreateOther },

    props: {
        darkMode: {
            type: Boolean,
            required: true
        },
        pusherAppKey: {
            type: String,
            required: true
        },
        pusherAppCluster: {
            type: String,
            required: true
        }
    },

    computed: {
        ...mapState(useContactIdentificationStore, [
            'createNewRecordComponentShown'
        ]),
    },

    methods: {
        async handleContactCreated({relationId, relationType}){

            await this.communicationApi.updateCallLog({
                id: this.currentCallInfo.logId,
                type: this.currentCallInfo.callLogType,
                relationId,
                relationType
            })

            this.isContactIdentificationModalVisible = false
            this.communicationPusherService.triggerOnContactIdentificationClose()
        },
        ...mapActions(useContactIdentificationStore, [
            CONTACT_IDENTIFICATION_STORE_METHODS.SET_CREATE_NEW_RECORD_COMPONENT_SHOWN
        ]),

        handleBack(){
            this[CONTACT_IDENTIFICATION_STORE_METHODS.SET_CREATE_NEW_RECORD_COMPONENT_SHOWN](null)
        }
    }
}
</script>

<style scoped>

</style>
