<template>
    <field-label :dark-mode="darkMode">
        Please leave a note about the call
    </field-label>
    <div class="flex flex-col items-end">
        <textarea
            v-model="note"
            :maxlength="maxNoteLength"
            class="min-h-[16rem] w-full border rounded pl-4 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
            placeholder="I was talking to..."
            type="text"
            :class="[darkMode ? 'border-blue-700 bg-dark-background text-blue-400' : 'border-grey-200 bg-grey-50']"
        />

        <small class="mt-1">{{ note.length }}/{{ maxNoteLength }}</small>
    </div>
    <custom-button class="mt-2 text-sm" :dark-mode="darkMode" @click="handleSave" :disabled="disableConfirm">
        <div class="relative">
            <span class="min-w-[6rem]" :class="saving ? 'invisible' : ''">Save</span>
            <div v-if="saving" role="status" class="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2">
                <svg aria-hidden="true"  class="w-6 h-6 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
            </div>
        </div>
    </custom-button>
</template>

<script>
import CustomButton from "../../../../../Shared/components/CustomButton.vue";
import FieldLabel from "../../../../../Shared/components/FieldLabel.vue";

import { CommunicationApiFactory } from "../../../../../../../services/api/communication/factory";
import { useContactIdentificationStore } from "../../../../../../../stores/communication/contactIdentification";

import {mapState, mapWritableState} from "pinia";
import {CommunicationPusherService} from "../../../../services/communicationPusher";

export default {
    name: "CreateOther",
    components: { CustomButton, FieldLabel },

    props: {
        darkMode: {
            type: Boolean,
            required: true
        },
        pusherAppKey: {
            type: String,
            required: true
        },
        pusherAppCluster: {
            type: String,
            required: true
        }
    },

    data(){
        return {
            communicationPusherService: null,
            communicationApi: CommunicationApiFactory.makeApiService('api'),
            note: '',
            saving: false
        }
    },

    mounted() {
        this.communicationPusherService = CommunicationPusherService.generate(this.pusherAppKey, this.pusherAppCluster)
    },

    computed: {
        ...mapState(useContactIdentificationStore, [
            'currentCallInfo',
            'callLogType'
        ]),
        ...mapWritableState(useContactIdentificationStore, [
            'isContactIdentificationModalVisible'
        ]),

        maxNoteLength(){
            return 500
        },

        disableConfirm(){
            return this.note.length === 0 || this.saving
        }
    },

    methods: {
        async handleSave(){
            this.saving = true

            try {
                await this.communicationApi.updateCallLog({
                    note: this.note,
                    id: this.currentCallInfo.logId,
                    type: this.currentCallInfo.callLogType
                })
            }catch (err) {
                console.error(err)
            }

            this.isContactIdentificationModalVisible = false
            this.communicationPusherService.triggerOnContactIdentificationClose()

            this.saving = false
        }
    }
}
</script>
