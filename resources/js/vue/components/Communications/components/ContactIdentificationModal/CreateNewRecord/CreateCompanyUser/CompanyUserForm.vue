<template>
    <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-3 gap-y-1 w-full">
        <div v-for="field in fields">
            <field-label :dark-mode="darkMode">
                {{ labels[field] }}
            </field-label>
            <input
                @input="$emit('clear-error', field)"
                :disabled="disabledFields[field]"
                class="w-full border h-9 rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                :placeholder="labels[field]"
                v-model="modelValue[field]"
                :class="[
                    darkMode ? 'border-blue-700 bg-dark-background text-blue-400' : 'border-grey-200 bg-grey-50',
                    disabledFields[field] ? 'cursor-not-allowed' : '',
                    hasError(field) ? 'border-red-500' : ''
                ]">
            <div class="h-5">
                <p class="text-red-500 text-xs pl-2" :class="[hasError(field) ? 'visible' : 'invisible']">
                    {{ getErrorMessage(errors[field]) }}
                </p>
            </div>
        </div>
    </div>
</template>

<script>
import Alert from "../../../../../Shared/components/Alert.vue";
import FieldLabel from "../../../../../Shared/components/FieldLabel.vue";

export const COMPANY_USER_FORM_FIELDS = {
    FIRST_NAME: 'first_name',
    LAST_NAME: 'last_name',
    TITLE: 'title',
    EMAIL: 'email',
    OFFICE_PHONE: 'office_phone',
    MOBILE: 'cell_phone',
}

export default {
    name: "CompanyUserForm",
    components: { FieldLabel, Alert },
    props: {
        darkMode: {
            type: Boolean,
            required: true
        },

        modelValue: {
            type: Object,
            required: true,
        },

        disabledFields: {
            type: Object,
            default: () => {}
        },

        errors: {
            type: Object,
            default: () => {}
        },
    },
    emits: ['clear-error'],

    computed:{
        fields(){
            return Object.values(COMPANY_USER_FORM_FIELDS)
        },
    },

    data(){
      return {
          labels: {
              [COMPANY_USER_FORM_FIELDS.FIRST_NAME]: "First name",
              [COMPANY_USER_FORM_FIELDS.LAST_NAME]: "Last name",
              [COMPANY_USER_FORM_FIELDS.TITLE]: "Title",
              [COMPANY_USER_FORM_FIELDS.EMAIL]: "Email",
              [COMPANY_USER_FORM_FIELDS.OFFICE_PHONE]: "Office Phone",
              [COMPANY_USER_FORM_FIELDS.MOBILE]: "Mobile"
          }
      }
    },

    methods: {
        hasError(field){
            return this.errors[field]
        },

        getErrorMessage(errorMessage){
            return errorMessage ? typeof errorMessage === 'string' ? errorMessage : errorMessage[0] : null
        }
    }
}
</script>

<style scoped>

</style>
