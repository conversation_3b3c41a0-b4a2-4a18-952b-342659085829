<template>
    <field-label :dark-mode="darkMode">
        What company are they associated with?
    </field-label>
    <autocomplete
        class="mb-4"
        :list-class="['absolute top-0 w-full border rounded', darkMode ?  'border-dark-40 bg-dark-module' : 'border-grey-200 bg-light-module']"
        :option-class="['cursor-pointer px-5 py-2 my-1', darkMode ? 'hover:bg-blue-800' : 'hover:bg-grey-100']"
        v-model="selectedCompanyId"
        :value="selectedCompanyId"
        :options="companyOptions"
        @search="searchCompany"
        placeholder="Enter company name">
    </autocomplete>
    <create-edit-contact-form
        v-model="contactFormData"
        :dark-mode="darkMode"
        :disabled-fields="disabledContactFormFields"
        :errors="contactFormDataErrors"
        @clear-error="(field) => contactFormDataErrors[field] = undefined"
    >
    </create-edit-contact-form>
    <custom-button class="mt-2 text-sm" :dark-mode="darkMode" @click="handleSave" :disabled="saving || selectedCompanyId === null">
        <div class="relative">
            <span class="min-w-[6rem]" :class="saving ? 'invisible' : ''">Save</span>
            <div v-if="saving" role="status" class="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2">
                <svg aria-hidden="true"  class="w-6 h-6 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
            </div>
        </div>
    </custom-button>
</template>

<script>
import CustomButton from "../../../../../Shared/components/CustomButton.vue";
import Autocomplete from "../../../../../Shared/components/Autocomplete.vue";
import FieldLabel from "../../../../../Shared/components/FieldLabel.vue";

import SharedApiService from "../../../../../Shared/services/api";

import CreateEditContactForm, { COMPANY_USER_FORM_FIELDS } from "./CompanyUserForm.vue";

import { useContactIdentificationStore } from "../../../../../../../stores/communication/contactIdentification";
import { CommunicationApiFactory } from "../../../../../../../services/api/communication/factory";

import { mapState, mapWritableState } from "pinia";
import {CommunicationPusherService} from "../../../../services/communicationPusher";
import {CommunicationRelationTypes} from "../../../../enums/communication";


export default {
    name: "CreateNewRecord",
    components: { CustomButton, FieldLabel, Autocomplete, CreateEditContactForm },

    props: {
        darkMode: {
            type: Boolean,
            required: true
        },
        pusherAppKey: {
            type: String,
            required: false
        },
        pusherAppCluster: {
            type: String,
            required: false
        },
        contactData: {
            type: Object,
            required: false
        }
    },

    data () {
        return {
            communicationPusherService: null,
            communicationApi: CommunicationApiFactory.makeApiService('api'),
            sharedApi: SharedApiService.make(),

            saving: false,
            saveError: 'Error',

            companyOptions: [],
            selectedCompany: {
                id: null,
                name: null
            },
            selectedCompanyId: null,

            disabledContactFormFields: {
                [COMPANY_USER_FORM_FIELDS.MOBILE]: true
            },
            contactFormData: {
                // [COMPANY_USER_FORM_FIELDS.MOBILE]: ''
            },
            contactFormDataErrors: {},
        }
    },
    emits: [ 'contact-saved'],

    computed: {
        ...mapState(useContactIdentificationStore, ['currentCallInfo', 'callLogType']),
        ...mapWritableState(useContactIdentificationStore, ['isContactIdentificationModalVisible']),
    },
    created() {
		if (this.contactData?.nominated_contact) {
			this.handleEditingContact()
		} else {
			this.handleNewContact()
		}

    },

    mounted() {
        if (this.pusherAppKey && this.pusherAppCluster) {
            this.communicationPusherService = CommunicationPusherService.generate(this.pusherAppKey, this.pusherAppCluster)
        }
        this.contactFormData[COMPANY_USER_FORM_FIELDS.MOBILE] = this.currentCallInfo.otherNumber

        this.disableOrEnableContactFields(false);
    },

    methods: {
		handleNewContact() {
			if (this.contactData.identifier_field_type === 'email') {
				this.contactFormData[COMPANY_USER_FORM_FIELDS.EMAIL] = this.contactData.identifier_value
			} else {
				this.contactFormData[COMPANY_USER_FORM_FIELDS.OFFICE_PHONE] = this.contactData.payload?.formatted_office_phone
				this.contactFormData[COMPANY_USER_FORM_FIELDS.MOBILE] = this.contactData.payload?.formatted_cell_phone
			}
		},
        handleEditingContact() {

            this.selectedCompanyId = this.contactData.nominated_contact.contact.company.id
            const selectedCompany = {
                id: this.contactData.nominated_contact.contact.company.id,
                name: this.contactData.nominated_contact.contact.company.name
            }
            this.companyOptions.push(selectedCompany)
            this.selectedCompany = selectedCompany

            this.contactFormData[COMPANY_USER_FORM_FIELDS.FIRST_NAME] = this.contactData.nominated_contact.contact.first_name
            this.contactFormData[COMPANY_USER_FORM_FIELDS.LAST_NAME] =  this.contactData.nominated_contact.contact.last_name
            this.contactFormData[COMPANY_USER_FORM_FIELDS.TITLE] = this.contactData.nominated_contact.contact.title
            this.contactFormData[COMPANY_USER_FORM_FIELDS.EMAIL] = this.contactData.nominated_contact.contact.email
            this.contactFormData[COMPANY_USER_FORM_FIELDS.OFFICE_PHONE] = this.contactData.nominated_contact.contact?.office_phone
            this.contactFormData[COMPANY_USER_FORM_FIELDS.MOBILE] = this.contactData.nominated_contact.contact?.phone

        },

        disableOrEnableContactFields(flag){
            // Keep mobile field always disabled
            Object.values(COMPANY_USER_FORM_FIELDS)
                .filter(key => ![COMPANY_USER_FORM_FIELDS.MOBILE].includes(key))
                .forEach(key =>{
                    this.disabledContactFormFields[key] = flag
                })
        },

        searchCompany (query) {
            this.sharedApi.getAdminCompanies(query).then(resp => this.companyOptions = resp.data.data.companies);
        },

        async handleSave(){
            if (this.saving) return;

            this.saving = true;

            try{
                this.contactFormData['is_contact'] = true;
                let created
                if (this.contactData?.nominated_contact !== null) {

                    this.contactFormData['id'] = this.contactData.nominated_contact.model_relation_id
                    await this.sharedApi.updateCompanyContact(this.selectedCompanyId, this.contactFormData)
                    created = await this.sharedApi.getCompanyContact(this.selectedCompanyId, this.contactData.nominated_contact.model_relation_id)
                } else {
                    created = await this.sharedApi.createCompanyContact(this.selectedCompany.id, this.contactFormData)
                }

                this.isContactIdentificationModalVisible = false

                this.$emit('contact-saved',
                    {
                        relationId: created.data.data.contact.id,
                        contact: created.data.data.contact,
                        company: this.selectedCompany,
                        relationType: CommunicationRelationTypes.COMPANY_USER
                    }
                )

            } catch (error){
                console.log(error)
                this.contactFormDataErrors = error.response.data.errors
            }
            this.saving = false
        },
    },

    watch: {
        selectedCompanyId: function () {
            const company = this.companyOptions.find(company => company.id === this.selectedCompanyId);

            if (company) {
                this.selectedCompany = { ...company };
                this.disableOrEnableContactFields(false);
            } else {
                this.selectedCompany = {id: null, name: null};
                this.companyServices = [];

                this.disableOrEnableContactFields(true);
            }
        },
        existingContact: {
            deep: true,
            handler(oldVal, newVal) {
                console.log("watcher hit!")
                console.log(newVal)
            }
        }
    },
}
</script>
