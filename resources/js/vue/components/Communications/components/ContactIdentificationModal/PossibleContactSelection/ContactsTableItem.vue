<template>
    <div class="font-medium text-sm border-2 px-5 py-3 mt-3 cursor-pointer rounded-lg"
         :class="[darkMode ? 'hover:bg-dark-background border-dark-border' : 'hover:bg-light-background border-light-border text-black',
         selected ? darkMode ? 'bg-dark-background border-primary-300' : 'bg-light-background border-primary-300' : '']">
        <div class="grid grid-cols-3 items-center">
            <span>
                {{ contact.friendlyRelationType }} <br>
                <span class="text-xs">
                    {{ contact.friendlyRelationSubtype }}
                </span>
            </span>
            <span>
                {{ contact.relationData.name }} <br>
                <span class="text-xs">
                    {{ contact.contactName }}
                </span>
            </span>
            <span>{{ contact.previousCommunicationCount }}</span>
        </div>
    </div>
</template>
<script>
export default {
    name: "ContactsTableItem",
    props: {
        contact: {
            type: Object,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: true
        },
        selected: {
            type: Boolean,
            default: true
        }
    },
}
</script>
