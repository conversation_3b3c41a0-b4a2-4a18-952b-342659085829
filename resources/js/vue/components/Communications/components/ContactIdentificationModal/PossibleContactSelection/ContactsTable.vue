<template>
    <div ref="test" class="w-full h-full text-center">
        <contacts-table-header></contacts-table-header>
        <div v-if="loading">
            <loading-spinner></loading-spinner>
        </div>
        <div v-else>
            <div v-if="contacts.length === 0" class="my-4 text-sm font-semibold">
                <p>No contacts found</p>
            </div>
            <div class="max-h-72 overflow-y-scroll" @scroll="handleScroll">
                <contacts-table-item
                    v-for="(contact, idx) in contacts"
                    :dark-mode="darkMode"
                    :contact="contact"
                    @click="handleContactClick(idx)"
                    :selected="selectedContactIdx === idx"
                >
                </contacts-table-item>
            </div>
            <div v-if="showAlert">
                <small :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">
                    {{ alert }}
                </small>
            </div>
        </div>
        <div class="mt-2 flex justify-end">
            <custom-button :dark-mode="darkMode" @click="handleConfirmation" :disabled="confirmationDisabled" color="green">
                <div class="relative">
                    <span class="min-w-[6rem]" :class="saving ? 'invisible' : ''">Confirm</span>
                    <div v-if="saving" role="status" class="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2">
                        <svg aria-hidden="true"  class="w-6 h-6 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
                    </div>
                </div>
            </custom-button>
        </div>
    </div>
</template>

<script>
import FilterConfigModal from "../../../../Shared/components/FilterConfigModal.vue";
import LoadingSpinner from "../../../../Shared/components/LoadingSpinner.vue";
import CustomButton from "../../../../Shared/components/CustomButton.vue";

import ContactsTableHeader from "./ContactsTableHeader.vue";
import ContactsTableItem from "./ContactsTableItem.vue";

import { useContactIdentificationStore } from "../../../../../../stores/communication/contactIdentification";
import { CommunicationApiFactory } from "../../../../../../services/api/communication/factory";


import {mapState, mapWritableState} from "pinia";
import {CommunicationPusherService} from "../../../services/communicationPusher";


const SCROLL_ALERT = {
    DOWN: 'Scroll down to see other possible contacts',
    UP: 'Scroll up to see other possible contacts'
}

export default {
    name: "ContactsTable",
    components: {LoadingSpinner, CustomButton, FilterConfigModal, ContactsTableHeader, ContactsTableItem},

    props: {
        darkMode: {
            type: Boolean,
            required: false,
            default: false,
        },
        contacts: {
            required: true,
            type: Array
        },
        loading: {
            type: Boolean,
            required: false,
            default: false
        },
        pusherAppKey: {
            type: String,
            required: true
        },
        pusherAppCluster: {
            type: String,
            required: true
        }
    },

    data(){
        return {
            communicationApi: CommunicationApiFactory.makeApiService('api'),
            communicationPusherService: null,

            alert: SCROLL_ALERT.DOWN,
            selectedContactIdx: null,
            saving: false,
        }
    },

    mounted() {
        this.communicationPusherService = CommunicationPusherService.generate(this.pusherAppKey, this.pusherAppCluster)
    },

    computed: {
        ...mapState(useContactIdentificationStore, ['currentCallInfo', 'callLogType']),
        ...mapWritableState(useContactIdentificationStore, ['isContactIdentificationModalVisible']),

        showAlert(){
            return this.contacts.length > 3
        },

        confirmationDisabled(){
            return this.selectedContactIdx === null
        }
    },

    methods: {
        handleContactClick(idx){
            // Deselect when clicked twice
            this.selectedContactIdx = idx === this.selectedContactIdx ? null : idx
        },

        async handleConfirmation(){
            const contact = this.contacts[this.selectedContactIdx]

            if (!contact) return

            const { relationId, relationSubtype } = contact

            this.saving = true

            try{
                await this.communicationApi.updateCallLog({
                    relationId,
                    relationType: relationSubtype,
                    id: this.currentCallInfo.logId,
                    type: this.currentCallInfo.callLogType
                })
            }catch(err) {
                console.error(err)
            }

            this.saving = false

            this.isContactIdentificationModalVisible = false
            this.communicationPusherService.triggerOnContactIdentificationClose()
        },

        handleScroll({ target: { scrollTop, clientHeight, scrollHeight }}){
            // Check if scroll is at the bottom
            this.alert = !(scrollTop + clientHeight >= scrollHeight) ? SCROLL_ALERT.DOWN : SCROLL_ALERT.UP
        }
    }
}
</script>

