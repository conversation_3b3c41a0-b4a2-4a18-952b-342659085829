<template>
    <div class="w-full mb-4 text-center text-sm">
        <p>Please choose who you spoke to that represents the phone number: <strong>{{ currentCallInfo.otherNumber }}</strong>. <br>
            If you don't find that person below, you can create a new record.
        </p>
        <p v-if="currentCallInfo.identified" class="mt-2">
            Currently, the call is related to <strong>{{currentCallInfo.identified.relationData.name}}</strong> - <strong>{{currentCallInfo.identified.contactName}}</strong>
        </p>
    </div>
    <contacts-table
        :dark-mode="darkMode"
        :contacts="possibleContacts"
        :loading="loading"
        :pusher-app-cluster="pusherAppCluster"
        :pusher-app-key="pusherAppCluster">
    </contacts-table>
    <button-dropdown @selected="handleSelect" :options="buttonOptions" options-list-placement="left">
        <button class="transition duration-200 text-sm text-white font-medium focus:outline-none py-2 rounded-md px-5 mt-4 bg-primary-500 "
                :class="[darkMode ? 'hover:bg-grey-600' : 'hover:bg-blue-500']"
        >
            Create new record
        </button>
    </button-dropdown>
</template>
<script>
import CustomButton from "../../../../Shared/components/CustomButton.vue";
import ButtonDropdown from "../../../../Shared/components/ButtonDropdown.vue";
import Modal from "../../../../Shared/components/Modal.vue";

import ContactsTable from "./ContactsTable.vue";

import { mapActions, mapState } from "pinia";

import {
    CONTACT_IDENTIFICATION_STORE_METHODS,
    useContactIdentificationStore
} from "../../../../../../stores/communication/contactIdentification";
import {CommunicationApiFactory} from "../../../../../../services/api/communication/factory";

// componentName means vue component file name
const CREATE_NEW_RECORD_OPTIONS = [
    {
        name: 'Company User',
        componentName: 'CreateCompanyUser',
        title: 'Create new company user'
    },
    {
        name: 'Other',
        componentName: 'CreateOther',
        title: 'Leave a note'
    }
]

export default {
    name: "PossibleContactSelection",
    components: {CustomButton, ButtonDropdown, Modal, ContactsTable},

    props: {
        darkMode: {
            type: Boolean,
            required: true,
        },
        pusherAppKey: {
            type: String,
            required: true
        },
        pusherAppCluster: {
            type: String,
            required: true
        }
    },

    data(){
        return {
            communicationApi: CommunicationApiFactory.makeApiService('api'),

            buttonOptions: CREATE_NEW_RECORD_OPTIONS,
            loading: false,
            possibleContacts: []
        }
    },

    async mounted() {
        this.loading = true

        try {
            await this.getPossibleContacts(this.currentCallInfo.otherNumber)
        }catch (err) {
            console.error(err)
        }

        this.loading = false
    },

    computed: {
        ...mapState(useContactIdentificationStore, [
            'currentCallInfo',
            'isContactIdentificationModalVisible'
        ]),
    },

    methods: {
        ...mapActions(useContactIdentificationStore, [
            CONTACT_IDENTIFICATION_STORE_METHODS.SET_CREATE_NEW_RECORD_COMPONENT_SHOWN,
            CONTACT_IDENTIFICATION_STORE_METHODS.RESET_STORE_STATE
        ]),

        async getPossibleContacts(number){
            const { data:lookupData } = await this.communicationApi.lookupCaller(number);

            const { possibleContacts } = lookupData.data

            // sort list to show contacts with previous communications first
            this.possibleContacts = possibleContacts.sort(
                (a,b) => a.previousCommunicationCount > b.previousCommunicationCount ? -1 : 1
            )
        },

        handleSelect(type){
            this[CONTACT_IDENTIFICATION_STORE_METHODS.SET_CREATE_NEW_RECORD_COMPONENT_SHOWN](type)
        },
    },

    watch: {
        isContactIdentificationModalVisible(){
            this[CONTACT_IDENTIFICATION_STORE_METHODS.RESET_STORE_STATE]()
        }
    }
}
</script>

