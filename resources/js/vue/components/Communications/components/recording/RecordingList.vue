<template>
    <div class="shadow-module border rounded-lg max-h-[300px] overflow-x-auto"
         :class="darkModeClasses">
        <ul class="border-t border-b overflow-y-auto" :class="listBackgroundClasses">
            <li v-for="(recording, i) in contactStore.recordings" :key="i"
                :class="recordingItemClasses(darkMode)"
                class="py-5 px-5 border-b transition duration-200 cursor-pointer grid">
                <div class="flex w-full justify-between mb-3">
                    <p class="text-sm font-medium truncate">Call Recording</p>
                    <p class="text-sm text-center" :class="dateTextClasses(darkMode)">
                        {{ $filters.dateFromTimestamp(recording.created_at) }}
                    </p>
                </div>

                <div class="flex">
                    <div class="flex items-center justify-end space-x-2">
                        <div v-if="recording.isPlaying && !recording.isPaused" @click="pause(recording)" class="w-6 h-6 flex justify-center items-center border-blue-550 border rounded bg-cyan-100">
                            <svg class="fill-current text-blue-550" width="10" height="12" viewBox="0 0 10 12" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0H3.75V12H0V0ZM6.25 0H10V12H6.25V0Z" />
                            </svg>
                        </div>

                        <div v-else-if="recording.isPaused" @click="resume(recording)" class="w-6 h-6 flex justify-center items-center border-blue-550 border rounded bg-cyan-100">
                            <svg class="fill-current text-blue-550" width="10" height="12" viewBox="0 0 10 12" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0V12L10 6L0 0Z" />
                            </svg>
                        </div>

                        <div v-else @click="play(recording)" class="w-6 h-6 flex justify-center items-center border-blue-550 border rounded bg-cyan-100">
                            <svg class="fill-current text-blue-550" width="12" height="12" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0V12L10 6L0 0Z" />
                            </svg>
                        </div>

                        <div v-if="recording.isPlaying" @click="stop(recording)" class=" ml-1.5 w-6 h-6 flex justify-center items-center border-blue-550 border rounded bg-cyan-100">
                            <svg class="fill-current text-blue-550" width="10" height="10" viewBox="0 0 10 10" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0 0H12V12H0V0Z" />
                            </svg>
                        </div>
                    </div>
                    <ProgressBar
                        :progress="recording.progress"
                        :playedTime="recording.playedTime || 0"
                        :duration="recording.duration_seconds || 0"
                        @seek="onSeek(recording, $event)"
                        class="w-full"/>
                </div>
            </li>
        </ul>
    </div>
</template>

<script>
import DispatchesGlobalEventsMixin from "../../../../mixins/dispatches-global-events-mixin.js";
import {useContactsStore} from "../../../../../stores/contacts.store.js";
import ProgressBar from './ProgressBar.vue';
import CommunicationMixin from "../../../../mixins/communcation/communication-mixin.js";

export default {
    name: "RecordingList",
    components: {
        ProgressBar
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        consumerProductId: {
            type: Number,
            default: 0
        },
        apiDriver: {
            type: String,
            default: 'api'
        }
    },
    mixins: [DispatchesGlobalEventsMixin, CommunicationMixin],
    data() {
        return {
            contactStore: useContactsStore(),
        };
    },
    computed: {
        darkModeClasses() {
            return {
                'bg-dark-module border-dark-border': this.darkMode,
                'bg-light-module border-light-border': !this.darkMode,
            };
        },
        listBackgroundClasses() {
            return {
                'bg-dark-background border-dark-border': this.darkMode,
                'bg-light-background border-light-border': !this.darkMode,
            };
        }
    },
    methods: {
        recordingItemClasses(darkMode) {
            return {
                'hover:bg-dark-module border-dark-border text-grey-120': darkMode,
                'hover:bg-light-module border-light-border text-grey-900': !darkMode,
            };
        },
        dateTextClasses(darkMode) {
            return {
                'text-blue-400': darkMode,
                'text-grey-400': !darkMode,
            };
        },
        stopIfPlaying(recording) {
            if (recording.audio) {
                recording.audio.pause();
                recording.audio.currentTime = 0;
                recording.isPlaying = false;
                recording.progress = 0;
                recording.isPaused = false;
                recording.audio = null;
            }
        },

        play(recording) {
            this.stopAllRecordings();
            recording.audio = new Audio(recording.recording_link);
            recording.isPlaying = true;
            recording.isPaused = false;

            recording.audio.addEventListener('loadedmetadata', () => {
                recording.duration = recording.audio.duration;
            });

            recording.audio.play();
            this.updateAudioProgress(recording);

            recording.audio.addEventListener('ended', () => {
                this.stopIfPlaying(recording);
            });
        },

        pause(recording) {
            recording.audio?.pause();
            recording.isPaused = true;
        },

        resume(recording) {
            recording.audio?.play();
            recording.isPaused = false;
        },

        stop(recording) {
            this.stopIfPlaying(recording);
        },

        stopAllRecordings() {
            this.contactStore.recordings.forEach(this.stopIfPlaying);
        },

        updateAudioProgress(recording) {
            recording.audio.addEventListener('timeupdate', () => {
                recording.playedTime = recording.audio.currentTime;
                recording.progress = (recording.audio.currentTime / recording.audio.duration) * 100;
            });
        },

        onSeek(recording, seekPercentage) {
            if (recording.audio) {
                const newTime = (seekPercentage / 100) * recording.audio.duration;
                recording.audio.currentTime = newTime;
                recording.progress = seekPercentage;
            }
        }
    },
    mounted() {
        this.dispatchGlobalEvent('request-service');

        this.clearCallCallRecordings();
        this.getCallRecordingsForLeadId(this.consumerProductId);
    }
};
</script>
