<template>
    <div class="relative w-full flex items-center">
        <div class="flex items-center text-sm text-grey-400 mx-3">
            <span>{{ formatTime(playedTime) }}</span>
            / <span>{{ formatTime(duration) }}</span>
        </div>

        <div
            class="relative w-full h-2 bg-gray-300 cursor-pointer rounded"
            @mousedown="startSeek"
            @mousemove="moveSeek"
            @mouseup="endSeek"
            @mouseleave="endSeek"
            ref="progressBar">
            <div
                class="absolute top-0 left-0 h-2 bg-blue-550 rounded"
                :style="{ width: `${progress}%` }">
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        progress: {
            type: Number,
            required: true
        },
        playedTime: {
            type: Number,
        },
        duration: {
            type: Number,
        }
    },
    methods: {
        getSeekPercentage(event) {
            const rect = this.$refs.progressBar.getBoundingClientRect();
            const offsetX = event.clientX - rect.left;
            return (offsetX / rect.width) * 100;
        },
        startSeek(event) {
            this.isSeeking = true;
            const seekPercent = this.getSeekPercentage(event);
            this.$emit('seek', seekPercent);
        },
        moveSeek(event) {
            if (this.isSeeking) {
                const seekPercent = this.getSeekPercentage(event);
                this.$emit('seek', seekPercent);
            }
        },
        endSeek() {
            this.isSeeking = false;
        },
        formatTime(timeInSeconds) {
            if (!timeInSeconds) return "0:00";
            const minutes = Math.floor(timeInSeconds / 60);
            const seconds = Math.floor(timeInSeconds % 60).toString().padStart(2, '0');
            return `${minutes}:${seconds}`;
        }
    }
};
</script>
