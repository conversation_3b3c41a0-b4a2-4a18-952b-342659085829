<template>
    <div :class="[blockDisplay ? '' : 'relative']" ref="container" @keyup="handleKeyUp" tabindex="0">
        <div v-if="!blockDisplay" @click="toggleDialerModal" :class="iconClasses" class="z-0 relative transition duration-200 w-10 h-10 rounded-full inline-flex justify-center items-center cursor-pointer">
            <svg class="fill-current w-4" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16.6877 13.3165L12.8086 9.78955C12.6252 9.62289 12.3842 9.534 12.1366 9.54166C11.8889 9.54931 11.6539 9.65291 11.4812 9.83058L9.19758 12.179C8.64792 12.0741 7.54287 11.7296 6.40537 10.595C5.26787 9.45651 4.92338 8.3486 4.82127 7.80276L7.16784 5.51824C7.34573 5.34564 7.44948 5.1106 7.45714 4.86286C7.4648 4.61511 7.37576 4.37411 7.20887 4.19085L3.68282 0.312706C3.51586 0.128873 3.28382 0.0173642 3.03597 0.00186253C2.78812 -0.0136391 2.54399 0.0680874 2.35542 0.229685L0.284644 2.00558C0.11966 2.17116 0.0211864 2.39153 0.00790328 2.6249C-0.00641086 2.86347 -0.279334 8.51464 4.1027 12.8986C7.92553 16.7204 12.7141 17 14.0329 17C14.2257 17 14.344 16.9943 14.3755 16.9924C14.6088 16.9793 14.8291 16.8804 14.9939 16.7147L16.7688 14.643C16.9311 14.455 17.0134 14.2111 16.9982 13.9632C16.9831 13.7154 16.8716 13.4833 16.6877 13.3165Z"/>
            </svg>
        </div>
        <div v-else>
            <div :class="[darkMode ? 'bg-blue-800 text-blue-550 border border-transparent hover:border-primary-500' : 'bg-cyan-125 text-blue-550 hover:bg-cyan-200']" class="cursor-pointer transition duration-200 rounded-full h-16 w-16 inline-flex items-center justify-center"
                  @click="toggleDialerModal">
                <svg class="fill-current w-6 h-6" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 2C4 3.10457 3.10457 4 2 4C0.89543 4 0 3.10457 0 2C0 0.89543 0.89543 0 2 0C3.10457 0 4 0.89543 4 2Z"/>
                    <path d="M10 2C10 3.10457 9.10457 4 8 4C6.89543 4 6 3.10457 6 2C6 0.89543 6.89543 0 8 0C9.10457 0 10 0.89543 10 2Z"/>
                    <path d="M16 2C16 3.10457 15.1046 4 14 4C12.8954 4 12 3.10457 12 2C12 0.89543 12.8954 0 14 0C15.1046 0 16 0.89543 16 2Z"/>
                    <path d="M16 8C16 9.10457 15.1046 10 14 10C12.8954 10 12 9.10457 12 8C12 6.89543 12.8954 6 14 6C15.1046 6 16 6.89543 16 8Z"/>
                    <path d="M16 14C16 15.1046 15.1046 16 14 16C12.8954 16 12 15.1046 12 14C12 12.8954 12.8954 12 14 12C15.1046 12 16 12.8954 16 14Z"/>
                    <path d="M10 14C10 15.1046 9.10457 16 8 16C6.89543 16 6 15.1046 6 14C6 12.8954 6.89543 12 8 12C9.10457 12 10 12.8954 10 14Z"/>
                    <path d="M10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8Z"/>
                    <path d="M4 8C4 9.10457 3.10457 10 2 10C0.89543 10 0 9.10457 0 8C0 6.89543 0.89543 6 2 6C3.10457 6 4 6.89543 4 8Z"/>
                    <path d="M4 14C4 15.1046 3.10457 16 2 16C0.89543 16 0 15.1046 0 14C0 12.8954 0.89543 12 2 12C3.10457 12 4 12.8954 4 14Z"/>
                </svg>
            </div>
            <p class="text-center font-medium mt-1" :class="[darkMode ? 'text-white' : ' text-black']">
                Dialer
            </p>
        </div>
    </div>
</template>

<script>
export default {
    name: "DialerWrapper",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        blockDisplay: {
            type: Boolean,
            default: false,
        }
    },
    emits: ['toggle-dialer-modal'],

    methods: {
        toggleDialerModal() {
            this.$emit('toggle-dialer-modal')
        },
    },
    computed: {
        iconClasses() {
            const classes = [];

            if(this.darkMode) {
                classes.push('bg-dark-module', 'text-slate-400', 'border-primary-500', 'hover:border-blue-400', 'hover:bg-dark-background');
            } else {
                classes.push('bg-light-module', 'text-slate-600', 'border-light-border', 'hover:border-primary-500', 'hover:bg-cyan-125');
            }
            return classes;
        },
    }
}
</script>

<style scoped>

</style>
