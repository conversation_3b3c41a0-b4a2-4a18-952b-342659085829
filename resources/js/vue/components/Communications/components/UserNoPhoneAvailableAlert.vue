
<template>
    <alert title="Sorry, you don't have a phone configured."
           body="After setting up a phone for you in user management, you will be able to dial your deals."
           button-text="Click here to reload this page"
           @button-click="handleClick"
    >
    </alert>
</template>

<script>
import Alert from "./Alert.vue";

export default {
    name: "UserNoPhoneAvailableAlert",
    components: { Alert },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },

    methods: {
        handleClick(){
            location.reload()
        }
    }
}
</script>
