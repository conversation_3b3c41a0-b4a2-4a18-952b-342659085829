<template>
    <alert title="Please! Allow microphone usage"
           body="We need access to you microphone to connect you with your leads."
           button-text="Click here to reload this page once allowed"
           @button-click="handleClick"
    >
    </alert>
</template>

<script>
import Alert from "./Alert.vue";

export default {
    name: "MicrophoneNotAllowedAlert",
    components: { Alert },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },

    methods: {
        handleClick(){
            location.reload()
        }
    }
}
</script>

<style scoped>

</style>
