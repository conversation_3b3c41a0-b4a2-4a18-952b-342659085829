<template>
    <div>
        <div class="absolute right-0 bottom-14" ref="container">
            <div class="absolute z-30">
                <div class="fixed inset-0 flex items-center justify-center bg-opacity-75 bg-dark-background">
                    <div class="p-4 mx-3 w-80 text-red-800 border border-red-300 rounded-lg bg-red-50"
                         :class="{darkMode: 'bg-gray-800 text-red-400 border-red-800'}"
                         role="alert">
                        <div class="flex items-center">
                            <h3 class="text-lg font-medium"><strong class="mr-1">{{title}}</strong></h3>
                        </div>
                        <div class="mt-2 mb-4 text-sm">
                            {{body}}
                        </div>
                        <div v-if="buttonText" class="w-full flex justify-center">
                            <button type="button"
                                    @click="$emit('button-click')"
                                    class="text-white bg-red-800 hover:bg-red-900 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-xs px-3 py-1.5 mr-2 text-center inline-flex items-center"
                                    :class="{darkMode: 'dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800'}"
                            >
                                {{buttonText}}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "Alert",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },

        title: {
            type: String,
            required: true,
        },
        body: {
            type: String,
            required: true,
        },
        buttonText: {
            type: String,
            required: true,
        }
    },

    emits: ['button-click']
}
</script>

<style scoped>

</style>
