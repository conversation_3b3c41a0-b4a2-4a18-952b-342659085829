<template>
    <div class="flex">
        <Modal
            :dark-mode="darkMode"
            :class="[darkMode ? 'text-gray-100' : 'text-grey-900']"
            :no-buttons="true"
            :small="true"
            @close="closeModal()"
        >
            <template v-slot:content>
                <div class="p-6">
                    <div  class="pb-3 text-xl font-medium text-center">
                        <h3>
                            {{ consumerName }}
                        </h3>
                    </div>

                    <div
                        class="border-l border-r border-t h-80 overflow-y-auto grid grid-cols-1 gap-3 items-end rounded-t p-4 max-h-[40rem]"
                        :class="[darkMode ?  'bg-dark-background border-dark-border'  : 'bg-light-module border-light-border']"
                    >
                        <div v-if="loading" >
                            <LoadingSpinner />
                        </div>
                        <div v-else v-for="message in messages">
                            <outgoing-message v-if="message.direction === 'outbound'" :message="message"
                                              :dark-mode="darkMode"/>
                            <incoming-message v-else :message="message" :dark-mode="darkMode"/>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>

    </div>
</template>

<script>
import { ApiFactory } from "../../LeadProcessing/services/api/factory.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Modal from "../../Shared/components/Modal.vue";
import OutgoingMessage from "../../LeadProcessingNotifications/components/OutgoingMessage.vue";
import IncomingMessage from "../../LeadProcessingNotifications/components/IncomingMessage.vue";

export default {
    name: "SMSHistory",
    components: { IncomingMessage, OutgoingMessage, Modal, LoadingSpinner },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        consumerPhone: {
            type: String,
            required: true,
        },
        consumerName: {
            type: String,
            default: 'Consumer',
        },
        apiDriver: {
            type: String,
            default: 'api',
        }
    },
    emits: ['close-modal'],
    data() {
        return {
            api: ApiFactory.makeApiService(this.apiDriver),
            loading: true,
            messages: [],
        }
    },
    mounted() {
        this.getSMSHistory();
    },
    methods: {
        async getSMSHistory() {
            this.loading = true;
            const response = await this.api.getSMSHistory(this.consumerPhone)
                .catch(e => e)
            if (response?.data?.data?.status) {
                this.messages = response.data.data.messages;
            }
            else {
                console.error(response?.message);
            }

            this.loading = false;
        },
        closeModal() {
            this.$emit('close-modal');
        }
    }
}
</script>