<template>
    <Modal small :dark-mode="darkMode" :hide-confirm="true" :hide-cancel="true" :no-close-button="true" :no-min-height="true" :no-scroll="true">
        <template #header>
            <h2 class="text-xl font-medium">
                <span v-text="title"></span>
            </h2>
        </template>
        <template #content>
            <div class="mt-4 flex items-center">
                <label for="role" class="w-1/3 block mb-1 font-semibold text-md" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']">User</label>
                <div class="flex flex-col w-full">
                    <DropdownWithSearchFilter v-model="form.user_id" :options="sortByName(props.users)" placeholder="Select" :darkMode="darkMode"></DropdownWithSearchFilter>
                    <p v-if="errors.user_id" class="mt-1 text-xs font-medium" :class="darkMode ? 'text-red-400' : 'text-red-400'">{{ errors.user_id[0] }}</p>
                </div>
            </div>

            <div class="mt-4 flex items-center">
                <label for="role" class="w-1/3 block mb-1 font-semibold text-md" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']">Role</label>
                <div class="flex flex-col w-full">
                    <DropdownWithSearchFilter v-model="form.role" :options="sortByName(friendlyRoles)" placeholder="Select" :darkMode="darkMode"></DropdownWithSearchFilter>
                    <p v-if="errors.role" class="mt-1 text-xs font-medium" :class="darkMode ? 'text-red-400' : 'text-red-400'">{{ errors.role[0] }}</p>
                </div>
            </div>

            <div class="mt-4 flex items-center">
                <label for="role" class="w-1/3 block mb-1 font-semibold text-md" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']">Manager</label>
                <div class="flex flex-col w-full">
                    <Dropdown v-model="form.parent_user_role_id" :options="sortByName(roleOptions)" placeholder="None" :darkMode="darkMode" :disabled="roleOptions.length < 2"></Dropdown>
                    <p v-if="errors.parent_user_role_id" class="mt-1 text-xs font-medium" :class="darkMode ? 'text-red-400' : 'text-red-400'">{{ errors.parent_user_role_id[0] }}</p>
                </div>
            </div>
        </template>
        <template #buttons>
            <CustomButton @click="$emit('close')" class="order-last" height="h-10" color="slate-light">Cancel</CustomButton>
            <CustomButton @click="save()" class="order-last" height="h-10" color="primary">Save</CustomButton>
        </template>
    </Modal>
</template>

<script setup>
import { ref, computed } from "vue"
import Modal from '../Shared/components/Modal.vue'
import CustomButton from "../Shared/components/CustomButton.vue"
import Dropdown from '../Shared/components/Dropdown.vue'
import DropdownWithSearchFilter from "../Shared/components/DropdownWithSearchFilter.vue";

const props = defineProps({
    darkMode: false,
    role: null,
    roles: {},
    userRoles: {},
    users: Array
})

const emit = defineEmits(['close', 'done'])

const updating = computed(() => !!props.role)

const title = computed(() => `${(updating.value ? 'Edit' : 'Assign')} User Role`)

const friendlyRoles = computed(() => props.roles.map(role => {
    return {
        id: role,
        name: role.replaceAll("-", " ")
    }
}));

const roleOptions = computed(() => {
    return [
        {
            id: ' ',
            name: 'None'
        }
    ].concat(props.userRoles.filter((item) => {
        return item.id != props.role?.id
    }).map(role => {
        return {
            id: role.id,
            name: role.role.name + ': ' + role.user.name
        }
    }))
})

const form = ref({
    user_id: props.role?.user?.id,
    role: props.role?.role?.id,
    parent_user_role_id: props.role?.parent?.id
})

const errors = ref([])

function save() {
    let route = '/internal-api/v2/user-roles'
    let method = 'post'

    if (updating.value) {
        route += `/${props.role.id}`
        method = 'patch'
    }

    axios({ method: method, url: route, data: form.value })
        .then(() => errors.value = [] && emit('done'))
        .catch(error => errors.value = error.response.data.errors)
}

const sortByName = (array) => {
    return [...array].sort((a, b) => {
        const nameA = a.name.split(" ")[0].toLowerCase();
        const nameB = b.name.split(" ")[0].toLowerCase();
        return nameA.localeCompare(nameB);
    });
};

</script>
