
<template>
    <alerts-container v-if="bundleStore.bundleError" alert-type="error" :text="bundleStore.bundleError" :dark-mode="darkMode"/>
    <alerts-container v-if="bundleStore.bundleSuccess" alert-type="success" :text="bundleStore.bundleSuccess" :dark-mode="darkMode"/>
    <alerts-container v-if="bundleStore.invoiceError" alert-type="error" :text="bundleStore.invoiceError" :dark-mode="darkMode"/>
    <alerts-container v-if="bundleStore.invoiceSuccess" alert-type="success" :text="bundleStore.invoiceSuccess" :dark-mode="darkMode"/>

    <div class="w-full flex-auto pt-3 relative"
         :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
        <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
            <div class="flex items-center justify-between flex-wrap py-4">
                <div class="flex items-center py-1">
                    <h3 class="text-xl font-medium pb-0 mr-4">Bundle Management</h3>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="col-span-1">
                    <bundles :dark-mode="darkMode"></bundles>
                </div>
                <div class="col-span-2">
                    <BundleInvoices :dark-mode="darkMode"></BundleInvoices>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Bundles from "./Components/Bundles.vue";
import BundleInvoices from "./Components/BundleInvoices.vue";
import {useBundlesStore} from "../../../stores/bundle-management.store";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";

export default {
    name: "BundleManagement",
    components: {
        AlertsContainer,
        Bundles,
        BundleInvoices,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        currentUserRoles: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            bundleStore: useBundlesStore(),
        }
    },
    mounted() {
    },
    created() {
        this.bundleStore.currentUserRoles = this.currentUserRoles;
    },
}
</script>

<style scoped>

</style>
