<template>
    <div class="grid lg:grid-cols-4 gap-4">
        <div class="lg:col-span-4 rounded-lg bg-red-100 p-4 mb-3" v-if="bundleStore.bundleError">
            <p class="text-sm font-medium text-red-800">{{ bundleStore.bundleError }}</p>
        </div>
        <div class="lg:col-span-4 border rounded-lg"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="p-5 flex items-center justify-between">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Bundles</h5>
                <div class="flex">
                    <button
                        @click="showFilters = !showFilters"
                        class="flex justify-between mr-3 text-sm font-medium py-2 px-5 transition duration-200 focus:outline-none rounded-md "
                        :class="{'hover:bg-primary-100 bg-light-background text-slate-800': !darkMode, 'bg-grey-600 hover:bg-grey-500 text-white': darkMode}"
                    >
                        Filters
                        <FilterIcon class="ml-3 h-5 w-5"/>
                    </button>
                    <button
                        v-if="bundleStore.canEditBundles()"
                        @click="addBundle"
                        class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                    >
                        Add New Bundle
                    </button>
                </div>
            </div>
            <div>
                <BundleSearch v-show="showFilters" :dark-mode="darkMode" />
            </div>

            <div class="grid grid-cols-12 gap-1 mb-2 px-5 pt-3">
                <p class="col-span-3 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs "
                   :class="bundleStore.bundleFilters.sort_col === 'name' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortBundles('name')">
                    Name
                    <span
                        v-if="bundleStore.bundleFilters.sort_col === 'name'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.bundleFilters.sort_col === 'name' && bundleStore.bundleFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.bundleFilters.sort_col === 'name' && bundleStore.bundleFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.bundleFilters.sort_col !== 'name'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-2 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                   :class="bundleStore.bundleFilters.sort_col === 'cost' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortBundles('cost')">
                    Cost
                    <span
                        v-if="bundleStore.bundleFilters.sort_col === 'cost'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.bundleFilters.sort_col === 'cost' && bundleStore.bundleFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.bundleFilters.sort_col === 'cost' && bundleStore.bundleFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.bundleFilters.sort_col !== 'cost'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-2 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                   :class="bundleStore.bundleFilters.sort_col === 'credit' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortBundles('credit')">
                    Credit
                    <span
                        v-if="bundleStore.bundleFilters.sort_col === 'credit'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.bundleFilters.sort_col === 'credit' && bundleStore.bundleFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.bundleFilters.sort_col === 'credit' && bundleStore.bundleFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.bundleFilters.sort_col !== 'credit'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-2 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs "
                   :class="bundleStore.bundleFilters.sort_col === 'industry_id' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortBundles('industry_id')">
                    Industry
                    <span
                        v-if="bundleStore.bundleFilters.sort_col === 'industry_id'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.bundleFilters.sort_col === 'industry_id' && bundleStore.bundleFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.bundleFilters.sort_col === 'industry_id' && bundleStore.bundleFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.bundleFilters.sort_col !== 'industry_id'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-2 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs "
                   :class="bundleStore.bundleFilters.sort_col === 'active' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortBundles('activated_at')">
                    Active
                    <span
                        v-if="bundleStore.bundleFilters.sort_col === 'activated_at'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.bundleFilters.sort_col === 'activated_at' && bundleStore.bundleFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.bundleFilters.sort_col === 'activated_at' && bundleStore.bundleFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.bundleFilters.sort_col !== 'activated_at'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs ">Actions</p>
            </div>
            <div class="flex items-center justify-center h-100" v-if="bundleStore.loadingBundles">
                <loading-spinner></loading-spinner>
            </div>
            <div class="border-t border-b h-100 overflow-y-auto" v-else
                 :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div>
                    <div
                        v-for="(bundle, index) in bundleStore.bundles" :key="bundle.id"
                        @click="selectBundle(bundle, index)"
                        class="grid grid-cols-12 gap-x-3 border-b px-5 py-3"
                        :class="{
                            'text-slate-900 hover:bg-light-module border-light-border': !darkMode,
                            'text-slate-100 hover:bg-dark-module border-dark-border': darkMode,
                            // Highlight the selected bundle row
                            'bg-light-module border border-blue-400' : activeIndex === index && !darkMode,
                            'bg-dark-module border border-blue-400' : activeIndex === index && darkMode
                        }">
                        <p class="col-span-3 text-sm">
                            {{ bundle.name }}
                        </p>
                        <p class="col-span-2 text-sm">
                            ${{ bundleStore.formatPrice(bundle.cost) }}
                        </p>
                        <p class="col-span-2 text-sm">
                            ${{ bundleStore.formatPrice(bundle.credit) }}
                        </p>
                        <p class="col-span-2 text-sm">
                            {{ bundle.industry_name ?? 'All' }}
                        </p>
                        <p class="col-span-2 text-sm">
                            <CheckCircleIcon v-if="bundle.active" class="w-6 h-6 text-green-500" />
                        </p>
                        <p class="col-span-1 ml-2">
                            <button @click="editBundle(bundle)">
                                <PencilIcon class="w-6 h-6 text-primary-500" />
                            </button>
                        </p>
                    </div>
                </div>
            </div>
            <div class="p-3">
                <Pagination
                    v-if="bundleStore.bundlePaginationData"
                    :dark-mode="darkMode"
                    :pagination-data="bundleStore.bundlePaginationData"
                    :show-pagination="true"
                    @change-page="bundleStore.handleBundlePaginationEvent"></Pagination>
            </div>
            <Modal
                v-if="showModal"
                container-classes="overflow-scroll max-h-[90vh] p-8"
                @confirm="saveBundle"
                @close="closeModal"
                :close-text="'Close'"
                :confirm-text="editingBundle.id ? 'Update' : 'Create'"
                :dark-mode="darkMode"
                :no-buttons="!bundleStore.canEditBundles()"
                :small="true">
                <template v-slot:header>
                    <h4 class="text-xl">{{ editingBundle.id ? 'Edit Bundle' : 'Add Bundle' }}</h4>
                </template>
                <template v-slot:content>
                    <div class="bundle--edit-modal" :class="darkMode ? 'darkmode' : ''">
                        <div class="mb-4">
                            <div class="w-full flex rounded-lg bg-blue-100 p-4 mb-3 text-blue-800">
                                <InformationCircleIcon class="h-10 w-10"/>
                                <p v-if="bundleStore.canEditBundles()" class="ml-4 text-sm font-medium">
                                    To keep our records accurate, the cost and credit fields will be disabled after creating a bundle. To update these fields, simply create a new bundle.
                                </p>
                                <p v-else class="ml-4 text-sm font-medium">Bundle fields are read-only.</p>
                            </div>
                            <label for="bundle-name"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Name *
                            </label>
                            <input
                                id="bundle-name"
                                name="bundle-name"
                                :disabled="!bundleStore.canEditBundles()"
                                required
                                v-model="editingBundle.name"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 disabled:opacity-50 sm:text-sm sm:leading-6"
                                placeholder="Enter bundle name"
                                type="text"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="bundle-active"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Active
                                <input
                                    id="bundle-active"
                                    name="bundle-active"
                                    type="checkbox"
                                    v-model="editingBundle.active"
                                    :disabled="!bundleStore.canEditBundles()"
                                    style="background-color: currentColor;"
                                    class="h-4 w-4 ml-4 rounded disabled:opacity-50 disabled:border-white border-blue-400 bg-dark-background text-blue-600"
                                >
                            </label>
                        </div>
                        <div class="mb-4">
                            <label
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Industry
                            </label>
                            <Dropdown
                                :disabled="!!editingBundle?.id || !bundleStore.canEditBundles()"
                                :dark-mode="darkMode"
                                v-model="editingBundle.industry_id"
                                placeholder="Choose an Industry"
                                :options="Array.from(industryDropdownOptions.values())"
                            ></Dropdown>
                        </div>
                        <div class="mb-4">
                            <label for="bundle-title"
                                class="block text-sm font-medium leading-6"
                                :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Title *
                            </label>
                            <input
                                id="bundle-title"
                                name="bundle-title"
                                aria-describedby="bundle-title-help"
                                required
                                :disabled="!bundleStore.canEditBundles()"
                                v-model="editingBundle.title"
                                class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 disabled:opacity-50 sm:text-sm sm:leading-6"
                                placeholder="A customer friendly title of the bundle"
                                type="text"
                            />
                            <p id="bundle-title-help" class="mt-2 text-sm" :class="{'text-grey-500': !darkMode, 'text-blue-300': darkMode}">
                                Displayed to the customer
                            </p>
                        </div>
                        <div class="mb-4">
                            <label for="bundle-title"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Description *
                            </label>
                            <input
                                id="bundle-desc"
                                name="bundle-desc"
                                aria-describedby="bundle-desc-help"
                                required
                                :disabled="!bundleStore.canEditBundles()"
                                v-model="editingBundle.description"
                                class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 disabled:opacity-50 sm:text-sm sm:leading-6"
                                placeholder="A short description of the bundle, why the customer would want to purchase it"
                                type="text"
                            />
                            <p id="bundle-desc-help" class="mt-2 text-sm" :class="{'text-grey-500': !darkMode, 'text-blue-300': darkMode}">
                                Displayed to the customer
                            </p>
                        </div>
                        <div class="mb-4">
                            <label for="bundle-cost"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Cost *
                            </label>
                            <input
                                id="bundle-cost"
                                name="bundle-cost"
                                required
                                v-model="editingBundle.cost"
                                :disabled="!!editingBundle?.id || !bundleStore.canEditBundles()"
                                class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 disabled:opacity-50 sm:text-sm sm:leading-6"
                                placeholder="Enter bundle cost to the customer"
                                type="number"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="bundle-credit"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Credit *
                            </label>
                            <input
                                id="bundle-credit"
                                name="bundle-credit"
                                required
                                v-model="editingBundle.credit"
                                :disabled="!!editingBundle?.id || !bundleStore.canEditBundles()"
                                class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 disabled:opacity-50 sm:text-sm sm:leading-6"
                                placeholder="Enter bundle credit awarded to the customer"
                                type="number"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="bundle-credit"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Admin Notes
                            </label>
                            <input
                                id="bundle-notes"
                                name="bundle-notes"
                                aria-describedby="bundle-notes-help"
                                :disabled="!bundleStore.canEditBundles()"
                                v-model="editingBundle.note"
                                class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 disabled:opacity-50 sm:text-sm sm:leading-6"
                                placeholder="Optional notes"
                                type="text"
                            />
                            <p id="bundle-notes-help" class="mt-2 text-sm" :class="{'text-grey-500': !darkMode, 'text-blue-300': darkMode}">
                                Optional notes only for SR staff
                            </p>
                        </div>
                    </div>
                </template>
            </Modal>
        </div>
    </div>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Pagination from "../../Shared/components/Pagination.vue";
import BundleSearch from "./BundleSearch.vue";
import {useBundlesStore} from "../../../../stores/bundle-management.store";
import {CheckCircleIcon, ChevronDownIcon, InformationCircleIcon, PencilIcon} from '@heroicons/vue/solid'
import {FilterIcon} from "@heroicons/vue/outline";

export default {
    name: "Bundles",
    components: {
        FilterIcon,
        InformationCircleIcon,
        CheckCircleIcon, PencilIcon, Pagination, Dropdown, Modal, LoadingSpinner, BundleSearch, ChevronDownIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            editingBundle: {},
            showModal: false,
            activeIndex: null,
            bundleStore: useBundlesStore(),
            showFilters: false,
            industryDropdownOptions: [],
        }
    },
    mounted() {
        this.setIndustryDropdownOptions()
    },
    methods: {
        addBundle() {
            this.editingBundle = {};
            this.showModal = true;
        },
        editBundle(bundle) {
            this.editingBundle = {...bundle};
            this.showModal = true;
        },
        saveBundle() {
            if (this.editingBundle.id) this.bundleStore.updateBundle(this.editingBundle);
            else this.bundleStore.createBundle(this.editingBundle);

            this.closeModal();
        },
        selectBundle(bundle, index) {
            // If the passed bundle is already selected, toggle off it's active state and display all Bundle invoices
            if (this.activeIndex === index) {
                this.bundleStore.bundleSelected();
                this.activeIndex = null;
            } else {
                this.bundleStore.bundleSelected(bundle);
                this.activeIndex = index;
            }
        },
        closeModal() {
            this.editingBundle = {};
            this.showModal = false;
        },
        setIndustryDropdownOptions() {
            this.bundleStore.getIndustries().then(resp => {
                if(resp.status) {
                    const industries = resp.data.data.industries;
                    if (industries) {
                        Object.assign(this.industryDropdownOptions, industries);
                    }
                }
                else {
                    throw new Error("Status was not successful");
                }
            }).catch((e) => {
                console.error("Error fetching bundles: ", e); // Log to console for bug reports if needed.
            });
        },
    }
}
</script>

<style lang="postcss" scoped>
.bundle--edit-modal input {
    @apply border-grey-350 bg-light-module;
}
.bundle--edit-modal input:disabled {
    @apply bg-slate-50  text-slate-500 border-slate-200 shadow-none;
}
.bundle--edit-modal.darkmode input {
    @apply border-blue-400 bg-dark-background text-blue-400;
}
.bundle--edit-modal.darkmode input:disabled {
    @apply bg-dark-background text-blue-100 border-blue-800 shadow-none;
}

</style>

