<template>
    <div class="grid lg:grid-cols-4 gap-4">
        <div class="lg:col-span-4 rounded-lg bg-red-100 p-4 mb-3" v-if="bundleStore.invoiceError">
            <p class="text-sm font-medium text-red-800">{{ bundleStore.invoiceError }}</p>
        </div>
        <div class="lg:col-span-4 border rounded-lg"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="p-5 flex items-center justify-between">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Bundle Invoices</h5>
                <div class="flex">
                    <button
                        @click="showFilters = !showFilters"
                        class="flex justify-between mr-3 text-sm font-medium py-2 px-5 transition duration-200 focus:outline-none rounded-md "
                        :class="{'hover:bg-primary-100 bg-light-background text-slate-800': !darkMode, 'bg-grey-600 hover:bg-grey-500 text-white': darkMode}"
                    >
                        Filters
                        <FilterIcon class="ml-3 h-5 w-5"/>
                    </button>
                    <button
                        v-if="bundleStore.canEditInvoices()"
                        @click="addInvoice"
                        class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                    >
                        Add New Invoice
                    </button>
                </div>
            </div>
            <div>
                <BundleInvoiceSearch v-show="showFilters" :dark-mode="darkMode" />
            </div>
            <div class="grid grid-cols-12 gap-x-3 mb-2 px-5  pt-3">
                <p class="col-span-2 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                   :class="bundleStore.invoiceFilters.sort_col === 'bundle_name' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortInvoices('bundle_name')">
                    Bundle Name
                    <span
                        v-if="bundleStore.invoiceFilters.sort_col === 'bundle_name'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.invoiceFilters.sort_col === 'bundle_name' && bundleStore.invoiceFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.invoiceFilters.sort_col === 'bundle_name' && bundleStore.invoiceFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.invoiceFilters.sort_col !== 'bundle_name'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                   :class="bundleStore.invoiceFilters.sort_col === 'status' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortInvoices('status')">
                    Status
                    <span
                        v-if="bundleStore.invoiceFilters.sort_col === 'status'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.invoiceFilters.sort_col === 'status' && bundleStore.invoiceFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.invoiceFilters.sort_col === 'status' && bundleStore.invoiceFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.invoiceFilters.sort_col !== 'status'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-3 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                   :class="bundleStore.invoiceFilters.sort_col === 'company_name' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortInvoices('company_name')">
                    Company
                    <span
                        v-if="bundleStore.invoiceFilters.sort_col === 'company_name'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.invoiceFilters.sort_col === 'company_name' && bundleStore.invoiceFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.invoiceFilters.sort_col === 'company_name' && bundleStore.invoiceFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.invoiceFilters.sort_col !== 'company_name'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                   :class="bundleStore.invoiceFilters.sort_col === 'cost' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortInvoices('cost')">
                    Cost
                    <span
                        v-if="bundleStore.invoiceFilters.sort_col === 'cost'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.invoiceFilters.sort_col === 'cost' && bundleStore.invoiceFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.invoiceFilters.sort_col === 'cost' && bundleStore.invoiceFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.invoiceFilters.sort_col !== 'cost'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                   :class="bundleStore.invoiceFilters.sort_col === 'credit' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortInvoices('credit')">
                    Credit
                    <span
                        v-if="bundleStore.invoiceFilters.sort_col === 'credit'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.invoiceFilters.sort_col === 'credit' && bundleStore.invoiceFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.invoiceFilters.sort_col === 'credit' && bundleStore.invoiceFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.invoiceFilters.sort_col !== 'credit'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-2 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                   :class="bundleStore.invoiceFilters.sort_col === 'issued_at' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortInvoices('issued_at')">
                    Issued At
                    <span
                        v-if="bundleStore.invoiceFilters.sort_col === 'issued_at'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.invoiceFilters.sort_col === 'issued_at' && bundleStore.invoiceFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.invoiceFilters.sort_col === 'issued_at' && bundleStore.invoiceFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.invoiceFilters.sort_col !== 'issued_at'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                   :class="bundleStore.invoiceFilters.sort_col === 'approved_at' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                   @click="bundleStore.sortInvoices('approved_at')">
                    Approved
                    <span
                        v-if="bundleStore.invoiceFilters.sort_col === 'approved_at'"
                        class="ml-2 transform transition-all duration-200 w-6 fill-current"
                        :class="{
                            'text-blue-550 rotate-180': bundleStore.invoiceFilters.sort_col === 'approved_at' && bundleStore.invoiceFilters.sort_dir === 'asc',
                            'text-blue-550': bundleStore.invoiceFilters.sort_col === 'approved_at' && bundleStore.invoiceFilters.sort_dir !== 'asc',
                            'hover:text-cyan-400': bundleStore.invoiceFilters.sort_col !== 'approved_at'
                        }">
                        <ChevronDownIcon />
                    </span>
                </p>
                <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs">Actions</p>
            </div>
            <div class="flex items-center justify-center h-100" v-if="bundleStore.loadingInvoices">
                <loading-spinner></loading-spinner>
            </div>
            <div class="border-t border-b h-100 overflow-y-auto" v-else
                 :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div>
                    <div v-if="!bundleStore.invoices.length" class="w-full flex rounded-lg bg-blue-100 p-4 mb-3 text-blue-800 justify-center">
                        <InformationCircleIcon style="width:20px; height:20px;"  />
                        <p class="ml-4 text-sm font-medium">
                            No invoices to show
                        </p>
                    </div>
                    <div v-for="invoice in bundleStore.invoices" :key="invoice.id"
                         class="grid grid-cols-12 gap-x-3 border-b px-5 py-3"
                         :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                    >
                        <div class="flex items-center col-span-2 text-sm gap-1">
                            <p>
                                {{ invoice.bundle_name }}
                            </p>
                        </div>
                        <p class="text-sm col-span-1">
                            {{ invoice.status }}
                        </p>
                        <p class="text-sm col-span-3">
                            <a :href="'/companies/' + invoice.company_id" class="text-">
                                {{ invoice.company_name }} : {{ invoice.company_id }}
                            </a>
                        </p>
                        <p class="text-sm col-span-1">
                            ${{ bundleStore.formatPrice(invoice.cost) }}
                        </p>
                        <p class="text-sm col-span-1">
                            ${{ bundleStore.formatPrice(invoice.credit) }}
                        </p>
                        <p class="text-sm col-span-2">
                            {{ invoice.issued_at ? $filters.dateFromTimestamp(invoice.issued_at, 'usWithTime') : '' }}
                        </p>
                        <p class="text-sm col-span-1 flex justify-center">
                            <CheckCircleIcon v-if="invoice.approved_at" class="w-6 h-6 text-green-500" />
                            <XCircleIcon v-if="invoice.denied_at" class="w-6 h-6 text-red-400" />
                        </p>
                        <p class="ml-2 col-span-1 text-center">
                            <button @click="editInvoice(invoice)">
                                <PencilIcon class="w-6 h-6 text-primary-500" />
                            </button>
                        </p>
                    </div>
                </div>
            </div>
            <div class="p-3">
                <Pagination
                    v-if="bundleStore.invoicePaginationData"
                    :dark-mode="darkMode"
                    :pagination-data="bundleStore.invoicePaginationData"
                    :show-pagination="true"
                    @change-page="bundleStore.handleInvoicePaginationEvent"></Pagination>
            </div>
            <bundle-invoice-modal
                :dark-mode="darkMode"
                :show-modal="showModal"
                :editing-invoice="editingInvoice"
                @close="closeModal"
            />
        </div>
    </div>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import {dateFromTimestamp} from "../../../../modules/helpers";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import DropdownSelector from "../../Shared/components/DropdownSelector.vue";
import BundleInvoiceSearch from "./BundleInvoiceSearch.vue";
import {useBundlesStore} from "../../../../stores/bundle-management.store";
import Pagination from "../../Shared/components/Pagination.vue";
import BundleInvoiceActionButtons from "./BundleInvoiceActionButtons.vue";
import Tab from "../../Shared/components/Tab.vue";
import BundleInvoiceModal from "./BundleInvoiceModal.vue";
import {ChevronDownIcon, InformationCircleIcon, PencilIcon, XCircleIcon, CheckCircleIcon} from '@heroicons/vue/solid';
import {FilterIcon} from "@heroicons/vue/outline";


export default {
    name: "BundleInvoices",
    components: {
        BundleInvoiceActionButtons,
        Pagination,
        DropdownSelector,
        Autocomplete,
        Dropdown,
        Modal,
        Tab,
        LoadingSpinner,
        BundleInvoiceSearch,
        BundleInvoiceModal,
        ChevronDownIcon,
        InformationCircleIcon,
        PencilIcon,
        XCircleIcon,
        CheckCircleIcon,
        FilterIcon
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            editingInvoice: {},
            showModal: false,
            tradingCompany: null,
            modalError: null,
            bundleStore: useBundlesStore(),
            bundleDropdownOptions: [],
            activeModalTab: 'General',
            showFilters: false
        }
    },
    created() {
    },
    methods: {
        dateFromTimestamp,
        addInvoice() {
            this.editingInvoice = {};
            this.showModal = true;
        },
        editInvoice(bundle) {
            this.editingInvoice = {...bundle};
            this.showModal = true;
        },
        closeModal() {
            this.showModal = false;
        },
    }
}
</script>

<style scoped>

</style>
