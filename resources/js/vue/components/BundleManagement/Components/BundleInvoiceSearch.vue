<template>
    <div class="border"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div>
            <div class="p-5">
                <div class="grid grid-cols-6 gap-3">
                    <div>
                        <input
                            id="invoice-search-bundle-name"
                            name="invoice-search-bundle-name"
                            v-model="bundleStore.invoiceFilters.bundle_name"
                            placeholder="Bundle Name"
                            type="text"
                            class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                        />
                    </div>
                    <div>
                        <autocomplete
                            :dark-mode="darkMode"
                            :inputClass="[
                                'block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6',
                                !darkMode ? 'border-grey-350 bg-light-module' : 'border-blue-400 bg-dark-background text-blue-400'
                            ]"
                            v-model="bundleStore.invoiceFilters.company_name"
                            :options="bundleStore.companyNameResults"
                            placeholder="Company Name"
                            :create-user-input-option="false"
                            @search="bundleStore.searchCompanyNames('name', $event)" />
                    </div>
                    <div>
                        <input
                            id="invoice-search-cost-from"
                            name="invoice-search-cost-from"
                            v-model="bundleStore.invoiceFilters.cost_from"
                            class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                            placeholder="Cost From"
                            type="text"
                        />
                    </div>
                    <div>
                        <input
                            id="invoice-search-cost-to"
                            name="invoice-search-cost-to"
                            v-model="bundleStore.invoiceFilters.cost_to"
                            class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                            placeholder="Cost To"
                            type="text"
                        />
                    </div>
                    <div>
                        <input
                            id="invoice-search-credit-from"
                            name="invoice-search-credit-from"
                            v-model="bundleStore.invoiceFilters.credit_from"
                            class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                            placeholder="Credit From"
                            type="text"
                        />
                    </div>
                    <div>
                        <input
                            id="invoice-search-credit-to"
                            name="invoice-search-credit-to"
                            v-model="bundleStore.invoiceFilters.credit_to"
                            class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                            placeholder="Credit To"
                            type="text"
                        />
                    </div>
                    <div>
                        <Datepicker
                            placeholder="Issued At"
                            v-model="bundleStore.invoiceFilters.issued_at"
                            :range="true"
                            :input-class-name="'bundle-invoice--filter-date ' + (this.darkMode ? 'dark' : '')"
                            :enable-time-picker="false" />
                    </div>
                    <div>
                        <select
                            id="invoice-search-status"
                            name="invoice-search-status"
                            v-model="bundleStore.invoiceFilters.status"
                            class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                            :class="{
                                'text-gray-900': !this.darkMode,
                                'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                            }">
                            <option value="">Any Status</option>
                            <option value="New">New</option>
                            <option value="Issued">Issued</option>
                            <option value="Paid">Paid</option>
                            <option value="Complete">Complete</option>
                            <option value="Cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="flex flex-row justify-start col-span-2">
                        <button
                            type="button"
                            @click="bundleStore.searchInvoices(1)"
                            class="px-4 py-2 h-full mr-3 text-sm rounded-lg text-white font-medium bg-primary-500 hover:bg-blue-500 transition duration-200 inline-flex items-center">
                            <SearchIcon class="mr-3 h-5 w-5"/>
                            Search
                        </button>
                        <button
                            type="button"
                            @click="bundleStore.resetInvoiceFilters()"
                            :class="[!darkMode ? 'bg-light-background hover:bg-light-background text-slate-900' : 'bg-grey-600 hover:bg-grey-500 text-slate-100']"
                            class="px-4 py-2 h-full mr-3 text-sm rounded-lg text-white font-medium  transition duration-200 inline-flex items-center">
                            <RefreshIcon class="mr-3 h-5 w-5"/>
                            Reset
                        </button>
                        <select
                            id="invoice-search-per-page"
                            name="invoice-search-per-page"
                            v-model="bundleStore.invoiceFilters.per_page"
                            class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                            :class="{
                                'text-gray-900': !this.darkMode,
                                'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                            }">
                            <option value="25">25/page</option>
                            <option value="50">50/page</option>
                            <option value="100">100/page</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import {useBundlesStore} from "../../../../stores/bundle-management.store";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import Datepicker from "@vuepic/vue-datepicker";
import {SearchIcon, RefreshIcon} from '@heroicons/vue/solid';
import {FilterIcon} from "@heroicons/vue/outline";

export default {
    name: "BundleInvoiceSearch",
    components: {
        Autocomplete,
        AlertsContainer,
        Datepicker,
        SearchIcon,
        RefreshIcon,
        FilterIcon
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data: function() {
        return {
            bundleStore: useBundlesStore(),
        };
    },
    created: function() {
        this.bundleStore.resetInvoiceFilters();
    }
}
</script>

<style lang="postcss" scoped>

:deep(.bundle-invoice--filter-date) {
    @apply block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6;
}

:deep(.bundle-invoice--filter-date.dark) {
    @apply bg-dark-background text-blue-400;
}

</style>

