<template>
<div>
    <alerts-container v-if="bundleStore.bundleError" :alert-type="'error'" :text="bundleStore.bundleError" :dark-mode="darkMode"/>

    <div class="border"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div>
            <div class="p-5">
                <div class="grid grid-cols-4 gap-3">
                    <div>
                        <input
                            id="bundle-search-cost-from"
                            name="bundle-search-cost-from"
                            v-model="bundleStore.bundleFilters.cost_from"
                            class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            placeholder="Cost From"
                            type="text"
                            :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                        />
                    </div>
                    <div>
                        <input
                            id="bundle-search-cost-to"
                            name="bundle-search-cost-to"
                            v-model="bundleStore.bundleFilters.cost_to"
                            class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            placeholder="Cost To"
                            type="text"
                            :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                        />
                    </div>
                    <div>
                        <input
                            id="bundle-search-credit-from"
                            name="bundle-search-credit-from"
                            v-model="bundleStore.bundleFilters.credit_from"
                            class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            placeholder="Credit From"
                            type="text"
                            :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                        />
                    </div>
                    <div>
                        <input
                            id="bundle-search-credit-to"
                            name="bundle-search-credit-to"
                            v-model="bundleStore.bundleFilters.credit_to"
                            class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            placeholder="Credit To"
                            type="text"
                            :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                        />
                    </div>
                    <div>
                        <input
                            id="bundle-search-name"
                            name="bundle-search-name"
                            v-model="bundleStore.bundleFilters.name"
                            class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            placeholder="Bundle Name"
                            type="text"
                            :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                        />
                    </div>
                    <div>
                        <select
                            id="bundle-search-active"
                            name="bundle-search-active"
                            v-model="bundleStore.bundleFilters.active"
                            class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                            :class="{
                                'text-gray-900': !this.darkMode,
                                'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                            }">
                            <option :value="null">All</option>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                    <div class="flex flex-row justify-start col-span-2">
                        <button
                            type="button"
                            @click="bundleStore.searchBundles(1)"
                            class="px-4 py-2 h-full mr-3 rounded-lg text-white font-medium bg-primary-500 hover:bg-blue-500 transition duration-200 inline-flex items-center">
                            <SearchIcon class="h-5 w-5"/>
                        </button>
                        <button
                            type="button"
                            @click="bundleStore.resetBundleFilters()"
                            :class="[!darkMode ? 'bg-light-background hover:bg-light-background text-slate-900' : 'bg-grey-600 hover:bg-grey-500 text-slate-100']"
                            class="px-4 py-2 h-full mr-3 rounded-lg text-white font-medium  transition duration-200 inline-flex items-center">
                            <RefreshIcon class="h-5 w-5"/>
                        </button>
                        <select
                            id="bundle-search-per-page"
                            name="bundle-search-per-page"
                            v-model="bundleStore.bundleFilters.per_page"
                            class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                            :class="{
                                'text-gray-900': !this.darkMode,
                                'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                            }">
                            <option value="25">25/page</option>
                            <option value="50">50/page</option>
                            <option value="100">100/page</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</template>

<script>
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import {useBundlesStore} from "../../../../stores/bundle-management.store";
import {RefreshIcon, SearchIcon} from "@heroicons/vue/solid";

export default {
    name: "BundleInvoiceSearch",
    components: {
        RefreshIcon,
        SearchIcon,
        AlertsContainer
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data: function() {
        return {
            bundleStore: useBundlesStore(),
        };
    },
    created: function() {
        this.bundleStore.resetBundleFilters();
    }
}
</script>
