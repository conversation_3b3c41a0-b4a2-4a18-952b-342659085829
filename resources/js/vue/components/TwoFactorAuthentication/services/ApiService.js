import axios from 'axios';

export default class ApiService {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('2fa');
    }

    setup(code) {
        return this.axios().post('/setup', {code});
    }

    verify(code) {
        return this.axios().post('/verify', {code});
    }
}
