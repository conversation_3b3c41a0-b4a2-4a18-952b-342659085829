<template>
    <div class="w-full p-4 mt-4">
        <div class="w-full max-w-xl shadow-md rounded-lg overflow-hidden border border-gray-200 mx-auto"
             :class="{'bg-light-module': !darkMode, 'bg-dark-module': darkMode}"
        >
            <div class="px-4 py-2 bg-gray-50 text-lg font-medium">
                Two Factor Authentication
            </div>

            <div class="p-4 text-center">
                <p>Enter the 6-digit authentication code generated by your app:</p>
                <div class="max-w-[50%] w-full mx-auto mt-2">
                    <TwoFactorInput v-model="code" />
                </div>
            </div>

            <div class="p-4 text-center">
                <button @click="authenticate" :disabled="String(code).length !== 6"
                        class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                        :class="{'bg-primary-500 hover:bg-blue-500 text-white': String(code).length === 6, 'bg-primary-200 text-white': String(code).length !== 6}"
                >
                    Verify
                </button>
            </div>

            <div class="p-4" v-if="error">
                <div class="p-2 bg-red-75 text-red-500 rounded-lg">
                    {{ error }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import TwoFactorInput from "./TwoFactorInput.vue";
import {ref, watch} from "vue";
import ApiService from "./services/ApiService";

const props = defineProps({
    darkMode: {
        type: Boolean,
        default: false
    }
});

const code = ref('');
const error = ref(null);
const apiService = ApiService.make();

const authenticate = () => {
    error.value = null;

    apiService.verify(code.value).then(resp => {
        if(resp.data.data.valid) {
            window.location.href = "/";
        } else {
            error.value = "The code you have entered is incorrect.";
        }
    }).catch(e => {
        error.value = `Something went wrong. ${e}`
    });
}

watch(code, () => {
    error.value = null;
})
</script>
