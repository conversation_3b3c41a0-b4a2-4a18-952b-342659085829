<template>
    <div class="w-full flex justify-around" @input="onInput">
        <template v-for="field in 6" :key="field">
            <input
                type="number"
                ref="firstInputEl"
                v-model="data[field - 1]"
                maxlength="1"
                class="border border-gray-200 rounded w-10 h-10 text-center"
                @paste="field === 1 && onPaste($event)"
            />
        </template>
    </div>
</template>

<script setup>
import {ref, watch} from "vue";

const data = ref([]);
const firstInputEl = ref(null);
const emit = defineEmits(['update:modelValue']);

const onInput = (e) => {
    if(e.data && e.target.nextElementSibling) {
        e.target.nextElementSibling.focus();
    } else if (e.data === null && e.target.previousElementSibling) {
        e.target.previousElementSibling.focus();
    }

    emit('update:modelValue', Number(data.value.join('')));
}

const onPaste = (e) => {
    const pasteData = e.clipboardData.getData('text');
    let nextEl = firstInputEl.value[0].nextElementSibling;
    for (let i = 1; i < pasteData.length; i++) {
        if (nextEl) {
            data.value[i] = pasteData[i];
            nextEl = nextEl.nextElementSibling;
        }
    }

    emit('update:modelValue', Number(data.value.join('')));
}
</script>
