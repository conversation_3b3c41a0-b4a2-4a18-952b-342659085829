<template>
    <div>
        <div class="main-layout font-body">
            <div class="w-full">
                <div class="w-full flex-auto pt-2 relative">
                    <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                        <div class="flex items-center justify-between py-4">
                            <div class="flex justify-between items-center py-2">
                                <h3 class="text-xl font-medium pb-0 leading-none mr-5">Minimum Pricing Management</h3>
                            </div>
                            <h3 class="flex text-lg font-medium leading-none items-center cursor-pointer"
                                :class="[darkMode ? 'text-grey-120' : 'text-black']"
                                @click="toggleDefaultPricing(true)"
                                v-if="canEditFloorPricing"
                            >
                                Default Pricing Config
                                <svg class="ml-2 fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0.123035 11.9439L1.96159 15.0579C2.08365 15.2645 2.2845 15.4151 2.51998 15.4767C2.75546 15.5383 3.00629 15.5058 3.21732 15.3864L4.50063 14.661C5.03381 15.0723 5.62307 15.4098 6.24266 15.6618V17.1C6.24266 17.3387 6.33951 17.5676 6.51191 17.7364C6.68431 17.9052 6.91813 18 7.16194 18H10.839C11.0829 18 11.3167 17.9052 11.4891 17.7364C11.6615 17.5676 11.7583 17.3387 11.7583 17.1V15.6618C12.3828 15.4073 12.9688 15.0706 13.5004 14.661L14.7837 15.3864C15.2222 15.6339 15.7866 15.4854 16.0394 15.0579L17.8779 11.9439C17.9989 11.7371 18.0316 11.4918 17.9687 11.2616C17.9059 11.0313 17.7526 10.8347 17.5424 10.7145L16.2812 10.0008C16.3797 9.33704 16.3791 8.66278 16.2793 7.9992L17.5406 7.2855C17.9781 7.038 18.1298 6.4845 17.8761 6.0561L16.0376 2.9421C15.9155 2.73555 15.7146 2.58491 15.4792 2.52331C15.2437 2.46171 14.9929 2.49418 14.7818 2.6136L13.4985 3.339C12.9677 2.92894 12.3818 2.59219 11.7574 2.3382V0.9C11.7574 0.661305 11.6606 0.432387 11.4882 0.263604C11.3158 0.0948211 11.0819 0 10.8381 0H7.16102C6.91721 0 6.68339 0.0948211 6.51099 0.263604C6.33859 0.432387 6.24174 0.661305 6.24174 0.9V2.3382C5.6173 2.59273 5.03122 2.92943 4.49971 3.339L3.21732 2.6136C3.11286 2.55435 2.99749 2.51585 2.87782 2.50029C2.75815 2.48474 2.63652 2.49244 2.51988 2.52295C2.40325 2.55346 2.29391 2.60618 2.19811 2.6781C2.10231 2.75003 2.02194 2.83974 1.96159 2.9421L0.123035 6.0561C0.00204679 6.26292 -0.030577 6.50817 0.0322679 6.73843C0.0951128 6.9687 0.248336 7.16532 0.458571 7.2855L1.71982 7.9992C1.62066 8.66287 1.62066 9.33713 1.71982 10.0008L0.458571 10.7145C0.0209951 10.962 -0.130686 11.5155 0.123035 11.9439V11.9439ZM8.99957 5.4C11.0275 5.4 12.6767 7.0146 12.6767 9C12.6767 10.9854 11.0275 12.6 8.99957 12.6C6.97165 12.6 5.32246 10.9854 5.32246 9C5.32246 7.0146 6.97165 5.4 8.99957 5.4Z"/>
                                </svg>
                            </h3>
                        </div>
                        <div v-if="!showDefaultPricing" class="grid">
                            <Pricing :api="api" :dark-mode="darkMode"/>
                        </div>
                        <div v-else>
                            <DefaultFloorPricing
                                :api="api"
                                :dark-mode="darkMode"
                                @close-default-pricing="toggleDefaultPricing(false)"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <AlertsContainer v-if="alertActive" :dark-mode="darkMode" :alert-type="alertType" :text="alertText" />
    </div>
</template>

<script>
import Pricing from "./components/Pricing.vue";
import RejectionTimeWindow from "./components/RejectionTimeWindow.vue";
import {ApiFactory} from "./services/api/factory";
import Dropdown from "../Shared/components/Dropdown.vue";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import DefaultFloorPricing from "./components/DefaultFloorPricing.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store.js";
export default {
    name: "FloorPriceManagement",
    components: { DefaultFloorPricing, CustomInput, CustomButton, LoadingSpinner, AlertsContainer, Dropdown, RejectionTimeWindow, Pricing},
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: 'api'
        },
    },
    created() {
        this.api = ApiFactory.makeApiService(this.apiDriver);
    },
    data() {
        return {
            api: null,
            showDefaultPricing: false,
            permissionStore: useRolesPermissions(),
        }
    },
    computed: {
        canEditFloorPricing() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_MINIMUM_PRICE_MANAGEMENT_EDIT)
        },
    },
    methods: {
        toggleDefaultPricing(show = true) {
            this.showDefaultPricing = show;
        },
    },
}
</script>
