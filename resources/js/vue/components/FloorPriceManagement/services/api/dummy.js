import {BaseApiService} from "./base";

class DummyApiService extends BaseApiService {
    constructor(delay = 150) {
        super("DummyApiService");

        this.delay = delay;
    }


    _makeResponse(data) {
        return new Promise((resolve, reject) => {
            setTimeout(() => resolve({data: {data}}), this.delay);
        });
    }

    updateAppointmentPricing(data) {
        console.log(data);
        return this._makeResponse({result: true});
    }

    getData(industry, propertyType, qualityTier, stateSelected) {
        return this._makeResponse({
            industry: industry,
            propertyType: propertyType,
            qualityTier: qualityTier,
            stateSelected: stateSelected,
            result: true,
            "data": {"base_table": {"quality_tiers": {"In-Home": {"states": {"AK": {"name": "Alaska", "key": "alaska", "apply_to_all_counties": false, "sales_types": {"Exclusive": {"inherent_price": null, "formula": {"multiplier_value": 1.75, "multiplier_type": "max_revenue", "multiplier_sales_type": "Quad", "multiplier_quality_tier": "Premium"}, "explicit_price": null, "price": 70, "calculated_price": null,}, "Duo": {"inherent_price": null, "formula": {"multiplier_value": 1.75, "multiplier_type": "max_revenue", "multiplier_sales_type": "Quad", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 70, "calculated_price": null,}, "Trio": {"inherent_price": null, "formula": {"multiplier_value": 1.75, "multiplier_type": "max_revenue", "multiplier_sales_type": "Quad", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 70, "calculated_price": null,}}, "counties": {"los-angeles": {"name": "Los Angeles", "sales_types": {"Exclusive": {"inherent_price": 49, "formula": {"multiplier_value": 0.5, "multiplier_type": "max_revenue", "multiplier_sales_type": "Trio", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 83}, "Duo": {"inherent_price": 245, "formula": {"multiplier_value": 0.5, "multiplier_type": "max_revenue", "multiplier_sales_type": "Trio", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 245}, "Trio": {"inherent_price": 245, "formula": {"multiplier_value": 0.5, "multiplier_type": "max_revenue", "multiplier_sales_type": "Trio", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 245}}}, "orange": {"name": "Orange", "sales_types": {"Exclusive": {"inherent_price": 49, "formula": {"multiplier_value": 0.5, "multiplier_type": "max_revenue", "multiplier_sales_type": "Trio", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 49}, "Duo": {"inherent_price": 245, "formula": {"multiplier_value": 0.5, "multiplier_type": "max_revenue", "multiplier_sales_type": "Trio", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 245}, "Trio": {"inherent_price": 245, "formula": {"multiplier_value": 0.5, "multiplier_type": "max_revenue", "multiplier_sales_type": "Trio", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 245}}}, "ventura": {"name": "Ventura", "sales_types": {"Exclusive": {"inherent_price": 49, "formula": {"multiplier_value": 0.5, "multiplier_type": "max_revenue", "multiplier_sales_type": "Trio", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 49}, "Duo": {"inherent_price": 245, "formula": {"multiplier_value": 0.5, "multiplier_type": "max_revenue", "multiplier_sales_type": "Trio", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 245}, "Trio": {"inherent_price": 245, "formula": {"multiplier_value": 0.5, "multiplier_type": "max_revenue", "multiplier_sales_type": "Trio", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 245}}},}}, "AR": {"name": "Arkansas", "key": "arkansas", "apply_to_all_counties": false, "sales_types": {"Exclusive": {"inherent_price": null, "formula": {"multiplier_value": 1.75, "multiplier_type": "max_revenue", "multiplier_sales_type": "Quad", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 70, "calculated_price": null,}, "Duo": {"inherent_price": null, "formula": {"multiplier_value": 1.75, "multiplier_type": "max_revenue", "multiplier_sales_type": "Quad", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 70, "calculated_price": null,}, "Trio": {"inherent_price": null, "formula": {"multiplier_value": 1.75, "multiplier_type": "max_revenue", "multiplier_sales_type": "Quad", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 70, "calculated_price": null,}}, "counties": []},}}, "Online": {"states": {"AK": {"name": "Alaska", "key": "alaska", "apply_to_all_counties": false, "sales_types": {"Exclusive": {"inherent_price": null, "formula": {"multiplier_value": 1.75, "multiplier_type": "max_revenue", "multiplier_sales_type": "Quad", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 200}, "Duo": {"inherent_price": null, "formula": {"multiplier_value": 1.75, "multiplier_type": "max_revenue", "multiplier_sales_type": "Quad", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 200}, "Trio": {"inherent_price": null, "formula": {"multiplier_value": 1.75, "multiplier_type": "max_revenue", "multiplier_sales_type": "Quad", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 200}}, "counties": {"los-angeles": {"name": "Los Angeles", "sales_types": {"Exclusive": {"inherent_price": 49, "formula": {"multiplier_value": 0.5, "multiplier_type": "max_revenue", "multiplier_sales_type": "Trio", "multiplier_quality_tier": "Standard"}, "explicit_price": null, "price": 83}, "Duo": {"inherent_price": 245, "formula": null, "explicit_price": null, "price": 245}, "Trio": {"inherent_price": 245, "formula": null, "explicit_price": null, "price": 245}}}, "orange": {"name": "Orange", "sales_types": {"Exclusive": {"inherent_price": 49, "formula": null, "explicit_price": null, "price": 49}, "Duo": {"inherent_price": 245, "formula": null, "explicit_price": null, "price": 245}, "Trio": {"inherent_price": 245, "formula": null, "explicit_price": null, "price": 245}}}, "ventura": {"name": "Ventura", "sales_types": {"Exclusive": {"inherent_price": 49, "formula": null, "explicit_price": null, "price": 49}, "Duo": {"inherent_price": 245, "formula": null, "explicit_price": null, "price": 245}, "Trio": {"inherent_price": 245, "formula": null, "explicit_price": null, "price": 245}}},}},}}}}, "formula_reference_table": {"quality_tiers": {"Standard": {"states": {"AK": {"name": "Alaska", "key": "alaska", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 20}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 30}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 40}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 50}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 60}}, "counties": {"los-angeles": {"name": "Los Angeles", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}},}, "orange": {"name": "Los Angeles", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}},},}}, "AR": {"name": "Arkansas", "key": "arkansas", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 20}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 30}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 40}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 50}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 60}}, "counties": {"los-angeles": {"name": "Los Angeles", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}},}, "orange": {"name": "Los Angeles", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}},},}},}}, "Premium": {"states": {"AK": {"name": "Alaska", "key": "alaska", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 50}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 100}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 150}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 200}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 250}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 300}}, "counties": {"los-angeles": {"name": "Los Angeles", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}},}, "orange": {"name": "Los Angeles", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}},},}}, "AR": {"name": "Arkansas", "key": "arkansas", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 20}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 30}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 40}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 50}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 60}}, "counties": {"los-angeles": {"name": "Los Angeles", "sales_types": {"Exclusive": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Duo": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Trio": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Quad": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Unverified": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}, "Email Only": {"inherent_price": null, "formula": null, "explicit_price": 10, "price": 10}},},}},}}}}},
        })
    }

}

export {DummyApiService};

