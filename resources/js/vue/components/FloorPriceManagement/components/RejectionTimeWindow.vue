<template>
    <div class="border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <p class="p-5">coming soon...</p>
        <div class="p-5 hidden">
            <h5 class="text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Appointment Rejection Time Window</h5>
            <div class="grid grid-cols-4 gap-4 mb-5">
                <div class="flex items-center">
                    <input v-if="editingTime"
                        class="text-center w-full border border-grey-350 rounded px-4 py-1 mr-2 outline-none"
                        min="0"
                        type="number"
                        placeholder="10"
                        :class="{'border-grey-350 bg-light-module': !darkMode}"
                    />
                    <p v-else class="mr-2">10</p>
                    <span class="text-sm">Day(s)</span>
                </div>
                <div class="flex items-center">
                    <input v-if="editingTime"
                        class="text-center w-full border border-grey-350 rounded px-4 py-1 mr-2 outline-none"
                        min="0"
                        type="number"
                        placeholder="10"
                        :class="{'border-grey-350 bg-light-module': !darkMode}"
                    />
                    <p v-else class="mr-2">10</p>
                    <span class="text-sm">Hour(s)</span>
                </div>
                <div class="flex items-center">
                    <input v-if="editingTime"
                        class="text-center w-full border border-grey-350 rounded px-4 py-1 mr-2 outline-none"
                        min="0"
                        type="number"
                        placeholder="10"
                        :class="{'border-grey-350 bg-light-module': !darkMode}"
                    />
                    <p v-else class="mr-2">10</p>
                    <span class="text-sm">Minute(s)</span>
                </div>
                <div class="flex items-center">
                    <input v-if="editingTime"
                        class="text-center w-full border border-grey-350 rounded px-4 py-1 mr-2 outline-none"
                        min="0"
                        type="number"
                        placeholder="10"
                        :class="{'border-grey-350 bg-light-module': !darkMode}"
                    />
                    <p v-else class="mr-2">10</p>
                    <span class="text-sm">Week(s)</span>
                </div>
            </div>
            <button @click="updateTimes" v-if="editingTime" class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5 mr-3 bg-slate-400 text-white"
            >
                Cancel
            </button>

            <button @click="updateTimes" v-if="editingTime" class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5 bg-primary-500 text-white"
            >
                Update Time Window
            </button>
            <button v-if="!editingTime" @click="updateTimes"
                class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5 border border-primary-500 text-primary-500"
            >
                Edit Time Window
            </button>
        </div>
    </div>
</template>

<script>
export default {
    name: "RejectionTimeWindow",
    props: {
        darkMode : {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            editingTime: false,
        }
    },
    methods: {
        updateTimes() {
            this.editingTime = ! this.editingTime
        }
    }
}
</script>

<style scoped>

</style>
