<template>
    <div>
        <div class="border rounded-lg overflow-hidden relative px-5 py-3"
             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
        >
            <div class="my-3">
                <h3 class="text-lg font-semibold text-primary-500 mb-2">Default pricing</h3>
                <p class="ml-2 italic whitespace-pre-wrap">Default pricing is primarily used to initialise new Industries and Services, or to repair existing Services where price types are missing.
                    <br>These prices are NOT currently used as a fallback for allocation - pricing must still be initialised for any new Services.</p>
            </div>
            <div v-if="saving || loading" class="w-full mt-24">
                <LoadingSpinner />
            </div>
            <div class="mt-6 px-3 py-3 max-h-[50vh] overflow-auto" v-else-if="defaultPricingTable">
                <div v-for="[productName, product] in Object.entries(defaultPricingTable)"
                     class="mb-3"
                >
                    <p class="uppercase text-primary-500 mb-1">
                        {{ productName }}
                    </p>
                    <div class="px-3 border rounded py-2"
                         :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border']"
                    >
                        <div v-for="[qualityTierName, qualityTier] in Object.entries(product)"
                             class="py-1 px-1"
                             :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border']"
                        >
                            <p class="uppercase bold mb-2">
                                {{ qualityTierName }}
                            </p>
                            <div class="grid px-3"
                                 :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border',
                                    gridColumns,
                                 ]"
                            >
                                <div v-for="[saleTypeName, saleType] in Object.entries(qualityTier)"
                                     class="flex items-center gap-x-3 mb-2"
                                     :class="[darkMode ? 'text-gray-300' : 'text-gray-700']"
                                >
                                    <p class="text-sm">{{ saleTypeName }}:</p>
                                    <div class="w-20">
                                        <CustomInput
                                            :dark-mode="true"
                                            v-model="saleType.price"
                                            type="number"
                                            input-class="w-20"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex items-center my-3 gap-x-4 mx-auto justify-center">
                <CustomButton
                    color="primary"
                    @click="saveDefaultPricing()"
                >
                    Save
                </CustomButton>
                <CustomButton
                    @click="closeDefaultPricing()"
                    color="slate-outline"
                >
                    Close
                </CustomButton>
            </div>
        </div>
        <AlertsContainer v-if="alertActive" :dark-mode="darkMode" :alert-type="alertType" :text="alertText" />
    </div>
</template>

<script>

import AlertsMixin from "../../../mixins/alerts-mixin.js";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";

export default {
    name: "DefaultFloorPricing",
    components: { LoadingSpinner, CustomInput, CustomButton, AlertsContainer },
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        api: {
            type: Object,
            default: {}
        },
    },
    emits: ['closeDefaultPricing'],
    data() {
        return {
            showDefaultPricing: false,
            defaultPricingTable: null,
            loading: false,
            saving: false,
            initialised: false,
        }
    },
    computed: {
        gridColumns() {
            let saleTypeMax = 0;
            if (this.defaultPricingTable) {
                for (const product in this.defaultPricingTable) {
                    for (const qualityTier in this.defaultPricingTable[product]) {
                        saleTypeMax = Math.max(Object.values(this.defaultPricingTable[product][qualityTier] ?? []).length, saleTypeMax);
                    }
                }
            }

            return `grid-cols-${saleTypeMax || 8}`;
        }
    },
    mounted() {
        this.getDefaultPricing();
    },
    methods: {
        closeDefaultPricing() {
            this.$emit('closeDefaultPricing');
        },
        getDefaultPricing() {
            if (this.loading) return;
            this.loading = true;

            this.api.getDefaultPricing().then(resp => {
                if (resp.data?.data?.status) {
                    this.defaultPricingTable = resp.data.data.default_pricing;
                }
                else {
                    this.showAlert('error', 'Error fetching default pricing table');
                }
            }).catch(() => {
                this.showAlert('error', 'Error fetching default pricing table');
            }).finally(() => {
                this.loading = false;
            });
        },
        saveDefaultPricing() {
            if (this.saving || !this.validateDefaultPricing()) return;
            this.saving = true;

            this.api.saveDefaultPricing(this.defaultPricingTable).then(resp => {
                if (resp.data?.data?.status)
                    this.showAlert('success', 'Default pricing saved.');
                else
                    this.showAlert('error', 'There was an error saving pricing.');
            }).catch(() => {
                this.showAlert('error', 'There was an error saving pricing.');
            }).finally(() => {
                this.saving = false;
            });
        },
        validateDefaultPricing() {
            for (const product in this.defaultPricingTable) {
                for (const qualityTier in this.defaultPricingTable[product]) {
                    for (const saleType in this.defaultPricingTable[product][qualityTier]) {
                        if (!(parseFloat(this.defaultPricingTable[product][qualityTier][saleType].price) > 0)) {
                            this.showAlert('error', 'All prices must be greater than 0.');
                            return false;
                        }
                    }
                }
            }

            return true;
        }
    },
}
</script>
