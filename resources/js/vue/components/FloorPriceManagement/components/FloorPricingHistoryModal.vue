<template>
    <modal
        :dark-mode="darkMode"
        @close="handleClose"
        :restrict-width="false"
        :full-width="false"
        no-buttons
    >
        <template v-slot:header>
            <div class="font-bold">
                Minimum Pricing History - {{ title }}
            </div>
        </template>
        <template v-slot:content>
            <simple-table
                :loading="loading"
                :dark-mode="darkMode"
                :data="data"
                :headers="headers"
                row-classes="gap-5 grid items-center py-4 rounded px-5"
                no-pagination
                not-found-message="No logs found"
            >
                <template #row.col.reference="{item}">
                    <p>{{item.county ?? item.state}}</p>
                </template>
                <template #row.col.price="{item}">
                    <div class="flex gap-1 items-center">
                        <p>{{ formatCurrency(item.price_from) }}</p>
                        <simple-icon
                            v-if="item.price_to"
                            :icon="simpleIcon.icons.ARROW_RIGHT"
                        ></simple-icon>
                        <p v-if="item.price_to">{{ formatCurrency(item.price_to) }}</p>
                        <simple-icon
                            v-if="item.price_to"
                            :icon="item.price_to > item.price_from ? simpleIcon.icons.ARROW_TENDING_UP : simpleIcon.icons.ARROW_TENDING_DOWN"
                            :color="item.price_to > item.price_from ? simpleIcon.colors.GREEN : simpleIcon.colors.RED"
                            :size="simpleIcon.sizes.MD"
                        ></simple-icon>
                    </div>
                </template>
            </simple-table>
        </template>
    </modal>
</template>
<script>
import Modal from "../../Shared/components/Modal.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import {ApiFactory} from "../services/api/factory.js";
import {useCurrencyHelper} from "../../../../composables/useCurrencyHelper.js";

const currencyHelper = useCurrencyHelper()

export default {
    name: 'PriceHistoryModal',
    components: {SimpleIcon, SimpleTable, LoadingSpinner, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        options: {
            type: Object,
            default: {}
        },

        apiDriver: {
            type: String,
            default: 'api'
        },
    },
    mounted() {
        this.parseOptions()
        this.getPriceHistory()
    },
    computed: {
        title() {
            return [
                this.industryName,
                this.industryServiceName,
                this.propertyType,
                this.qualityTier,
                this.selectedProduct,
                this.selectedStateKey ? 'in ' + this.selectedStateKey : ''
            ].join(' ')
        },
    },
    data() {
        return {
            industryId: null,
            industryName: null,
            industryServiceId: null,
            industryServiceName: null,
            propertyType: null,
            qualityTier: null,
            validSalesTypes: null,
            selectedProduct: null,
            selectedStateId: null,
            selectedStateKey: null,

            api: ApiFactory.makeApiService(this.apiDriver),

            simpleIcon: useSimpleIcon(),
            loading: false,
            data: [],
            headers: [
                {title: 'Date', field: 'date'},
                {title: 'State / County', field: 'reference'},
                {title: 'Sale Type', field: 'sale_type'},
                {title: 'Price', field: 'price'},
                {title: 'Causer', field: 'causer_name'},
            ],
        }
    },
    methods: {
        formatCurrency(amount) {
            return currencyHelper.formatCurrency(amount, {
                decimals: 0
            })
        },
        parseOptions() {
            const {
                industry_id: industryId,
                industry_name: industryName,
                industry_service_id: industryServiceId,
                industry_service_name: industryServiceName,
                property_type: propertyType,
                quality_tier: qualityTier,
                valid_sales_types: validSalesTypes,
                selected_product: selectedProduct,
                selected_state_id: selectedStateId,
                selected_state_key: selectedStateKey,
            } = this.options

            Object.assign(this, {
                industryId,
                industryName,
                industryServiceId,
                industryServiceName,
                propertyType,
                qualityTier,
                validSalesTypes,
                selectedProduct,
                selectedStateId,
                selectedStateKey,
            })
        },
        async getPriceHistory() {
            this.loading = true

            const res = await this.api.getPriceHistory(
                this.selectedProduct,
                this.industryId,
                this.industryServiceId,
                this.propertyType,
                this.qualityTier,
                this.selectedStateId,
            )

            this.data = res.data.data.history;

            this.loading = false
        },
        handleClose() {
            this.$emit('close')
        },
    }
}
</script>
