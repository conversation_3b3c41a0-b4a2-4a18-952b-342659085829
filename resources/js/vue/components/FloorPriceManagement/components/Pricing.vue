<template>
    <div class="border rounded-lg overflow-hidden relative" :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <Tab
            :dark-mode="darkMode"
            :tabs="tabs"
            @selected="processTabFilter"
            :tab-type="'Normal'"
            :tabs-classes="'w-full md:w-2/3 lg:w-1/3'"
        />

        <div class="grid grid-cols-6 gap-3 px-5 mt-6">
            <div v-if="industryFilterOptions">
                <p :class="[darkMode ? 'text-slate-300' : 'text-slate-500']" class="mb-2 text-sm font-medium">Industry</p>
                <Dropdown :dark-mode="darkMode" v-model="industryId" :options="industryFilterOptions" v-on:update:modelValue="updateIndustryServiceOptions"/>
            </div>
            <div v-if="industryServiceOptions">
                <p :class="[darkMode ? 'text-slate-300' : 'text-slate-500']" class="mb-2 text-sm font-medium">Industry Service</p>
                <Dropdown :dark-mode="darkMode" v-model="industryServiceId" :options="industryServiceOptions" />
            </div>
            <div>
                <p :class="[darkMode ? 'text-slate-300' : 'text-slate-500']" class="mb-2 text-sm font-medium">Property Type</p>
                <Dropdown :dark-mode="darkMode" v-model="propertyType" :options="propertyTypeFilterOptions" v-on:update:modelValue="getData(!this.stateSelected)"/>
            </div>
            <div>
                <p :class="[darkMode ? 'text-slate-300' : 'text-slate-500']" class="mb-2 text-sm font-medium">Quality Tier</p>
                <Dropdown :dark-mode="darkMode" v-model="qualityTier" :options="qualityTierFilterOptions" v-on:update:modelValue="getData(!this.stateSelected)"/>
            </div>
            <div class="flex items-end col-start-5 w-max gap-x-4">
                <CustomButton @click="getExportableIndustryServices" v-if="canEditFloorPricing">
                    Import Pricing
                </CustomButton>
                <CustomButton
                    @click="showSetNationalPricingModal"
                    color="primary-outline"
                    :dark-mode="darkMode"
                >
                    National Pricing
                </CustomButton>
                <CustomButton
                    @click="togglePriceHistoryModal(true)"
                    color="slate-outline"
                    :dark-mode="darkMode"
                >
                    View history
                </CustomButton>
            </div>
        </div>

        <div class="px-5 pt-7 flex items-center">
            <div class="flex items-center">
                <h5 @click="returnToStateLevel" :class="[darkMode ? (stateSelected ? 'text-primary-500 hover:text-primary-600' : 'text-slate-100') :  (stateSelected ? 'text-primary-500 hover:text-primary-600' : 'text-slate-900')]" class="cursor-pointer text-base capitalize font-medium leading-tight">States</h5>
                <svg v-if="stateSelected != null"  class="h-full w-2 flex-shrink-0 mx-3 fill-current text-primary-500" width="17" height="32" viewBox="0 0 17 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L16.7071 15.2929C17.0976 15.6834 17.0976 16.3166 16.7071 16.7071L1.70711 31.7071C1.31658 32.0976 0.683417 32.0976 0.292893 31.7071C-0.0976311 31.3166 -0.0976311 30.6834 0.292893 30.2929L14.5858 16L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"/>
                </svg>
                <h5 v-if="stateSelected != null" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="text-base capitalize font-medium leading-tight">
                    {{ $filters.toProperCase(stateSelected.key) }}
                </h5>
            </div>
        </div>

        <div class="grid gap-3 mb-2 mt-8 px-5 text-slate-400 mr-3" :class="[gridColumns]">
            <p class="font-medium tracking-wide uppercase text-xs"> {{ stateSelected === null ? 'State' : 'County' }}</p>
            <p v-for="saleType in validSalesTypes"
                class="font-medium tracking-wide uppercase text-xs text-center"
               >
                {{ $filters.toProperCase(saleType) }}
            </p>
            <p v-if="useFormulaPricing" class="font-medium tracking-wide uppercase text-xs text-right">Price Configuration</p>
        </div>

        <div class="border-y overflow-y-auto h-88"
             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
            <div v-if="loading || saving" class="flex h-full items-center justify-center">
                <LoadingSpinner/>
            </div>
            <!-- STATE LEVEL -->
            <div v-else-if="needsInitialising || needsRepair">
                <div class="flex flex-col items-center gap-4 justify-center text-center mx-auto mt-16 max-w-[48rem]">
                    <p v-if="needsInitialising" class="whitespace-pre-line">No Minimum Pricing has been set up for {{ getIndustryName(industryId) }} / {{ getServiceName(industryServiceId) }}.
                        <br>Use the button below to set up standard pricing, or the 'Copy Pricing' button in the top bar to copy Minimum Prices from another Industry Service.</p>
                    <p v-else-if="needsRepair" class="whitespace-pre-line">There are missing State prices for {{ getIndustryName(industryId) }} / {{ getServiceName(industryServiceId) }}.
                        <br>This generally means a property type, quality tier or sale type has been added to the Industry Service since the pricing was initialised.
                        <br>Use the button below to automatically fill in the missing prices from existing data.
                        <br>Alternatively, manually fill in any 0 prices below.
                    </p>
                    <CustomButton
                        @click="needsInitialising ? initialisePricingForIndustryService() : repairPricingForIndustryService()"
                        class="my-4"
                    >
                        {{ needsInitialising ? 'Initialise Pricing' : 'Repair Pricing' }}
                    </CustomButton>
                </div>
            </div>
            <div v-if="stateSelected === null && !loading" v-for="([stateKey, state]) in Object.entries(pricingData)"
                 :key="stateKey"
                 class="grid gap-3 border-b relative px-5 py-6 items-center"
                 :class="[
                     darkMode ? 'text-slate-100 hover:bg-dark-module border-dark-border' : 'text-slate-900 hover:bg-light-module border-light-border',
                     gridColumns
                 ]"
            >
                <p @click="selectState(state.state_location_id, stateKey)" class="font-semibold cursor-pointer text-primary-500 text-sm">{{ $filters.toProperCase(stateKey) }}</p>
                <div v-for="salesType in validSalesTypes">
                    <div v-if="state[salesType]?.explicit_price">
                        <p class="text-sm tracking-wide text-center font-medium"
                           :class="[state[salesType].calculated_price ? 'line-through opacity-50' : '']"
                        >
                            {{ formatPrice(state[salesType].explicit_price) }}
                        </p>
                        <svg v-if="state[salesType].calculated_price" class="mx-2 flex-shrink-0 fill-amber-600" width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M10.293 0.292787C10.4805 0.105316 10.7348 0 11 0C11.2652 0 11.5195 0.105316 11.707 0.292787L15.707 4.29279C15.8945 4.48031 15.9998 4.73462 15.9998 4.99979C15.9998 5.26495 15.8945 5.51926 15.707 5.70679L11.707 9.70679C11.5184 9.88894 11.2658 9.98974 11.0036 9.98746C10.7414 9.98518 10.4906 9.88001 10.3052 9.6946C10.1198 9.5092 10.0146 9.25838 10.0123 8.99619C10.01 8.73399 10.1108 8.48139 10.293 8.29279L12.586 5.99979H1C0.734784 5.99979 0.48043 5.89443 0.292893 5.70689C0.105357 5.51936 0 5.265 0 4.99979C0 4.73457 0.105357 4.48022 0.292893 4.29268C0.48043 4.10514 0.734784 3.99979 1 3.99979H12.586L10.293 1.70679C10.1055 1.51926 10.0002 1.26495 10.0002 0.999786C10.0002 0.734622 10.1055 0.480314 10.293 0.292787V0.292787Z"/>
                        </svg>
                        <p v-if="state[salesType].calculated_price" class="text-sm text-center font-semibold">${{ formatPrice(state[salesType].calculated_price) }}</p>
                        <div v-if="state[salesType].calculated_price || state.apply_to_all_counties" class="absolute left-0 h-full animate-pulse w-1 bg-amber-600"></div>
                    </div>
                    <p v-else class="text-sm tracking-wide text-center font-medium text-red-500">
                        {{ formatPrice(0) }}
                    </p>
                </div>

                <div v-if="canEditFloorPricing" @click="editPricingConfig(state, stateKey, 'state')" class=" cursor-pointer inline-flex items-center justify-end">
                    <span class="mr-2 text-sm text-primary-500">Edit</span>
                    <svg  class="cursor-pointer" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0.0956937 9.2897L1.52568 11.7117C1.62062 11.8724 1.77684 11.9895 1.95999 12.0374C2.14314 12.0853 2.33823 12.0601 2.50236 11.9672L3.50049 11.403C3.91519 11.7229 4.3735 11.9854 4.8554 12.1814V13.3C4.8554 13.4857 4.93073 13.6637 5.06482 13.795C5.19891 13.9263 5.38077 14 5.5704 14H8.43037C8.62 14 8.80186 13.9263 8.93595 13.795C9.07003 13.6637 9.14536 13.4857 9.14536 13.3V12.1814C9.63104 11.9834 10.0869 11.7216 10.5003 11.403L11.4984 11.9672C11.8395 12.1597 12.2785 12.0442 12.4751 11.7117L13.9051 9.2897C13.9992 9.12884 14.0245 8.93809 13.9757 8.759C13.9268 8.5799 13.8076 8.42697 13.6441 8.3335L12.6631 7.7784C12.7398 7.26215 12.7393 6.73772 12.6617 6.2216L13.6427 5.6665C13.983 5.474 14.101 5.0435 13.9036 4.7103L12.4737 2.2883C12.3787 2.12765 12.2225 2.01048 12.0393 1.96257C11.8562 1.91466 11.6611 1.93992 11.497 2.0328L10.4988 2.597C10.086 2.27806 9.63031 2.01615 9.14465 1.8186V0.7C9.14465 0.514348 9.06932 0.336301 8.93523 0.205025C8.80114 0.0737498 8.61928 0 8.42965 0H5.56968C5.38005 0 5.19819 0.0737498 5.0641 0.205025C4.93002 0.336301 4.85469 0.514348 4.85469 0.7V1.8186C4.36901 2.01657 3.91318 2.27845 3.49978 2.597L2.50236 2.0328C2.42111 1.98672 2.33138 1.95677 2.23831 1.94467C2.14523 1.93257 2.05062 1.93856 1.95991 1.96229C1.8692 1.98602 1.78415 2.02703 1.70964 2.08297C1.63513 2.13891 1.57262 2.20868 1.52568 2.2883L0.0956937 4.7103C0.00159194 4.87116 -0.0237821 5.06191 0.0250973 5.241C0.0739766 5.4201 0.19315 5.57303 0.356666 5.6665L1.33764 6.2216C1.26051 6.73779 1.26051 7.26221 1.33764 7.7784L0.356666 8.3335C0.0163295 8.526 -0.101644 8.9565 0.0956937 9.2897V9.2897ZM6.99967 4.2C8.57694 4.2 9.85964 5.4558 9.85964 7C9.85964 8.5442 8.57694 9.8 6.99967 9.8C5.42239 9.8 4.13969 8.5442 4.13969 7C4.13969 5.4558 5.42239 4.2 6.99967 4.2Z" fill="#0081FF"/>
                    </svg>
                </div>
            </div>
            <!-- COUNTY LEVEL -->
            <div v-else v-for="([countyKey, county]) in Object.entries(pricingData)"
                 :key="countyKey"
                 class="grid gap-3 border-b relative px-5 py-6 items-center"
                 :class="[
                     darkMode ? 'text-slate-100 hover:bg-dark-module border-dark-border' : 'text-slate-900 hover:bg-light-module border-light-border',
                     gridColumns,
                 ]"
            >
                <p class="font-semibold text-sm">{{ $filters.toProperCase(countyKey) }}</p>
                <div v-for="salesType in validSalesTypes" class="flex items-center justify-center">
                    <div v-if="county[salesType]">
                        <p class="text-sm text-center font-medium"
                           :class="[county[salesType].calculated_price ? 'line-through opacity-50' : '']"
                        >
                            {{ formatPrice(county[salesType].explicit_price) }}
                        </p>
                        <div v-if="useFormulaPricing">
                            <svg v-if="county[salesType].calculated_price" class="mx-2 flex-shrink-0 fill-amber-600" width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.293 0.292787C10.4805 0.105316 10.7348 0 11 0C11.2652 0 11.5195 0.105316 11.707 0.292787L15.707 4.29279C15.8945 4.48031 15.9998 4.73462 15.9998 4.99979C15.9998 5.26495 15.8945 5.51926 15.707 5.70679L11.707 9.70679C11.5184 9.88894 11.2658 9.98974 11.0036 9.98746C10.7414 9.98518 10.4906 9.88001 10.3052 9.6946C10.1198 9.5092 10.0146 9.25838 10.0123 8.99619C10.01 8.73399 10.1108 8.48139 10.293 8.29279L12.586 5.99979H1C0.734784 5.99979 0.48043 5.89443 0.292893 5.70689C0.105357 5.51936 0 5.265 0 4.99979C0 4.73457 0.105357 4.48022 0.292893 4.29268C0.48043 4.10514 0.734784 3.99979 1 3.99979H12.586L10.293 1.70679C10.1055 1.51926 10.0002 1.26495 10.0002 0.999786C10.0002 0.734622 10.1055 0.480314 10.293 0.292787V0.292787Z"/>
                            </svg>
                            <p v-if="county[salesType].calculated_price" class="text-sm text-center font-semibold">${{ formatPrice(county[salesType].calculated_price) }}</p>
                            <div v-if="county[salesType].calculated_price || county.apply_to_all_counties" class="absolute left-0 h-full animate-pulse w-1 bg-amber-600"></div>
                        </div>
                    </div>
                    <p v-else class="text-sm text-center font-medium text-red-500">
                        {{ formatPrice(0) }}
                    </p>
                </div>

                <div v-if="canEditFloorPricing" @click="editPricingConfig(county, countyKey, 'county')" class="cursor-pointer inline-flex items-center justify-end">
                    <span class="mr-2 text-sm text-primary-500">Edit</span>
                    <svg  class="cursor-pointer" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0.0956937 9.2897L1.52568 11.7117C1.62062 11.8724 1.77684 11.9895 1.95999 12.0374C2.14314 12.0853 2.33823 12.0601 2.50236 11.9672L3.50049 11.403C3.91519 11.7229 4.3735 11.9854 4.8554 12.1814V13.3C4.8554 13.4857 4.93073 13.6637 5.06482 13.795C5.19891 13.9263 5.38077 14 5.5704 14H8.43037C8.62 14 8.80186 13.9263 8.93595 13.795C9.07003 13.6637 9.14536 13.4857 9.14536 13.3V12.1814C9.63104 11.9834 10.0869 11.7216 10.5003 11.403L11.4984 11.9672C11.8395 12.1597 12.2785 12.0442 12.4751 11.7117L13.9051 9.2897C13.9992 9.12884 14.0245 8.93809 13.9757 8.759C13.9268 8.5799 13.8076 8.42697 13.6441 8.3335L12.6631 7.7784C12.7398 7.26215 12.7393 6.73772 12.6617 6.2216L13.6427 5.6665C13.983 5.474 14.101 5.0435 13.9036 4.7103L12.4737 2.2883C12.3787 2.12765 12.2225 2.01048 12.0393 1.96257C11.8562 1.91466 11.6611 1.93992 11.497 2.0328L10.4988 2.597C10.086 2.27806 9.63031 2.01615 9.14465 1.8186V0.7C9.14465 0.514348 9.06932 0.336301 8.93523 0.205025C8.80114 0.0737498 8.61928 0 8.42965 0H5.56968C5.38005 0 5.19819 0.0737498 5.0641 0.205025C4.93002 0.336301 4.85469 0.514348 4.85469 0.7V1.8186C4.36901 2.01657 3.91318 2.27845 3.49978 2.597L2.50236 2.0328C2.42111 1.98672 2.33138 1.95677 2.23831 1.94467C2.14523 1.93257 2.05062 1.93856 1.95991 1.96229C1.8692 1.98602 1.78415 2.02703 1.70964 2.08297C1.63513 2.13891 1.57262 2.20868 1.52568 2.2883L0.0956937 4.7103C0.00159194 4.87116 -0.0237821 5.06191 0.0250973 5.241C0.0739766 5.4201 0.19315 5.57303 0.356666 5.6665L1.33764 6.2216C1.26051 6.73779 1.26051 7.26221 1.33764 7.7784L0.356666 8.3335C0.0163295 8.526 -0.101644 8.9565 0.0956937 9.2897V9.2897ZM6.99967 4.2C8.57694 4.2 9.85964 5.4558 9.85964 7C9.85964 8.5442 8.57694 9.8 6.99967 9.8C5.42239 9.8 4.13969 8.5442 4.13969 7C4.13969 5.4558 5.42239 4.2 6.99967 4.2Z" fill="#0081FF"/>
                    </svg>
                </div>
            </div>
        </div>

        <!--   EDIT PRICES MODAL     -->
        <Modal v-if="editingPricingConfig !== null"
           :dark-mode="darkMode"
           :no-buttons="true"
           :no-close-button="true"
           :full-width="true"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium"><span class="text-slate-500">Configure Pricing for</span> {{editingPricingConfig.name}}</h4>
            </template>
            <template v-slot:content>
                <div class="relative">
                    <div :class="[saving ? 'opacity-30 pointer-events-none' : '']">
                        <div v-if="editingPricingConfig.region_type === 'national'" class="text-red-400 text-center mb-4 px-5 mx-auto">
                            WARNING: Setting National prices will overwrite every State and County price for every Sale Type in this Industry Service / Property Type / Quality Tier.
                        </div>
                        <div class="flex border-b pb-2"
                            :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                        >
                            <div class="w-full">
                                <div class="grid gap-3"
                                     :class="[darkMode ? 'text-slate-400' : 'text-slate-500',
                                        stateSelected ? 'grid-cols-4' : 'grid-cols-3',
                                ]">
                                    <p class="font-medium tracking-wide uppercase text-xs">Sale Type</p>
                                    <p v-show="stateSelected" class="font-medium tracking-wide uppercase text-xs text-center">State Price</p>
                                    <p class="font-medium tracking-wide uppercase text-xs text-center">Price</p>
                                    <p class="font-medium tracking-wide uppercase text-xs text-center">Initial Price</p>
                                </div>
                                <div v-for="salesType in validSalesTypes"
                                     :key="salesType"
                                     class="grid gap-3 relative py-6 items-center"
                                     :class="[darkMode ? 'border-dark-border' : 'border-light-border',
                                        stateSelected ? 'grid-cols-4' : 'grid-cols-3',
                                     ]"
                                >
                                    <div class="flex items-center font-bold">
                                        <span>{{ $filters.toProperCase(salesType) }}</span>
                                    </div>
                                    <p v-show="stateSelected" class="opacity-80 text-center">{{ formatPrice(editingPricingConfig.prices[salesType].inherited_price) }}</p>
                                    <input v-model="editingPricingConfig.prices[salesType].explicit_price" type="number"
                                       :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']"
                                       class="px-2 py-1 h-9 text-center rounded border w-full"
                                       @change="handleProposedPriceChange"
                                       @keyup.enter="saveFloorPricing()"
                                    />
                                    <p class="text-center opacity-70">
                                        {{ formatPrice(editingPricingConfig.initial[salesType].explicit_price) }}
                                    </p>
                                </div>
                            </div>
                            <div v-if="editingPricingConfig?.pricesLowered"
                                 class="border-l ml-3 pl-3 w-2/3 relative"
                                :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                            >
                                <p class="uppercase leading-tight text-primary-500 text-sm">Lowered Price Policy</p>
                                <div class="flex mt-2 items-center gap-x-2">
                                    <Dropdown
                                        :dark-mode="darkMode"
                                        :options="loweredFloorPolicyOptions"
                                        v-model="selectedLoweredFloorPolicy"
                                    />
                                    <Tooltip
                                        v-if="selectedLoweredFloorPolicy > 0"
                                        :dark-mode="darkMode"
                                        :right="true"
                                    >
                                        <template v-slot:default>
                                            <p class="whitespace-pre-wrap">
                                                {{ loweredFloorPolicyDescription[selectedLoweredFloorPolicy] ?? '' }}
                                            </p>
                                        </template>
                                    </Tooltip>
                                </div>
                                <div v-if="selectedLoweredFloorPolicy === 2"
                                    class="mt-4"
                                >
                                    <p class="text-sm pb-2">
                                        Create bids for Campaigns with leads purchased in the last X days:
                                    </p>
                                    <CustomInput
                                        :dark-mode="darkMode"
                                        v-model="loweredFloorPriceRecentPurchaseThreshold"
                                        type="number"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center justify-end mt-6">
                            <button @click="saveFloorPricing()" class="bg-primary-500 hover:bg-primary-600 transition duration-200 text-white rounded-md px-5 py-2 text-sm font-semibold mr-4">Save</button>
                            <button @click="discardAllChanges()" class="bg-primary-500 hover:bg-primary-600 transition duration-200 text-white rounded-md px-5 py-2 text-sm font-semibold">Close</button>
                        </div>
                    </div>
                    <div v-if="saving" class="absolute flex mx-auto h-full w-full items-center pointer-events-none top-[-2rem]">
                        <LoadingSpinner />
                    </div>
                </div>


            </template>
        </Modal>

        <!--    IMPORT FLOOR PRICING MODAL    -->
        <Modal
            v-if="showImportPricingModal"
            :dark-mode="darkMode"
            :small="true"
            confirm-text="Import"
            close-text="Cancel"
            @close="toggleImportPricingModal(false)"
            @confirm="copyPricingFromIndustryService"
            :disable-confirm="!!importing || saving || !(importIndustryOptions.length > 0)"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium inline-flex items-center">
                    Import Pricing
                </h4>
            </template>
            <template v-slot:content>
                <div v-if="loadingModal || importing">
                    <p v-if="importing">Importing prices from {{ getImportIndustryName() }} / {{ getImportServiceName() }}, this will take a few moments...</p>
                    <loading-spinner />
                </div>
                <div v-else>
                    <div class="flex flex-col gap-2 items-center justify-center">
                        <p>Import all minimum prices from another Industry Service.</p>
                        <p>Warning: this will overwrite ALL prices for {{ getIndustryName(industryId) }} / {{ getServiceName(industryServiceId) }}.</p>
                    </div>
                    <p class="mt-6 text-center">Import pricing from...</p>
                    <div v-if="!(importIndustryOptions.length > 0)" class="py-4 text-center font-semibold">
                        No Industry Services with exportable prices were found. Please Initialize the pricing for at least on Service.
                    </div>
                    <div v-else class="flex items-center gap-x-6 my-4 justify-center">
                        <div class="min-w-[20rem] w-max">
                            <p :class="[darkMode ? 'text-slate-300' : 'text-slate-500']" class="mb-2 text-sm font-medium">Industry</p>
                            <Dropdown :dark-mode="darkMode" v-model="selectedImportIndustryId" :options="importIndustryOptions" @update:modelValue="getIndustryServicesForImport" />
                        </div>
                        <div class="min-w-[20rem] w-max">
                            <p :class="[darkMode ? 'text-slate-300' : 'text-slate-500']" class="mb-2 text-sm font-medium">Industry Service</p>
                            <Dropdown :dark-mode="darkMode" v-model="selectedImportServiceId" :options="importIndustryServiceOptions" />
                        </div>
                    </div>
                </div>
            </template>
        </Modal>

        <!--    CONFIRMATION MODAL    -->
        <Modal
            v-if="confirmation"
            :dark-mode="darkMode"
            :small="true"
            :disable-confirm="confirmation?.disableConfirm"
            :close-text="confirmation?.closeText ?? 'Cancel'"
            @close="cancelConfirmationModal()"
            @confirm="confirmConfirmationModal()"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium inline-flex items-center">
                    {{ confirmation.title }}
                </h4>
            </template>
            <template v-slot:content>
                <div class="px-5">
                    <p class="whitespace-pre-wrap">{{ confirmation.message }}</p>
                </div>
            </template>
        </Modal>

        <FloorPricingHistoryModal
            v-if="showPriceHistoryModal"
            :dark-mode="darkMode"
            @close="togglePriceHistoryModal(false)"
            :options="priceHistoryModalOptions"
        />
        <AlertsContainer :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
    </div>
</template>

<script>
import AlertsMixin from "../../../mixins/alerts-mixin";
import Tab from "../../Shared/components/Tab.vue";
import Modal from "../../Shared/components/Modal.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import INDUSTRIES from "../../../../modules/industries";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import Tooltip from "../../Shared/components/Tooltip.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import FloorPricingHistoryModal from "./FloorPricingHistoryModal.vue";
import {computed} from "vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import {ApiFactory} from "../services/api/factory.js"

const defaultFilters = {
    Appointment: {
        qualityTier: 'Online',
    },
    propertyType: 'Residential',
    qualityTier: 'Standard',
}

export default {
    name: "Pricing",
    components: {FloorPricingHistoryModal, CustomInput, Tooltip, AlertsContainer, CustomButton, LoadingSpinner, Dropdown, Modal, Tab },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        api: '',
    },
    mixins: [
        AlertsMixin
    ],
    data() {
        return {
            permissionStore: useRolesPermissions(),
            priceHistoryModalOptions: {},
            initialised: false,
            loading: false,
            saving: false,
            loadingModal: false,
            importing: false,
            stateSelected: null,
            editingPricingConfig: null,
            tabs: [{ name: 'Lead', current: true }],
            filterTab: 'Lead',

            useFormulaPricing: false,
            needsInitialising: false,
            needsRepair: false,
            defaultFormula: "",

            pricingData: {},

            validSalesTypes: [],

            industryServiceData: [],
            industryId: 1,
            industryFilterOptions: null,

            industryServiceId: null,
            industryServiceOptions: null,

            propertyType: defaultFilters.propertyType,
            propertyTypeFilterOptions: [{id: 'Residential', name: 'Residential'}],

            qualityTier: defaultFilters.qualityTier,
            qualityTierFilterOptions: [{id: 'Standard', name: 'Standard'}],

            showImportPricingModal: false,
            selectedImportIndustryId: null,
            selectedImportServiceId: null,

            importIndustryOptions: [],
            allImportIndustryServiceOptions: [],
            importIndustryServiceOptions: [],
            INDUSTRIES,
            confirmation: null,

            //TODO: supply from backend
            loweredFloorPolicyOptions: [
                { name: '-', id: 0 },
                { name: 'Lower prices for everyone', id: 1 },
                { name: 'Do not lower prices for current buyers', id: 2 },
            ],
            loweredFloorPolicyDescription: {
                0: null,
                1: 'No action will be taken. \nCampaigns with no explicit bid price set will immediately use the new floor price.',
                2: 'Campaigns which have recently purchased leads will have a bid price set at the pre-modified price. This means they will continue to receive leads at the existing price, unless they modify their bid.\nNew campaigns will default to the new floor price.'
            },
            selectedLoweredFloorPolicy: 0,
            loweredFloorPriceRecentPurchaseThreshold: 30,
            showPriceHistoryModal: false,
            api: ApiFactory.makeApiService('api'),
        }
    },
    created() {
        this.getIndustryServiceData().then(() => {
            this.initialiseFilters();
            this.getData().then(() => {
                this.initialised = true;
            }).catch((e) =>
                this.showAlert('error', e.message)
            );
        });
    },
    computed: {
        canEditFloorPricing() {
           return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_MINIMUM_PRICE_MANAGEMENT_EDIT)
        },
        selectedProduct() {
            return this.filterTab;
        },
        gridColumns() {
            return `grid-cols-${this.validSalesTypes?.length + 2 || 8}`
        },
    },
    methods: {
        togglePriceHistoryModal(show){
            this.priceHistoryModalOptions = show ? {
                industry_id: this.industryId,
                industry_name: this.getIndustryName(this.industryId),
                industry_service_id: this.industryServiceId,
                industry_service_name: this.getServiceName(this.industryServiceId),
                property_type: this.propertyType,
                quality_tier: this.qualityTier,
                valid_sales_types: this.validSalesTypes,
                selected_product: this.selectedProduct,
                selected_state_id: this.stateSelected?.id,
                selected_state_key: this.stateSelected?.key,
            } : {}

            this.showPriceHistoryModal = show
        },
        initialiseFilters() {
            this.updateProductOptions();
            this.updateIndustryOptions();
            this.updateIndustryServiceOptions();
        },
        async getIndustryServiceData() {
            await this.api.getIndustryServicesByProduct().then(resp => {
                if (resp.data?.data?.status) {
                    this.industryServiceData = resp.data.data.services;
                }
                else {
                    console.error('Failed to fetch IndustryService data');
                }
            }).catch(e => {
                console.error(e);
            });
        },
        updateProductOptions() {
            this.tabs = Object.keys(this.industryServiceData).map(productName => ({ name: productName, current: productName === this.filterTab }))
                .sort((a, b) => a.name === 'Lead' ? -1 : b.name === 'Lead' ? 1 : 0);
        },
        updateIndustryOptions() {
            const productName = this.filterTab;
            this.industryFilterOptions = Object.entries(this.industryServiceData[productName]).map(([industryName, serviceArray]) => {
                return { name: industryName, id: serviceArray[0].industry_id }
            });
            if (!this.industryId || !this.industryFilterOptions.map(option => option.id).includes(this.industryId)) {
                this.industryId = this.industryFilterOptions?.[0]?.id ?? null;
            }
        },
        updateIndustryServiceOptions() {
            const productName = this.filterTab;
            const industryName = this.industryFilterOptions.find(option => option.id === this.industryId)?.name ?? null;
            if (industryName) {
                this.industryServiceOptions = this.industryServiceData[productName][industryName].map(service => {
                    return { name: service.industry_service_name, id: service.industry_service_id }
                });
                if (!this.industryServiceId || !this.industryServiceOptions.map(option => option.id).includes(this.industryServiceId)) {
                    this.industryServiceId = this.industryServiceOptions?.[0]?.id ?? null;
                }
            }
        },
        async getIndustryServicesForImport() {
            this.importIndustryServiceOptions = this.allImportIndustryServiceOptions.reduce((output, service) => {
                return service.industry_id === this.selectedImportIndustryId
                    ? [ ...output, service ]
                    : output;
            }, []);
            this.selectedImportServiceId = this.importIndustryServiceOptions[0]?.id;
        },
        formatPrice(value) {
            let val = (value/1).toFixed(2).replace(',', '.');

            return `$${val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
        },
        processTabFilter(filter) {
            this.filterTab = filter;
            this.tabs.forEach(tab => tab.current = tab.name === filter);
            this.updateIndustryOptions();
            this.updateIndustryServiceOptions();
            this.getData(false, true);
        },
        selectState(stateId, stateKey) {
            this.stateSelected = {
                id: stateId,
                key: stateKey
            };

            this.getData();
        },
        returnToStateLevel() {
            this.stateSelected = null;

            this.getData();
        },
        // Adds Specific State or County to Payload, allowing you to edit its pricing configs.
        editPricingConfig(entity, locationKey, regionType) {
            this.editingPricingConfig = {
                prices: JSON.parse(JSON.stringify(entity)),
            }
            this.editingPricingConfig.initial = JSON.parse(JSON.stringify(entity));
            this.editingPricingConfig.region_type = regionType;
            this.editingPricingConfig.quality_tier = this.qualityTier;
            this.editingPricingConfig.property_type = this.propertyType;
            this.editingPricingConfig.industry_id = this.industryId;
            this.editingPricingConfig.industry_service_id = this.industryServiceId;
            this.editingPricingConfig.product = this.selectedProduct;
            this.editingPricingConfig.name = this.$filters.toProperCase(locationKey);
            this.editingPricingConfig.state_location_id = regionType === 'state'
                ? entity.state_location_id
                : this.stateSelected?.id ?? null;
            this.editingPricingConfig.county_location_id = entity.county_location_id ?? null;
            this.editingPricingConfig.pricesLowered = false;
        },
        handleProposedPriceChange() {
            if (this.editingPricingConfig) {
                for (const saleType in this.editingPricingConfig.prices) {
                    if (this.editingPricingConfig.initial[saleType].explicit_price > this.editingPricingConfig.prices[saleType].explicit_price) {
                        this.editingPricingConfig.pricesLowered = true;
                        return;
                    }
                }
                this.editingPricingConfig.pricesLowered = false;
            }
        },
        discardAllChanges() {
            this.editingPricingConfig = null;
            this.selectedLoweredFloorPolicy = 0;
            this.loweredFloorPriceRecentPurchaseThreshold = 30;
        },
        updateQualityTierOptions(options) {
            if (options?.length)
                this.qualityTierFilterOptions = options.map(option => ({ name: option, id: option }));
            if (!this.qualityTierFilterOptions.map(option => option.id).includes(this.qualityTier))
                this.qualityTier = defaultFilters[this.selectedProduct]?.qualityTier ?? defaultFilters.qualityTier;
        },
        updatePropertyTypeOptions(options) {
            if (options?.length)
                this.propertyTypeFilterOptions = options.map(option => ({ name: option, id: option }));
            if (!this.propertyTypeFilterOptions.map(option => option.id).includes(this.propertyType))
                this.propertyType = defaultFilters[this.selectedProduct]?.propertyType ?? defaultFilters.propertyType;
        },
        resetStateSelect() {
            this.stateSelected = null;
        },
        async getData(ignoreState = false, newProduct = false) {
            this.loading = true;
            const qualityTier = newProduct ? null : this.qualityTier;
            const propertyType = newProduct ? null : this.propertyType;

            await this.api.getData(this.selectedProduct, this.industryId, this.industryServiceId, propertyType, qualityTier, ignoreState ? null : this.stateSelected?.id)
                .then(resp => {
                    this.pricingData = resp.data.data.prices ?? {};
                    this.defaultFormula = resp.data.data.configuration.default_formula ?? "";
                    this.validSalesTypes = resp.data.data.configuration.sale_types ?? [];
                    this.useFormulaPricing = resp.data.data.configuration.use_formulas;
                    this.needsInitialising = resp.data.data.configuration.needs_initialising;
                    this.needsRepair = resp.data.data.configuration.needs_repair;
                    this.updateQualityTierOptions(resp.data.data.configuration.quality_tiers);
                    this.updatePropertyTypeOptions(resp.data.data.configuration.property_types);
                    this.editingPricingConfig = null;
                }).catch(() => {
                    this.showAlert('error', 'There was an error retrieving the data.')
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        validateFloorPricing(ignoreInheritedPriceRestriction = false) {
            const errors = [];

            for (const saleTypeKey in this.editingPricingConfig.prices) {
                const price = this.editingPricingConfig.prices[saleTypeKey];

                if (price.explicit_price < 0) errors.push(`${this.$filters.toProperCase(saleTypeKey)} price must be more than $0`);
            }

            if (this.editingPricingConfig.pricesLowered && !(this.selectedLoweredFloorPolicy > 0)) {
                errors.push("A Lowered Price Policy must be selected when dropping floor prices.");
            }

            if (errors.length) {
                this.showAlert('error', errors.join("\n"));
                return false;
            }
            else
                return true;
        },
        saveFloorPricing(ignoreInheritedPriceRestriction = false) {
            if (!this.validateFloorPricing(ignoreInheritedPriceRestriction)) return;

            if (this.saving) return;
            this.saving = true;
            this.confirmation = null;

            this.editingPricingConfig.update_inherited_price = !!ignoreInheritedPriceRestriction;

            // If prices were lowered, attach the policy to use for modifying campaigns
            if (this.editingPricingConfig.pricesLowered) {
                this.editingPricingConfig.lowered_price_policy = {
                    policy_type: this.selectedLoweredFloorPolicy,
                    recent_purchase_threshold: this.loweredFloorPriceRecentPurchaseThreshold,
                }
            }
            this.api.updateFloorPricing(this.editingPricingConfig).then(resp => {
                if (resp.data?.data?.status) {
                    this.discardAllChanges();
                    this.pricingData = resp.data.data.prices;
                }
                else this.showAlert('error', this.getResponseError(resp));
            }).catch(e => {
                console.error(e);
                this.showAlert('error', this.getResponseError(e));
            }).finally(() => {
                this.saving = false;
            });
        },
        getIndustryName(industryId) {
            return this.industryFilterOptions?.find(industry => industry.id === industryId)?.name ?? '';
        },
        getServiceName(serviceId) {
            return this.industryServiceOptions?.find(service => service.id === serviceId)?.name ?? 'Unknown Service';
        },
        getImportIndustryName() {
            return this.importIndustryOptions?.find(industry => industry.id === this.selectedImportIndustryId)?.name ?? 'Unknown Industry';
        },
        getImportServiceName() {
            return this.importIndustryServiceOptions?.find(service => service.id === this.selectedImportServiceId)?.name ?? 'Unknown Service';
        },
        initialisePricingForIndustryService() {
            this.saving = true;

            this.api.initialiseFloorPricing(this.industryServiceId, this.selectedProduct, this.qualityTier, this.propertyType).then(resp => {
                if (resp.data.data.status) {
                    this.getData();
                }
                else {
                    this.showAlert('error', 'Failed to initialise pricing. Is default pricing config set up?');
                }
            }).catch(err => {
                this.showAlert('error', err.message ?? 'There was an error creating pricing');
            }).finally(() => {
                this.saving = false;
            });
        },
        repairPricingForIndustryService() {
            if (this.saving) return;
            this.saving = true;

            this.api.repairFloorPricing(this.industryServiceId, this.selectedProduct).then(resp => {
                if (resp.data.data.status) {
                    this.getData();
                }
                if (!resp.data.data.status || resp.data.data.message) {
                    this.confirmation = {
                        title: 'Failed to repair pricing',
                        message: resp.data.data.message ?? 'An unknown error occurred.',
                        disableConfirm: true,
                        closeText: 'OK',
                    }
                }
            }).catch(err => {
                this.showAlert('error', err.message ?? 'There was an error repairing pricing');
            }).finally(() => {
                this.saving = false;
            });
        },
        copyPricingFromIndustryService() {
            this.importing = true;
            const productName = this.filterTab

            this.api.importFloorPricing(this.selectedImportServiceId, this.industryServiceId, productName).then(resp => {
                if (resp.data.data.status) {
                    this.getData();
                    this.toggleImportPricingModal(false);
                }
            }).catch(err => {
                this.showAlert('error', err.message ?? 'There was an error creating pricing');
            }).finally(() => {
                this.importing = false;
            });
        },
        getExportableIndustryServices() {
            this.loadingModal = true;
            this.toggleImportPricingModal(true);
            const productName = this.filterTab

            this.api.getExportableIndustryServices(productName).then(resp => {
                if (resp.data.data.status) {
                    this.importIndustryOptions = resp.data.data.industries;
                    this.allImportIndustryServiceOptions = resp.data.data.services;
                    if (this.importIndustryOptions.length) {
                        this.selectedImportIndustryId = this.importIndustryOptions[0].id;
                        this.getIndustryServicesForImport();
                    }
                }
            }).catch(err => {
                this.showAlert('error', err.response?.data?.message)
            }).finally(() => {
                this.loadingModal = false;
            });
        },
        toggleImportPricingModal(show) {
            this.showImportPricingModal = !!show;
        },
        showSetNationalPricingModal() {
            const prices = this.validSalesTypes.reduce((output, leadSaleType) => {
                output[leadSaleType] = { explicit_price: 0, inherent_price: null };
                return output;
            }, {});

            this.editPricingConfig(prices, 'national', 'national');
        },
        confirmConfirmationModal() {
            if (!this.confirmation) return;
            if (this.confirmation.id === 'inheritedPriceRestriction')
                this.saveFloorPricing(true);

            this.cancelConfirmationModal();
        },
        cancelConfirmationModal() {
            this.confirmation = null;
        },
        getResponseError(resp) {
            return resp?.response?.data?.message ?? resp.message ?? `An unknown error has occurred.`;
        },
    },
    watch: {
        industryServiceId(newVal, oldVal) {
            if (this.initialised && newVal !== oldVal) {
                this.resetStateSelect();
                this.getData();
            }
        }
    }
}
</script>
