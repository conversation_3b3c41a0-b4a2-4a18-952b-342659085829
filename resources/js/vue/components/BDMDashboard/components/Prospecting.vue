<script setup>
import CustomInput from "../../Shared/components/CustomInput.vue";
import {onMounted, ref, defineEmits, onBeforeUnmount, computed} from "vue";
import DropdownWithSearchFilter from "../../Shared/components/DropdownWithSearchFilter.vue";
import FilledCheckIcon from "../../Prospects/components/FilledCheckIcon.vue";
import MultiSelect from "../../Shared/components/MultiSelect.vue";
import SharedApiServiceFactory from "../../Shared/services/api.js";
import StepWizard from "./StepWizard.vue";
import CompanyInfoSidebar from "./CompanyInfoSidebar.vue";
import Modal from "../../Shared/components/Modal.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import BaseTable from "../../Shared/components/BaseTable.vue";
import CustomTextarea from "../../Shared/components/CustomTextarea.vue";
import EmailModal from "../../Emails/components/EmailModal.vue";
import {EMAIL_TEMPLATE_TYPE} from "../../../../stores/email/email.store.js";
import DuplicateCompaniesModule from "./DuplicateCompaniesModule.vue";
import Contacts from "./Contacts.vue";

const props = defineProps({
    prospect: {},
    apiService: null,
    darkMode: false,
    themeClasses: '',
    themeBackgroundClasses: '',
    loading: false,
})

const stateOptions = ref([])
onMounted(() => {
    props.apiService.getStateOptions().then(res => {
        stateOptions.value = res.data
        refreshCityOptions(false)
    })
})

const cityOptions = ref([])
const refreshCityOptions = (clearCurrentCity) => {
    if (clearCurrentCity)
        props.prospect.address_city_key = null

    props.apiService.getCityOptions(props.prospect.address_state_abbr).then(res => cityOptions.value = res.data)
}

const emit = defineEmits(['save', 'flash-alert', 'reset', 'duplicate-flag-success', 'converted-flag-success']);
function save(nextProspect, message) {
    emit('save', nextProspect, message);
}
function flashAlert(type, message) {
    emit('flash-alert', type, message);
}
const sharedApi = SharedApiServiceFactory.make()
const industries = ref([])
onMounted(() => {
    const loadingIndustries = ref(true)
    sharedApi.getOdinIndustries().then(res => {
        if(res.data.data.status === true) {
            industries.value = res.data.data.industries.map((i) => {
                return {
                    id: i.id,
                    name: i.name
                };
            });
        }
    })
        .catch(() => {
            flashAlert('error', 'Could not load industries.');
        })
        .finally(() => {
            loadingIndustries.value = false
        });
})

const requiredProspectFields = [
    'company_name',
    'company_website',
    'industry_ids',
    'decision_maker_first_name',
    'decision_maker_last_name',
    'address_state_abbr',
];

const requiredFieldsFilled = computed(() => {
    let filledAmount = requiredProspectFields.reduce((count, field) => {
        const fieldValue = props.prospect[field]
        if(field === 'company_website')
        {
            return count + (websiteValidation(fieldValue) ? 1 : 0)
        }
        else if(field === 'decision_maker_email')
        {
            return count + (emailValidation(fieldValue) ? 1 : 0)
        }
        else if (field === 'company_phone') {
            return count + (phoneValidation(fieldValue) ? 1 : 0)
        }
        else if (field === 'decision_maker_phone') {
            return count + (phoneValidation(fieldValue) ? 1 : 0)
        }
        else {
            const isFilled = Array.isArray(fieldValue) ? fieldValue.length > 0 : Boolean(fieldValue);
            return count + (isFilled ? 1 : 0);
        }
    }, 0);
    return Number(filledAmount) === requiredProspectFields.length;
});

const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
function websiteValidation(value) {
    return urlRegex.test(value);
}

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
function emailValidation(value) {
    return emailRegex.test(value);
}

const phoneRegex = /^(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/;
function phoneValidation(value) {
    return phoneRegex.test(value);
}

const convertToCompany = () => props.apiService.convertToCompany(props.prospect).then(res => {
    emit('converted-flag-success', res.data)
    save(false, ' has successfully been converted.')
    emit('reset')
})
const archiveProspect = async () => {
    try {
        await props.apiService.archiveProspect(props.prospect);
        await new Promise((resolve) => {
            save(false, ' has been archived.');
            resolve();
        });
        flashAlert('success', props.prospect.company_name + ' successfully archived.');
        emit('reset')
    } catch (error) {
        console.error('Error archiving prospect:', error);
    }
};
const releaseBackToQueue = () => props.apiService.releaseBackToQueue(props.prospect).then(res => save(false, ' has been released back to Queue.'))

const closer = ref(null)

// TODO: Change Calendly Meeting URL
const openCalendly = async () => {

    props.apiService.getCloserForDemo().then(res => {
        closer.value = res.data
        try {
            Calendly.initPopupWidget({
                url: closer.value.meeting_url,
                prefill: {
                    name: (props.prospect.decision_maker_first_name ?? '') + ' ' + (props.prospect.decision_maker_last_name ?? ''),
                    email: props.prospect.decision_maker_email,
                },
            });
        } catch (error) {
            flashAlert('error', 'Error opening Calendly')
        }
    })
};

const isCalendlyScheduledEvent = (e) => {
    return e.data.event && e.data.event.indexOf('calendly') === 0 && e.data.event === 'calendly.event_scheduled'
};
const calendlyListener = (e) => {
    if (isCalendlyScheduledEvent(e)) {demoBooked(e)}
}

onMounted(() => {
    window.addEventListener('message', calendlyListener)
    window.scrollTo({top: 0})
})

onBeforeUnmount(() => {
    window.removeEventListener('message', calendlyListener)
})

const demoBooked = (e) => {
    props.prospect.demo = {
        booked: true,
        user_id: closer.value.user_id,
        calendly_event_url: e.data.payload.event.uri
    }
    convertToCompany()
}

const showDupeModal = ref(false)

function triggerEvent(event) {
    switch (event) {
        case 'archive':
            archiveProspect()
            break;
        case 'convert':
            convertToCompany()
            break;
        case 'book-demo':
            openCalendly()
            break;
        case 'next-company':
            save(true, 'Prospect saved.')
            break;
        case 'release':
            releaseBackToQueue()
            break;
        case 'save':
            save(false)
            break;
        case 'select-dupe-prompt':
            showDupeModal.value = true
            break;
        case 'decision-maker-confirmed':
            props.prospect.decision_maker_confirmed = true
            save(false)
            break;
        case 'add-note-for-no-answer':
            if(!props.prospect.notes.includes('No answer')) {
                props.prospect.notes += ' No answer, '
            }
            save(false)
            break;
        case 'add-note-for-follow-up-needed':
            if(!props.prospect.notes.includes('Follow up needed')) {
                props.prospect.notes += ' Follow up needed, '
            }
            save(false)
            break;
    }
}

const dupeCompanyId = ref(null)
const confirmDuplicate = () => {
    props.apiService.flagAsDuplicate(props.prospect.reference, dupeCompanyId.value).then(res => {
        // todo: display company record IF the dupe is assigned to this user - for now, we just clear the data
        const companyData = props.prospect.duplicate_companies.find(company => company.company_id === dupeCompanyId.value)
        duplicateFlagSuccess(companyData)
        emit('reset')
    })
}
function duplicateFlagSuccess(companyData) {
    emit('duplicate-flag-success', companyData)
}
const emailModal = ref(false)
function email() {
    emailModal.value = ! emailModal.value
}

const closeEmailModal = () => emailModal.value = false;

const quickNotes = [
    "Receptionist took message",
    "Left Voicemail",
]

function addQuickNote(note) {
    props.prospect.notes += note +', '
}
</script>

<template>
    <EmailModal
        :email-modal="emailModal"
        v-if="emailModal"
        :dark-mode="darkMode"
        :email-template-type="EMAIL_TEMPLATE_TYPE.BDM_PROSPECTING_EMAIL"
        :recipient-email="prospect.decision_maker_email"
        :recipient-id="prospect.reference"
        @close="closeEmailModal"
        @flash-alert="flashAlert"
    ></EmailModal>
    <modal container-classes="pt-8 pb-5" button-wrapper-classes="px-5" :dark-mode="darkMode" v-if="showDupeModal" @close="showDupeModal = false" @confirm="confirmDuplicate" :disable-confirm="!dupeCompanyId" confirm-text="Confirm Duplicate">
        <template #header><p class="font-semibold">Duplicate Company</p></template>
        <template #content>
            <BaseTable :dark-mode="darkMode" :loading="loading">
                <template #head>
                    <tr>
                        <th>
                            Company
                        </th>
                        <th>
                            Select
                        </th>
                    </tr>
                </template>
                <template #body>
                    <tr v-for="company in props.prospect.duplicate_companies">
                        <td>
                            <a target="_blank" class="text-primary-500" :href="company.profile_link">{{company.company_name}}</a>
                        </td>
                        <td>
                            <custom-button :dark-mode="darkMode" @click="dupeCompanyId = company.company_id" :color="(company.company_id === dupeCompanyId) ? 'primary' : 'primary-outline'">
                                {{ company.company_id === dupeCompanyId ? "Selected" : "Select" }}
                            </custom-button>
                        </td>
                    </tr>
                </template>
            </BaseTable>
        </template>
    </modal>

    <div class="grid lg:flex gap-6">
        <div class="lg:w-72 lg:flex-shrink-0">
            <div class="lg:top-24 lg:sticky border rounded-lg" :class="[themeClasses]">
                <CompanyInfoSidebar
                    @save="save"
                    @archive="archiveProspect"
                    :prospect="prospect"
                    :dark-mode="darkMode"
                    :theme-classes="themeClasses"
                    :theme-background-classes="themeBackgroundClasses"
                    @email="email"
                ></CompanyInfoSidebar>
            </div>
        </div>
        <div class="flex-grow grid lg:grid-cols-2 gap-6">
           <div class="lg:col-span-2">
               <StepWizard
                   :prospect="prospect"
                   :dark-mode="darkMode"
                   :theme-classes="themeClasses"
                   :theme-background-classes="themeBackgroundClasses"
                   @trigger-event="triggerEvent"
                   :required-fields-filled="requiredFieldsFilled"
               >
               </StepWizard>
           </div>
            <div>
                <div class="rounded-lg border mb-6 p-5 " :class="themeClasses">
                    <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                        Company
                    </h3>
                    <div class="grid gap-5">
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Name
                                <FilledCheckIcon :filled="props.prospect.company_name"></FilledCheckIcon>
                            </label>
                            <custom-input placeholder="Name" :dark-mode="darkMode" v-model="props.prospect.company_name"></custom-input>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Website
                                <FilledCheckIcon :filled="websiteValidation(props.prospect.company_website)"></FilledCheckIcon>
                            </label>
                            <custom-input placeholder="Website" :dark-mode="darkMode" v-model="props.prospect.company_website"></custom-input>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Phone
                            </label>
                            <custom-input placeholder="Company Phone" :dark-mode="darkMode" v-model="props.prospect.company_phone"></custom-input>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Industry
                                <FilledCheckIcon :filled="props.prospect.industry_ids.length > 0"></FilledCheckIcon>
                            </label>
                            <div>
                                <multi-select :options="industries"
                                              :dark-mode="darkMode"
                                              :show-search-box="true"
                                              :text-place-holder="'Select Industries'"
                                              :selected-ids="props.prospect.industry_ids"
                                />
                            </div>

                        </div>
                    </div>
                </div>
                <Contacts v-if="props.prospect.contacts.length > 0" :prospect="prospect" :darkMode="darkMode" :themeClasses="themeClasses"  />
                <div v-else class="rounded-lg border mb-6 p-5" :class="themeClasses">
                    <div>
                        <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                            Decision Maker
                        </h3>
                    </div>
                    <div class="grid gap-5">
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                First Name
                                <FilledCheckIcon :filled="props.prospect.decision_maker_first_name"></FilledCheckIcon>
                            </label>
                            <custom-input :dark-mode="darkMode" placeholder="First name" v-model="props.prospect.decision_maker_first_name"></custom-input>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Last Name
                                <FilledCheckIcon :filled="props.prospect.decision_maker_last_name"></FilledCheckIcon>
                            </label>
                            <custom-input :dark-mode="darkMode" placeholder="Last name" v-model="props.prospect.decision_maker_last_name"></custom-input>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Email Address
                            </label>
                            <custom-input :dark-mode="darkMode" placeholder="Email address" v-model="props.prospect.decision_maker_email"></custom-input>
                        </div>
                        <div>
                            <div class="flex justify-between items-center">
                                <label class="inline-flex mb-1 text-sm font-semibold">
                                    Phone
                                </label>
                            </div>
                            <div class=" flex items-center">
                                <custom-input class="w-full" :dark-mode="darkMode" placeholder="Decision Maker Phone" v-model="props.prospect.decision_maker_phone"></custom-input>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="rounded-lg border mb-6 p-5 " :class="themeClasses">
                    <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                        Address
                    </h3>
                    <div class="grid gap-5">
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                State
                                <FilledCheckIcon :filled="props.prospect.address_state_abbr"></FilledCheckIcon>
                            </label>
                            <DropdownWithSearchFilter placeholder="Select" :dark-mode="darkMode" @input="refreshCityOptions(true)" :options="stateOptions" v-model="props.prospect.address_state_abbr"></DropdownWithSearchFilter>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                City
                            </label>
                            <DropdownWithSearchFilter placeholder="Select" :dark-mode="darkMode" :options="cityOptions" v-model="props.prospect.address_city_key"></DropdownWithSearchFilter>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Street
                            </label>
                            <custom-input :dark-mode="darkMode" placeholder="Street" v-model="props.prospect.address_street" ></custom-input>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <DuplicateCompaniesModule
                    :dark-mode="darkMode"
                    :company="props.prospect"
                    :loading="loading"
                >
                </DuplicateCompaniesModule>
                <div class="rounded-lg border mb-6 p-5" :class="themeClasses">
                    <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                        Description
                    </h3>
                    <div class="grid gap-5">
                        <div v-if="props.prospect.description" v-html="props.prospect.company_description"></div>
                    </div>
                </div>
                <div class="rounded-lg border mb-6 p-5" :class="themeClasses">
                    <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                        Notes
                    </h3>
                    <div class="overflow-x-auto w-[85vw] lg:w-auto">
                        <CustomTextarea placeholder="Write notes here..." :dark-mode="darkMode" rows="8" v-model="props.prospect.notes"></CustomTextarea>
                    </div>
                    <div class="flex flex-wrap items-center gap-2 pt-3">
                        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.1592 0.45105C4.64518 0.45105 0.15918 4.93705 0.15918 10.451C0.15918 15.965 4.64518 20.451 10.1592 20.451C15.6732 20.451 20.1592 15.965 20.1592 10.451C20.1592 4.93705 15.6732 0.45105 10.1592 0.45105ZM9.15918 16.451V11.451H5.15918L11.1592 4.45105V9.45105H15.1592L9.15918 16.451Z" fill="#0081FF"/>
                        </svg>
                        <p v-for="note in quickNotes"
                           @click="addQuickNote(note)"
                           :class="[darkMode ? 'bg-opacity-[10%] hover:bg-opacity-25 text-slate-50' : 'text-slate-900']"
                           class="inline-flex items-center bg-primary-100 hover:bg-primary-200 cursor-pointer capitalize px-2 py-0.5 rounded-md text-sm font-medium">
                            {{ note }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style>
.toastui-editor-dark.toastui-editor-defaultUI {
    @apply border-dark-border text-white;
}
.toastui-editor-main .toastui-editor-md-container,
.toastui-editor-main .toastui-editor-ww-container {
    @apply bg-light-background;
}
.toastui-editor-defaultUI-toolbar {
    @apply bg-light-module border-transparent;
}
.toastui-editor-mode-switch {
    @apply bg-light-module border-transparent;
}
.toastui-editor-dark .toastui-editor-md-container,
.toastui-editor-dark .toastui-editor-ww-container {
    @apply bg-dark-background;
}

.toastui-editor-dark .toastui-editor-defaultUI-toolbar {
    @apply bg-dark-module border-transparent;
}

.toastui-editor-dark .toastui-editor-mode-switch {
    @apply bg-dark-module border-transparent;
}

.toastui-editor-dark .toastui-editor-mode-switch .tab-item {
    @apply border-transparent bg-dark-module text-slate-500;
}

.toastui-editor-dark .toastui-editor-mode-switch .tab-item.active {
    @apply border-dark-border bg-dark-background text-slate-300;
}

.toastui-editor-dark .toastui-editor-toolbar-icons {
    @apply bg-dark-module border-transparent;
}

.toastui-editor-dark .toastui-editor-toolbar-icons:not(:disabled):hover {
    @apply bg-dark-border border-transparent;
}

.toastui-editor-toolbar-icons {
    @apply bg-light-module border-transparent;
}

.toastui-editor-toolbar-icons:not(:disabled):hover {
    @apply bg-light-border border-transparent;
}
</style>
