<script>
import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin.js";
export default {
    name: "DialProspect",
    props: {
        darkMode: {
            default: false,
            type: Boolean
        },
        name: {
          default: '',
          type: String,
        },
        phoneNumber: {
            default: '',
            type: String,
        },
        disabled: false,
    },
    mixins: [
        DispatchesGlobalEventsMixin
    ],
    methods: {
        formatPhoneNumber(phoneNumber) {
            if (phoneNumber !== null)
            return phoneNumber.replace(/^\+1/, '').replace(/[^0-9]/g, '');
        },
        call() {
            this.dispatchGlobalEvent('call', {
                phone: this.formatPhoneNumber(this.phoneNumber),
                name: this.name,
            });
        }
    }
}
</script>

<template>
    <div
        @click="call" class="flex-shrink-0 inline-flex justify-center items-center gap-1 rounded-md border border-primary-500 px-3 py-1 group cursor-pointer" :class="[darkMode ? 'bg-dark-module hover:bg-dark-border' : 'bg-light-module hover:bg-primary-100', !disabled ? 'opacity-25 pointer-events-none cursor-not-allowed' : '']">
        <p class="text-xs text-primary-500 font-semibold">Call</p>
    </div>
</template>

<style scoped>

</style>
