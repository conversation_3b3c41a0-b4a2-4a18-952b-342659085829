<script setup>
import Badge from "../../Shared/components/Badge.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import DialPhoneNumber from "./DialPhoneNumber.vue";
import {defineEmits} from "vue";
import SendEmail from "./SendEmail.vue";
import TextPhoneNumber from "./TextPhoneNumber.vue";
const props = defineProps({
    prospect: {},
    apiService: null,
    darkMode: false,
    themeClasses: '',
    themeBackgroundClasses: ''
})

const emit = defineEmits(['save', 'archive', 'email']);
function save(nextProspect, message) {
    emit('save', nextProspect, message);
}

function email() {
    emit('email');
}

function archive() {
    emit('archive');
}

const phoneRegex = /^(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/;
function phoneValidation(value) {
    return phoneRegex.test(value);
}

const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
function websiteValidation(value) {
    return urlRegex.test(value);
}

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
function emailValidation(value) {
    return emailRegex.test(value);
}

</script>

<template>
    <div>
        <div class="px-5 pt-5 pb-3">
            <h4 class="font-semibold text-lg">{{prospect.company_name}}</h4>
            <div v-if="prospect.source === 'registration'" class="pt-2">
                <badge :dark-mode="darkMode" color="orange">New Registration</badge>
            </div>
            <div v-if="prospect.source === 'salesintel'" class="pt-2">
                <badge :dark-mode="darkMode" color="purple">Sales Intel</badge>
            </div>
        </div>
        <div class="grid text-sm mt-2 divide-y" :class="[darkMode ? 'divide-dark-border' : 'divide-light-border']">
            <div class="px-5 pb-5">
                <p class="font-medium pb-3" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">Company</p>
                <div class="grid gap-2.5">
                    <div>
                        <a :href="prospect.company_website" target="_blank" class="inline-flex items-start gap-2 break-all" :class="websiteValidation(prospect.company_website) ? 'text-primary-500' : ''">
                            <svg class="flex-shrink-0 mt-1" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.06569 6.39481C4.91975 5.54075 6.40927 5.54075 7.26333 6.39481L7.79627 6.92775L8.86215 5.86187L8.32921 5.32893C7.61837 4.61734 6.67159 4.22461 5.66451 4.22461C4.65742 4.22461 3.71065 4.61734 2.99981 5.32893L1.40023 6.92775C0.694894 7.63536 0.298828 8.59372 0.298828 9.59283C0.298828 10.5919 0.694894 11.5503 1.40023 12.2579C1.7499 12.6081 2.16532 12.8857 2.62262 13.0748C3.07991 13.264 3.57007 13.3609 4.06493 13.36C4.55993 13.361 5.05024 13.2642 5.50768 13.075C5.96512 12.8859 6.38065 12.6082 6.73039 12.2579L7.26333 11.725L6.19745 10.6591L5.66451 11.192C5.23972 11.6149 4.66471 11.8523 4.06531 11.8523C3.46591 11.8523 2.8909 11.6149 2.46611 11.192C2.04285 10.7674 1.80518 10.1924 1.80518 9.59283C1.80518 8.9933 2.04285 8.41823 2.46611 7.99363L4.06569 6.39481Z" fill="#0081FF"/><path d="M6.73039 1.59714L6.19745 2.13008L7.26333 3.19596L7.79627 2.66302C8.22106 2.24013 8.79607 2.00271 9.39547 2.00271C9.99487 2.00271 10.5699 2.24013 10.9947 2.66302C11.4179 3.08762 11.6556 3.6627 11.6556 4.26222C11.6556 4.86175 11.4179 5.43682 10.9947 5.86142L9.39509 7.46024C8.54103 8.3143 7.05151 8.3143 6.19745 7.46024L5.66451 6.9273L4.59863 7.99318L5.13157 8.52612C5.84241 9.23771 6.78919 9.63044 7.79627 9.63044C8.80336 9.63044 9.75013 9.23771 10.461 8.52612L12.0605 6.9273C12.7659 6.21969 13.162 5.26133 13.162 4.26222C13.162 3.26311 12.7659 2.30475 12.0605 1.59714C11.3531 0.891436 10.3947 0.495117 9.39547 0.495117C8.39624 0.495117 7.43781 0.891436 6.73039 1.59714Z" fill="#0081FF"/></svg>
                            {{ prospect.company_website ?? "?" }}</a>
                    </div>
                    <div class="flex items-center gap-2 justify-between">
                        <p class="inline-flex items-start gap-2 break-all">
                            <svg class="flex-shrink-0 mt-1" width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.9258 10.1387L9.9906 7.46995C9.85186 7.34385 9.66953 7.27659 9.48213 7.28238C9.29472 7.28818 9.1169 7.36657 8.98621 7.501L7.25831 9.27801C6.8424 9.19858 6.00624 8.93792 5.14554 8.07938C4.28484 7.21795 4.02417 6.37964 3.94691 5.96661L5.72247 4.23799C5.85707 4.10738 5.93558 3.92954 5.94137 3.74208C5.94717 3.55462 5.8798 3.37226 5.75352 3.23359L3.08549 0.299115C2.95915 0.160014 2.78357 0.075639 2.59603 0.0639093C2.4085 0.0521797 2.22377 0.11402 2.08109 0.236296L0.514208 1.58006C0.38937 1.70535 0.314859 1.8721 0.304808 2.04868C0.293977 2.2292 0.0874664 6.50527 3.40319 9.82245C6.29579 12.7143 9.91912 12.9259 10.917 12.9259C11.0629 12.9259 11.1524 12.9216 11.1762 12.9201C11.3528 12.9102 11.5195 12.8354 11.6441 12.71L12.9872 11.1424C13.1099 11.0002 13.1722 10.8156 13.1608 10.6281C13.1493 10.4405 13.065 10.2649 12.9258 10.1387Z" fill="#0081FF"/></svg>
                            {{ prospect.company_phone ?? "?" }}</p>
                        <div class="inline-flex gap-1">
                            <TextPhoneNumber
                                :dark-mode="darkMode"
                                :name="prospect.company_name"
                                :phone-number="prospect.company_phone"
                                :disabled="phoneValidation(prospect.company_phone)"
                            ></TextPhoneNumber>
                            <DialPhoneNumber
                                :dark-mode="darkMode"
                                :name="prospect.company_name"
                                :phone-number="prospect.company_phone"
                                :disabled="phoneValidation(prospect.company_phone)"
                            ></DialPhoneNumber>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-5">
                <p class="font-medium pb-3" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">Address</p>
                <div class="grid gap-2.5">
                    <div>
                        <p class="inline-flex items-start gap-2 break-all">
                            <svg class="flex-shrink-0 mt-1" width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.29974 0.353913C7.14874 0.202963 6.94398 0.118164 6.73047 0.118164C6.51696 0.118164 6.31219 0.202963 6.1612 0.353913L0.524855 5.99026C0.378183 6.14212 0.297024 6.34551 0.298859 6.55663C0.300693 6.76775 0.385374 6.9697 0.534663 7.11899C0.683953 7.26828 0.885905 7.35296 1.09702 7.35479C1.30814 7.35663 1.51154 7.27547 1.6634 7.1288L1.89932 6.89288V12.1959C1.89932 12.4094 1.98415 12.6142 2.13515 12.7652C2.28616 12.9162 2.49096 13.0011 2.70451 13.0011H4.31489C4.52844 13.0011 4.73325 12.9162 4.88425 12.7652C5.03525 12.6142 5.12008 12.4094 5.12008 12.1959V10.5855C5.12008 10.3719 5.20492 10.1671 5.35592 10.0161C5.50692 9.86513 5.71173 9.78029 5.92528 9.78029H7.53566C7.74921 9.78029 7.95401 9.86513 8.10502 10.0161C8.25602 10.1671 8.34085 10.3719 8.34085 10.5855V12.1959C8.34085 12.4094 8.42568 12.6142 8.57669 12.7652C8.72769 12.9162 8.93249 13.0011 9.14604 13.0011H10.7564C10.97 13.0011 11.1748 12.9162 11.3258 12.7652C11.4768 12.6142 11.5616 12.4094 11.5616 12.1959V6.89288L11.7975 7.1288C11.9494 7.27547 12.1528 7.35663 12.3639 7.35479C12.575 7.35296 12.777 7.26828 12.9263 7.11899C13.0756 6.9697 13.1602 6.76775 13.1621 6.55663C13.1639 6.34551 13.0828 6.14212 12.9361 5.99026L7.29974 0.353913Z" fill="#0081FF"/>
                            </svg>
                            {{ prospect.address_street }}, {{ prospect.address_city_key }}, {{ prospect.address_state_abbr }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="p-5">
                <p class="font-medium pb-3" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">Decision Maker</p>
                <div class="grid gap-2.5">
                    <div>
                        <p class="inline-flex items-start gap-2 break-all">
                            <svg class="flex-shrink-0 mt-1" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.51465 3.31055C3.51465 5.08354 4.95748 6.52637 6.73047 6.52637C8.50346 6.52637 9.94629 5.08354 9.94629 3.31055C9.94629 1.53756 8.50346 0.0947266 6.73047 0.0947266C4.95748 0.0947266 3.51465 1.53756 3.51465 3.31055ZM12.4475 13.6726H13.1621V12.958C13.1621 10.2003 10.9175 7.95562 8.15972 7.95562H5.30122C2.54276 7.95562 0.298828 10.2003 0.298828 12.958V13.6726H12.4475Z" fill="#0081FF"/>
                            </svg>
                            {{ prospect.decision_maker_first_name }} {{ prospect.decision_maker_last_name }}</p>
                    </div>
                    <div>
                        <div class="flex justify-between items-start gap-2">
                            <div class="inline-flex items-start gap-2 break-all">
                                <svg class="mt-1.5 flex-shrink-0" width="14" height="11" viewBox="0 0 14 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0.300781 2.09766L6.73001 5.31187L13.1592 2.09766C13.1354 1.688 12.9559 1.30294 12.6574 1.02135C12.3589 0.739751 11.9641 0.582933 11.5537 0.583008H1.90628C1.49593 0.582933 1.10108 0.739751 0.802592 1.02135C0.504108 1.30294 0.324582 1.688 0.300781 2.09766Z" fill="#0081FF"/>
                                    <path d="M13.1621 3.89355L6.73047 7.10938L0.298828 3.89355V8.62242C0.298828 9.04886 0.468232 9.45784 0.769774 9.75938C1.07132 10.0609 1.48029 10.2303 1.90674 10.2303H11.5542C11.9806 10.2303 12.3896 10.0609 12.6912 9.75938C12.9927 9.45784 13.1621 9.04886 13.1621 8.62242V3.89355Z" fill="#0081FF"/>
                                </svg>
                                <p class="mt-0.5">{{ prospect.decision_maker_email }}</p>
                            </div>
                            <SendEmail :dark-mode="darkMode"
                                       :disabled="emailValidation(prospect.decision_maker_email)"
                                       :prospect="prospect"
                                       @email="email"
                            ></SendEmail>
                        </div>
                    </div>
                    <div class="flex items-center gap-2 justify-between">
                        <p class="inline-flex items-start gap-2 break-all">
                            <svg class="flex-shrink-0 mt-1" width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.9258 10.1387L9.9906 7.46995C9.85186 7.34385 9.66953 7.27659 9.48213 7.28238C9.29472 7.28818 9.1169 7.36657 8.98621 7.501L7.25831 9.27801C6.8424 9.19858 6.00624 8.93792 5.14554 8.07938C4.28484 7.21795 4.02417 6.37964 3.94691 5.96661L5.72247 4.23799C5.85707 4.10738 5.93558 3.92954 5.94137 3.74208C5.94717 3.55462 5.8798 3.37226 5.75352 3.23359L3.08549 0.299115C2.95915 0.160014 2.78357 0.075639 2.59603 0.0639093C2.4085 0.0521797 2.22377 0.11402 2.08109 0.236296L0.514208 1.58006C0.38937 1.70535 0.314859 1.8721 0.304808 2.04868C0.293977 2.2292 0.0874664 6.50527 3.40319 9.82245C6.29579 12.7143 9.91912 12.9259 10.917 12.9259C11.0629 12.9259 11.1524 12.9216 11.1762 12.9201C11.3528 12.9102 11.5195 12.8354 11.6441 12.71L12.9872 11.1424C13.1099 11.0002 13.1722 10.8156 13.1608 10.6281C13.1493 10.4405 13.065 10.2649 12.9258 10.1387Z" fill="#0081FF"/></svg>
                            {{ prospect.decision_maker_phone }}</p>
                        <div class="inline-flex gap-1">
                            <TextPhoneNumber
                                :dark-mode="darkMode"
                                :name="prospect.decision_maker_first_name + ' ' + prospect.decision_maker_last_name"
                                :phone-number="prospect.decision_maker_phone"
                                :disabled="phoneValidation(prospect.decision_maker_phone)"
                            ></TextPhoneNumber>
                            <DialPhoneNumber
                                :dark-mode="darkMode"
                                :name="prospect.decision_maker_first_name + ' ' + prospect.decision_maker_last_name"
                                :phone-number="prospect.decision_maker_phone"
                                :disabled="phoneValidation(prospect.decision_maker_phone)"
                            ></DialPhoneNumber>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-5 grid grid-cols-1 lg:grid-cols-2 gap-4">
                <CustomButton @click="archive" :dark-mode="darkMode" color="red-outline">Archive</CustomButton>
                <CustomButton @click="save(false)" :dark-mode="darkMode" color="primary-outline">Save</CustomButton>
            </div>
        </div>
    </div>
</template>

<style scoped>

</style>
