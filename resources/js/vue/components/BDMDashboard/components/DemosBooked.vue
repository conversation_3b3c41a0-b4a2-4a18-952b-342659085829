<script setup>
import BaseTable from "../../Shared/components/BaseTable.vue";
import {ref} from "vue";

const props = defineProps({
    darkMode: false,
    themeClasses: ''
})

const demosBooked = ref([
    {name: 'test', date: 'Wednesday, February 19, 2025', time: '10:00 AM'},
    {name: 'test 2', date: 'Wednesday, February 19, 2025', time: '12:00 PM'},
])
</script>

<template>
    <div class="border rounded-lg flex-grow" :class="[themeClasses]">
        <h3 class="font-bold text-sm uppercase text-primary-500 p-5">
            <svg class="inline mr-1" width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2.34521 12.4579H14.0152V1.55724C14.0152 1.14424 13.8511 0.748145 13.5591 0.456106C13.267 0.164066 12.8709 0 12.4579 0H2.33586C1.39685 0 0 0.622118 0 2.33586V13.2366C0 14.9503 1.39685 15.5724 2.33586 15.5724H14.0152V14.0152H2.34521C1.98548 14.0058 1.55724 13.8633 1.55724 13.2366C1.55724 12.6098 1.98548 12.4673 2.34521 12.4579ZM3.8931 3.11448H10.9007V4.67173H3.8931V3.11448Z" fill="#0081FF"/>
            </svg>
            Demos Booked
        </h3>
        <BaseTable :dark-mode="darkMode" :loading="loading">
            <template #head>
                <tr>
                    <th>
                        Company
                    </th>
                    <th>
                        Date / Time
                    </th>
                </tr>
            </template>
            <template #body>
                <tr v-for="demo in demosBooked" :key="demo.id">
                    <td>
                        {{demo.name}}
                    </td>
                    <td>
                        {{ demo.date }} {{demo.time }}
                    </td>
                </tr>
            </template>
        </BaseTable>
    </div>
</template>

<style scoped>

</style>
