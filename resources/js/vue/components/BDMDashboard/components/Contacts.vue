<script setup>
    import Badge from "../../Shared/components/Badge.vue";

    const props = defineProps({
        prospect: {},
        apiService: null,
        darkMode: false,
        themeClasses: '',
    });

    function setDecisionMaker(contact) {
        props.prospect.decision_maker_first_name = contact.first_name
        props.prospect.decision_maker_last_name = contact.last_name
        props.prospect.decision_maker_email = contact.email
        props.prospect.decision_maker_phone = contact.cell_phone ?? contact.office_phone
    }

    function getTitleAndDepartment(contact) {
        const title = contact.title === null || contact.title === '' ? "N/A" : contact.title
        const department = contact.department === null || contact.department === '' ? "N/A" : contact.department

        if (title === 'N/A' && department === 'N/A') {
            return 'N/A'
        }

        return `${title} - ${department}`
    }
</script>

<template>
    <div class="rounded-lg border mb-6 p-5" :class="themeClasses">
        <div>
            <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                Contacts
            </h3>
        </div>
        <div class="grid auto-cols-min gap-5" style="grid-template-columns: repeat(auto-fit, minmax(16rem, 1fr));">
            <div v-for="contact in props.prospect.contacts" :key="contact.id" class="grid gap-2 p-3 rounded-lg min-w-64" :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
                <div class="flex items-center gap-2 justify-between mb-2">
                    <p class="font-medium">
                        {{ contact.first_name }} {{ contact.last_name }}
                    </p>
                    <div v-if="props.prospect.decision_maker_email !== contact.email"
                        @click="setDecisionMaker(contact)"
                        class="flex-shrink-0 inline-flex justify-center items-center gap-1 rounded border border-amber-400 p-1 group cursor-pointer text-xs font-semibold"
                        :class="[darkMode ? 'bg-dark-border/50 text-amber-400 hover:bg-dark-border' : 'bg-amber-100/50 text-amber-900 hover:bg-amber-100']"
                    >
                        Make Decision Maker
                    </div>
                    <badge v-else :dark-mode="darkMode" color="purple">Decision Maker</badge>
                </div>
                <div class="flex items-center gap-2 justify-between">
                    <p class="inline-flex items-start gap-2 break-all text-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="mt-0.5 flex-shrink-0 size-4" fill="#0081FF">
                            <path d="M3 4a2 2 0 0 0-2 2v1.161l8.441 4.221a1.25 1.25 0 0 0 1.118 0L19 7.162V6a2 2 0 0 0-2-2H3Z" />
                            <path d="m19 8.839-7.77 3.885a2.75 2.75 0 0 1-2.46 0L1 8.839V14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.839Z" />
                        </svg>
                        {{ contact.email }}
                    </p>
                </div>
                <div class="flex items-center gap-2 justify-between">
                    <p class="inline-flex items-start gap-2 break-all text-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="mt-0.5 flex-shrink-0 size-4" fill="#0081FF">
                            <path fill-rule="evenodd" d="M1 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3V6Zm4 1.5a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm2 3a4 4 0 0 0-3.665 2.395.75.75 0 0 0 .416 1A8.98 8.98 0 0 0 7 14.5a8.98 8.98 0 0 0 3.249-.604.75.75 0 0 0 .416-1.001A4.001 4.001 0 0 0 7 10.5Zm5-3.75a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Zm0 6.5a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Zm.75-4a.75.75 0 0 0 0 1.5h2.5a.75.75 0 0 0 0-1.5h-2.5Z" clip-rule="evenodd" />
                        </svg>
                        <span v-text="getTitleAndDepartment(contact)"></span>
                    </p>
                </div>
                <div class="flex items-center gap-2 justify-between">
                    <p class="inline-flex items-start gap-2 break-all text-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="flex-shrink-0 size-4" fill="#0081FF">
                            <path fill-rule="evenodd" d="M9.293 2.293a1 1 0 0 1 1.414 0l7 7A1 1 0 0 1 17 11h-1v6a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-6H3a1 1 0 0 1-.707-1.707l7-7Z" clip-rule="evenodd" />
                        </svg>
                        {{ contact.cell_phone ?? 'N/A' }}
                    </p>
                </div>
                <div class="flex items-center gap-2 justify-between">
                    <p class="inline-flex items-start gap-2 break-all text-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="flex-shrink-0 size-4" fill="#0081FF">
                            <path fill-rule="evenodd" d="M6 3.75A2.75 2.75 0 0 1 8.75 1h2.5A2.75 2.75 0 0 1 14 3.75v.443c.572.055 1.14.122 1.706.2C17.053 4.582 18 5.75 18 7.07v3.469c0 1.126-.694 2.191-1.83 2.54-1.952.599-4.024.921-6.17.921s-4.219-.322-6.17-.921C2.694 12.73 2 11.665 2 10.539V7.07c0-1.321.947-2.489 2.294-2.676A41.047 41.047 0 0 1 6 4.193V3.75Zm6.5 0v.325a41.622 41.622 0 0 0-5 0V3.75c0-.69.56-1.25 1.25-1.25h2.5c.69 0 1.25.56 1.25 1.25ZM10 10a1 1 0 0 0-1 1v.01a1 1 0 0 0 1 1h.01a1 1 0 0 0 1-1V11a1 1 0 0 0-1-1H10Z" clip-rule="evenodd" />
                            <path d="M3 15.055v-.684c.*************.39.142 2.092.642 4.313.987 6.61.987 2.297 0 4.518-.345 6.61-.987.135-.041.264-.089.39-.142v.684c0 1.347-.985 2.53-2.363 2.686a41.454 41.454 0 0 1-9.274 0C3.985 17.585 3 16.402 3 15.055Z" />
                        </svg>
                        {{ contact.office_phone ?? "N/A" }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>
