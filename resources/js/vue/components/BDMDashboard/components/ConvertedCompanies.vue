<script setup>
import {onMounted, ref, defineEmits, computed} from "vue";
import ApiService from "./../../Prospects/api.js";
import BaseTable from "../../Shared/components/BaseTable.vue";
import MissedProductsModal from "./modals/MissedProductsModal.vue";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import Badge from "../../Shared/components/Badge.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import SharedApiServiceFactory from "../../Shared/services/api.js";
import Filterable from "../../Shared/components/Filterables/Filterable.vue";
import FilterableActivePills from "../../Shared/components/Filterables/FilterableActivePills.vue";
import { DateTime } from "luxon";

const props = defineProps({
    darkMode: false,
    themeClasses: '',
})

onMounted(() => {
    getMyConvertedCompanies()
    getSaleStatusTypes()
})

const apiService = ApiService.make();
const loading = ref(false)
const convertedCompanies = ref([]);
const showMissedProductsModal = ref(false);
const modalContextCompany = ref(null);
const missedProductConfig = ref(null);
const now = ref(Date.now());

const getMyConvertedCompanies = () => {
    loading.value = true
    apiService.getMyConvertedCompanies().then(res => {
        if(res.data.companies)
            convertedCompanies.value = res.data.companies.map((i) => {
                return i;
            });
            orderBy.value = 'ascending'
            sortDataBy('name')
    }).catch((e) => {
        flashAlert('error', e)
    }).finally(() => {
        loading.value = false
    })
}

const salesStatusOptions = ref([])
const loadingSaleStatus = ref(false)
function getSaleStatusTypes() {
    loadingSaleStatus.value = true;
    sharedApi.getSaleStatusTypes().then(resp => {
        const statuses = resp.data.data.statuses;
        salesStatusOptions.value = Object.keys(statuses).map((status) => ({id: statuses[status], name: status}));
    }).catch(e => console.error("Get Sales Status Options Error: ", e))
        .finally(() => {
            loadingSaleStatus.value = false;
        });
}

const emit = defineEmits(['flash-alert', 'edit-prospect'])
function flashAlert(type, message) {
    emit('flash-alert', type, message);
}

const scanForMissedProducts = async () => {
    loading.value = true;
    now.value = Date.now();
    apiService.scanMissedProducts().then(res => {
        if(res.data.missed_products) {
            missedProductConfig.value = res.data.missed_products.config ?? missedProductConfig.value;
            Object.entries(res.data.missed_products.companies ?? {}).forEach(([id, payload]) => {
                const target = convertedCompanies.value.find(company => company.id === parseInt(id));
                if (target)
                    Object.assign(target, payload);
            });
        }
    }).catch((e) => {
        flashAlert('error', e);
    }).finally(() => {
        loading.value = false;
    });
}

const toggleMissedProductsModal = (show) => {
    showMissedProductsModal.value = show == null
        ? !showMissedProductsModal.value
        : !!show;
}

const showMissedProductModalForCompany = (company) => {
    modalContextCompany.value = company;
    if (modalContextCompany.value?.id)
        toggleMissedProductsModal(true);
}

const hideMissedProductModal = () => {
    modalContextCompany.value = 0;
    toggleMissedProductsModal(false);
}

const companyInsideMinimumSendThreshold = (company) => {
    return (company.can_send_at ?? 0) > now.value;
}

const companyExceedsMissedProductThreshold = (company) => {
    return missedProductConfig.value?.product_threshold
        ? company.missed_product_count >= missedProductConfig.value.product_threshold
        : false;
}

const companyHasReceivedNotification = (company) => company.last_sent_at > 0;

const activeClientsCustomActions = ref([
    {event: 'scan-missed-products', name: 'Scan for Missed Products'},
])

const orderBy = ref("ascending")
const lastSortBy = ref(null)
const sortDataBy = (key) => {
    if(lastSortBy.value !== key) {
        orderBy.value = "ascending"
    }

    lastSortBy.value = key

    convertedCompanies.value.sort((first, second) => {
        let a = typeof first[key] === 'string' ? first[key].toLowerCase() : first[key];
        let b = typeof second[key] === 'string' ? second[key].toLowerCase() : second[key];

        switch(true) {
            case a === null:
                return 1;
            case b === null:
                return -1;
            case a < b:
                return -1;
            case a > b:
                return 1;
            default:
                return 0;
        };
    });

    if (orderBy.value === 'ascending') {
        orderBy.value = 'descending';
    } else {
        orderBy.value = 'ascending';
        convertedCompanies.value.reverse();
    }
};

const sharedApi = SharedApiServiceFactory.make()
const industries = ref([])
const loadingIndustries = ref(true)
onMounted(() => {
    sharedApi.getOdinIndustries().then(res => {
        if(res.data.data.status === true) {
            industries.value = res.data.data.industries.map((i) => {
                return {
                    id: i.id,
                    name: i.name
                };
            });
        }
    }).catch(() => {
        flashAlert('error', 'Could not load industries.');
    }).finally(() => {
        loadingIndustries.value = false
    });
})

const transformedIndustryData = computed(() =>
    industries.value.reduce((acc, item) => {
        acc[item.name] = item.id;
        return acc;
    }, {})
)

const transformedStatusData = computed(() =>
    salesStatusOptions.value.reduce((acc, item) => {
        acc[item.name] = item.id;
        console.log(acc)
        return acc;
    }, {})
)

const sourceFilterOptions = ref(
    { "Existing": "existing", "Prospecting":  "prospecting"}
)

const stateOptions = ref([])
onMounted(() => {
    apiService.getStateOptions().then(res => {
        stateOptions.value = res.data
    })
})
const transformedStateData = computed(() =>
    stateOptions.value
        .slice()
        .sort((a, b) => a.name.localeCompare(b.name))
        .reduce((acc, item) => {
            acc[item.name] = item.id;
            return acc;
        }, {})
)
const filters = ref([
    { "type": "select", "name": "Industry", "id": "industry", "clearable": true, "options": transformedIndustryData, "show": true },
    { "type": "select", "name": "State", "id": "state", "clearable": true, "options": transformedStateData, "show": true },
    { "type": "select", "name": "Sales Status", "id": "sales_status", "clearable": true, "options": transformedStatusData, "show": true },
    { "type": "select", "name": "Source", "id": "source", "clearable": true, "options": sourceFilterOptions, "show": true },
])
const filterInputs = ref({})
const filteredConvertedCompanies = computed(() => {
    let items = convertedCompanies.value
    if(filterInputs.value.industry) {
        items = items.filter(item => {
            let mapped = []
            if(item.industries.length > 0)
            mapped = item.industries.map(i => (i.id))
            return mapped.includes(filterInputs.value.industry)
        })
    }
    if(filterInputs.value.state) {
        items = items.filter(item => {
            if(item.state) {
                return item.state.includes(filterInputs.value.state)
            }
        });
    }
    if(filterInputs.value.sales_status) {
        items = items.filter(item => item.sales_status === filterInputs.value.sales_status);
    }
    if(filterInputs.value.source) {
        items = items.filter(item => item.source === filterInputs.value.source);
    }
    else {
        items = items.filter(item => item)
    }
    return items;
})
function clearFilter (filterId) {
    delete filterInputs.value[filterId]
}

const verifiedCompanyActions = ref([
    {event: 'release-back-to-queue', name: 'Release Back to Queue'},
]);

function releaseCompanyBackToQueue(company) {
    apiService.releaseCompanyBackToQueue(company)
        .then(() => getMyConvertedCompanies())
        .catch(error => console.log(error))
}

const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    const month = date.toLocaleString('en-US', { month: 'long' });
    const day = date.getDate();
    const time = date.toLocaleString('en-US', {
        hour: 'numeric',
        minute: 'numeric',
        hour12: true
    });
    return `${month} ${day}, ${time}`;
};

</script>

<template>
    <div class="border rounded-lg flex-grow" :class="[themeClasses]">
        <div class="flex justify-between px-5 pt-5 pb-4">
            <h3 class="font-bold text-sm uppercase text-primary-500">
                <svg class="inline mr-1 mb-1" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M5.24147 2.39236C5.88473 2.341 6.49538 2.08799 6.98647 1.66936C7.54113 1.19695 8.2459 0.9375 8.97447 0.9375C9.70304 0.9375 10.4078 1.19695 10.9625 1.66936C11.4536 2.08799 12.0642 2.341 12.7075 2.39236C13.4339 2.45042 14.1159 2.76532 14.6312 3.28063C15.1465 3.79593 15.4614 4.47793 15.5195 5.20436C15.5705 5.84736 15.8235 6.45836 16.2425 6.94936C16.7149 7.50402 16.9743 8.20879 16.9743 8.93736C16.9743 9.66593 16.7149 10.3707 16.2425 10.9254C15.8238 11.4164 15.5708 12.0271 15.5195 12.6704C15.4614 13.3968 15.1465 14.0788 14.6312 14.5941C14.1159 15.1094 13.4339 15.4243 12.7075 15.4824C12.0642 15.5337 11.4536 15.7867 10.9625 16.2054C10.4078 16.6778 9.70304 16.9372 8.97447 16.9372C8.2459 16.9372 7.54113 16.6778 6.98647 16.2054C6.49538 15.7867 5.88473 15.5337 5.24147 15.4824C4.51504 15.4243 3.83304 15.1094 3.31773 14.5941C2.80243 14.0788 2.48753 13.3968 2.42947 12.6704C2.37811 12.0271 2.1251 11.4164 1.70647 10.9254C1.23406 10.3707 0.974609 9.66593 0.974609 8.93736C0.974609 8.20879 1.23406 7.50402 1.70647 6.94936C2.1251 6.45827 2.37811 5.84762 2.42947 5.20436C2.48753 4.47793 2.80243 3.79593 3.31773 3.28063C3.83304 2.76532 4.51504 2.45042 5.24147 2.39236ZM12.6815 7.64436C12.8636 7.45576 12.9644 7.20316 12.9621 6.94096C12.9599 6.67876 12.8547 6.42795 12.6693 6.24254C12.4839 6.05713 12.2331 5.95196 11.9709 5.94969C11.7087 5.94741 11.4561 6.0482 11.2675 6.23036L7.97447 9.52336L6.68147 8.23036C6.49287 8.0482 6.24026 7.94741 5.97807 7.94969C5.71587 7.95196 5.46506 8.05713 5.27965 8.24254C5.09424 8.42795 4.98907 8.67876 4.98679 8.94096C4.98452 9.20316 5.08531 9.45576 5.26747 9.64436L7.26747 11.6444C7.455 11.8318 7.70931 11.9371 7.97447 11.9371C8.23963 11.9371 8.49394 11.8318 8.68147 11.6444L12.6815 7.64436Z" fill="currentColor"/>
                </svg>
                Verified Companies <span class="text-xs">({{ convertedCompanies.length }})</span>
            </h3>
            <div v-if="false">
                <ActionsHandle :dark-mode="darkMode"
                               :custom-actions="activeClientsCustomActions"
                               @scan-missed-products="scanForMissedProducts"
                               :no-custom-action="false"
                               :no-delete-button="true"
                               :no-edit-button="true"
                               width="w-64"
                >
                </ActionsHandle>
            </div>
        </div>
        <div class="flex justify-between items-center px-5 pb-5">
            <div class="inline-flex items-center gap-3">
                <Filterable
                    :dark-mode="darkMode"
                    :filters="filters"
                    v-model="filterInputs"
                />
                <FilterableActivePills
                    v-if="filters.length"
                    :filters="filters"
                    :active-filters="filterInputs"
                    :dark-mode="darkMode"
                    @reset-filter="clearFilter"
                />
            </div>
        </div>

        <BaseTable :dark-mode="darkMode" :loading="loading">
            <template #head>
                <tr>
                    <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('name')">
                        Company
                        <span v-if="lastSortBy === 'name'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class="cursor-default">
                        Status
                    </th>
                    <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('last_contact_date')">
                        Contacted On
                        <span v-if="lastSortBy === 'last_contact_date'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class="cursor-default">
                        Scheduled Task
                    </th>
                    <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('days_since_last_lead')">
                        Days Since Last Lead
                        <span v-if="lastSortBy === 'days_since_last_lead'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class="cursor-default">
                        Industries
                    </th>
                    <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('decision_maker')">
                        Decision Maker
                        <span v-if="lastSortBy === 'decision_maker'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('website')">
                        Website
                        <span v-if="lastSortBy === 'website'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class="cursor-default !text-center">
                        Actions
                    </th>
                </tr>
            </template>
            <template #body>
                <tr v-for="company in filteredConvertedCompanies" :key="company.id">
                    <td>
                        <div class="flex items-center">
                            <a target="_blank" :href="company.profile_link" class="cursor-pointer block hover:text-primary-500 font-medium">
                                {{company.name}}
                            </a>
                            <svg class="flex-shrink-0 inline w-3.5 ml-2" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                            <div v-if="missedProductConfig"
                                class="cursor-pointer"
                                @click="() => showMissedProductModalForCompany(company)"
                            >
                                <div
                                     :class="[
                                         companyHasReceivedNotification(company) ? 'text-orange-600 hover:text-orange-400'
                                            : companyExceedsMissedProductThreshold(company)
                                                ? 'text-primary-500 hover:text-primary-300'
                                                : 'text-slate-500']"
                                     :title="companyInsideMinimumSendThreshold(company)
                                        ? 'This company has previously received a missed product notification.'
                                        : companyExceedsMissedProductThreshold(company)
                                            ? 'This company has enough missed products for a notification'
                                            : 'This company cannot be sent a notification at this time.'"
                                >
                                    <svg class="w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 9v.906a2.25 2.25 0 0 1-1.183 1.981l-6.478 3.488M2.25 9v.906a2.25 2.25 0 0 0 1.183 1.981l6.478 3.488m8.839 2.51-4.66-2.51m0 0-1.023-.55a2.25 2.25 0 0 0-2.134 0l-1.022.55m0 0-4.661 2.51m16.5 1.615a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V8.844a2.25 2.25 0 0 1 1.183-1.981l7.5-4.039a2.25 2.25 0 0 1 2.134 0l7.5 4.039a2.25 2.25 0 0 1 1.183 1.98V19.5Z" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="">
                        <span>
                            <loading-spinner v-if="loadingSaleStatus" size="w-6 h-6"></loading-spinner>
                            <badge color="primary" :dark-mode="darkMode" v-else>
                                <span v-for="status in salesStatusOptions">
                                    <span v-if="status.id === company.sales_status">
                                        {{status.name}}
                                    </span>
                                </span>
                            </badge>
                        </span>
                    </td>
                    <td class="text-sm">
                        <span v-if="company?.last_contact_date">
                            {{formatDate(company.last_contact_date)}}
                        </span>
                        <span v-else>N/A</span>
                    </td>
                    <td class="text-sm">
                        <a
                            v-if="company?.most_recent_task_date"
                            class="text-blue-500 hover:underline"
                            :href="`/tasks/queue?tasks=${company?.most_recent_task_id}`"
                            target="_blank"
                        >{{ DateTime.fromISO(company.most_recent_task_date).toLocaleString(DateTime.DATETIME_SHORT) }}</a>
                        <span v-else>N/A</span>
                    </td>
                    <td class="text-sm">
                        <span v-if="company.days_since_last_lead !== null">
                            {{ company.days_since_last_lead }}
                        </span>
                        <span v-else>N/A</span>
                    </td>
                    <td class="">
                        <span>
                            <loading-spinner size="w-6 h-6" v-if="loadingIndustries"></loading-spinner>
                            <span v-else>
                                <badge v-if="company.industries.length > 1" class="group relative" :dark-mode="darkMode" color="green" >
                                <svg class="inline mr-1 w-3.5" viewBox="0 0 22 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.3994 6C10.869 6 10.3603 6.21071 9.9852 6.58579C9.61013 6.96086 9.39941 7.46957 9.39941 8C9.39941 8.53043 9.61013 9.03914 9.9852 9.41421C10.3603 9.78929 10.869 10 11.3994 10C11.9298 10 12.4386 9.78929 12.8136 9.41421C13.1887 9.03914 13.3994 8.53043 13.3994 8C13.3994 7.46957 13.1887 6.96086 12.8136 6.58579C12.4386 6.21071 11.9298 6 11.3994 6ZM8.57099 5.17157C9.32113 4.42143 10.3385 4 11.3994 4C12.4603 4 13.4777 4.42143 14.2278 5.17157C14.978 5.92172 15.3994 6.93913 15.3994 8C15.3994 9.06087 14.978 10.0783 14.2278 10.8284C13.4777 11.5786 12.4603 12 11.3994 12C10.3385 12 9.32113 11.5786 8.57099 10.8284C7.82084 10.0783 7.39941 9.06087 7.39941 8C7.39941 6.93913 7.82084 5.92172 8.57099 5.17157Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M2.91143 8C4.14693 11.4964 7.48265 14 11.3994 14C15.3171 14 18.6519 11.4965 19.8874 8C18.6519 4.50354 15.3171 2 11.3994 2C7.48265 2 4.14693 4.50359 2.91143 8ZM0.903357 7.7004C2.3045 3.23851 6.47319 0 11.3994 0C16.3267 0 20.4944 3.23856 21.8955 7.7004C21.9567 7.89544 21.9567 8.10456 21.8955 8.2996C20.4944 12.7614 16.3267 16 11.3994 16C6.47319 16 2.3045 12.7615 0.903357 8.2996C0.84211 8.10456 0.84211 7.89544 0.903357 7.7004Z" fill="currentColor"/></svg>
                                Multi-Industry
                                <span class="absolute z-50 top-0 left-0 group-hover:visible invisible rounded-md px-3 border shadow-md py-2" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                                    <span class="mr-2 inline" v-for="id in company.industries" >
                                        <span>{{id.name}}</span>
                                    </span>
                                </span>
                            </badge>
                            <badge v-else :dark-mode="darkMode" color="primary" class="mr-1" v-for="id in company.industries">
                                {{ id.name }}
                            </badge>
                            </span>
                        </span>
                    </td>
                    <td class="">
                        {{company.decision_maker}}
                    </td>
                    <td>
                        <a v-if="company.website" class="hover:text-primary-500 inline-flex" target="_blank" :href="company.website">
                            <p class="truncate w-3/4">
                                {{company.website}}
                            </p>
                            <svg class="inline w-3.5 ml-1" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                        </a>
                    </td>
                    <td>
                        <ActionsHandle
                            :dark-mode="darkMode"
                            width="w-52"
                            :custom-actions="verifiedCompanyActions"
                            :no-custom-action="false"
                            :no-delete-button="true"
                            :no-edit-button="true"
                            @release-back-to-queue="releaseCompanyBackToQueue(company)"
                        ></ActionsHandle>
                    </td>
                </tr>
            </template>
        </BaseTable>
        <MissedProductsModal
            v-if="showMissedProductsModal"
            :dark-mode="darkMode"
            :company="modalContextCompany"
            :parent-loading="loading"
            :api-service="apiService"
            :notification-config="missedProductConfig"
            @close-modal="() => hideMissedProductModal()"
            @flash-alert="flashAlert"
        />
    </div>
</template>

<style scoped>

</style>
