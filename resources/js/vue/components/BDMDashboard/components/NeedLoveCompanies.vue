<template>
    <div class="border rounded-lg flex-grow" :class="[themeClasses]">
        <div class="flex justify-between px-5 pt-5 pb-4">
            <h3 class="font-bold text-sm uppercase text-primary-500">
                <svg class="inline mr-1 mb-1" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 0.5C3.80558 0.5 0 4.30558 0 9C0 13.6944 3.80558 17.5 8.5 17.5C13.1944 17.5 17 13.6944 17 9C17 4.30558 13.1944 0.5 8.5 0.5ZM8.5 15.5C4.91015 15.5 2 12.5899 2 9C2 5.41015 4.91015 2.5 8.5 2.5C12.0899 2.5 15 5.41015 15 9C15 12.5899 12.0899 15.5 8.5 15.5ZM8.5 4.5C8.22386 4.5 8 4.72386 8 5V9C8 9.27614 8.22386 9.5 8.5 9.5H12C12.2761 9.5 12.5 9.27614 12.5 9C12.5 8.72386 12.2761 8.5 12 8.5H9V5C9 4.72386 8.77614 4.5 8.5 4.5Z" fill="currentColor"/>
                </svg>
                BDM Company Focus <span class="text-xs">({{ needLoveCompanies.length }})</span>
            </h3>
        </div>

        <BaseTable :dark-mode="darkMode" :loading="loading">
            <template #head>
                <tr>
                    <th class="cursor-default">
                        Company
                        <span v-if="lastSortBy === 'company_name'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class="cursor-default">
                        Last Contact
                        <span v-if="lastSortBy === 'last_activity'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class="cursor-default flex gap-1">
                        May Lose By
                        <Tooltip>You may lose this company if they haven't been activated by the following date</Tooltip>
                    </th>
                    <th class="cursor-default">
                        Scheduled Task
                    </th>
                </tr>
            </template>
            <template #body>
                <tr v-for="company in sortedCompanies" :key="company.id">
                    <td class="vertical-mid">
                        <div class="flex items-center gap-x-5">
                            <a target="_blank" :href="company.profile_url" class="cursor-pointer inline-flex items-center hover:text-primary-500 font-medium truncate">
                                {{company.company_name}}
                                <svg class="w-3.5 ml-2" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                            </a>
                        </div>
                    </td>
                    <td class="text-sm">
                        {{company.last_activity ? new Date(company.last_activity).toLocaleDateString('en-US', {month: 'long', day: 'numeric', year: 'numeric'}) : 'na'}}
                    </td>
                    <td>
                        {{company.probation_end_date ? new Date(company.probation_end_date).toLocaleDateString('en-US', {month: 'long', day: 'numeric'}) : 'na'}}
                    </td>
                    <td>
                        {{company.next_task_date ? new Date(company.next_task_date).toLocaleDateString('en-US', {month: 'long', day: 'numeric'}) : 'na'}}
                    </td>
                </tr>
            </template>
        </BaseTable>
    </div>
</template>

<script setup>
import {onMounted, ref, computed} from 'vue';
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import ApiService from "../api/need_love_companies_api.js";
import BaseTable from "../../Shared/components/BaseTable.vue";
import Badge from "../../Shared/components/Badge.vue";
import Tooltip from "../../Shared/components/Tooltip.vue";

const props = defineProps({
    darkMode: {
        type: Boolean,
        default: false
    },
    themeClasses: {
        type: String,
        required: true
    }
});

const loading = ref(false);
const apiService = ApiService.make();

onMounted(() => {
    getNeedLoveCompanies();
});

const needLoveCompanies = ref([]);
const orderBy = ref("ascending");
const lastSortBy = ref(null);

const sortedCompanies = computed(() => {
    return needLoveCompanies.value;
});

const getNeedLoveCompanies = () => {
    loading.value = true;
    apiService.getNeedLoveCompanies().then(res => {
        needLoveCompanies.value = res.data.data;
    }).finally(() => {
        loading.value = false;
    });
};
</script>

<style scoped>
</style>
