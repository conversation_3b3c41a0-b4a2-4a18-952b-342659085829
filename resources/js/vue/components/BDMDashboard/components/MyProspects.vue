<script setup>
import {computed, onMounted, ref, defineEmits} from "vue";
import ApiService from "./../../Prospects/api.js";
import BaseTable from "../../Shared/components/BaseTable.vue";
import Badge from "../../Shared/components/Badge.vue";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import SharedApiServiceFactory from "../../Shared/services/api.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Filterable from "../../Shared/components/Filterables/Filterable.vue";
import FilterableActivePills from "../../Shared/components/Filterables/FilterableActivePills.vue";

const props = defineProps({
    darkMode: false,
    themeClasses: '',
})

onMounted(() => {
    getMyProspects()
})

const apiService = ApiService.make();
const loading = ref(false)
const myProspects = ref([]);
const getMyProspects = () => {
    loading.value = true
    apiService.getMyProspects().then(res => {
        if(res.data.prospects)
            myProspects.value = res.data.prospects.map((i) => {
                return i;
            });
        sortDataBy('company_name')
    }).catch((e) => {
        flashAlert(e)
    }).finally(() => {
        loading.value = false
    })
}

const orderBy = ref("ascending")
const lastSortBy = ref(null)
const sortDataBy = (sortBy) => {
    if(lastSortBy.value !== sortBy) {
        orderBy.value = "ascending"
    }
    lastSortBy.value = sortBy
    if(orderBy.value === "ascending") {
        myProspects.value.sort((a, b) => {
            if (typeof a[sortBy] === "number" && typeof a[sortBy] === "number") {
                if (a[sortBy] < b[sortBy]) return -1;
                if (a[sortBy] > b[sortBy]) return 1;
            }
            else {
                if(a[sortBy] === null) return 1;
                if(b[sortBy] === null) return -1;
                if (a[sortBy].toLowerCase() < b[sortBy].toLowerCase()) return -1;
                if (a[sortBy].toLowerCase() > b[sortBy].toLowerCase()) return 1;
            }
        });
        orderBy.value = "descending"
    }
    else {
        myProspects.value.sort((a, b) => {
            if (typeof a[sortBy] === "number" && typeof a[sortBy] === "number") {
                if (a[sortBy] < b[sortBy]) return 1;
                if (a[sortBy] > b[sortBy]) return -1;
            }
            else {
                if(a[sortBy] === null) return -1;
                if(b[sortBy] === null) return 1;
                if (a[sortBy].toLowerCase() < b[sortBy].toLowerCase()) return 1;
                if (a[sortBy].toLowerCase() > b[sortBy].toLowerCase()) return -1;
            }
        });
        orderBy.value = "ascending"
    }
};

function editProspect(prospect) {
    emit('edit-prospect', prospect);
}

const emit = defineEmits(['flash-alert', 'edit-prospect'])
function flashAlert(type, message) {
    emit('flash-alert', type, message);
}

const myProspectActions = ref([
    {event: 'edit-prospect', name: 'Edit Prospect'},
    {event: 'release-back-to-queue', name: 'Release Back to Queue'},
]);

function save(nextProspect, message) {
    emit('save', nextProspect, message);
}
function releaseBackToQueue(prospect) {
    if(prospect.status === 'active') {
        apiService.releaseBackToQueue(prospect)
            .then(res => save(false, ' has been released back to Queue.'))
            .finally(
                getMyProspects()
            )
    }
}
const sharedApi = SharedApiServiceFactory.make()
const industries = ref([])
const loadingIndustries = ref(true)
onMounted(() => {
    sharedApi.getOdinIndustries().then(res => {
        if(res.data.data.status === true) {
            industries.value = res.data.data.industries.map((i) => {
                return {
                    id: i.id,
                    name: i.name
                };
            });
        }
    }).catch(() => {
            flashAlert('error', 'Could not load industries.');
    }).finally(() => {
            loadingIndustries.value = false
    });
})
const transformedIndustryData = computed(() =>
    industries.value.reduce((acc, item) => {
        acc[item.name] = item.id;
        return acc;
    }, {})
)

const stateOptions = ref([])
onMounted(() => {
    apiService.getStateOptions().then(res => {
        stateOptions.value = res.data
    })
})
const transformedStateData = computed(() =>
    stateOptions.value
        .slice()
        .sort((a, b) => a.name.localeCompare(b.name))
        .reduce((acc, item) => {
            acc[item.name] = item.id;
            return acc;
        }, {})
)

const sourceOptions = computed(() => {
    return {'New Registration': 'registration'}
})
const filters = ref([
    { "type": "select", "name": "Industry", "id": "industry", "clearable": true, "options": transformedIndustryData, "show": true },
    { "type": "select", "name": "State", "id": "state", "clearable": true, "options": transformedStateData, "show": true },
    { "type": "select", "name": "Source", "id": "source", "clearable": true, "options": sourceOptions, "show": true },
])
const filterInputs = ref({})
const filteredMyProspects = computed(() => {
    let items = myProspects.value
    if(filterInputs.value.industry) {
        items = items.filter(item => item.industry_ids.includes(filterInputs.value.industry));
    }
    if(filterInputs.value.state) {
        items = items.filter(item => {
            if(item.address_state_abbr) {
                return item.address_state_abbr.includes(filterInputs.value.state)
            }
        });
    }
    if(filterInputs.value.source) {
        items = items.filter(item => item.source === filterInputs.value.source);
    }
    else {
        items = items.filter(item => item)
    }
    return items;
})
function clearFilter (filterId) {
    delete filterInputs.value[filterId]
}

</script>

<template>
    <div class="border rounded-lg flex-grow" :class="[themeClasses]">
        <div class="flex items-center gap-4 px-5 pt-5 pb-4">
            <h3 class="font-bold text-sm uppercase text-primary-500 content-center">
                <svg class="inline mr-1" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.79492 16.833C10.5699 16.8326 12.2937 16.2384 13.6919 15.145L18.0879 19.541L19.5019 18.127L15.1059 13.731C16.1999 12.3327 16.7945 10.6084 16.7949 8.83301C16.7949 4.42201 13.2059 0.833008 8.79492 0.833008C4.38392 0.833008 0.794922 4.42201 0.794922 8.83301C0.794922 13.244 4.38392 16.833 8.79492 16.833ZM8.79492 2.83301C12.1039 2.83301 14.7949 5.52401 14.7949 8.83301C14.7949 12.142 12.1039 14.833 8.79492 14.833C5.48592 14.833 2.79492 12.142 2.79492 8.83301C2.79492 5.52401 5.48592 2.83301 8.79492 2.83301Z" fill="currentColor"/>
                    <path d="M8.02539 10.4662V10.3454C8.02776 9.93114 8.06445 9.60088 8.13548 9.35467C8.20887 9.10846 8.3154 8.90959 8.45508 8.75808C8.59476 8.60656 8.76284 8.46925 8.95934 8.34615C9.10612 8.25145 9.23751 8.1532 9.35352 8.0514C9.46952 7.9496 9.56185 7.83715 9.6305 7.71405C9.69916 7.58857 9.73349 7.44889 9.73349 7.29501C9.73349 7.13166 9.69443 6.98843 9.6163 6.86532C9.53818 6.74222 9.43282 6.64752 9.30025 6.58123C9.17004 6.51494 9.02563 6.4818 8.86701 6.4818C8.71313 6.4818 8.56753 6.51613 8.43022 6.58478C8.29291 6.65107 8.18046 6.7505 8.09286 6.88308C8.00527 7.01329 7.95792 7.17546 7.95082 7.36958H6.50195C6.51379 6.8961 6.62743 6.50548 6.84286 6.19771C7.0583 5.88758 7.34357 5.65675 7.69869 5.50524C8.0538 5.35136 8.44561 5.27441 8.87411 5.27441C9.34523 5.27441 9.7619 5.35254 10.1241 5.50879C10.4863 5.66267 10.7704 5.88639 10.9764 6.17995C11.1824 6.47351 11.2853 6.82744 11.2853 7.24174C11.2853 7.51873 11.2392 7.76494 11.1468 7.98038C11.0569 8.19345 10.9302 8.38284 10.7669 8.54856C10.6035 8.71191 10.4106 8.85988 10.188 8.99245C10.001 9.10372 9.84712 9.21973 9.72639 9.34047C9.60801 9.4612 9.51924 9.60088 9.46005 9.7595C9.40323 9.91812 9.37364 10.1134 9.37127 10.3454V10.4662H8.02539ZM8.72852 12.7389C8.49177 12.7389 8.28936 12.656 8.12127 12.4903C7.95555 12.3222 7.87388 12.121 7.87624 11.8866C7.87388 11.6546 7.95555 11.4558 8.12127 11.29C8.28936 11.1243 8.49177 11.0415 8.72852 11.0415C8.95342 11.0415 9.1511 11.1243 9.32156 11.29C9.49201 11.4558 9.57842 11.6546 9.58079 11.8866C9.57842 12.0429 9.53699 12.1861 9.4565 12.3163C9.37837 12.4442 9.27539 12.5471 9.14755 12.6253C9.01971 12.701 8.88003 12.7389 8.72852 12.7389Z" fill="currentColor"/>
                </svg>
                Unverified Companies <span class="text-xs">({{ myProspects.length }})</span>
            </h3>
        </div>
        <div class="flex items-center gap-3 px-5 pb-5">
            <Filterable
                :dark-mode="darkMode"
                :filters="filters"
                v-model="filterInputs"
            />
            <FilterableActivePills
                v-if="filters.length"
                :filters="filters"
                :active-filters="filterInputs"
                :dark-mode="darkMode"
                @reset-filter="clearFilter"
            />
        </div>
        <BaseTable :dark-mode="darkMode" :loading="loading">
            <template #head>
                <tr>
                    <th class=" cursor-pointer hover:text-primary-500" @click="sortDataBy('company_name')">
                        Company
                        <span v-if="lastSortBy === 'company_name'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class="cursor-default">
                        Industries
                    </th>
                    <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('address_state_abbr')">
                        State
                        <span v-if="lastSortBy === 'address_state_abbr'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class="cursor-pointer hover:text-primary-500" @click="sortDataBy('source')">
                        Source
                        <span v-if="lastSortBy === 'source'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class=" cursor-pointer hover:text-primary-500" @click="sortDataBy('decision_maker_first_name')">
                        Decision Maker
                        <span v-if="lastSortBy === 'decision_maker_first_name'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class=" cursor-pointer hover:text-primary-500" @click="sortDataBy('company_website')">
                        Website
                        <span v-if="lastSortBy === 'company_website'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class=" cursor-pointer hover:text-primary-500" @click="sortDataBy('notes')">
                        Notes
                        <span v-if="lastSortBy === 'notes'">
                            <span>
                                <svg class="w-3 mb-1 ml-1 inline fill-current text-primary-500 transform transition-all duration-200" :class="[orderBy !== 'ascending' ? '' : 'rotate-180']" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.70692 5.70679C9.51939 5.89426 9.26508 5.99957 8.99992 5.99957C8.73475 5.99957 8.48045 5.89426 8.29292 5.70679L4.99992 2.41379L1.70692 5.70679C1.51832 5.88894 1.26571 5.98974 1.00352 5.98746C0.741321 5.98518 0.490509 5.88001 0.305101 5.6946C0.119692 5.5092 0.0145233 5.25838 0.0122448 4.99619C0.00996641 4.73399 0.110761 4.48139 0.292919 4.29279L4.29292 0.292787C4.48045 0.105316 4.73475 0 4.99992 0C5.26508 0 5.51939 0.105316 5.70692 0.292787L9.70692 4.29279C9.89439 4.48031 9.99971 4.73462 9.99971 4.99979C9.99971 5.26495 9.89439 5.51926 9.70692 5.70679Z"/>
                                </svg>
                            </span>
                        </span>
                    </th>
                    <th class="cursor-default !text-center">
                        Actions
                    </th>
                </tr>
            </template>
            <template #body>
                <tr v-for="prospect in filteredMyProspects" :key="prospect.id">
                    <td>
                        <p @click="editProspect(prospect)" class="hover:text-primary-500 font-medium truncate cursor-pointer py-3">{{prospect.company_name}}</p>
                    </td>
                    <td>
                        <span>
                            <loading-spinner size="w-6 h-6" v-if="loadingIndustries"></loading-spinner>
                            <span v-else>
                                <badge v-if="prospect.industry_ids.length > 1" class="group relative" :dark-mode="darkMode" color="green" >
                                <svg class="inline mr-1 w-3.5" viewBox="0 0 22 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.3994 6C10.869 6 10.3603 6.21071 9.9852 6.58579C9.61013 6.96086 9.39941 7.46957 9.39941 8C9.39941 8.53043 9.61013 9.03914 9.9852 9.41421C10.3603 9.78929 10.869 10 11.3994 10C11.9298 10 12.4386 9.78929 12.8136 9.41421C13.1887 9.03914 13.3994 8.53043 13.3994 8C13.3994 7.46957 13.1887 6.96086 12.8136 6.58579C12.4386 6.21071 11.9298 6 11.3994 6ZM8.57099 5.17157C9.32113 4.42143 10.3385 4 11.3994 4C12.4603 4 13.4777 4.42143 14.2278 5.17157C14.978 5.92172 15.3994 6.93913 15.3994 8C15.3994 9.06087 14.978 10.0783 14.2278 10.8284C13.4777 11.5786 12.4603 12 11.3994 12C10.3385 12 9.32113 11.5786 8.57099 10.8284C7.82084 10.0783 7.39941 9.06087 7.39941 8C7.39941 6.93913 7.82084 5.92172 8.57099 5.17157Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M2.91143 8C4.14693 11.4964 7.48265 14 11.3994 14C15.3171 14 18.6519 11.4965 19.8874 8C18.6519 4.50354 15.3171 2 11.3994 2C7.48265 2 4.14693 4.50359 2.91143 8ZM0.903357 7.7004C2.3045 3.23851 6.47319 0 11.3994 0C16.3267 0 20.4944 3.23856 21.8955 7.7004C21.9567 7.89544 21.9567 8.10456 21.8955 8.2996C20.4944 12.7614 16.3267 16 11.3994 16C6.47319 16 2.3045 12.7615 0.903357 8.2996C0.84211 8.10456 0.84211 7.89544 0.903357 7.7004Z" fill="currentColor"/></svg>
                                Multi-Industry
                                <span class="absolute z-50 top-0 left-0 group-hover:visible invisible rounded-md px-3 border shadow-md py-2" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                                    <span class="mr-2 inline" v-for="id in prospect.industry_ids" >
                                        <span>{{ industries[id - 1].name}},</span>
                                    </span>
                                </span>
                            </badge>
                            <badge v-else :dark-mode="darkMode" color="primary" class="mr-1" v-for="id in prospect.industry_ids">
                                {{ industries[id - 1].name}}
                            </badge>
                            </span>
                        </span>
                    </td>
                    <td>
                        <span v-if="prospect.address_state_abbr">{{prospect.address_state_abbr}}</span>
                    </td>
                    <td>
                        <Badge v-if="prospect.source === 'registration'" :dark-mode="darkMode" color="purple" class="capitalize">New Registration</Badge>
                    </td>
                    <td>
                        <span class="mr-2" v-if="prospect.decision_maker_first_name">{{prospect.decision_maker_first_name}}</span>
                        <span v-if="prospect.decision_maker_last_name">{{prospect.decision_maker_last_name}}</span>
                    </td>
                    <td>
                        <a v-if="prospect.company_website" class="hover:text-primary-500 inline-flex gap-2 w-3/4" target="_blank" :href="prospect.company_website">
                            <p class="truncate">
                                {{prospect.company_website}}
                            </p>
                            <svg class="inline w-3.5 flex-shrink-0" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                        </a>
                    </td>
                    <td>
                        <p v-html="prospect.notes ? prospect.notes : ''" class="col-span-2 line-clamp-2 text-xs pr-12" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']"></p>
                    </td>
                    <td>
                        <ActionsHandle
                            :dark-mode="darkMode"
                            width="w-52"
                            :custom-actions="myProspectActions"
                            :no-custom-action="false"
                            :no-delete-button="true"
                            :no-edit-button="true"
                            @edit-prospect="editProspect(prospect)"
                            @release-back-to-queue="releaseBackToQueue(prospect)"
                        ></ActionsHandle>
                    </td>
                </tr>
            </template>
        </BaseTable>
    </div>
</template>

<style scoped>

</style>
