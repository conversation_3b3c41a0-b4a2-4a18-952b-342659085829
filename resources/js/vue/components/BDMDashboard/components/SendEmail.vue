<script setup>
import Modal from "../../Shared/components/Modal.vue";
import {defineEmits, ref} from "vue";
import CustomInput from "../../Shared/components/CustomInput.vue";

const props = defineProps({
    darkMode: {
        default: false,
        type: Boolean
    },
    prospect: {},
    disabled: false,
})
const emit = defineEmits(['email']);
function email() {
    emit('email');
}
</script>

<template>
    <div>
        <div
            @click="email" class="flex-shrink-0 inline-flex justify-center items-center gap-1 rounded-md border border-primary-500 px-3 py-1 group cursor-pointer" :class="[darkMode ? 'bg-dark-module hover:bg-dark-border' : 'bg-light-module hover:bg-primary-100', !disabled ? 'opacity-25 pointer-events-none cursor-not-allowed' : '']">
            <p class="text-xs text-primary-500 font-semibold">Email</p>
        </div>
    </div>


</template>

<style scoped>

</style>
