<script setup>
import ConvertedCompanies from "./components/ConvertedCompanies.vue";
import {computed, onMounted, ref} from "vue";
import MyProspects from "./components/MyProspects.vue";
import Prospecting from "./components/Prospecting.vue";
import ApiService from "../Prospects/api.js";
import CustomButton from "../Shared/components/CustomButton.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import Alert from "../Shared/components/Alert.vue";
import MultiSelect from "../Shared/components/MultiSelect.vue";
import Modal from "../Shared/components/Modal.vue";
import NeedLoveCompanies from "./components/NeedLoveCompanies.vue";
import { useUserStore } from "../../../stores/user-store";

const userStore = useUserStore();
const isBDM = computed(() => userStore.user?.roles?.includes('business-development-manager'));

const flashAlert = (type, message) => {
    if(timeout){
        clearTimeout(timeout.value)
        timeout.value = null
    }
    alert.value = {type, message}
    timeout.value = setTimeout(() => alert.value = {}, 4000)
}
const save = (nextProspect, message) => {
    apiService.saveProspect(prospect.value).then(res => {
        if (message) {
            flashAlert('success', prospect.value.company_name + message)
        } else {
            flashAlert('success', prospect.value.company_name + ' Saved Successfully')
        }

        if(nextProspect){
            getNextAvailableProspect()
        }else {
            prospect.value = res.data.prospect
        }
    })
}

const alert = ref({})
const timeout = ref(null)
const loading = ref(false)

const apiService = ApiService.make();

const props = defineProps({
    darkMode: false,
})

onMounted(() => {
    getVerifiedCompanyCount()
    const params = new URLSearchParams(window.location.search)
    const prospectId = params.get('prospect_id')
    if(prospectId)
        loadProspectById(prospectId)
})

const loadProspectById = (prospectId) => {
    apiService.getProspect(prospectId).then(res => {
        editProspect(res.data)
    })
}

const themeClasses = computed(() => {
    if(props.darkMode === true) {
        return 'bg-dark-module border-dark-border text-slate-50'
    }
    else {
        return 'bg-light-module border-light-border text-slate-900'
    }
})

const themeBackgroundClasses = computed(() => {
    if(props.darkMode === true) {
        return 'bg-dark-background border-dark-border text-slate-50'
    }
    else {
        return 'bg-light-background border-light-border text-slate-900'
    }
})

const selectedTabName = ref('Dashboard')
function selectTab(tabName) {
   selectedTabName.value = tabName;
}

const prospect = ref(null)
function editProspect(selectedProspect) {
    prospect.value = selectedProspect
    duplicateCompanyData.value = null
    convertedCompanyData.value = null
    selectedProspect.company_website = enforceHttps(selectedProspect.company_website)
    selectedProspect.company_phone = formatPhoneNumber(selectedProspect.company_phone)
    selectedProspect.decision_maker_phone = formatPhoneNumber(selectedProspect.decision_maker_phone)
    save(false) // saving to run the dupe check
    selectTab('Unverified')
    window.scrollTo(0, 0);
}
function enforceHttps(url) {
    if (url !== null) {
        if (!/^(https?:\/\/)/i.test(url)) {
            return 'https://' + url;
        }
    }
    return url;
}
const registration = ref(null)

const message = ref(null)
const getNextAvailableProspect = () => {
    loading.value = true
    window.scrollTo(0, 0);
    apiService.getNextAvailableProspect(timezones.value).then(res => {
        if(res.data.prospect) {
            prospect.value = res.data.prospect
            prospect.value.company_website = enforceHttps(prospect.value.company_website)
            prospect.value.company_phone = formatPhoneNumber(prospect.value.company_phone)
            prospect.value.decision_maker_phone = formatPhoneNumber(prospect.value.decision_maker_phone)
        }
        else
            message.value = 'No New Prospects Available. Try Again Later'
    }).finally(() => {
        loading.value = false
    })
}

const clearProspect = () => {
    prospect.value = null
    registration.value = null
}

const duplicateCompanyData = ref(null)
function duplicateFlagSuccess(companyData) {
    duplicateCompanyData.value = companyData
}
const convertedCompanyData = ref(null)
function convertedFlagSuccess(companyData) {
    convertedCompanyData.value = companyData
}
function formatPhoneNumber(phoneNumber) {
    if (phoneNumber !== null)
    return phoneNumber.replace(/^\+1/, '').replace(/[^0-9]/g, '');
}

const timezoneOptions = [{id:'Eastern', name: 'Eastern'}, {id:'Central', name: 'Central'}, {id:'Mountain', name: 'Mountain'}, {id:'Pacific', name: 'Pacific'}];
const timezones = ref([])

const timezoneModal = ref(false)
function toggleTimezoneModal() {
    timezoneModal.value = ! timezoneModal.value;
}


const nextExistingCompany = ref(null)
const nextExistingButtonTimeout = ref(false)
const getNextAvailableExistingCompany = () => {
    loading.value = true
    apiService.getNextAvailableExistingCompany(timezones.value).then(res => {
        if(res.data) {
            nextExistingCompany.value = res.data
            if(nextExistingCompany.value) {
                if(nextExistingCompany.value.profile_link) {
                    window.open(nextExistingCompany.value.profile_link, "_blank")
                    nextExistingButtonTimeout.value = true;
                    setTimeout(() => {
                        nextExistingButtonTimeout.value = false;
                    }, 5000);
                }
                else {
                    message.value = 'Company Profile Link Unavailable'
                }
            }
            else {
                message.value = 'No companies available. Please try again later.'
            }
        }
        else {
            message.value = 'Something went wrong.'
        }
    }).finally(() => {
        loading.value = false
    })
}

const verifiedCompanyCount = ref(null)
const getVerifiedCompanyCount = () => apiService.getVerifiedCompanyCount().then(res => verifiedCompanyCount.value = res.data.count)

</script>

<template>
    <div>
        <Alert v-if="alert.message" :dark-mode="darkMode" class="fixed flex justify-center top-12 z-50 inset-x-0" :alert-type="alert.type" :text="alert.message"></Alert>
        <div :class="[themeClasses]" class="relative z-30 shadow-sm">
            <div class="px-4 md:px-10 pt-6">
                <h1 class="text-lg font-semibold pb-0 leading-none mb-6" :class="[darkMode ? 'text-slate-50' : 'text-slate-900']">
                    Business Development Manager
                </h1>
            </div>
            <!-- Tabs -->
            <div class="border-b overflow-x-auto" :class="[themeClasses]">
                <div class="flex items-center overflow-x-auto">
                    <p @click="selectTab('Dashboard')" :class="(selectedTabName === 'Dashboard') ? 'font-semibold text-primary-500 border-primary-500' : (darkMode ? 'text-slate-400' : 'text-slate-600') + ' border-transparent font-medium'" class="whitespace-nowrap text-sm px-8 md:px-16 border-b-2 cursor-pointer pb-4">
                        Dashboard
                    </p>
                    <p @click="selectTab('Unverified')" :class="(selectedTabName === 'Unverified') ? 'font-semibold text-primary-500 border-primary-500' : (darkMode ? 'text-slate-400' : 'text-slate-600') + ' border-transparent font-medium'" class="whitespace-nowrap text-sm px-8 md:px-16 border-b-2 cursor-pointer pb-4">
                        Unverified
                    </p>
                    <p @click="selectTab('Existing Companies')" :class="(selectedTabName === 'Existing Companies') ? 'font-semibold text-primary-500 border-primary-500' : (darkMode ? 'text-slate-400' : 'text-slate-600') + ' border-transparent font-medium'" class="whitespace-nowrap text-sm px-8 md:px-16 border-b-2 cursor-pointer pb-4">
                        Existing Companies
                    </p>
                </div>
            </div>
            <div class="inline-flex gap-2 mt-4 pb-4 px-4 lg:pb-0 lg:px-0 lg:mt-0 lg:absolute right-10 bottom-4">
                <CustomButton :dark-mode="darkMode" @click="toggleTimezoneModal" color="primary-outline">
                    <svg class="mr-1 w-4" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 0.820312C4.486 0.820312 0 5.30631 0 10.8203C0 16.3343 4.486 20.8203 10 20.8203C15.514 20.8203 20 16.3343 20 10.8203C20 5.30631 15.514 0.820312 10 0.820312ZM13.293 15.5273L9 11.2343V4.82031H11V10.4063L14.707 14.1133L13.293 15.5273Z" fill="#0081FF"/>
                    </svg>
                    Timezones: {{timezones.length > 0 ? timezones.join(", ") : 'All'}}
                </CustomButton>
            </div>
        </div>
        <div class="mx-4 mt-4 pb-16 md:mx-10 md:mt-8">
            <!-- Dashboard -->
            <div v-if="selectedTabName === 'Dashboard'">
                <div>
                    <div class="mb-6" v-if="isBDM">
                        <NeedLoveCompanies :dark-mode="darkMode" :theme-classes="themeClasses" />
                    </div>
                    <div class="mb-6">
                        <ConvertedCompanies
                            :dark-mode="darkMode"
                            :theme-classes="themeClasses"
                            @flash-alert="flashAlert"
                        />
                    </div>
                    <div class="mb-6">
                        <MyProspects @edit-prospect="editProspect" :dark-mode="darkMode" :theme-classes="themeClasses"></MyProspects>
                    </div>
                </div>
            </div>
            <div v-else-if="selectedTabName === 'Unverified'">
                <div v-if="loading" class="h-[412px] flex justify-center items-center">
                    <LoadingSpinner></LoadingSpinner>
                </div>
                <Prospecting
                    v-else-if="prospect"
                    @save="save"
                    :dark-mode="darkMode"
                    :prospect="prospect"
                    :api-service="apiService"
                    @flash-alert="flashAlert"
                    :theme-classes="themeClasses"
                    :theme-background-classes="themeBackgroundClasses"
                    @reset="clearProspect"
                    :loading="loading"
                    @duplicate-flag-success="duplicateFlagSuccess"
                    @converted-flag-success="convertedFlagSuccess"
                >
                </Prospecting>
                <div v-else class="flex justify-center h-[412px] items-center border rounded-lg" :class="themeClasses">
                    <div class="text-center">
                        <div v-if="duplicateCompanyData" class="mb-4">
                            Duplicate confirmed as
                            <a target="_blank" class="inline text-primary-500" :href="duplicateCompanyData.profile_link">{{duplicateCompanyData.company_name}}
                                <svg class="inline w-3.5 mb-1 ml-1" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                            </a>
                        </div>
                        <div v-if="convertedCompanyData" class="mb-4">
                            <a target="_blank" class="inline text-primary-500 mr-2" :href="convertedCompanyData.profile_link">{{convertedCompanyData.company_name}}
                                <svg class="inline w-3.5 mb-1 ml-1" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                            </a>
                             has been converted to a company.
                        </div>
                        <CustomButton icon @click="getNextAvailableProspect">
                            <template #icon>
                                <svg class="w-4 mr-1" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.65894 16.4672C10.4339 16.4668 12.1577 15.8726 13.5559 14.7792L17.9519 19.1752L19.3659 17.7612L14.9699 13.3652C16.0639 11.9669 16.6585 10.2427 16.6589 8.46721C16.6589 4.05621 13.0699 0.467213 8.65894 0.467213C4.24794 0.467213 0.658936 4.05621 0.658936 8.46721C0.658936 12.8782 4.24794 16.4672 8.65894 16.4672ZM8.65894 2.46721C11.9679 2.46721 14.6589 5.15821 14.6589 8.46721C14.6589 11.7762 11.9679 14.4672 8.65894 14.4672C5.34994 14.4672 2.65894 11.7762 2.65894 8.46721C2.65894 5.15821 5.34994 2.46721 8.65894 2.46721Z" fill="white"/></svg>
                            </template>
                            Search for Unverified Companies
                        </CustomButton>
                        <div class="pt-4" v-if="message">{{message}}</div>
                    </div>
                </div>
            </div>
            <div v-else-if="selectedTabName === 'Existing Companies'">
                <div class="flex justify-center h-[412px] items-center border rounded-lg" :class="themeClasses">
                    <div v-if="loading" class="h-[412px] flex justify-center items-center">
                        <LoadingSpinner></LoadingSpinner>
                    </div>
                    <div class="text-center" v-else>
                        <div v-if="verifiedCompanyCount < 500">
                            <CustomButton :dark-mode="darkMode" :disabled="nextExistingButtonTimeout" @click="getNextAvailableExistingCompany">Get Next Best Existing Company</CustomButton>
                            <div class="text-center mt-2">
                                <p v-if="nextExistingCompany">
                                    You've been assigned to
                                    <a target="_blank" class="inline text-primary-500" :href="nextExistingCompany.profile_link">{{nextExistingCompany.company_name}}
                                        <svg class="inline w-3.5 mb-1 ml-1" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                                    </a>
                                </p>
                                <p v-else>
                                    <span v-if="message">{{message}}</span>
                                </p>
                            </div>
                        </div>
                        <div v-else>
                            Limit of <b>500</b> companies reached
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <Modal
            :dark-mode="darkMode"
            :small="true"
            v-if="timezoneModal"
            hide-cancel
            hide-confirm
            @close="toggleTimezoneModal"
            no-min-height
            content-max-height="min-h-[30vh]"
        >
            <template #header>
                <p class="text-lg font-semibold">Timezone Settings</p>
            </template>
            <template #content>
                <div class="grid lg:grid-cols-2 gap-4">
                    <div>
                        <label :class="[darkMode ? 'text-slate-50' : 'text-slate-900']" class="font-semibold mb-2 block text-sm">
                           Timezones
                        </label>
                        <multi-select :dark-mode="darkMode" text-place-holder="All" :selected-ids="timezones" :options="timezoneOptions"></multi-select>
                    </div>
                </div>
            </template>
            <template #buttons>
                <CustomButton @click="toggleTimezoneModal">
                    Done
                </CustomButton>
            </template>
        </Modal>
    </div>
</template>

<style scoped>

</style>
