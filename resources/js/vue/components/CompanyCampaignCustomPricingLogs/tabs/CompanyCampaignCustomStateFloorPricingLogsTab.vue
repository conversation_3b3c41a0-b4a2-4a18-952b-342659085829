<template>
    <div class="mx-10">
        <CompanyCampaignCustomStateFloorPrice :dark-mode="darkMode" />
    </div>
</template>
<script>
import CompanyCampaignCustomStateFloorPrice
    from "../components/CompanyCampaignCustomStateFloorPrice/CompanyCampaignCustomStateFloorPrice.vue";
export default {
    name: "CompanyCampaignCustomStateFloorPricingLogsTab",
    components: {CompanyCampaignCustomStateFloorPrice},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    }
}
</script>

<style scoped>

</style>
