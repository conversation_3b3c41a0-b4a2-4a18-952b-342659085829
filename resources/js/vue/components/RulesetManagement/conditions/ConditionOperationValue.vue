<template>
    <div class="flex items-center">
        <span v-if="rule.data.rule_reference?.variable_prefix">
            {{rule.data.rule_reference.variable_prefix}}
        </span>
        <Dropdown
            v-if="rule.data.rule_reference.comparison_type === 'entry'"
            v-model="localReference"
            :dark-mode="darkMode"
            :options="customEntryOptions"
            :selected="localReference"
            @change="handleChange"
        />
        <custom-inline-input
            v-else
            v-model="localReference"
            type="number"
            :dark-mode="darkMode"
            @update:modelValue="handleChange"
        />
        <span v-if="rule.data.rule_reference?.variable_suffix">
            {{rule.data.rule_reference.variable_suffix}}
        </span>
    </div>
</template>

<script>
import Dropdown from "../../Shared/components/Dropdown.vue";
import CustomInlineInput from "../../Shared/components/CustomInlineInput.vue";

export default {
    name: "ConditionOperationValue",
    components: {
        CustomInlineInput,
        Dropdown

    },
    props: {
        operationValue:{
          required: true
        },
        darkMode: {
            type: Boolean,
            default: false
        },
        rule: {
            type: Object,
            required: true
        },
    },
    computed: {
        customEntryOptions(){
            return this.rule.data.rule_reference.condition_option ? this.rule.data.rule_reference.condition_option.items : []
        }
    },
    data(){
        return {
            localReference: null
        }
    },
    created() {
        this.localReference = this.operationValue
    },
    methods: {
        handleChange(val){
            this.$emit('update:operationValue', val)
        }
    }
}

</script>

