<template>
    <div class="flex items-center gap-2 h-9">
        <p v-if="rulesetType === 'ranking' && operationIndex === 0" class="capitalize">When</p>
        <custom-inline-select
            v-if="operationIndex !== 0"
            :options="logicalOperatorOptions"
            :extra-width="1"
            v-model="operation.logical_operator"
        />
        <custom-inline-select
            :options="availableVariables"
            v-model="operation.comparison_field_name"
        />
        <span v-if="rulesetType === 'ranking'">is</span>
        <custom-inline-select
            class="flex justify-center"
            :options="operationOptions"
            v-model="operation.type"
        />
        <div
            v-if="['lessThan', 'lessThanOrEqualTo', 'greaterThan', 'greaterThanOrEqualTo', 'equalTo'].includes(operation.type)"
            class="flex items-center"
        >
            <condition-operation-value
                :rule="rule"
                :operation-value="operation.reference.start"
                @update:operation-value="$event = operation.reference.start = $event"
            />
        </div>
        <div
            v-else-if="operation.type === 'between'"
            class="flex items-center gap-2"
        >
            <condition-operation-value
                :rule="rule"
                :operation-value="operation.reference.start"
                @update:operation-value="$event = operation.reference.start = $event"
            />
            <p>and</p>
            <condition-operation-value
                :rule="rule"
                :operation-value="operation.reference.end"
                @update:operation-value="$event = operation.reference.end = $event"
            />
        </div>
        <!--   FOR EACH   -->
        <div
            v-else-if="operation.type === 'rate'"
            class="flex items-center"
        >
            <custom-inline-input
                v-model="operation.reference.rate"
                type="number"
                :dark-mode="darkMode"
            />
        </div>
        <div
            v-else-if="operation.type === 'in'"
            class="flex items-center"
        >
            <dropdown
                v-model="operation.reference.start"
                :dark-mode="darkMode"
                :options="customEntryOptions"
                :selected="operation.reference.start"
            />
        </div>
        <div v-else>
            Unknown Operator selected.
        </div>
        <div v-if="operationIndex !== 0" class="ml-4">
            <delete-button-confirmation
                :dark-mode="darkMode"
                :small="true"
                @confirm="handleConfirmDelete"
            />
        </div>
    </div>
</template>

<script>
import CustomInput from "../../Shared/components/CustomInput.vue";
import CustomInlineInput from "../../Shared/components/CustomInlineInput.vue";
import CustomInlineSelect from "../../Shared/components/CustomInlineSelect.vue";
import ConfirmDeleteButton from "../../IndustryManagement/WebsiteManagement/ApiKeys/components/ConfirmDeleteButton.vue";
import DeleteButtonConfirmation from "../../Shared/components/DeleteButtonConfirmation.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import ConditionOperationValue from "./ConditionOperationValue.vue";

export default {
    name: "ConditionOperation",
    components: {
        ConditionOperationValue,
        Dropdown,
        DeleteButtonConfirmation,
        ConfirmDeleteButton,
        CustomInlineSelect,
        CustomInlineInput,
        CustomInput,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        operation: {
            type: Object,
            required: true,
        },
        operationIndex: {
            type: Number,
            default: 0,
        },
        availableVariables: {
            type: Array,
            default: [{ value: 'value', name: 'value' }],
        },
        operationsQuantity: {
            type: Number,
            required: true
        },
        rule: {
            type: Object,
            required: true
        },
        rulesetType: {
            type: String,
            required: true
        }
    },
    emits: ['confirmDelete'],
    computed: {
        operationOptions(){
            return this.rule.data.rule_reference.available_operation_types.map(e => ({ value: e.id, name: e.name }))
        },
        customEntryOptions(){
            return this.rule.data.rule_reference.condition_option ? this.rule.data.rule_reference.condition_option.items : []
        }
    },
    data() {
        return {
            operatorOptions: [
                { value: null, name: '-' },
                { value: 'lessThan', name: 'less than' },
                { value: 'lessThanOrEqualTo', name: 'less than or equal' },
                { value: 'greaterThan', name: 'greater than' },
                { value: 'greaterThanOrEqualTo', name: 'greater than or equal' },
                { value: 'between', name: 'between' },
                { value: 'equalTo', name: 'equal to' },
                { value: 'rate', name: 'with each worth' },
                { value: 'in', name: 'in' },
            ],
            logicalOperatorOptions: [
                { value: 'and', name: 'AND' },
                { value: 'or', name: 'OR' },
            ]
        }
    },
    methods: {
        handleConfirmDelete() {
            this.$emit("confirmDelete", this.operationIndex);
        }
    },
}

</script>

