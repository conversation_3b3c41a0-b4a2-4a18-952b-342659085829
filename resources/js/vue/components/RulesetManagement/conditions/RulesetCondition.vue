<template>
    <div class="flex items-start p-2"
         :class="[darkMode ? 'text-slate-300' : 'text-slate-700']"
    >
        <div class="h-9 flex items-center justify-center">
            <delete-button-confirmation
                class="mr-2"
                :small="true"
                :dark-mode="darkMode"
                @confirm="deleteCondition"
            />
        </div>
        <div class="flex-1 flex flex-col items-start justify-start px-2">
            <div class="flex flex-col items-start gap-2">
                <div v-if="rulesetType === 'ranking'" class="flex gap-2 items-center h-9">
                    <p>Assign</p>
                    <custom-inline-input
                        v-model="condition.points"
                    />
                    <p>points</p>
                </div>
                <div class="flex flex-col gap-2">
                    <div
                        v-for="(operation, index) in condition.operations"
                        :key="Math.random()"
                        :class="index > 0 ? 'ml-6' : ''"
                    >
                        <ruleset-operation
                            :ruleset-type="rulesetType"
                            :operationsQuantity="condition.operations.length"
                            :operation="operation"
                            :operation-index="index"
                            :dark-mode="darkMode"
                            :available-variables="availableVariables"
                            :rule="rule"
                            @confirm-delete="deleteOperation"
                        />
                    </div>
                </div>
            </div>
            <div v-if="!hasRateOperation" class="col-span-4 my-2">
                <custom-button
                    @click="addOperation"
                    color="primary-outline"
                    :icon="true"
                    :dark-mode="darkMode"
                >
                    <template v-slot:icon>
                        <svg class="w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                        </svg>
                    </template>
                    <p class="ml-2">Add Operation</p>
                </custom-button>
            </div>
        </div>
    </div>
</template>

<script>
import CustomInput from "../../Shared/components/CustomInput.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInlineInput from "../../Shared/components/CustomInlineInput.vue";
import ConfirmDeleteButton from "../../IndustryManagement/WebsiteManagement/ApiKeys/components/ConfirmDeleteButton.vue";
import RulesetOperation from "./ConditionOperation.vue";
import DeleteButtonConfirmation from "../../Shared/components/DeleteButtonConfirmation.vue";

export default {
    name: "RulesetCondition",
    components: {
        DeleteButtonConfirmation,
        ConfirmDeleteButton,
        CustomInlineInput,
        CustomButton,
        RulesetOperation,
        CustomInput,
    },
    props: {
        rulesetType: {
            type: String,
            required: true,
        },
        darkMode: {
            type: Boolean,
            default: false
        },
        condition: {
            type: Object,
            required: true,
        },
        conditionIndex: {
            type: Number,
            required: true,
        },
        availableVariables: {
            type: Array,
            default: [{ value: 'value', name: 'value' }],
        },
        rule: {
            type: Object,
            required: true,
        },
    },
    emits: ['deleteCondition'],
    computed: {
        hasRateOperation(){
            return this.condition.operations.find(o => o.type === 'rate')
        }
    },
    methods: {
        addOperation() {
            this.condition.operations.push(JSON.parse(JSON.stringify(this.rule.data.rule_data.conditions[this.conditionIndex].operations[0])));
        },
        deleteOperation(operationIndex) {
            if (this.condition.operations.length > 1) {
                this.condition.operations.splice(operationIndex, 1);
            }
        },
        deleteCondition() {
            this.$emit('deleteCondition', this.conditionIndex);
        },
    },
}

</script>



