import axios from 'axios';
import { BaseApiService } from "./base";
import { REQUEST } from "../../../../constants/APIRequestKeys";

class ApiService extends BaseApiService {

    /**
     * @param {string} baseUrl
     * @param {string} baseEndpoint
     * @param {number} version
     */
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    /**
     * Handles setting up configurations for the api service.
     *
     * @returns {AxiosInstance}
     */
    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    /**
     * Fetches a list of defined rulesets from the system.
     *
     * @returns {Promise<AxiosResponse<any>>}
     */
    getRulesets() {
        return this.axios().get('/');
    }

    /**
     * Adds a new ruleset in the system based on the given data.
     *
     * @param {string} rulesetName
     * @param {string} type
     * @param {string} source
     * @param {object} filter
     * @param {string} rulesetName
     * @param {object} payload
     * @returns {Promise<AxiosResponse<any>>}
     **/
    createRuleset(rulesetName, type, source, filter, payload) {
        return this.axios().post('/', {
            [REQUEST.NAME]      : rulesetName,
            [REQUEST.SOURCE]    : source,
            [REQUEST.TYPE]      : type,
            [REQUEST.FILTER]    : filter,
            [REQUEST.RULES]   : payload,
        });
    }

    /**
     * Handles updating an existing ruleset in the system.
     *
     * @param {number} id
     * @param {string} rulesetName
     * @param {string} type
     * @param {string} source
     * @param {object} filter
     * @param {string} rulesetName
     * @param {object} payload
     * @returns {Promise<AxiosResponse<any>>}
     **/
    updateRuleset(id, rulesetName, type, source, filter, payload) {
        return this.axios().patch(`/${id}`, {
            [REQUEST.NAME]      : rulesetName,
            [REQUEST.RULES]   : payload,
            [REQUEST.SOURCE]    : source,
            [REQUEST.TYPE]      : type,
            [REQUEST.FILTER]    : filter,
        });
    }

    /**
     * Removes a ruleset from the system.
     *
     * @param {number} id
     * @returns {Promise<AxiosResponse<any>>}
     **/
    deleteRuleset(id) {
        return this.axios().delete(`/${id}`);
    }

    /**
     * Fetches a base template carrying a set of rules.
     *
     * @returns {Promise<AxiosResponse<any>>}
     */
    getRulesetTemplate() {
        return this.axios().get('/template');
    }

}

export { ApiService }
