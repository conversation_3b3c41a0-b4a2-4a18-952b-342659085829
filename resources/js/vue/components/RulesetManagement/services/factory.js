import { DummyApiService } from "./dummy";
import { ApiService } from "./api"
import { DRIVER } from "../../../../constants/APIRequestKeys";

const BASE_URL      = 'internal-api';
const BASE_ENDPOINT = 'rulesets';
const VERSION       = 1;

class ApiFactory {
    static makeApiService(driver) {
        switch(driver) {
            case DRIVER.API:
                return new ApiService(BASE_URL, BASE_ENDPOINT, VERSION);
            case DRIVER.DUMMY:
            default:
                return new DummyApiService();
        }
    }
}

export { ApiFactory }
