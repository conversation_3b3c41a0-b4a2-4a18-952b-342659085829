import { BaseApiService } from "./base";
import { REQUEST } from "../../../../constants/APIRequestKeys";

class DummyApiService extends BaseApiService {
    constructor(delay = 150) {
        super("DummyApiService");

        this.delay = delay;
    }

    _makeResponse(data) {
        return new Promise((resolve) => {
            setTimeout(() => resolve({data: {data}}), this.delay);
        });
    }

    getRulesets() {
        return this._makeResponse({
            [REQUEST.STATUS]   : true,
            [REQUEST.RULESETS] : [
                { ...this.rulesetTemplate,
                    [REQUEST.ID]          : 1,
                    [REQUEST.INDUSTRY_ID] : 1
                },
             ],
        });
    }

    createRuleset(rulesetName, payload) {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : `Dummy API only, the ruleset '${rulesetName}' not added.`
        });
    }

    updateRuleset(id, rulesetName, payload) {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : `Dummy API only, the ruleset Id '${id}' not updated.`
        });
    }

    deleteRuleset(id) {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : `Dummy API only, the ruleset Id '${id}' not deleted.`
        });
    }

    getRulesetTemplate() {
        return this._makeResponse({
            [REQUEST.STATUS]   : true,
            [REQUEST.TEMPLATE] : this.rulesetTemplate,
        });
    }

    rulesetTemplate = {
        "id": null,
        "name": "Rulestemplate",
        "is_production": false,
        "total_points": 110,
        "industry_id": null,
        "rules": [
            {
                "max_points": 40,
                "rule_type": "campaign_services",
                "is_active": true,
                "rule_reference": {
                    "available_variables": {
                        "value": "Money"
                    }
                },
                "rule_data": {
                    "conditions": [
                        {
                            "points": 40,
                            "operations": [
                                {
                                    "logical_operator": "and",
                                    "type": "between",
                                    "comparison_field_name": "value",
                                    "reference": {
                                        "start": 10,
                                        "end": 50,
                                        "rate": null
                                    }
                                }
                            ]
                        }
                    ]
                }
            },
            {
                "rule_type": "leads",
                "max_points": 20,
                "is_active": true,
                "rule_reference": {
                    "available_variables": {
                        "value": "Leads"
                    }
                },
                "rule_data": {
                    "conditions": [
                        {
                            "points": 10,
                            "operations": [
                                {
                                    "logical_operator": "and",
                                    "type": "lessThan",
                                    "comparison_field_name": "value",
                                    "reference": {
                                        "start": 10000,
                                        "end": null,
                                        "rate": null
                                    }
                                }
                            ]
                        },
                        {
                            "points": 20,
                            "operations": [
                                {
                                    "logical_operator": "and",
                                    "type": "greaterThan",
                                    "comparison_field_name": "value",
                                    "reference": {
                                        "start": 10000,
                                        "end": null,
                                        "rate": null
                                    }
                                }
                            ]
                        }
                    ]
                }
            },
            {
                "rule_type": "locations",
                "max_points": 10,
                "is_active": true,
                "rule_reference": {
                    "available_variables": {
                        "value": "Locations"
                    }
                },
                "rule_data": {
                    "conditions": [
                        {
                            "points": 10,
                            "operations": [
                                {
                                    "type": "rate",
                                    "comparison_field_name": "value",
                                    "reference": {
                                        "rate": 2
                                    }
                                }
                            ]
                        }
                    ]
                }
            },
            {
                "rule_type": "google_reviews",
                "max_points": 10,
                "is_active": true,
                "rule_reference": {
                    "available_variables": {
                        "one_star": "Onestar",
                        "two_stars": "Twostars",
                        "three_stars": "Threestars",
                        "four_stars": "Fourstars",
                        "five_stars": "Fivestars"
                    }
                },
                "rule_data": {
                    "conditions": [
                        {
                            "points": 10,
                            "operations": [
                                {
                                    "logical_operator": "and",
                                    "type": "greaterThan",
                                    "comparison_field_name": "four_stars",
                                    "reference": {
                                        "start": 20
                                    }
                                }
                            ]
                        }
                    ]
                }
            },
            {
                "rule_type": "employees",
                "max_points": 20,
                "is_active": true,
                "rule_reference": {
                    "available_variables": {
                        "value": "Employees"
                    }
                },
                "rule_data": {
                    "conditions": [
                        {
                            "points": 0,
                            "operations": [
                                {
                                    "logical_operator": "and",
                                    "type": "lessThan",
                                    "comparison_field_name": "value",
                                    "reference": {
                                        "start": 10
                                    }
                                }
                            ]
                        },
                        {
                            "points": 5,
                            "operations": [
                                {
                                    "logical_operator": "and",
                                    "type": "between",
                                    "comparison_field_name": "value",
                                    "reference": {
                                        "start": 10,
                                        "end": 20
                                    }
                                }
                            ]
                        },
                        {
                            "points": 10,
                            "operations": [
                                {
                                    "logical_operator": "and",
                                    "type": "between",
                                    "comparison_field_name": "value",
                                    "reference": {
                                        "start": 20,
                                        "end": 50
                                    }
                                }
                            ]
                        },
                        {
                            "points": 20,
                            "operations": [
                                {
                                    "logical_operator": "and",
                                    "type": "between",
                                    "comparison_field_name": "value",
                                    "reference": {
                                        "start": 50,
                                        "end": 200
                                    }
                                }
                            ]
                        },
                        {
                            "points": 10,
                            "operations": [
                                {
                                    "logical_operator": "and",
                                    "type": "greaterThan",
                                    "comparison_field_name": "value",
                                    "reference": {
                                        "start": 100
                                    }
                                }
                            ]
                        }
                    ]
                }
            },
            {
                "rule_type": "adwords",
                "max_points": 10,
                "is_active": true,
                "rule_reference": {
                    "available_variables": {
                        "value": "Adwordscustomer"
                    }
                },
                "rule_data": {
                    "conditions": [
                        {
                            "points": 10,
                            "operations": [
                                {
                                    "logical_operator": "and",
                                    "type": "equalTo",
                                    "comparison_field_name": "value",
                                    "reference": {
                                        "start": 1
                                    }
                                }
                            ]
                        }
                    ]
                }
            }
        ]
    };

}

export { DummyApiService }
