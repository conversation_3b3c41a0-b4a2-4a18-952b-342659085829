<template>
    <div data-accordion="collapse" data-active-classes="bg-blue-100 text-blue-600">
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>

        <h2>
            <div
                class="flex items-center justify-between w-full p-5 font-medium text-left border"
                :class="darkMode ? 'dark-module border-dark-border' : 'text-gray-500 border-gray-200'"
            >
                <span>Company Quality Score</span>
            </div>
        </h2>
        <div :class="[ruleset.type === 'ranking' ? 'visible' : 'hidden']">
            <div class="p-5 border" :class="darkMode ? 'dark-module border-dark-border' : 'text-gray-500 border-gray-200'">
                <loading-spinner v-if="loading" />
                <div v-else>
                    <div class="flex flex-col gap-y-4">
                        <div v-show="!loading">
                            <h5>
                                Test Companies ({{ testCompanies.length }})
                            </h5>
                            <div class="flex items-center gap-x-4 w-full">
                                <autocomplete
                                    class="w-full"
                                    :dark-mode="darkMode"
                                    v-model="selectedCompanyId"
                                    :options="companyOptions"
                                    @search="searchCompany"
                                    @update:modelValue="addCompany"
                                    :placeholder="'Enter Name or Company ID'"
                               />
                            </div>
                        </div>

                    </div>
                    <simple-table class="mt-4" :dark-mode="darkMode" :data="testCompanies" :headers="headers" no-pagination :not-found-message="''">
                        <template v-slot:row.col.id="{ value }">
                            <div class="flex justify-center relative">
                                <button
                                    class="text-red-500 hover:text-red-400 ml-2 absolute left-0"
                                    @click="removeCompany(value)"
                                >
                                    <svg class="w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" >
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </button>
                                <p>{{ value }}</p>
                            </div>
                        </template>
                        <template v-slot:row.col.score="{ value }">
                            <div class="flex justify-center relative">
                                <div v-if="value && value.total_score_in_percentage >= 0" class="flex flex-col items-center">
                                    <p>{{ value.total_score_in_percentage }}%</p>
                                    <small>
                                        {{ value.total_score_in_points }} / {{ value.total_points_available }}
                                    </small>
                                </div>
                                <span v-else>N/A</span>
                            </div>
                        </template>
                    </simple-table>
                    <custom-button class="mt-4" @click="testRuleset" :disabled="loadingTest">
                        <svg aria-hidden="true" v-if="loadingTest" class="w-6 h-6 mr-2 text-gray-200 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
                        Test
                    </custom-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {InformationCircleIcon} from "@heroicons/vue/solid";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import Autocomplete from "../../../Shared/components/Autocomplete.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin";
import SharedApiService from "../../../Shared/services/api";
import {nextTick} from "vue";
import {ApiFactory} from "../../../CompanyQualityScoreManagement/services/factory";
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";

export default {
    name: "CompanyQualityScoreTesting",
    components: {
        AlertsContainer,
        SimpleTable,
        LoadingSpinner,
        Dropdown,
        CustomButton,
        Autocomplete,
        InformationCircleIcon,
    },
    props: {
        darkMode: {
            type: Boolean,
            required: true
        },
        ruleset: {
            type: Object,
            required: true
        }
    },
    mixins: [AlertsMixin],
    emits: ['update:testCompanies'],
    data() {
        return {
            testResults: null,
            api: ApiFactory.makeApiService('api'),
            sharedApi: SharedApiService.make(),
            testCompanies: [],
            companyOptions: [],
            selectedCompanyId: null,
            industryOptions: [],
            loading: false,
            selectedIndustryId: 0,
            loadingTest: false,
            headers: [
                { title: 'Id', field: 'id' },
                { title: 'Name', field: 'name' },
                { title: 'Score', field: 'score' },
            ],
        }
    },
    mounted() {
        this.getIndustryOptions();
    },
    methods: {
        async testRuleset(){
            this.loadingTest = true
            try {
                this.testCompanies.forEach(c => c.score = null)

                const companyIds = this.testCompanies.map(c => c.id)
                const res = await this.api.testRule(this.ruleset, companyIds)

                Object.entries(res.data.data.result).forEach(([companyId, result]) => {
                    const companyIndex = this.testCompanies.findIndex(c => c.id == companyId)
                    if (companyIndex >= 0) {
                        this.testCompanies[companyIndex]['score'] = result.result
                    }
                })
            }catch(err) {
                this.showAlert('error', err.response.data.message);
            }

            this.loadingTest = false
        },
        getIndustryOptions() {
            this.loading = true;
            this.sharedApi.getOdinIndustries().then(resp => {
                if (resp.data?.data?.status) {
                    this.industryOptions = [
                        { id: 0, name: 'Global' },
                        ...resp.data.data.industries
                    ];
                }
                else this.showAlert('error', resp.message || resp.data?.message || "An unknown error occurred.");
            })
                .catch(e => this.showAlert('error', e.response.data.message))
                .finally(() => {
                    this.loading = false;
                    if (this.selectedIndustryId != null) this.getTestCompanies();
                });
        },
        async getTestCompanies() {
            if (this.loading) return;
            this.loading = true;

            this.selectedCompanyId = null

            await nextTick();
            this.api.getTestCompanies(this.selectedIndustryId).then(resp => {
                if (resp.data?.data?.status) {
                    this.testCompanies = resp.data.data.test_companies;
                }
                else this.showAlert('error', resp.message || resp.data?.message || "An unknown error occurred.");
            })
                .catch(e => this.showAlert('error', e.response.data.message))
                .finally(() => this.loading = false);
        },
        searchCompany(query) {
            const additionalQuery = {}
            if (this.selectedIndustryId > 0) {
                Object.assign(additionalQuery, { industry_id: this.selectedIndustryId })
            }

            this.sharedApi.getAdminCompanies(query, additionalQuery).then(resp => this.companyOptions = resp.data.data.companies);
        },
        addCompany(companyId) {
            const { id, name } = this.companyOptions.find(company => company.id === companyId);
            this.testCompanies = [ ...this.testCompanies, { id, name } ];
            this.selectedCompanyId = null
        },
        removeCompany(companyId) {
            const index = this.testCompanies.findIndex(e => e.id === companyId);

            if (index >= 0) this.testCompanies.splice(index, 1)
        },
    }
}
</script>
