<template>
    <div class="p-5"
         :class="[savingRuleset ? 'pointer-events-none grayscale-[50%]' : '']"
    >
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>
        <loading-spinner v-if="loading" />
        <div v-else>
            <div class="grid md:grid-cols-3 gap-x-6">
                <!-- Ruleset list -->
                <div class="border rounded" :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                    <div class="mb-3">
                        <tab :tabs="tabs" tabs-classes="w-full" :dark-mode="darkMode" :show-total="false" @selected="tabSelected" :default-tab-index="selectedTabIndex"></tab>
                    </div>
                    <div v-if="selectedTabName === 'Rulesets'">
                        <div class="grid grid-cols-7 gap-x-3 mb-2 px-5" v-if="!loading">
                            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Name</p>
                            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Type</p>
                            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Last Updated At</p>
                            <p class=""></p>
                        </div>
                        <div v-if="rulesets.length < 1" class="ml-4 py-4">
                            No Rules found.
                        </div>
                        <div v-else
                             v-if="!loading"
                             :class="{'bg-light-background': !darkMode, 'bg-dark-40': darkMode}"
                        >
                            <div v-for="ruleset in rulesets"
                                 :key="ruleset.id"
                                 @click="editRuleset(ruleset.id)"
                                 class="grid grid-cols-7 gap-x-3 border-b  px-5 py-3 items-center cursor-pointer h-16 hover:bg-primary-500 hover:text-white"
                                 :class="[
                                     darkMode ? 'text-slate-100 border-dark-border' : 'border-light-border',
                                     editingRuleset?.id === ruleset?.id ? 'bg-primary-500 text-white' : ''
                                 ]"
                            >
                                <p class="text-sm col-span-2 overflow-x-hidden" :title="ruleset.name">
                                    {{ ruleset.name }}
                                </p>
                                <p class="text-sm col-span-2 overflow-x-hidden" :title="ruleset.name">
                                    {{ $filters.toProperCase(ruleset.type) }}
                                </p>
                                <p class="text-sm col-span-2 whitespace-pre-wrap">
                                    {{ dateFromTimestamp(ruleset.updated_at, 'usWithTime') }}
                                </p>
                                <delete-button-confirmation
                                    class="mr-2"
                                    small
                                    :dark-mode="darkMode"
                                    @confirm="deleteRuleset(ruleset)"
                                    direct
                                />
                            </div>
                        </div>
                    </div>
                    <div v-else-if="'Testing'" class="p-2">
                        <div
                            class="flex items-center p-4 mb-4 text-sm border rounded-lg"
                            :class="[darkMode ? 'border-blue-400 text-blue-400' : 'border-blue-400 bg-blue-50 text-blue-400']"
                        >
                            <svg class="flex-shrink-0 inline w-4 h-4 mr-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                            </svg>
                            <p><strong>This is only a preview</strong>. When you are happy with your ruleset, please save it and go to the integration's page and attach it.</p>
                        </div>

                        <company-quality-score-testing :ruleset="editingRuleset" :dark-mode="darkMode"/>
                        <opportunity-notification-testing class="mt-4" :ruleset="editingRuleset" :dark-mode="darkMode"/>
                    </div>
                </div>
                <!-- Ruleset Editor -->
                <div class="col-span-2 min-h-[20rem] justify-center">
                    <div class="mx-auto mb-5 text-center">
                        <custom-button
                            @click="createNewRuleset"
                        >
                            Create New Ruleset
                        </custom-button>
                    </div>
                    <div v-if="!editingRuleset || loadingRuleset || savingRuleset"
                         class="border rounded-lg p-6"
                         :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}"
                    >
                        <loading-spinner v-if="savingRuleset || loadingRuleset" />
                        <p v-else class="text-center mx-auto p-6">No ruleset selected.</p>
                    </div>
                    <ruleset-editor
                        v-else
                        :api="api"
                        :dark-mode="darkMode"
                        :ruleset="editingRuleset"
                        :us-states="usStates"
                        @ruleset:changes="rulesetHasChanges = true"
                        @ruleset:cancel="cancelRuleset"
                        @ruleset:save="saveRuleset"
                    />
                </div>
            </div>
        </div>
        <!-- Unsaved changes modal -->
        <modal
            v-if="showConfirmUnsavedChanges"
            confirm-text="Abandon Changes"
            close-text="Cancel"
            :small="true"
            :dark-mode="darkMode"
            @confirm="confirmAbandonChanges = true"
            @close="confirmAbandonChanges = false"
        >
            <template v-slot:header>
                <h4 class="text-xl">Unsaved Changes</h4>
            </template>
            <template v-slot:content>
                <p class="p-4">The current ruleset has unsaved changes, are you sure you wish to continue?</p>
            </template>
        </modal>
        <!-- Confirm delete/save modal -->
        <modal
            v-if="showUpdateConfirmModal"
            :confirm-text="deletingRulesetId ? 'Delete Ruleset' : 'Overwrite Ruleset'"
            close-text="Cancel"
            :small="true"
            :dark-mode="darkMode"
            @confirm="confirmUpdate"
            @close="cancelUpdate"
        >
            <template v-slot:header>
                <h4 class="text-xl">{{ deletingRulesetId ? 'Delete' : 'Save' }} Ruleset</h4>
            </template>
            <template v-slot:content>
                <p v-if="deletingRulesetId" class="p-4">
                    Are you sure you wish to delete ruleset "{{ getRulesetName(deletingRulesetId) }}"?
                    {{ affectedConfigs }} Op Notification config{{ affectedConfigs > 1 ? 's' : '' }} and {{ affectedCqs }} CQS rule{{ affectedCqs > 1 ? 's' : '' }} will be affected.
                </p>
                <div v-else>
                    <p class="p-4 whitespace-pre-line">
                        Are you sure you wish to save over ruleset "{{ editingRuleset.name }}"?
                        <br>If you don't wish to overwrite the file you can rename it before saving.
                    </p>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";
import Modal from "../../../Shared/components/Modal.vue";
import { nextTick } from "vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin";
import Tab from "../../../Shared/components/Tab.vue";
import { REQUEST } from "../../../../../constants/APIRequestKeys";
import RulesetEditor from "../RulesetEditor.vue";
import {dateFromTimestamp} from "../../../../../modules/helpers";
import CompanyQualityScoreTesting from './CompanyQualityScoreTesting.vue';
import OpportunityNotificationTesting from "./OpportunityNotificationTesting.vue";
import DeleteButtonConfirmation from "../../../Shared/components/DeleteButtonConfirmation.vue";

export default {
    name: "RulesetListings",
    components: {
        DeleteButtonConfirmation,
        OpportunityNotificationTesting,
        Tab,
        AlertsContainer,
        Modal,
        ActionsHandle,
        CustomButton,
        LoadingSpinner,
        RulesetEditor,
        CompanyQualityScoreTesting
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        api: {
            type: Object,
            required: true,
        },
        usStates: {
            type: Array,
            required: true
        }
    },
    mixins: [AlertsMixin],
    data() {
        return {
            loading: true,
            saving: false,
            loadingRuleset: false,
            savingRuleset: false,
            rulesets: [],
            editingRuleset: null,
            rulesetHasChanges: false,
            showConfirmUnsavedChanges: false,
            confirmAbandonChanges: null,
            removeWatcher: null,
            showUpdateConfirmModal: false,
            deletingRulesetId: null,
            affectedConfigs: null,
            affectedCqs: null,
            savingRulesetId: null,
            tabs: [
                { name: 'Rulesets', current: true }
            ],
            selectedTabName: 'Rulesets',
            selectedTabIndex: 0,
            errorMessage: 'An unknown error occurred.',
        }
    },
    mounted() {
        this.getRulesets();
    },
    methods: {
        dateFromTimestamp,
        getRulesets({keepCurrentEditing = false} = {}) {
            this.loading = true;

            this.api.getRulesets()
                .then(resp => {
                    if (resp.data?.data?.status) {
                        this.rulesets = resp.data?.data?.[REQUEST.RULESETS] ?? [];

                        if (this.rulesets.length > 0 && !keepCurrentEditing) this.editRuleset(this.rulesets[0].id)
                        else if (!keepCurrentEditing) this.editingRuleset = null
                    } else {
                        this.showAlert('error', resp.message || resp.data?.message || this.errorMessage);
                    }
                })
                .catch(e => this.showAlert('error', e.response?.data?.message || this.errorMessage))
                .finally(() => this.loading = false);
        },
        async createNewRuleset() {
            if (this.loadingRuleset || !(await this.abandonUnsavedChanges())) {
                return;
            }

            this.loadingRuleset = true;

            this.api.getRulesetTemplate()
                .then(resp => {
                    if (resp.data?.data?.status) {
                        this.editingRuleset = resp.data?.data?.[REQUEST.TEMPLATE] ?? null;
                        this.clearHasChangesFlag();
                        this.watchForChanges();
                    } else {
                        this.error = resp.message ?? this.errorMessage;
                    }
                })
                .catch(e => this.error = e.response?.data?.message || this.errorMessage)
                .finally(() => this.loadingRuleset = false);
        },
        async editRuleset(rulesetId) {
            this.loadingRuleset = true;

            if (!this.rulesetHasChanges || await this.abandonUnsavedChanges()) {
                const targetRuleset = this.rulesets.find(ruleset => ruleset.id === parseInt(rulesetId));

                if (targetRuleset) {
                    this.editingRuleset = null;
                    await nextTick();
                    this.editingRuleset = JSON.parse(JSON.stringify(targetRuleset));
                    this.calculateTotalPoints();
                    this.clearHasChangesFlag();
                    this.watchForChanges();
                }
            }
            this.loadingRuleset = false;
        },
        // Watch-once for a change in the ruleset open in the editor
        watchForChanges() {
            if (this.removeWatcher) this.removeWatcher();
            this.removeWatcher = this.$watch('editingRuleset', () => {
                this.rulesetHasChanges = true;
                this.removeWatcher();
                this.removeWatcher = null;
            }, { deep: true });
        },
        // Throw a modal requiring User action to abandon unsaved changes
        async abandonUnsavedChanges() {
            if (this.rulesetHasChanges) {
                this.showConfirmUnsavedChanges = true;

                await this.watchCondition(() => this.confirmAbandonChanges !== null, 100, 8000, false);

                const response = this.confirmAbandonChanges;
                this.confirmAbandonChanges = null;
                this.showConfirmUnsavedChanges = false;
                if (response) this.clearHasChangesFlag();

                return response;
            }
            return true;
        },
        saveRuleset() {
            if (!this.editingRuleset[REQUEST.ID]) {
                if(this.savingRuleset || this.loadingRuleset || this.checkRulesetNameInUse()) {
                    return;
                }

                this.savingRuleset = true;
                this.api.createRuleset(
                    this.editingRuleset[REQUEST.NAME],
                    this.editingRuleset[REQUEST.TYPE],
                    this.editingRuleset[REQUEST.SOURCE],
                    this.editingRuleset[REQUEST.FILTER],
                    this.editingRuleset[REQUEST.RULES]
                )
                    .then(async (resp) => {
                        if (resp.data?.data?.status) {
                            await this.getRulesets({keepCurrentEditing: true});
                            this.editingRuleset = resp.data?.data?.[REQUEST.RULESET];
                            this.clearHasChangesFlag();
                            this.showAlert('success', `Successfully saved new ruleset "${this.editingRuleset[REQUEST.NAME]}"`);
                        } else {
                            this.showAlert('error', resp.message || resp.data?.message || this.errorMessage);
                        }
                    })
                    .catch(e => this.showAlert('error', e.response?.data?.message || this.errorMessage))
                    .finally(() => this.savingRuleset = false);
            } else {
                this.savingRulesetId = this.editingRuleset.id;
                this.deletingRulesetId = null;
                this.showUpdateConfirmModal = true;
            }
        },
        confirmOverwriteRuleset() {
            this.savingRuleset = true;
            this.showUpdateConfirmModal = false;

            this.api.updateRuleset(
                this.editingRuleset[REQUEST.ID],
                this.editingRuleset[REQUEST.NAME],
                this.editingRuleset[REQUEST.TYPE],
                this.editingRuleset[REQUEST.SOURCE],
                this.editingRuleset[REQUEST.FILTER],
                this.editingRuleset[REQUEST.RULES]
            )
                .then(async (resp) => {
                    if (resp.data?.data?.status) {
                        await this.getRulesets({keepCurrentEditing: true});
                        this.showAlert('success', `Successfully updated ruleset "${this.editingRuleset[REQUEST.NAME]}"`);
                        this.clearHasChangesFlag();
                    } else {
                        this.showAlert('error', resp.message || resp.data?.message || this.errorMessage);
                    }
                })
                .catch(e => this.showAlert('error', e.response?.data?.message || e.response?.message || this.errorMessage))
                .finally(() => {
                    this.savingRuleset = false;
                    this.savingRulesetId = null;
                    this.showUpdateConfirmModal = false;
                });
        },
        getRulesetName(rulesetId) {
            return this.rulesets.find(ruleset => ruleset.id === rulesetId)?.name || 'Unknown Ruleset';
        },
        confirmDelete() {
            if (this.savingRuleset) {
                return;
            }

            this.savingRuleset = true;

            this.api.deleteRuleset(this.deletingRulesetId)
                .then(resp => {
                    if (resp.data?.data?.status) {
                        this.getRulesets();
                    } else {
                        this.showAlert('error', resp.message || resp.data?.message || this.errorMessage);
                    }
                })
                .catch(e => this.showAlert('error', e.response?.data?.message || this.errorMessage))
                .finally(() => {
                    this.savingRuleset = false;
                    this.deletingRulesetId = null;
                    this.showUpdateConfirmModal = false;
                });
        },
        confirmUpdate() {
            if (this.deletingRulesetId) {
                this.confirmDelete();
            } else {
                this.confirmOverwriteRuleset();
            }
        },
        cancelUpdate() {
            this.deletingRulesetId = null;
            this.savingRulesetId = null;
            this.showUpdateConfirmModal = false;
        },
        async cancelRuleset() {
            if (await this.abandonUnsavedChanges()) {
                this.clearHasChangesFlag();
                this.editingRuleset = null;
            }
        },
        deleteRuleset(ruleset) {
            if (this.loading || this.showUpdateConfirmModal) {
                return;
            }

            this.deletingRulesetId = ruleset.id;
            this.affectedCqs = ruleset.cqs_count;
            this.affectedConfigs = ruleset.op_notification_config_count;
            this.savingRulesetId = null;
            this.showUpdateConfirmModal = true;
        },
        clearHasChangesFlag() {
            if (this.removeWatcher) {
                this.removeWatcher();
                this.removeWatcher = null;
            }
            this.rulesetHasChanges = false;
        },
        async watchCondition(condition, interval, timeout, timeoutValue) {
            return new Promise(res => {
                let time = 0;
                const watcher = setInterval(() => {

                    if (time >= timeout) {
                        clearInterval(watcher);
                        res(timeoutValue);
                    }

                    const check = condition();
                    if (condition()) {
                        clearInterval(watcher);
                        res(check);
                    }

                    time += interval;
                }, interval);
            });
        },
        tabSelected(tabName) {
            this.selectedTabName = tabName;
        },
        calculateTotalPoints() {
            this.editingRuleset.total_points = this.editingRuleset.rules.reduce((output, rule) => rule.is_active ? output + parseInt(rule.max_points) : output, 0);
        },
        checkRulesetNameInUse() {
            if (this.rulesets.find(ruleset => ruleset.name.trim().toLowerCase() === this.editingRuleset.name.trim().toLowerCase() && ruleset.id !== this.editingRuleset.id)) {
                this.showAlert('error', 'The name is already in use, please rename the current Ruleset before saving.');
                return true;
            }
            return false;
        },
    },
    watch: {
        editingRuleset(newVal) {
            if (newVal && !this.tabs.find(t => t.name === 'Testing')) {
                this.tabs.push({ name: 'Testing', current: false })
            }
        }
    },
}

</script>
