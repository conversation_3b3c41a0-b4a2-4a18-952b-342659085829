<template>
    <div>
        <div class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 mb-3">
            <div class="col-span-2 flex flex-col">
                <label class="text-sm font-medium">
                    Source
                </label>
                <Dropdown
                    v-model="localSource"
                    :dark-mode="darkMode"
                    placeholder="Data source"
                    :options="sourceFilterOptions"
                />
            </div>
        </div>
        <component v-if="rulesetEntitySourceFilterComponent" :dark-mode="darkMode" :is="rulesetEntitySourceFilterComponent" v-model="localFilter" :us-states="usStates"/>
    </div>
</template>

<script>
import {defineComponent} from "vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CompanyFilterOptions from "./CompanyFilterOptions.vue";

export default defineComponent({
    components: { Dropdown, CompanyFilterOptions },
    props: {
        darkMode: {
            type: Boolean,
            required: true
        },

        source: {
            type: String,
            required: true
        },

        filter: {
            type: Object,
            required: true
        },

        usStates: {
            type: Array,
            required: true
        }
    },
    data(){
        return {
            sourceFilterOptions: [
                {
                    id: "companies",
                    name: "Companies"
                },
            ],
            localSource: 'companies',
            localFilter: {},
        }
    },

    mounted() {
        this.localSource = this.source ? this.source : this.localSource
        this.localFilter = this.filter ? this.filter : this.localFilter
    },

    watch: {
        localSource: {
            handler() {
                this.$emit('update:source', this.localSource);
            },
        },

        localFilter: {
            handler() {
                this.$emit('update:filter', this.localFilter);
            },
            deep: true
        },
    },

    computed: {
        rulesetEntitySourceFilterComponent(){
            switch (this.localSource) {
                case 'companies':
                    return 'CompanyFilterOptions'
                default:
                    return null
            }
        }
    },
})
</script>
