<template>
    <div class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
        <div class="col-span-2 flex flex-col" v-if="industryOptions.length > 0">
            <label class="font-medium text-sm">
                Industry
            </label>
            <multi-select
                :options="industryOptions"
                :dark-mode="darkMode"
                text-place-holder="Select Industries"
                :show-search-box="false"
                @input="handleIndustryInput"
                :selected-ids="modelValue.industries"
            />
        </div>
        <div class="col-span-2 flex flex-col">
            <label class="text-sm">
                Industry Service
            </label>
            <div :class="industryServicesOptions <= 0 ? 'opacity-50 grayscale pointer-events-none' : ''">
                <multi-select
                    :options="industryServicesOptions"
                    :dark-mode="darkMode"
                    text-place-holder="Select an Industry First"
                    :show-search-box="false"
                    @input="(v) => handleInput(v, 'industry_services')"
                    :selected-ids="modelValue.industry_services"
                />
            </div>
            <label v-if="loadingIndustryServices" class="text-xs mt-1">
                Loading industry services...
            </label>
        </div>
        <div class="col-span-2 flex flex-col" v-if="statusOptions.length > 0">
            <label class="text-sm">
                Status
            </label>
            <multi-select
                :options="statusOptions"
                :dark-mode="darkMode"
                text-place-holder="Select Statuses"
                :show-search-box="false"
                @input="(v) => handleInput(v, 'statuses')"
                :selected-ids="modelValue.statuses"
            />
        </div>
        <div class="col-span-2 flex flex-col">
            <label class="font-medium text-sm">
                Sale Status
            </label>
            <multi-select
                :options="saleStatusOptions"
                :dark-mode="darkMode"
                text-place-holder="Select Sale Status"
                :show-search-box="false"
                @input="(v) => handleInput(v, 'sale_status')"
                :selected-ids="modelValue.sale_status"
            />
        </div>
        <div class="col-span-2 flex flex-col">
            <label class="text-sm">
                State
            </label>
            <multi-select
                :options="usStates"
                :dark-mode="darkMode"
                text-place-holder="Select Locations"
                :show-search-box="false"
                @input="(v) => handleStateInput(v, 'locations')"
                :selected-ids="modelValue.locations"
            />
        </div>
        <div class="col-span-2 flex flex-col">
            <label class="text-sm">
                County
            </label>
            <div :class="counties.length <= 0 ? 'opacity-50 grayscale pointer-events-none' : ''">
                <multi-select
                    :options="counties"
                    :dark-mode="darkMode"
                    text-place-holder="Select a State First"
                    :show-search-box="false"
                    @input="(v) => handleInput(v, 'counties')"
                    :selected-ids="modelValue.counties"
                />
            </div>
            <label v-if="loadingCounties" class="text-xs mt-1">
                Loading counties...
            </label>
        </div>
        <div class="col-span-2 flex flex-col" v-if="industryOptions.length > 0">
            <label class="font-medium text-sm">
                Payment method
            </label>
            <multi-select
                :options="paymentMethodsOptions"
                :dark-mode="darkMode"
                text-place-holder="Select payment methods"
                :show-search-box="false"
                @input="(v) => handleInput(v, 'payment_methods')"
                :selected-ids="modelValue.payment_methods"
            />
        </div>
    </div>
</template>

<script>
import {defineComponent} from "vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";
import SharedApiService from "../../../Shared/services/api";

export default defineComponent({
    components: {MultiSelect, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            required: true
        },

        modelValue: {
            type: Object,
            required: true
        },

        usStates: {
            type: Array,
            required: true
        }
    },
    data() {
        return {
            loadingIndustryServices: false,
            loadingCounties: false,
            sharedApi: SharedApiService.make(),
            industryOptions: [],
            industryServicesOptions: [],
            statusOptions: [],
            counties: [],
            paymentMethodsOptions:  [
                {id: 'no_payment', name: "No payment method"},
                {id: 'pg', name: "Payment Gateway"},
                {id: 'wt', name: "Wire Transfer"},
                {id: 'arb', name: "Auto Recurring Billing"},
            ],
            saleStatusOptions: []
        }
    },
    watch: {
        'modelValue.locations': {
            handler(newValue) {
                this.populateCountyOptions(newValue)
            },
        },
        'modelValue.industries': {
            handler(newValue) {
                this.populateServicesUnderIndustries(newValue)
            },
        },
    },
    mounted() {
        this.sharedApi.getOdinIndustries().then(resp => {
            if (resp.data?.data?.status) this.industryOptions = resp.data.data.industries.map(i => ({
                id: i.id,
                name: i.name
            }));
        });

        this.sharedApi.getCompanyStatuses().then(resp => {
            if (resp.data?.data?.status) this.statusOptions = resp.data.data.statuses.map(status => ({
                id: status.value,
                name: status.label,
            })).filter(status => (status.name !== 'Unknown'));
        });

        this.sharedApi.getSaleStatusTypes().then(resp => {
            if(resp?.data?.data?.status === true) {
                this.saleStatusOptions = resp?.data?.data?.statuses_as_select_array ?? [];
            } else {
                console.error(resp?.data?.data?.message || this.errorMessage)
            }
        }).catch(e => console.error("Get Sales Status Options Error: ", e));

        if (this.modelValue.locations && this.modelValue.locations.length > 0) {
            this.populateCountyOptions(this.modelValue.locations);
        }

    },
    methods: {
        makeOptionsUniqueAndSort(options){
            return [...new Map(options.map(option => [option.id, option])).values()].sort((a, b) => a.name.localeCompare(b.name))
        },
        async populateServicesUnderIndustries(industryIds){
            this.industryServicesOptions = []
            if (industryIds.length === 0) {
                this.$emit('update:modelValue', {...this.modelValue, industry_services: []})
            }
            else await Promise.all(industryIds.map(this.populateIndustryServiceOptions))
        },
        async populateIndustryServiceOptions(industryId){
            this.loadingIndustryServices = true
            try {
                const industryServicesResponse = await this.sharedApi.getIndustryServices({industryId})
                if (industryServicesResponse.data.data.status) {
                    const options = [
                        ...this.industryServicesOptions,
                        ...industryServicesResponse.data.data.industry_services.map(is => ({name: `${is.name} - ${is.industry}`, id: is.num_id}))
                    ]

                    this.industryServicesOptions = this.makeOptionsUniqueAndSort(options)
                }
            }catch (err) {
                console.error(err)
            }
            this.loadingIndustryServices = false
        },
        async handleIndustryInput(industryIds){
            // TODO - Only query new ids
            this.handleInput(industryIds, 'industries')
            await this.populateServicesUnderIndustries(industryIds)
        },
        handleInput(e, field){
            this.$emit('update:modelValue', {...this.modelValue, [field]: e})
        },
        handleStateInput(e, field){
            this.handleInput(e, field)
            this.populateCountyOptions(e)
        },
        populateCountyOptions(states) {
            if (this.handleNoStatesSelected(states)) return;

            this.loadingCounties = true

            this.sharedApi.getCountiesByStates(states).then(resp => {
                if (resp.data?.data?.status) {
                    if (this.handleNoStatesSelected(states)) return;

                    const options = resp.data.data.counties.map(county => ({
                        id: county.id,
                        name: `${county.name} - ${county.state_abbr}`,
                    })).filter(county => (county.id !== 'Unknown'));

                    this.counties = this.makeOptionsUniqueAndSort(options)
                }

                this.loadingCounties = false
            })
        },
        handleNoStatesSelected(states) {
            if (!states.length) {
                this.counties = [];
                this.handleInput([], 'counties')
                return true;
            }
        }
    },
})
</script>
