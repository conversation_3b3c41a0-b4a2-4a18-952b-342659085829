<template>
    <div class="w-full flex-auto pt-3 relative" :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
        <div class="" :class="[darkMode ? 'text-white' : 'text-slate-900']">
            <div class="border rounded-lg" :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                <div>
                    <ruleset-listings
                        :dark-mode="darkMode"
                        :api="api"
                        :us-states="usStates"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import { ApiFactory } from "./services/factory";
import RulesetListings from "./components/RulesetListings/RulesetListings.vue";
import { DRIVER } from "../../../constants/APIRequestKeys";

export default {
    name: "RulesetManagement",
    components: {
        RulesetListings,
    },
    props: {
        darkMode: {
            type: <PERSON>olean,
            required: true
        },
        usStates: {
            type: Array,
            required: true
        }
    },
    data() {
        return {
            api: ApiFactory.makeApiService(DRIVER.API),
        }
    },
}

</script>

