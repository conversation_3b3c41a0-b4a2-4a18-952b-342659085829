<template>
    <div>
        <alerts-container v-if="store.alertActive" :dark-mode="darkMode" :alert-type="store.alertType" :text="store.alertText" />
        <div class="inline-flex mx-5 my-3 items-center cursor-pointer text-primary-500 font-semibold" @click="$emit('back')">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" height="15" width="15" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="m18.75 4.5-7.5 7.5 7.5 7.5m-6-15L5.25 12l7.5 7.5" />
            </svg>
            <p class="text-sm">Back</p>
        </div>
        <templates-list
            v-if="store.view === 'list'"
            :dark-mode="darkMode"
        />
        <templates-editor
            v-if="store.view === 'editor'"
            :dark-mode="darkMode"
        />
    </div>
</template>

<script>
    import TemplatesEditor from "./components/TemplatesEditor.vue";
    import TemplatesList from "./components/TemplatesList.vue";
    import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
    import AlertsContainer from "../Shared/components/AlertsContainer.vue";
    import {useEmailTemplatesStore} from "./store/store";

    export default {
        name: "EmailTemplates",
        props: {
            darkMode: {
                type: Boolean,
                default: false
            }
        },
        components: {
            LoadingSpinner,
            AlertsContainer,
            TemplatesEditor,
            TemplatesList
        },
        setup: function() {
            const store = useEmailTemplatesStore();

            return {
                store
            };
        },
        created: function() {

        },
        data: function() {
            return {

            };
        },
        emits: ['back'],
        methods: {

        }
    }
</script>

<style scoped>

</style>
