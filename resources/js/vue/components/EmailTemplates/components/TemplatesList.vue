<template>
    <div class="border rounded-lg h-auto"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
             class="flex flex-row mb-8 border-b">
            <div
                :class="store.templateTypeTab === 'content' ? 'text-blue-550 border-primary-500' : 'text-slate-400 border-transparent'"
                class="uppercase border-b-2 px-6 pt-5 text-sm cursor-pointer pb-3 font-semibold"
                @click="switchTab('content')">
                Content
            </div>
            <div
                :class="store.templateTypeTab === 'background' ? 'text-blue-550 border-primary-500' : 'text-slate-400 border-transparent'"
                class="uppercase border-b-2 px-6 pt-5 text-sm cursor-pointer pb-3 font-semibold"
                @click="switchTab('background')">
                Header & Footer
            </div>
        </div>
        <div class="grid grid-cols-8 px-5">
            <h5 class="col-start-1 col-end-2 text-blue-550 text-sm uppercase font-semibold leading-tight">Email Templates </h5>
            <button class="col-start-8 col-end-9 transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                    @click="toggleNewTemplateModal(true)">
                Create New Template
            </button>
        </div>
        <div v-if="store.templateTypeTab === 'content'">
            <div class="flex items-center flex-wrap gap-3 px-5">
                <CustomInput
                    :dark-mode="darkMode"
                    class="w-full max-w-sm"
                    search-icon placeholder="Search by template name"
                    v-model="store.searchInputGeneral"
                    @keyup.enter="submitSearch"
                />
                <CustomButton type="submit" :dark-mode="darkMode"
                              @click="submitSearch"
                >
                    Search
                </CustomButton>
                <CustomButton :dark-mode="darkMode" color="slate-inverse" type="reset"
                              @click="store.resetFilters"
                >
                    Reset
                </CustomButton>
                <Filterable
                    :dark-mode="darkMode"
                    :filters="store.filters"
                    v-model="store.filterInputs"/>
            </div>
            <FilterableActivePills
                class="px-5 mb-6 mt-5"
                v-if="store.filters?.length"
                :filters="store.filters"
                :active-filters="store.filterInputs"
                :dark-mode="darkMode"
                @reset-filter="store.clearFilter"
            />
        </div>
        <div>
            <div class="grid gap-x-3 mb-2 px-5"
                 :class="store.templateTypeTab === 'content' ? 'grid-cols-10' : 'grid-cols-9'"
            >
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">
                    Name</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">
                    Industry</p>
                <p v-if="store.templateTypeTab === 'content'" class="text-slate-400 font-medium tracking-wide uppercase text-xs">
                    Type</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">
                    Creator</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Last
                    Updated</p>
            </div>
            <div :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}"
                 class="border-t border-b h-auto overflow-y-auto">
                <div v-if="store?.templates?.data?.length && store.templates.data.length > 0">
                    <div v-for="template in store.templates.data"
                         :key="template.id"
                         :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode,
                                             'text-slate-100 hover:bg-dark-module border-dark-border': darkMode,
                                             'grid-cols-10' : store.templateTypeTab === 'content', 'grid-cols-9' : store.templateTypeTab === 'background'}"
                         class="grid gap-x-3 border-b px-5 py-3 items-center"
                    >
                        <p class="text-sm col-span-2 truncate">
                            {{ template.name }}
                        </p>
                        <p class="text-sm col-span-2 truncate">
                            {{ template.industry }}
                        </p>
                        <p v-if="store.templateTypeTab === 'content'" class="text-sm truncate">
                            {{ template.type_name }}
                        </p>
                        <p class="text-sm col-span-2 truncate">
                            {{ template.owner_name }}
                        </p>
                        <p class="text-sm col-span-2 truncate">
                            {{ template.last_updated }}
                        </p>
                        <div v-if="store.editTemplateLoading && store.templateId === template.id">
                            <loading-spinner :size="'w-4 h-4'"/>
                        </div>
                        <ActionsHandle :dark-mode="darkMode" :noDeleteButton="!canDeleteEmailTemplate" @delete="toggleDeleteModal(true, template.id)"
                                       @edit="store.editTemplate(template.id)"/>
                    </div>
                </div>
                <div v-else class="text-center text-2xl">
                    No templates
                </div>
            </div>
            <div class="p-3">
                <pagination
                    :dark-mode="darkMode"
                    :pagination-data="store.templates"
                    :show-pagination="store.templates?.last_page > 1"
                    class="mt-5"
                    @change-page="loadNextPage($event)"
                />
            </div>
        </div>
    </div>
    <div>
        <modal v-if="showDeleteModal"
               :close-text="'No'"
               :confirm-text="'Yes'"
               :dark-mode="darkMode"
               :no-buttons="deleting"
               :small="true"
               @close="toggleDeleteModal(false)"
               @confirm="deleteTemplate">
            <template v-if="!deleting" v-slot:header>
                <span class="font-bold">Confirm Deletion</span>
            </template>
            <template v-slot:content>
                <div v-if="deleting" class="text-center">Deleting...</div>
                <div v-else class="text-red-500">Are you sure you want to delete this template?</div>
            </template>
        </modal>

        <modal v-if="showNewTemplateModal"
               :close-text="'Cancel'"
               :confirm-text="'Confirm'"
               :dark-mode="darkMode"
               :small="true"
               @close="toggleNewTemplateModal(false)"
               @confirm="toggleNewTemplateModal(false); store.changeTemplateTypeTab(store.templateType); store.editTemplate();">
            <template v-slot:header>
                <span class="font-bold">Choose Template Type</span>
            </template>
            <template v-slot:content>
                <p class="font-bold">Template Type</p>
                <select v-model="store.templateType" :class="{'hover:bg-grey-120 border-grey-200 text-grey-400 bg-grey-50': !darkMode, 'hover:border-blue-400 border-blue-700 text-blue-400 bg-dark-background': darkMode}"
                        class="w-full p-2 rounded my-1">
                    <option value="content">Content</option>
                    <option value="background">Header & Footer</option>
                </select>
            </template>
        </modal>
    </div>
</template>

<script>
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Pagination from "../../Shared/components/Pagination.vue";
import Modal from "../../Shared/components/Modal.vue";
import {useEmailTemplatesStore} from "../store/store";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store";
import CustomInput from "../../Shared/components/CustomInput.vue";
import MultiSelect from "../../Shared/components/MultiSelect.vue";
import Filterable from "../../Shared/components/Filterables/Filterable.vue";
import FilterableActivePills from "../../Shared/components/Filterables/FilterableActivePills.vue";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import CompanyCampaignStatusBadge from "../../Shared/components/CompanyCampaign/CompanyCampaignStatusBadge.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import AlertsContainer from "../../LeadProcessing/components/AlertsContainer.vue";
import Dropdown from "../../LeadProcessing/components/Dropdown.vue";

export default {
    name: "TemplatesList",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    components: {
        Dropdown,
        AlertsContainer,
        CustomButton,
        CompanyCampaignStatusBadge,
        Autocomplete,
        FilterableActivePills,
        Filterable,
        MultiSelect,
        CustomInput,
        ToggleSwitch,
        ActionsHandle,
        LoadingSpinner,
        Pagination,
        Modal
    },
    setup: function () {
        const store = useEmailTemplatesStore();
        return {
            store
        };
    },
    created: function () {
        this.loadTemplatesList(this.store.templateTypeTab);

        this.store.getFilterOptions();
        this.store.getEngineOptions();
    },
    data: function () {
        return {
            loading: false,
            showDeleteModal: false,
            deleteTemplateId: 0,
            deleting: false,
            showNewTemplateModal: false,
            showPersonalEmailTemplates: false,
            userCanDeleteEmailTemplate: false,
            permissionStore: useRolesPermissions(),
        };
    },
    computed: {
        canDeleteEmailTemplate() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_EMAIL_TEMPLATE_DELETE);
        }
    },
    methods: {
        loadTemplatesList(templateType) {
            this.loading = true;

            this.store.getEmailTemplates(templateType).finally(() => {
                this.loading = false;
            });
        },
        toggleDeleteModal(show, templateId = 0) {
            this.deleteTemplateId = show ? templateId : 0;
            this.showDeleteModal = show;
        },
        toggleNewTemplateModal(show) {
            this.store.reset();
            this.showNewTemplateModal = show;
        },
        deleteTemplate() {
            this.deleting = true;

            this.store.deleteTemplate(this.deleteTemplateId).then(() => {
                this.loadTemplatesList(this.store.templateTypeTab);
            }).finally(() => {
                this.toggleDeleteModal(false);
                this.deleting = false;
            });
        },
        switchTab(templateType) {
            this.store.filterInputs = [];
            localStorage.setItem('previousTemplateScope', this.showPersonalEmailTemplates ? 'personal' : '');
            this.store.templates.current_page = 1;
            this.store.changeTemplateTypeTab(templateType);
            const scope = this.showPersonalEmailTemplates ? 'personal' : '';
            this.loadTemplatesList(templateType, scope);
        },
        loadNextPage(event) {
            this.store.templates.current_page = event.newPage ?? 1
            this.loadTemplatesList(this.store.templateTypeTab);
        },
        submitSearch() {
            this.loading = true;

            this.store.submitSearch().finally(() => {
                this.loading = false;
            });
        }
    },
}
</script>
