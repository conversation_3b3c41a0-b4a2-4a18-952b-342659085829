import {defineStore} from 'pinia';
import Api from "../API/api";
import axios from "axios";
import { replaceShortcodes } from "../../Shared/services/markdownImageTags";
import { REQUEST } from "../../../../constants/APIRequestKeys";
import _, {template} from "lodash";
import {nextTick} from "vue";
import {stripSpecialChars} from "../../../../modules/helpers.js";
import shortcodesPlugin from "../../Shared/includes/shortcodesPlugin.js";
export const useEmailTemplatesStore = defineStore('email_templates', {
    state: () => ({
        api: Api.make(),

        templateId: 0,
        templateName: '',
        templateSubject: '',
        templateContent: '',
        templateHeader: '',
        templateFooter: '',
        templateContentImages: {},
        templateHeaderImages: {},
        templateFooterImages: {},
        personal: true,
        templateIndustry: '',
        templateType: 'content',
        templateTypeOption: 0,
        templateBackground: 0,
        templateOwner: '',
        isTemplateOwner: false,
        templateLastUpdated: '',
        duplicateTemplateName: '',
        templateEngineOptions: [],
        templateEngineOption: null,
        templateActive: false,
        searchInputGeneral: '',
        templateAttachments: [],

        templateHTML: '',

        templateTypeTab: 'content',
        view: 'list',

        templates: {},
        templateBackgrounds: {},

        alertActive: false,
        alertType: '',
        alertText: '',
        templateOptions: {},
        availableTemplateOptions: {},

        editTemplateLoading: false,

        showDuplicateTemplateModal: false,
        creatingTemplateDuplicate: false,

        filters: [],
        filterInputs: {},

        editorPlugins: [],
        shortcodes: [],
    }),
    getters: {

    },
    actions: {
        changedTemplateIndustry(newIndustry) {
            this.templateIndustry = newIndustry;
            this.getTemplateBackgrounds();
        },
        changedTemplateType(newType) {
            this.templateTypeOption = newType;
        },
        changesEngineOption(newEngine) {
            this.templateEngineOption = newEngine;
        },
        showAlert(type, message) {
            this.alertType = type;
            this.alertText = message;
            this.alertActive = true;

            setTimeout( () => {
                this.alertActive = false;
                this.alertText = '';
                this.alertType = '';
            }, 5000);
        },
        reset() {
            this.templateId = 0;
            this.templateContent = '';
            this.templateHeader = '';
            this.templateFooter = '';
            this.templateContentImages = {};
            this.templateHeaderImages = {};
            this.templateFooterImages = {};
            this.templateName = '';
            this.templateSubject = '';
            this.personal = true;
            this.templateIndustry = '';
            this.templateTypeOption = 0;
            this.templateOwner = '';
            this.templateLastUpdated = '';
            this.duplicateTemplateName = '';
            this.templateEngineOption = null;
            this.templateActive = false;
            this.togglePreview(false);
            this.searchInputGeneral = '';
            this.templateAttachments = []
            this.templateOptions = {}
        },
        changeTemplateTypeTab(templateTypeTab) {
            this.templateTypeTab = templateTypeTab;
        },
        showTemplatesList() {
            this.reset();
            this.getEmailTemplates(this.templateTypeTab);
            this.view = 'list';
        },
        togglePreview(show, html = '') {
            this.templateHTML = show ? html : '';
        },
        editTemplate(templateId = null) {
            this.reset();

            if(this.templateTypeTab === 'content') {
                this.editContentTemplate(templateId);
            }
            else if(this.templateTypeTab === 'background') {
                this.editBackgroundTemplate(templateId);
            }
        },
        editContentTemplate(templateId) {
            this.templateType = 'content';

            if(templateId) {
                this.templateId = templateId;
                this.templateName = this.templates[templateId]?.name;

                this.editTemplateLoading = true;

                return this.api.getEmailTemplate(templateId).then(res => {
                    if(res.data.data.status === true) {
                        const template = res.data.data.template;

                        this.templateId = templateId;
                        this.templateName = template[REQUEST.NAME];
                        this.templateSubject = template[REQUEST.SUBJECT];
                        this.templateContentImages = template[REQUEST.CONTENT_IMAGES];
                        this.templateContent = replaceShortcodes(template[REQUEST.CONTENT], this.templateContentImages);
                        this.personal = !!template[REQUEST.PERSONAL];
                        this.templateIndustry = template[REQUEST.INDUSTRY_ID];
                        this.templateTypeOption = template[REQUEST.TYPE];
                        this.templateBackground = template[REQUEST.BACKGROUND_ID];
                        this.isTemplateOwner = template[REQUEST.IS_OWNER];
                        this.templateOwner = template[REQUEST.OWNER_NAME];
                        this.templateLastUpdated = template[REQUEST.LAST_UPDATED];
                        this.templateEngineOption = template[REQUEST.ENGINE];
                        this.templateActive = !!template[REQUEST.ACTIVE];
                        this.templateOptions = template[REQUEST.PAYLOAD]
                        this.togglePreview(false);

                        this.getTemplateBackgrounds();
                        this.getShortcodes().then(() => {
                            this.getTemplateOptions()
                        });

                        this.view = 'editor';
                    }
                    else {
                        this.showAlert('error', 'Failed to load template');
                    }
                }).catch(err => {
                    this.showAlert('error', 'Error loading template');
                }).finally(() => {
                    this.editTemplateLoading = false;
                })
            }
            else {
                this.getTemplateBackgrounds();
                this.view = 'editor';
            }
        },
        editBackgroundTemplate(templateId) {
            this.templateType = 'background';

            if(templateId) {
                this.templateId = templateId;
                this.templateName = this.templates[templateId]?.name;

                this.editTemplateLoading = true;

                return this.api.getEmailTemplateBackground(templateId).then(res => {
                    if(res.data.data.status === true) {
                        const template = res.data.data.template;

                        this.templateId = templateId;
                        this.templateName = template[REQUEST.NAME];
                        this.templateHeaderImages = template[REQUEST.HEADER_IMAGES];
                        this.templateFooterImages = template[REQUEST.FOOTER_IMAGES];
                        this.templateHeader = replaceShortcodes(template[REQUEST.HEADER], this.templateHeaderImages);
                        this.templateFooter = replaceShortcodes(template[REQUEST.FOOTER], this.templateFooterImages);
                        this.personal = !!template[REQUEST.PERSONAL];
                        this.templateIndustry = template[REQUEST.INDUSTRY_ID];
                        this.isTemplateOwner = template[REQUEST.IS_OWNER];
                        this.templateOwner = template[REQUEST.OWNER_NAME];
                        this.templateLastUpdated = template[REQUEST.LAST_UPDATED];
                        this.togglePreview(false);

                        this.view = 'editor';
                    }
                    else {
                        this.showAlert('error', 'Failed to load template');
                    }
                }).catch(err => {
                    this.showAlert('error', 'Error loading template');
                }).finally(() => {
                    this.editTemplateLoading = false;
                })
            }
            else {
                this.view = 'editor';
            }
        },

        /**
         * Prepares to suggest (auto-generate) a new name for the duplicate template.
         */
        autoGenerateDuplicateTemplateName() {
            let nameList = this.templateName.split(' ');
            let postfix = '';
            let defaultInitial = 2;

            if(nameList.length > 0) {
                let lastIndex = nameList[nameList.length - 1];

                postfix = _.isFinite(Number(lastIndex)) === true
                    ? Number(lastIndex) + 1
                    : defaultInitial;

                this.duplicateTemplateName = _.isFinite(Number(lastIndex)) === true
                    ? this.templateName.replace(` ${lastIndex}`, ` ${postfix}`)
                    : this.templateName + ' ' + defaultInitial;
            }
            else {
                this.duplicateTemplateName = this.templateName + ' ' + defaultInitial;
            }
        },

        /**
         * Validates the template type and triggers content/background duplication accordingly.
         *
         * @param {number}  templateId
         * @param {boolean} loadNewTemplate
         */
        duplicateTemplate(templateId, loadNewTemplate = false) {
            if(!templateId) {
                return;
            }

            if(this.templateTypeTab === 'content') {
                this.duplicateContentTemplate(templateId, loadNewTemplate)
                    .then();
            }
            else if(this.templateTypeTab === 'background') {
                this.duplicateBackgroundTemplate(templateId, loadNewTemplate)
                    .then();
            }
        },

        /**
         * Handles creating a content template from the given template ID.
         *
         * @param {number}  templateId
         * @param {boolean} loadNewTemplate
         * @return {Promise<unknown>}
         */
        duplicateContentTemplate(templateId, loadNewTemplate = false) {
            this.templateType = 'content';

            if(templateId) {
                this.editTemplateLoading = true;
                this.creatingTemplateDuplicate = true;

                return this.api.duplicateEmailTemplate(
                    this.duplicateTemplateName,
                    templateId
                ).then(response => {
                    if(response.data.data.status === true) {
                        this.showAlert('success', 'Duplicate template created.');

                        if(loadNewTemplate === true) {
                            const template = response.data.data.template;

                            this.templateId = template[REQUEST.ID];
                            this.templateName = template[REQUEST.NAME];
                            this.templateSubject = template[REQUEST.SUBJECT];
                            this.templateContentImages = template[REQUEST.CONTENT_IMAGES];
                            this.templateContent = replaceShortcodes(template[REQUEST.CONTENT], this.templateContentImages);
                            this.personal = !!template[REQUEST.PERSONAL];
                            this.templateIndustry = template[REQUEST.INDUSTRY_ID];
                            this.templateTypeOption = template[REQUEST.TYPE];
                            this.templateBackground = template[REQUEST.BACKGROUND_ID];
                            this.isTemplateOwner = template[REQUEST.IS_OWNER];
                            this.templateOwner = template[REQUEST.OWNER_NAME];
                            this.templateLastUpdated = template[REQUEST.LAST_UPDATED];
                            this.templateEngineOption = template[REQUEST.ENGINE];
                            this.templateActive = template[REQUEST.ACTIVE];
                            this.togglePreview(false);

                            this.getTemplateBackgrounds();

                            this.view = 'editor';
                        }
                    }
                    else {
                        this.showAlert('error', 'Failed to duplicate template');
                    }
                }).catch(error => {
                    this.showAlert('error', error.response?.data?.message || 'Error duplicating template');
                    console.error("Error duplicating template: ", error);
                }).finally(() => {
                    this.showDuplicateTemplateModal = false;
                    this.creatingTemplateDuplicate = false;
                    this.editTemplateLoading = false;
                    this.duplicateTemplateName = '';
                })
            }
            else {
                this.view = 'editor';
            }
        },

        /**
         * Handles creating a background template from the given template ID.
         *
         * @param {number}  templateId
         * @param {boolean} loadNewTemplate
         * @return {Promise<unknown>}
         */
        duplicateBackgroundTemplate(templateId, loadNewTemplate = false) {
            this.templateType = 'background';

            if(templateId) {
                this.editTemplateLoading = true;
                this.creatingTemplateDuplicate = true;

                return this.api.duplicateEmailTemplateBackground(
                    this.duplicateTemplateName,
                    templateId
                ).then(response => {
                    if(response.data.data.status === true) {
                        this.showAlert('success', 'Duplicate template created.');

                        if(loadNewTemplate === true) {
                            const template = response.data.data.template;

                            this.templateId = template[REQUEST.ID];
                            this.templateName = template[REQUEST.NAME];
                            this.templateHeaderImages = template[REQUEST.HEADER_IMAGES];
                            this.templateFooterImages = template[REQUEST.FOOTER_IMAGES];
                            this.templateHeader = replaceShortcodes(template[REQUEST.HEADER], this.templateHeaderImages);
                            this.templateFooter = replaceShortcodes(template[REQUEST.FOOTER], this.templateFooterImages);
                            this.personal = !!template[REQUEST.PERSONAL];
                            this.templateIndustry = template[REQUEST.INDUSTRY_ID];
                            this.isTemplateOwner = template[REQUEST.IS_OWNER];
                            this.templateOwner = template[REQUEST.OWNER_NAME];
                            this.templateLastUpdated = template[REQUEST.LAST_UPDATED];
                            this.togglePreview(false);

                            this.view = 'editor';
                        }
                    }
                    else {
                        this.showAlert('error', 'Failed to duplicate template');
                    }
                }).catch(error => {
                    this.showAlert('error', error.response?.data?.message || 'Error duplicating template');
                    console.error("Error duplicating template: ", error);
                }).finally(() => {
                    this.showDuplicateTemplateModal = false;
                    this.creatingTemplateDuplicate = false;
                    this.editTemplateLoading = false;
                    this.duplicateTemplateName = '';
                })
            }
            else {
                this.view = 'editor';
            }
        },

        deleteTemplate(templateId) {
            let promise;
            if(this.templateTypeTab === 'content') {
                promise = this.api.deleteEmailTemplate(templateId);
            }
            else if(this.templateTypeTab === 'background') {
                promise = this.api.deleteEmailTemplateBackground(templateId);
            }

            return promise.then(res => {
                if (res.data.data.status === true) {
                    this.showAlert('success', 'Template deleted');
                }
                else {
                    this.showAlert('error', 'Failed to delete template');
                }
            }).catch(err => {
                this.showAlert('error', 'Error deleting template');
            });
        },
        async getEmailTemplates(templateType) {
            let url;
            const queryParams = {
                page: this.templates.current_page ?? 1,
                per_page: this.templates.per_page ?? 10,
            }

            let endpoint;
            if (templateType === 'content') {
                endpoint = '/list';
                return this.submitSearch();
            } else if (templateType === 'background') {
                endpoint = '/backgrounds/list';
            } else {
                throw new Error("Invalid template type");
            }

            url = Api.getBaseUrl() + endpoint;


            try {
                const response = await axios.get(url, { params: queryParams });
                const { data } = response.data;
                if (data.status === true) {
                    this.templates = data.templates;
                } else {
                    this.showAlert('error', 'Failed to load templates list');
                }
            } catch (error) {
                this.showAlert('error', 'Error loading templates list');
            }

            this.getShortcodes();
        },
        async getTemplateBackgrounds() {
            await this.api.getEmailTemplateBackgroundsByIndustry(this.templateIndustry).then(res => {
                if (res.data.data.status === true) {
                    this.templateBackgrounds = res.data.data.templates;
                }
                else {
                    this.showAlert('error', 'Failed to load background templates');
                }
            }).catch(err => {
                this.showAlert('error', 'Error loading background templates');
            });
        },
        async getFilterOptions() {
            await this.api.getFilterOptions().then(resp => {
                if (resp.data?.data?.status) {
                    this.filters = resp.data?.data?.filter_options;
                }
            }).catch(err => {
                this.showAlert('error', err.response?.data?.message || 'Error getting Filter Options');
            });
        },
        clearFilter(filterId) {
            delete this.filterInputs[filterId];
        },
        submitSearch() {
            const searchParams = {
                search: this.searchInputGeneral,
                filters: this.filterInputs,
                page: this.templates.current_page ?? 1,
                per_page: this.templates.per_page ?? 10,
            }
            return this.api.search(searchParams).then(resp => {
                if (resp.data?.data?.status === true) {
                    this.templates = resp?.data.data.templates;
                } else {
                    this.showAlert('error', 'Failed to load templates list');
                }
            }).catch(err => {
                this.errorHandler.handleError(err)
                this.showAlert('error', err.message);
            });
        },
        async resetFilters() {
            await nextTick();
            this.searchInputGeneral = '';
            this.filterInputs = {};
            await this.submitSearch();
        },
        getShortcodes() {
            return this.api.getShortcodes(this.templateIndustry, this.templateTypeOption, this.templateEngineOption).then(res => {
                if(res.data.data.status === true) {
                    this.shortcodes = res.data.data.shortcodes;

                    this.editorPlugins = [
                        [shortcodesPlugin, res.data.data.shortcodes]
                    ];
                }
                else {
                    this.store.showAlert('error', 'Failed to load shortcodes list');
                }
            }).catch(err => {
                this.store.showAlert('error', 'Error loading shortcodes list');
            });
        },
        getEngineOptions() {
            return this.api.getEngineOptions(this.templateIndustry, this.templateTypeOption).then(res => {
                if(res.data.data.status === true) {
                    this.templateEngineOptions = res.data.data.engine_options
                }
                else {
                    this.store.showAlert('error', 'Failed to load shortcodes list');
                }
            }).catch(err => {
                this.store.showAlert('error', 'Error loading shortcodes list');
            });
        },
        async getTemplateOptions() {
            this.availableTemplateOptions = {}

            if (!this.templateTypeOption) {
                return
            }

            const response = await this.api.getTemplateOptions(this.templateTypeOption)

            this.availableTemplateOptions = response.data?.options

            if (this.availableTemplateOptions?.shortcodes?.length > 0) {
                this.editorPlugins = [
                    [shortcodesPlugin, this.availableTemplateOptions?.shortcodes]
                ];
            }
        },
    }
});
