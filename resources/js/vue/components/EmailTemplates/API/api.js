import { REQUEST } from "../../../../constants/APIRequestKeys";

const __API_BASE_URL__ = 'internal-api';
const __VERSION__ = 1;
const __BASE_ENDPOINT__ = 'email-templates';

export default class Api {
    constructor() {

    }

    axios() {
        const axiosConfig = {
            baseURL: Api.getBaseUrl()
        }

        return axios.create(axiosConfig);
    }

    static getBaseUrl() {
        return `/${__API_BASE_URL__}/v${__VERSION__}/${__BASE_ENDPOINT__}`;
    }

    static make() {
        return new Api();
    }

    getShortcodes(industryId, type, engine) {
        return this.axios().get('shortcodes', {
            params: {
                [REQUEST.INDUSTRY_ID]   : industryId,
                [REQUEST.TYPE]          : type,
                [REQUEST.ENGINE]        : engine,
            }
        });
    }

    getEngineOptions() {
        return this.axios().get('engine-options');
    }

    getTemplateOptions(type) {
        return this.axios().get('options', {
            params: {
                type
            }
        });
    }
    getEmailTemplate(id) {
        return this.axios().get(`/template/${id}`);
    }

    previewEmailTemplate(content, backgroundId) {
        return this.axios().post('preview', {
            [REQUEST.CONTENT]       : content,
            [REQUEST.BACKGROUND_ID] : backgroundId,
        });
    }

    createEmailTemplate(
        name,
        subject,
        content,
        personal,
        industryId,
        backgroundId,
        type,
        engine,
        active,
    )
    {
        return this.axios().post('create', {
            [REQUEST.NAME]          : name,
            [REQUEST.SUBJECT]       : subject,
            [REQUEST.CONTENT]       : content,
            [REQUEST.PERSONAL]      : personal ? 1 : 0,
            [REQUEST.INDUSTRY_ID]   : industryId,
            [REQUEST.BACKGROUND_ID] : backgroundId,
            [REQUEST.TYPE]          : type,
            [REQUEST.ENGINE]        : engine,
            [REQUEST.ACTIVE]        : active,
        });
    }

    updateEmailTemplate(
        id,
        name,
        subject,
        content,
        personal,
        industryId,
        backgroundId,
        type,
        engine,
        active,
        payload = {}
    )
    {
        return this.axios().patch(`update/${id}`, {
            [REQUEST.NAME]          : name,
            [REQUEST.SUBJECT]       : subject,
            [REQUEST.CONTENT]       : content,
            [REQUEST.PERSONAL]      : personal ? 1 : 0,
            [REQUEST.INDUSTRY_ID]   : industryId,
            [REQUEST.BACKGROUND_ID] : backgroundId,
            [REQUEST.TYPE]          : type,
            [REQUEST.ENGINE]        : engine,
            [REQUEST.ACTIVE]        : active,
            [REQUEST.PAYLOAD]        : payload,
        });
    }

    deleteEmailTemplate(id) {
        return this.axios().delete(`delete/${id}`);
    }

    getEmailTemplateBackground(id) {
        return this.axios().get(`/backgrounds/template/${id}`);
    }

    getEmailTemplateBackgroundsByIndustry(industryId) {
        return this.axios().get(`/backgrounds/list-industry`, {
            params: {
                [REQUEST.INDUSTRY_ID] : industryId
            }
        });
    }

    getEmailTemplateBackgrounds() {
        return this.axios().get(`/backgrounds/list`);
    }

    previewEmailTemplateBackground(header, footer) {
        return this.axios().post('/backgrounds/preview', {
            [REQUEST.HEADER] : header,
            [REQUEST.FOOTER] : footer,
        });
    }

    createEmailTemplateBackground(
        name,
        header,
        footer,
        personal,
        industryId,
    )
    {
        return this.axios().post('/backgrounds/create', {
            [REQUEST.NAME]        : name,
            [REQUEST.HEADER]      : header,
            [REQUEST.FOOTER]      : footer,
            [REQUEST.PERSONAL]    : personal ? 1 : 0,
            [REQUEST.INDUSTRY_ID] : industryId,
        });
    }

    updateEmailTemplateBackground(
        id,
        name,
        header,
        footer,
        personal,
        industryId,
    )
    {
        return this.axios().patch(`/backgrounds/update/${id}`, {
            [REQUEST.NAME]        : name,
            [REQUEST.HEADER]      : header,
            [REQUEST.FOOTER]      : footer,
            [REQUEST.PERSONAL]    : personal ? 1 : 0,
            [REQUEST.INDUSTRY_ID] : industryId,
        });
    }

    deleteEmailTemplateBackground(id) {
        return this.axios().delete(`/backgrounds/delete/${id}`);
    }

    saveTemplateImage(name, imageDataUrl, templateId, templateType) {
        return this.axios().post('image', {
            image_name: name,
            image_data_url: imageDataUrl,
            template_id: templateId,
            template_type: templateType
        });
    }

    /**
     * Handles creating a duplicate template from the given template ID.
     *
     * @param {string} name
     * @param {number} templateId
     * @return {Promise<AxiosResponse<any>>}
     */
    duplicateEmailTemplate(
        name,
        templateId,
    )
    {
        return this.axios().post('duplicate', {
            [REQUEST.NAME]        : name,
            [REQUEST.TEMPLATE_ID] : templateId,
        });
    }

    /**
     * Handles creating a duplicate template background from the given background ID.
     *
     * @param {string} name
     * @param {number} backgroundId
     * @return {Promise<AxiosResponse<any>>}
     */
    duplicateEmailTemplateBackground(
        name,
        backgroundId,
    )
    {
        return this.axios().post('/backgrounds/duplicate', {
            [REQUEST.NAME]          : name,
            [REQUEST.BACKGROUND_ID] : backgroundId,
        });
    }

    getAllEmailTemplates() {
        return this.axios().get('/all');
    }

    retrieveEmailTemplateList(searchInputs) {
        return this.axios().post('/retrieve', searchInputs)
    }

    getFilterOptions() {
        return this.axios().get('/filter-options');
    }

    search(searchInputs) {
        return this.axios().post('/search', searchInputs);
    }

    sendTestEmail(templateId) {
        return this.axios().post(`/send-test/${templateId}`);
    }

}
