<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div class="flex flex-col px-5 pt-5 border-b mb-5 gap-4"
                         :class="{'bg-light-module': !darkMode, 'bg-dark-module': darkMode}"
                    >
                        <div class="flex items-center justify-between flex-wrap pl-4">
                            <h3 class="text-xl font-medium pb-0 leading-none mr-5"
                                :class="[darkMode ? 'text-primary-500' : '']">Flow Management</h3>
                        </div>
                        <div class="flex justify-between">
                            <tab
                                :dark-mode="darkMode"
                                :tabs="tabTitles"
                                @selected="selectTab"
                                tab-style="fit"
                                background-color="light"
                                tab-type="Normal"
                            />
                        </div>
                    </div>
                    <div class="mx-10 rounded-md flex flex-col flex-grow"
                         :class="[darkMode ? 'bg-dark-module border-dark-border text-slate-200' : 'bg-light-module']">
                        <component
                            :is="currentTabComponent"
                            :dark-mode="darkMode"
                            :flow-api="flowApi"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
import {defineComponent, markRaw} from 'vue'
import {ApiFactory} from "./services/api/factory.js";
import Pricing from "../FloorPriceManagement/components/Pricing.vue";
import Flows from "./Flows.vue";
import Tab from "../Shared/components/Tab.vue";
import ConfigurableEngineVariables from "./ConfigurableEngineVariables/ConfigurableEngineVariables.vue";
import useQueryParams from "../../../composables/useQueryParams.js";
import {useRolesPermissions, PERMISSIONS} from "../../../stores/roles-permissions.store.js";

export default defineComponent({
    name: "FlowManagement",
    components: {
        Flows,
        Pricing,
        Tab
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: 'api'
        },
    },
    created() {
        this.flowApi = ApiFactory.makeApiService(this.apiDriver);
    },
    data() {
        return {
            queryParamsHelper: useQueryParams(),
            loading: false,
            flowApi: null,
            selectedTab: null,
            tabs: [
                {
                    name: 'Flow Management', component: markRaw(Flows), current: true,
                }
            ],
            rolesAndPermissions: useRolesPermissions()
        }
    },
    mounted() {
        this.rolesAndPermissions.initialize().then(() => {
            const canViewConfigurableVariables = this.rolesAndPermissions.hasPermission(PERMISSIONS.FLOW_ENGINES_CONFIGURABLE_VARIABLES_VIEW);

            if (canViewConfigurableVariables) {
                this.tabs.push({
                    name: 'Configurable Engine Variables',
                    component: markRaw(ConfigurableEngineVariables),
                    current: false,
                })
            }
        })
    },
    methods: {
        selectTab(tab) {
            this.queryParamsHelper.setQueryParamsOnCurrentUrl({tab})
            this.setSelectedTab(tab)
        },
        setSelectedTab(tab) {
            this.selectedTab = tab;
            this.tabTitles.forEach(e => {
                e.current = e.name === this.selectedTab
            })
        },
    },
    computed: {
        tabTitles() {
            return this.tabs;
        },
        currentTabComponent() {
            return this.tabTitles.find(e => e.current)?.component
        },
    },
});


</script>

<style scoped>

</style>
