<template>
    <div class="p-3">
        <div class="flex items-center mb-6 pb-6 ml-2 border-b" :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="flex items-center">
                <p class="uppercase font-semibold text-xs w-max mr-3"
                   :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                    Show History
                </p>
                <ToggleSwitch v-model="showHistory" :dark-mode="darkMode" />
            </div>
            <div class="flex items-center ml-10">
                <p class="uppercase font-semibold text-xs w-max mr-3"
                   :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                    Show Legacy
                </p>
                <ToggleSwitch v-model="showLegacy" :dark-mode="darkMode" />
            </div>
            <div class="flex items-center ml-10"
                v-if="canDelete"
            >
                <p class="uppercase font-semibold text-xs w-max mr-3"
                   :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                    Show Trashed
                </p>
                <ToggleSwitch v-model="showTrashed" :dark-mode="darkMode" />
            </div>
        </div>
        <flows-tree :flow-api="flowApi" :dark-mode="darkMode" :show-history="showHistory" :show-legacy="showLegacy" :show-trashed="showTrashed"></flows-tree>
    </div>
</template>

<script>
import { defineComponent } from 'vue'
import CustomButton from "../Shared/components/CustomButton.vue";
import FlowsTree from "./FlowsTree.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import ToggleSwitch from "../Shared/components/ToggleSwitch.vue";
import { PERMISSIONS, useRolesPermissions } from "../../../stores/roles-permissions.store.js";

export default defineComponent({
    name: "Flows",
    components: { ToggleSwitch, Dropdown, CustomButton, FlowsTree },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        flowApi: {
            type: Object,
            default: null,
            required: true,
        },
    },

    data() {
        return {
            metaData: {},
            showHistory: false,
            showLegacy: true,
            showTrashed: false,
            permissionStore: useRolesPermissions(),
        }
    },
    computed: {
        canDelete() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_FLOW_MANAGEMENT_DELETE);
        }
    },
});
</script>
