import axios from 'axios';
import {BaseApiService} from "./base";

export class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getRevisionMetaData() {
        return this.axios().get(`/`);
    }

    createNewFlow(flowPayload) {
        return this.axios().post(`/create-flow`, flowPayload);
    }

    setAsProduction(flowId, revisionId) {
        return this.axios().post(`/${flowId}/${revisionId}/set-production`);
    }

    copyRevision(copyRevisionData) {
        return this.axios().post(`/${copyRevisionData.fromFlow}/${copyRevisionData.fromRevisionId}/copy-to`, {
            'to_flow': copyRevisionData.toFlow,
            'name': copyRevisionData.name,
            'description': copyRevisionData.description,
            'working': copyRevisionData.working,
        });
    }

    renameRevisionGroup({ newName, oldName, flowId }) {
        return this.axios().patch(`/${flowId}/rename-revision-group`, {
            new_name: newName,
            old_name: oldName,
        });
    }

    deleteRevision(flowId, revisionId) {
        return this.axios().delete(`/${flowId}/${revisionId}`);
    }

    deleteRevisionGroup(flowId, revisionGroupName) {
        const escapedString = encodeURIComponent(revisionGroupName);
        return this.axios().delete(`/${flowId}/${escapedString}/delete-group`);
    }

    undeleteRevision(flowId, revisionId) {
        return this.axios().patch(`/${flowId}/${revisionId}/undelete`);
    }

    hardDeleteRevision(flowId, revisionId) {
        return this.axios().delete(`/${flowId}/${revisionId}/hard-delete`);
    }
}
