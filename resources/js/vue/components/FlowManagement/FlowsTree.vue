<template>
    <loading-spinner v-if="loading || saving"></loading-spinner>
    <div v-else class="pb-12 h-[50vh] overflow-y-auto">
        <div class="flex items-center">
            <div @click="flowsExpanded = !flowsExpanded" class="cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6" v-if="flowsExpanded">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 13.5H9m4.06-7.19l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                </svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6" v-else>
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 10.5v6m3-3H9m4.06-7.19l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                </svg>
            </div>
            <div class="ml-1 flex items-center">
                Flows
                <button @click="toggleCreateFlowModal(true)" class="text-xs bg-primary-500 text-white border rounded px-2 ml-2" title="Add Flow">+</button>
            </div>
            <div @click="getFlows"
                class="ml-4 cursor-pointer"
            >
                <svg  class="w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                </svg>
            </div>
        </div>

        <!--    Default View (Revisions grouped by name)    -->
        <div class="border-l ml-3" v-if="flowsExpanded" :class="borderClasses">
            <!--    Flow level    -->
            <div v-if="!Object.keys(defaultFlowView).length">
                <p class="pt-2 ml-4">
                    No revisions found for this Flow.
                </p>
            </div>
            <div v-else>
                <div class="pt-4" v-for="flow in defaultFlowView" :key="flow.id">
                    <div class="flex items-center">
                        <div class="border-b w-4" :class="borderClasses"></div>
                        <div
                            class="cursor-pointer"
                            @click="toggleFlowExpanded(flow.id)"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6" v-if="expandedFlows[flow.id]">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 13.5H9m4.06-7.19l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6" v-else>
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 10.5v6m3-3H9m4.06-7.19l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                            </svg>
                        </div>
                        <div class="ml-1 flex items-center cursor-pointer" :title="flow.description">
                            <div class="flex items-center">
                                <p>{{ flow.name }}</p>
                            </div>
                            <button class="text-xs bg-primary-500 text-white border rounded px-2 mx-2" title="Add Revision" @click="flowEditorPage(flow.id)">+</button>
                        </div>
                    </div>
                    <!--    Expanded Flow node     -->
                    <div class="pl-7" v-if="expandedFlows[flow.id]">
                        <div class="border-l" :class="borderClasses">
                            <div class="pl-8 mb-3">
                                <div class="flex items-center gap-x-2 text-xs pt-1">
                                    <p class="uppercase text-slate-500">id:</p>
                                    <p>{{ flow.id }}</p>
                                </div>
                                <div class="flex items-center gap-x-2 text-xs pt-1">
                                    <p class="uppercase text-slate-500">description:</p>
                                    <p>{{ flow.description || 'None' }}</p>
                                </div>
                                <div class="flex items-center gap-x-2 text-xs pt-1">
                                    <p class="uppercase text-slate-500">production revision:</p>
                                    <p>{{ flow.versions.production || '-' }}</p>
                                </div>
                            </div>
                            <p class="pt-2 ml-4 uppercase text-xs text-slate-500">
                                Revisions
                            </p>
                            <div v-if="!Object.keys(flow.revisions ?? {}).length">
                                <p class="pt-2 ml-4">
                                    No revisions found for this Flow.
                                </p>
                            </div>
                            <div v-else>
                                <!--    Revision Groups (grouped by name i.e. same Revision, different versions)    -->
                                <div v-for="([revisionGroupName, revisionGroup], index) in getSortedRevisions(flow)" :key="index" class="pt-2">
                                    <div class="flex items-center" v-if="revisionGroup.visible || showTrashed">
                                        <div class="border-b w-4" :class="borderClasses"></div>
                                        <div
                                            class="cursor-pointer"
                                            @click.stop="toggleRevisionExpanded(revisionGroupName)"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6" v-if="expandedRevisions[revisionGroupName]">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 13.5H9m4.06-7.19l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                                            </svg>
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6" v-else>
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 10.5v6m3-3H9m4.06-7.19l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                                            </svg>
                                        </div>
                                        <div class="ml-1 flex items-center"
                                            :class="[revisionGroup.contains_production_revision ? 'text-green-500' : '']"
                                        >
                                            {{ revisionGroupName }}
                                        </div>
                                        <div>
                                            <actions-handle
                                                width="w-[14rem]"
                                                :dark-mode="darkMode"
                                                :no-custom-action="false"
                                                :no-delete-button="true"
                                                :no-edit-button="true"
                                                :open-right="true"
                                                :custom-actions="getRevisionGroupActions(flow, revisionGroup)"
                                                @rename-revision-group="renameRevisionGroup(flow.id, revisionGroupName)"
                                                @delete-revision-group="deleteRevisionGroup(flow.id, revisionGroupName)"
                                            />
                                        </div>
                                        <div v-if="!revisionGroup.is_v2">
                                            <span class="italic text-xs ml-4" :class="darkMode ? 'text-slate-400' : 'text-slate-600'">
                                                - This Revision can be updated to v2 in Flow Builder
                                            </span>
                                        </div>
                                    </div>
                                    <!--    Expanded Revision Group   -->
                                    <div v-if="expandedRevisions[revisionGroupName]" class="pl-7">
                                        <div class="border-l" :class="borderClasses">
                                            <p class="pt-2 ml-6 uppercase text-xs text-slate-500">
                                                Versions
                                            </p>
                                            <!--    Revision Versions   -->
                                            <div v-for="revision in revisionGroup.versions">
                                                <div class="pt-2" v-if="showRevisionVersion(flow, revision, revisionGroup)">
                                                    <div>
                                                        <div class="flex items-center w-[18rem] justify-between"
                                                             :class="[revisionActive(flow.versions, revision) && 'text-green-500',
                                                                isDeleted(revision) && 'text-orange-700 italic'
                                                         ]">
                                                            <div class="flex items-center"  :class="[revision.type === 'history' ? 'opacity-70' : '']">
                                                                <div class="border-b w-4" :class="borderClasses"></div>
                                                                <div class="ml-1 flex items-center cursor-pointer" :title="revision.description">
                                                                    <div @click="toggleRevisionDetails(revision.id)">
                                                                        <svg class="w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                                                                        </svg>
                                                                    </div>
                                                                    <p class="pl-1 overflow-x-hidden">v{{ getVersion(revision, revisionGroup) }}</p><p class="text-xs pl-1">({{ getTypeDisplay(flow, revision, revisionGroup) }})</p>
                                                                </div>
                                                            </div>
                                                            <div class="flex items-center">
                                                                <div v-if="isDeleted(revision)" title="This Revision has been soft deleted" class="cursor-pointer">
                                                                    <div v-if="revision.fetched_while_trashed">
                                                                        <div title="WARNING: This Revision has been fetched by the Client while soft-deleted!" class="pointer">
                                                                            <svg class="w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                                                                <path fill-rule="evenodd" d="M5.25 9a6.75 6.75 0 0113.5 0v.75c0 2.123.8 4.057 2.118 5.52a.75.75 0 01-.297 1.206c-1.544.57-3.16.99-4.831 1.243a3.75 3.75 0 11-7.48 0 24.585 24.585 0 01-4.831-*********** 0 01-.298-1.205A8.217 8.217 0 005.25 9.75V9zm4.502 8.9a2.25 2.25 0 104.496 0 25.057 25.057 0 01-4.496 0z" clip-rule="evenodd" />
                                                                            </svg>
                                                                        </div>
                                                                    </div>
                                                                    <div v-else>
                                                                        <svg class="w-4 text-orange-700" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                                                            <path fill-rule="evenodd" d="M16.5 4.478v.227a48.816 48.816 0 013.878.512.75.75 0 11-.256 1.478l-.209-.035-1.005 13.07a3 3 0 01-2.991 2.77H8.084a3 3 0 01-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 01-.256-1.478A48.567 48.567 0 017.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 013.369 0c1.603.051 2.815 1.387 2.815 2.951zm-6.136-1.452a51.196 51.196 0 013.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 00-6 0v-.113c0-.794.609-1.428 1.364-1.452zm-.355 5.945a.75.75 0 10-1.5.058l.347 9a.75.75 0 101.499-.058l-.346-9zm5.48.058a.75.75 0 10-1.498-.058l-.347 9a.75.75 0 001.5.058l.345-9z" clip-rule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                </div>
                                                                <div v-else-if="unpublished(revision)" title="This Revision has not yet been published" class="cursor-pointer">
                                                                    <svg class="w-4 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                                                                    </svg>
                                                                </div>
                                                                <div v-else-if="hasChanges(revision, revisionGroup)" title="This Revision has unpublished changes." class="cursor-pointer">
                                                                    <svg class="w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                                                                    </svg>
                                                                </div>
                                                                <actions-handle
                                                                    width="w-[14rem]"
                                                                    :dark-mode="darkMode"
                                                                    :no-custom-action="false"
                                                                    :no-delete-button="true"
                                                                    :no-edit-button="true"
                                                                    :open-right="true"
                                                                    :custom-actions="getRevisionActions(flow, revision, revisionGroup)"
                                                                    @edit-current-version="editCurrentVersionOfRevision(flow.id, revision.id)"
                                                                    @edit-working-version="flowEditorPage(flow.id, revision.id)"
                                                                    @edit-working-version-v2="editWorkingRevisionV2(flow.id, revisionGroup)"
                                                                    @set-as-production-revision="setProductionRevision(flow.id, revision.id)"
                                                                    @copy-revision="copyRevisionToFlow(flow.id, revision.id, !revision.is_published)"
                                                                    @copy-working-revision="copyWorkingRevisionToFlow(flow.id, revisionGroup)"
                                                                    @delete-revision="deleteRevision(flow.id, revisionGroupName, revision.id)"
                                                                    @undelete-revision="undeleteRevision(flow.id, revision.id)"
                                                                    @hard-delete-revision="hardDeleteRevision(flow.id, revision.id)"
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!--    Revision version Details expanded  -->
                                                    <div v-if="expandedDetails[revision.id]" class="pl-7">
                                                        <div class="border-l pl-4" :class="borderClasses">
                                                            <div class="flex items-center gap-x-2 text-xs pt-1">
                                                                <p class="uppercase text-slate-500">id:</p>
                                                                <p>{{ revision.id }}</p>
                                                                <p v-if="revision.type === 'variant' && revision.is_published && revisionGroup.working_revision_id"
                                                                    class="opacity-60 italic"
                                                                >
                                                                    (working - {{ revisionGroup.working_revision_id }})
                                                                </p>
                                                            </div>
                                                            <div class="flex items-center gap-x-2 text-xs pt-1">
                                                                <p class="uppercase text-slate-500">description:</p>
                                                                <p>{{ revision.description || 'None' }}</p>
                                                            </div>
                                                            <div class="flex items-center gap-x-2 text-xs pt-1">
                                                                <p class="uppercase text-slate-500">schema:</p>
                                                                <p>{{ parseFloat(revision.schema ?? 1).toFixed(1) }}</p>
                                                            </div>
                                                            <div class="flex items-center gap-x-2 text-xs pt-1" v-if="revision.schema >= 2">
                                                                <p class="uppercase text-slate-500">slide summary:</p>
                                                                <p>{{ getSlideSummary(revision) }}</p>
                                                            </div>
                                                            <div v-if="revision.created_at" class="flex items-center gap-x-2 text-xs pt-1">
                                                                <p class="uppercase text-slate-500">created:</p>
                                                                <p>{{ $filters.dateFromTimestamp(revision.created_at, 'long') }}</p>
                                                            </div>
                                                            <div v-if="showUpdated(revision, revisionGroup)" class="flex items-center gap-x-2 text-xs pt-1">
                                                                <p class="uppercase text-slate-500">updated:</p>
                                                                <p>{{ $filters.dateFromTimestamp(getUpdated(revision, revisionGroup), 'long') }}</p>
                                                            </div>
                                                            <div v-if="revision.actioned_by && revision.is_published" class="flex items-center gap-x-2 text-xs pt-1">
                                                                <p class="uppercase text-slate-500">Published by:</p>
                                                                <p>{{ revision.actioned_by ?? '-' }}</p>
                                                            </div>
                                                            <div v-if="showUpdated(revision, revisionGroup)" class="flex items-center gap-x-2 text-xs pt-1">
                                                                <p class="uppercase text-slate-500">Last updated by:</p>
                                                                <p>{{ getUpdated(revision, revisionGroup, true) }}</p>
                                                            </div>
                                                            <div v-if="revision.ttlMilliseconds || revision.ttlMilliseconds === 0" class="flex items-center gap-x-2 text-xs pt-1">
                                                                <p class="uppercase text-slate-500">Deleted at:</p>
                                                                <p>{{ $filters.dateFromTimestamp(revision.deleted_at, 'long') }}</p>
                                                            </div>
                                                            <div v-if="revision.ttlMilliseconds || revision.ttlMilliseconds === 0" class="flex items-center gap-x-2 text-xs pt-1">
                                                                <p class="uppercase text-slate-500">Soft Delete TTL:</p>
                                                                <p>{{ processTtlForDisplay(revision.ttlMilliseconds) }}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--    New Flow modal    -->
        <modal
            v-if="showCreateFlowModal"
            @confirm="createNewFlow"
            @cancel="cancelCreateNewFlow"
            :small="true"
            close-text="Cancel"
            :dark-mode="darkMode"
            @close="cancelCreateNewFlow"
            :class="[saving ? 'pointer-events-none blur-sm grayscale-[90%]' : '']"
        >
            <template v-slot:header>
                <h3>Create new Flow</h3>
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-1 gap-y-4">
                    <custom-input
                        label="Name"
                        v-model="editFlowData.name"
                        placeholder="Name..."
                        :dark-mode="darkMode"
                    />
                    <custom-input
                        label="Description"
                        v-model="editFlowData.description"
                        placeholder="Description..."
                        :dark-mode="darkMode"
                    />
                    <div v-if="errorMessage" class="py-4 text-red-500 text-sm text-center whitespace-pre-line">
                        {{ errorMessage }}
                    </div>
                </div>
            </template>
        </modal>

        <!--   Copy Revision modal   -->
        <modal
            v-if="showCopyRevisionModal"
            @confirm="confirmCopyRevision"
            @cancel="cancelCopyRevision"
            close-text="Cancel"
            :dark-mode="darkMode"
            @close="cancelCopyRevision"
            :small="true"
            :class="[saving ? 'pointer-events-none blur-sm grayscale-[90%]' : '']"
        >
            <template v-slot:header>
                <h3>Copy Revision</h3>
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-1 gap-y-4">
                    <dropdown
                        label="Copy To Flow"
                        v-model="copyRevision.toFlow"
                        :options="copyRevisionOptions"
                        placeholder="Select Flow..."
                        :dark-mode="darkMode"
                    />
                    <custom-input
                        label="New Revision Name"
                        v-model="copyRevision.name"
                        placeholder="Name..."
                        :dark-mode="darkMode"
                    />
                    <custom-input
                        label="Description"
                        v-model="copyRevision.description"
                        placeholder="Description..."
                        :dark-mode="darkMode"
                    />
                    <div v-if="errorMessage" class="py-4 text-red-500 text-sm text-center whitespace-pre-line">
                        {{ errorMessage }}
                    </div>
                </div>
            </template>
        </modal>

        <!--   Delete Revisions Modal    -->
        <modal
            v-if="showDeleteModal"
            @confirm="confirmDelete"
            @cancel="cancelDelete"
            confirm-text="Delete"
            close-text="Cancel"
            :dark-mode="darkMode"
            @close="cancelDelete"
            :small="true"
            :class="[saving ? 'pointer-events-none blur-sm grayscale-[90%]' : '']"
        >
            <template v-slot:header>
                <h3>Delete Revision{{ deletingRevision.deleteGroup ? ' Group' : '' }}</h3>
            </template>
            <template v-slot:content>
                <div class="whitespace-pre-line">
                    <p v-if="deletingRevision.hardDelete">
                        Are you sure you wish to permanently delete {{ getRevisionName(deletingRevision.flowId, deletingRevision.revisionGroupName, deletingRevision.revisionId) }}?
                        This operation cannot be undone.
                    </p>
                    <p v-else-if="deletingRevision.deleteGroup">
                        This will soft delete all Revisions in the group "{{ deletingRevision.revisionGroupName }}".
                        Are you sure you wish to proceed?
                    </p>
                    <p v-else>
                        Are you sure you wish to soft delete {{ getRevisionName(deletingRevision.flowId, deletingRevision.revisionGroupName, deletingRevision.revisionId) }}?
                    </p>
                </div>
                <div v-if="errorMessage" class="py-4 text-red-500 text-sm text-center whitespace-pre-line">
                    {{ errorMessage }}
                </div>
            </template>
        </modal>

        <!--    Rename Revision Group    -->
        <modal
            v-if="showRenameModal"
            @confirm="confirmRenameRevisionGroup"
            @cancel="cancelRenameRevisionGroup"
            :small="true"
            close-text="Cancel"
            :dark-mode="darkMode"
            @close="cancelRenameRevisionGroup"
            :class="[saving ? 'pointer-events-none blur-sm grayscale-[90%]' : '']"
        >
            <template v-slot:header>
                <h3>Rename Revision Group</h3>
            </template>
            <template v-slot:content>
                <div>

                    <custom-input
                        label="Name"
                        v-model="renamingRevisionGroup.newName"
                        placeholder="Name..."
                        :dark-mode="darkMode"
                    />
                </div>
            </template>
        </modal>

    </div>
</template>

<script>
import ActionsHandle from "../Shared/components/ActionsHandle.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import Tooltip from "../Shared/components/Tooltip.vue";
import Modal from "../Shared/components/Modal.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import { useRolesPermissions, PERMISSIONS } from "../../../stores/roles-permissions.store.js";
export default {
    name: "FlowsTree",
    components: { Dropdown, CustomInput, Modal, Tooltip, LoadingSpinner, ActionsHandle},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        flowApi: {
            type: Object,
            default: null,
            required: true,
        },
        showHistory: {
            type: Boolean,
            default: false,
        },
        showLegacy: {
            type: Boolean,
            default: true,
        },
        showTrashed: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            flows: [],
            defaultFlowView: [],
            familyTreeFlowView: [],
            flowsExpanded: true,
            expandedFlows: {},
            expandedRevisions: {},
            expandedDetails: {},
            loading: false,
            saving: false,
            showCreateFlowModal: false,
            editFlowData: {
                name: null,
                description: null,
            },
            showCopyRevisionModal: false,
            copyRevision: {
                fromFlow: null,
                toFlow: null,
                name: null,
                description: null,
                fromRevisionId: null,
            },
            copyRevisionOptions: [],
            errorMessage: null,
            editCurrentVersion: false,

            permissionStore: useRolesPermissions(),

            showDeleteModal: false,
            deletingRevision: {
                flowId: null,
                revisionGroupName: null,
                revisionId: null,
                deleteGroup: false,
                hardDelete: false,
            },

            showRenameModal: false,
            renamingRevisionGroup: {
                newName: "",
                flowId: null,
                oldName: null,
            },
        }
    },

    created() {
        this.getFlows();
    },

    computed: {
        borderClasses() {
            const classes = [' border-dashed'];

            if (this.darkMode) classes.push('border-blue-400');
            else classes.push('border-gray-800')

            return classes;
        },
        canDelete() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_FLOW_MANAGEMENT_DELETE);
        }
    },

    methods: {
        getFlows() {
            this.loading = true;

            this.flowApi.getRevisionMetaData().then(resp => {
                if (resp.data?.data?.status) {
                    const metaData = resp.data.data.revision_meta;
                    Object.keys(metaData).forEach(key => this.flows.push({...metaData[key], id: key}));
                    this.sortRevisions();
                    this.setRevisionGroupVisibility();
                }
            }).finally(() => {
                this.updateFlowOptions();
                this.loading = false
            });
        },

        revisionActive(activeVersions, revision) {
            for (const key in activeVersions) {
                if (activeVersions[key] === revision.id) return true;
            }

            return false;
        },

        editCurrentVersionOfRevision(flowId, revisionId) {
            this.editCurrentVersion = true;
            this.flowEditorPage(flowId, revisionId);
        },

        flowEditorPage(flowId, revisionId = null) {
            const versionQuery = this.editCurrentVersion
                ? `&version=current`
                : '';
            const location = `/flow-management/flow-editor?flowId=${flowId}${versionQuery}`;

            if (revisionId !== null) window.open(`${location}&revisionId=${revisionId}`, '_blank');
            else window.open(location, '_blank');

            this.editCurrentVersion = false;
        },

        editWorkingRevisionV2(flowId, revisionGroup) {
            const workingRevisionId = revisionGroup.working_revision_id;
            this.flowEditorPage(flowId, workingRevisionId);
        },

        toggleFlowExpanded(flowId) {
            this.expandedFlows[flowId] = !this.expandedFlows[flowId];
        },

        toggleRevisionExpanded(revisionName) {
            this.expandedRevisions[revisionName] = !this.expandedRevisions[revisionName];
        },

        toggleRevisionDetails(revisionId) {
            this.expandedDetails[revisionId] = !this.expandedDetails[revisionId];
        },

        hasChanges(revision, revisionGroup) {
            if (revision.schema >= 2 && revision.type === 'variant') {
                return (revision.is_published && revisionGroup.working_revision_version && this.minorVersion(revisionGroup.working_revision_version) > 0);
            }
            return !/\.0$/.test(revision.version);
        },

        unpublished(revision) {
            return /^0\./.test(revision.version) && revision.type === 'variant';
        },

        isDeleted(revision) {
            return !!revision.deleted_ttl;
        },

        toggleCreateFlowModal(show) {
            this.showCreateFlowModal = !!show;
        },

        cancelCreateNewFlow() {
            Object.assign(this.editFlowData, {
                name: null,
                description: null,
            });
            this.toggleCreateFlowModal(false);
        },

        createNewFlow() {
            this.errorMessage = null;
            if (this.validateFlow()) {
                this.saving = true;
                this.flowApi.createNewFlow(this.editFlowData).then(resp => {
                    if (resp.data.data?.status) {
                        this.toggleCreateFlowModal(false);
                        this.getFlows();
                    }
                    else {
                        this.errorMessage = resp.data.data?.message ?? resp.message ?? 'An unknown error has occurred.'
                    }
                }).catch((err) => {
                    this.errorMessage = err.response?.data?.message ?? err.response?.message ?? 'An unknown error has occurred.'
                }).finally(() => {
                    this.saving = false;
                });
            }
        },

        validateFlow() {
            const errors = [];
            if (!(this.editFlowData.name?.length > 3)) {
                errors.push(`The Flow name must be at least 4 characters.`);
            }
            if (errors.length) {
                this.errorMessage = errors.join('\n');
                return false;
            }
            else return true;
        },

        getTypeDisplay(flow, revision, revisionGroup) {
            if (revision.id && revision.id === flow.versions.production) {
                return 'Production'
            }
            else if (revisionGroup.is_v2) {
                return this.getTypeDisplayV2(flow, revision);
            }
            else {
                return revision.type === 'variant'
                    ? 'Latest'
                    : 'History';
            }
        },

        getTypeDisplayV2(flow, revision) {
            if (!revision.schema || revision.schema < 2) {
                return revision.type === 'variant'
                        ? 'Latest Legacy'
                        : 'History Legacy';
            }
            else {
                return revision.is_published
                    ? revision.type === 'variant'
                        ? 'Latest'
                        : 'History'
                    : 'Unpublished'
            }
        },

        getVersion(revision, revisionGroup) {
            return revision.schema >= 2 && revision.type === 'variant' && revision.is_published && revisionGroup.working_revision_version
                ? revisionGroup.working_revision_version
                : revision.version;
        },

        showUpdated(revision, revisionGroup) {
            return (revision.type === 'variant' && revision.is_published && this.minorVersion(revisionGroup.working_revision_version) > 0)
                || revision.updated_at;
        },

        getUpdated(revision, revisionGroup, getUpdatedBy = false) {
            const targetProperty = getUpdatedBy
                ? 'actioned_by'
                : 'updated_at';
            const updated = (revision.type === 'variant' && revision.is_published && revisionGroup.working_revision_id)
                ? revisionGroup.versions.find(revision => revision.id === revisionGroup.working_revision_id)?.[targetProperty]
                : revision[targetProperty];

            return updated || '-';
        },

        getRevisionActions(flow, revision, revisionGroup) {
            if (revision.ttlMilliseconds || revision.ttlMilliseconds === 0) return this.getActionsForTrashedRevision(flow, revision);

            if (revision.schema >= 2) return this.getRevisionActionsV2(flow, revision, revisionGroup);

            const versionParts = (revision.version || '0.1').split(/\./).map(v => parseInt(v));
            const editOptions = [];
            if (revision.version === '0.1') {
                editOptions.push({ name: `Open Legacy v0.1`, event: 'edit-current-version' });
            }
            else {
                if (versionParts[1] > 0) {
                    editOptions.push({ name: `Open Legacy v${revision.version ?? '0.1?'}`, event: 'edit-working-version' })
                }
                if (versionParts[0] !== 0) {
                    editOptions.push({ name: `Open Legacy v${versionParts[0]}.0`, event: 'edit-current-version' });
                }
            }
            if (versionParts[0] > 0) {
                editOptions.push({ name: `Set as Production`, event: 'set-as-production-revision' });
            }
            if (this.canDelete && flow.versions.production !== revision.id && revision.type === 'history') {
                editOptions.push({ name: `Delete v${revision.version ?? '0.1?'}`, event: `delete-revision` });
            }
            return editOptions;
        },

        getRevisionActionsV2(flow, revision, revisionGroup) {
            const editOptions = [];
            if (revision.type !== 'history' && revisionGroup.working_revision_id) {
                editOptions.push({ name: `Edit v${revisionGroup.working_revision_version || '0.1?'}`, event: 'edit-working-version-v2' });
            }
            if (revision.is_published) {
                editOptions.push({ name: `Open v${revision.version}`, event: 'edit-current-version' });
                if (flow.versions.production !== revision.id) {
                    editOptions.push({ name: `Set v${revision.version} as Production`, event: 'set-as-production-revision' });
                }
            }
            if (revision.type !== 'history' && revision.is_published && revisionGroup.working_revision_id) {
                editOptions.push({ name: `Copy Revision v${revisionGroup.working_revision_version || '0.1?'}`, event: 'copy-working-revision' });
            }
            editOptions.push({ name: `Copy Revision v${revision.version}`, event: 'copy-revision' });
            if (this.canDelete && flow.versions.production !== revision.id && revision.type === 'history') {
                editOptions.push({ name: `Delete v${revision.version ?? '0.1?'}`, event: `delete-revision` });
            }

            return editOptions;
        },

        getActionsForTrashedRevision(flow, revision) {
            const editOptions = [];
            editOptions.push({ name: `Undelete v${revision.version ?? '0.1?'}`, event: `undelete-revision` });
            if (revision.ttlMilliseconds <= 0) {
                editOptions.push({ name: `Permanently Delete v${revision.version ?? '0.1?'}`, event: `hard-delete-revision` });
            }

            return editOptions;
        },

        getRevisionGroupActions(flow, revisionGroup) {
            const editOptions = [];
            editOptions.push({ name: 'Rename Revision Group', event: 'rename-revision-group' });
            if (this.canDelete && !revisionGroup.contains_production_revision) {
                editOptions.push({ name: 'Delete Revision Group', event: 'delete-revision-group' });
            }

            return editOptions;
        },

        setProductionRevision(flowId, revisionId) {
            if (this.saving) return;
            this.saving = true;
            this.flowApi.setAsProduction(flowId, revisionId).then(resp => {
                if (resp.data?.data?.status) {
                    this.getFlows();
                }
            }).catch((err) => {
                this.errorMessage = err.response?.data?.message ?? err.response?.message ?? 'An unknown error has occurred.'
            }).finally(() => {
                this.saving = false;
            });
        },

        showRevisionVersion(flow, revision, revisionGroup) {
            if (revision.deleted_ttl) {
                return (this.canDelete && this.showTrashed);
            }
            if (revision.schema >= 2 && !revision.is_published && (this.majorVersion(revision.version) !== 0 && revisionGroup.is_published_v2)) {
                return false;
            }
            else {
                if (this.revisionActive(flow.versions, revision)) return true;
                return !((!this.showHistory && revision.type === 'history')
                    || (!this.showLegacy && !(revision.schema || revision.schema < 2)));
            }
        },

        getSlideSummary(revision) {
            if (!revision.flow) return "";
            let slideCount = revision.flow.branches.reduce((total, branch) => {
                return total + branch.slides?.length ?? 0;
            }, 0);
            slideCount += revision.flow.slides?.length ?? 0;
            const branchCount = revision.flow.branches?.length ?? 0;
            const templateCount = revision.flow.templates?.length ?? 0;

            return `${slideCount} Slides, ${branchCount} Branches, ${templateCount} Templates`
        },

        sortRevisions() {
            const familyTree = {};
            const defaultView = {};
            this.flows.forEach(flow => {
                familyTree[flow.id] = {};
                defaultView[flow.id] = {};
                for (const key in flow) {
                    if (key !== 'revisions') {
                        familyTree[flow.id][key] = flow[key];
                        defaultView[flow.id][key] = flow[key];
                    } else {
                        const revisions = flow[key];
                        familyTree[flow.id].revisions = [];
                        defaultView[flow.id].revisions = {};
                        this.processTimestamps(defaultView[flow.id]);
                        this.sortDefaultView(revisions, defaultView[flow.id].revisions, flow.versions?.production);
                    }
                }
            });
            this.defaultFlowView = defaultView;
            this.familyTreeFlowView = familyTree;
        },

        sortDefaultView(revisionsArray, defaultView, productionVersionId) {
            const names = revisionsArray.reduce((output, rev) => output.includes(rev.name) ? output : [ ...output, rev.name ], []);
            names.forEach(name => {
                defaultView[name] = {};
                let v2 = false;
                let v2Published = false;
                defaultView[name].versions = revisionsArray.filter(revision => {
                    if (revision.name === name) {
                        this.processTimestamps(revision);
                        if (revision.deleted_ttl) {
                            revision.ttlMilliseconds = Math.max((new Date(revision.deleted_ttl)).getTime() - Date.now(), 0);
                        }
                        if (productionVersionId && revision.id === productionVersionId) defaultView[name].contains_production_revision = true;
                        if (revision.schema >= 2) {
                            v2 = true;
                            if (revision.is_published) v2Published = true;
                        }
                        if (revision.schema >= 2 && !revision.is_published) {
                            defaultView[name].working_revision_id = revision.id;
                            defaultView[name].working_revision_version = revision.version;
                        }
                        return true;
                    }
                    return revision.name === name;
                }).sort((a, b) => parseFloat(a.version) > parseFloat(b.version) ? -1 : 1);

                defaultView[name].is_v2 = defaultView[name].versions.length === 1
                    ? defaultView[name].versions[0].schema >= 2
                    : v2;
                defaultView[name].is_published_v2 = v2Published;
            });
        },

        setRevisionGroupVisibility() {
            for (const flow in this.defaultFlowView) {
                for (const revisionGroup in this.defaultFlowView[flow].revisions) {
                    for (const version of this.defaultFlowView[flow].revisions[revisionGroup].versions) {
                        if (!version.deleted_ttl) {
                            this.defaultFlowView[flow].revisions[revisionGroup].visible = true;
                            break;
                        }
                        else this.defaultFlowView[flow].revisions[revisionGroup].visible = false;
                    }
                }
            }
        },

        processTimestamps(revision) {
            const convertFirestoreTimestamp = ({ _seconds, _nanoseconds}) => {
                return new Date(_seconds * 1000 + (_nanoseconds ?? 0) / 1000000).toISOString();
            }
            ['created_at', 'updated_at', 'deleted_at', 'deleted_ttl'].forEach(timestampKey => {
                if (revision[timestampKey]?._seconds) {
                    revision[timestampKey] = convertFirestoreTimestamp(revision[timestampKey]);
                }
            });
        },

        processTtlForDisplay(milliseconds) {
            if (milliseconds === 0) return "TTL Expired - this Revision can be permanently removed.";
            const minutes = Math.floor(milliseconds / 1000 / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);

            return days > 1
                ? `${days} days`
                : days === 1
                    ? `${days} day, ${hours%24} hours`
                    : hours > 1
                        ? `${hours} hours`
                        : `${minutes} minute(s)`;
            },

        // full nested family tree array with parent pointers, not yet implemented in UI
        sortFamilyTree(revisionsArray, familyTree) {
            const rootLevel = familyTree.revisions;

            for (let i = revisionsArray.length - 1; i >= 0; i--) {
                if (revisionsArray[i].parent_revision === null) {
                    rootLevel.push({
                        ...revisionsArray[i],
                        siblings: [],
                        children: [],
                    });
                    revisionsArray.splice(i, 1);
                }
                rootLevel.sort((a, b) => a.created_at > b.created_at ? 1 : -1);
            }

            const findParent = (sourceRevision, targetArray, metadata) => {
                for (let i = targetArray.length - 1; i >= 0; i--) {
                    const targetRevision = targetArray[i];
                    if (targetRevision.id === sourceRevision.parent_revision) {
                        const newEntry = {
                            ...sourceRevision,
                            siblings: [],
                            children: [],
                        };
                        if (sourceRevision.name === targetRevision.name) targetRevision.children.push(newEntry);
                        else targetRevision.siblings.push(newEntry);
                        return true;
                    }
                    if (findParent(sourceRevision, targetRevision.siblings, metadata)) {
                        metadata.siblingDepth ++;
                        return true;
                    }
                    if (findParent(sourceRevision, targetRevision.children, metadata)) {
                        return true;
                    }
                }
                return false;
            };

            let previousStartLength = null;
            const metadata = {
                siblingDepth: 0,
                orphans: [],
            };

            while (revisionsArray.length) {
                if (revisionsArray.length === previousStartLength) break;
                previousStartLength = revisionsArray.length;
                for (let i = revisionsArray.length - 1; i >= 0; i--) {
                    if (findParent(revisionsArray[i], rootLevel, metadata)) {
                        revisionsArray.splice(i, 1);
                    }
                }
            }
            metadata.orphans.push(...revisionsArray);

            Object.assign(familyTree, metadata);
        },

        minorVersion(versionString) {
            const [_major, minor] = (versionString || '0.1').split('.');
            return parseInt(minor) || 0;
        },

        majorVersion(versionString) {
            const [major] = (versionString || '0.1').split('.');
            return parseInt(major) || 0;
        },

        updateFlowOptions() {
            this.copyRevisionOptions = Object.values(this.defaultFlowView).map(flow => ({ id: flow.id, name: flow.name }));
        },

        copyRevisionToFlow(flowId, revisionId, workingRevision) {
            this.copyRevision.fromRevisionId = revisionId;
            this.copyRevision.fromFlow = flowId;
            this.showCopyRevisionModal = true;
            this.copyRevision.working = workingRevision;
        },

        copyWorkingRevisionToFlow(flowId, revisionGroup) {
            this.copyRevision.fromRevisionId = revisionGroup.working_revision_id;
            this.copyRevision.fromFlow = flowId;
            this.copyRevision.working = true;
            this.showCopyRevisionModal = true;
        },

        validateCopyRevision() {
            const errors = [];
            this.errorMessage = null;
            if (!this.copyRevision.name) errors.push("The new Revision must have a name.");
            if (!this.copyRevision.toFlow) errors.push("Please select a Flow to copy to.");

            if (errors.length) {
                this.errorMessage = errors.join("\n");
                return false;
            }
            return true;
        },

        confirmCopyRevision() {
            if (this.saving) return;
            if (!this.validateCopyRevision()) return;
            this.saving = true;

            this.flowApi.copyRevision(this.copyRevision).then(resp => {
                if (resp.data?.data?.status) {
                    this.getFlows();
                    this.cancelCopyRevision();
                }
                else {
                    this.errorMessage = resp.data.data.message || 'An unknown error has occurred.'
                }
            }).catch((err) => {
                this.errorMessage = err.response?.data?.message ?? err.response?.message ?? 'An unknown error has occurred.'
            }).finally(() => {
                this.saving = false;
            });
        },

        cancelCopyRevision() {
            this.copyRevision = {
                fromFlow: null,
                toFlow: null,
                newRevisionName: null,
                newDescription: null,
                fromRevisionId: null,
                working: null,
            }
            this.showCopyRevisionModal = false;
            this.errorMessage = null;
        },

        deleteRevision(flowId, revisionGroupName, revisionId) {
            this.deletingRevision = {
                flowId,
                revisionGroupName,
                revisionId,
            };

            this.showDeleteModal = true;
        },

        deleteRevisionGroup(flowId, revisionGroupName) {
            this.deletingRevision = {
                flowId,
                revisionGroupName,
                deleteGroup: true,
            };

            this.showDeleteModal = true;
        },

        cancelDelete() {
            this.deletingRevision = {
                flowId: null,
                revisionGroupName: null,
                revisionId: null,
                deleteGroup: false,
            };

            this.showDeleteModal = false;
        },

        confirmDelete() {
            this.saving = true;
            if (this.deletingRevision.hardDelete) {
                this.flowApi.hardDeleteRevision(this.deletingRevision.flowId, this.deletingRevision.revisionId).then(resp => {
                    if (resp.data?.data?.status) {
                        this.getFlows();
                        this.cancelDelete();
                    }
                    else this.errorMessage = resp.response?.data?.message ?? resp.data?.message ?? 'An unknown error has occurred.'
                }).catch(err => {
                    this.errorMessage = err.response?.data?.message ?? err.data?.message ?? 'An unknown error has occurred.'
                }).finally(() => {
                    this.saving = false;
                    this.setRevisionGroupVisibility();
                });
            }
            else if (this.deletingRevision.deleteGroup)
                this.flowApi.deleteRevisionGroup(this.deletingRevision.flowId, this.deletingRevision.revisionGroupName).then(resp => {
                    if (resp.data?.data?.status) {
                        this.cancelDelete();
                        this.getFlows();
                    }
                    else this.errorMessage = resp.response?.data?.message ?? resp.data?.message ?? 'An unknown error has occurred.'
                }).catch(err => this.errorMessage = err.response?.data?.message ?? err.data?.message ?? 'An unknown error has occurred.')
                .finally(() => {
                    this.saving = false;
                    this.setRevisionGroupVisibility();
                });
            else {
                this.flowApi.deleteRevision(this.deletingRevision.flowId, this.deletingRevision.revisionId).then(resp => {
                    if (resp.data?.data?.status) {
                        this.cancelDelete();
                        this.getFlows();
                    }
                    else this.errorMessage = resp.response?.data?.message ?? resp.data?.message ?? 'An unknown error has occurred.'
                }).catch(err => this.errorMessage = err.response?.data?.message ?? err.data?.message ?? 'An unknown error has occurred.')
                .finally(() => {
                    this.saving = false;
                    this.setRevisionGroupVisibility();
                });
            }
        },

        renameRevisionGroup(flowId, revisionGroupName) {
            this.renamingRevisionGroup = {
                newName: revisionGroupName,
                oldName: revisionGroupName,
                flowId,
            };

            this.showRenameModal = true;
        },

        confirmRenameRevisionGroup() {
            this.errorMessage = null;
            if (this.validateRenameRevisionGroup()) {
                this.saving = true;
                this.flowApi.renameRevisionGroup(this.renamingRevisionGroup).then(resp => {
                    if (resp.data?.data?.status) {
                        this.getFlows();
                        this.showRenameModal = false;
                    }
                    else {
                        this.errorMessage = resp.response?.data?.message ?? resp.data?.message ?? 'An unknown error has occurred.'
                    }
                }).catch(err => {
                    this.errorMessage = err.response?.data?.message ?? err.data?.message ?? 'An unknown error has occurred.'
                }).finally(() => {
                    this.saving = false;
                });
            }
        },

        cancelRenameRevisionGroup() {
            this.renamingRevisionGroup = {
                oldName: null,
                newName: "",
                flowId: null,
            };
            this.showRenameModal = false;
        },

        validateRenameRevisionGroup() {
            const errors = [];
            if (!this.renamingRevisionGroup.flowId || !this.renamingRevisionGroup.oldName) errors.push("The selected Flow Revision is invalid.");
            if (this.renamingRevisionGroup.newName.trim().length < 4) errors.push("Please enter at least 4 characters.");

            const flowNames = Object.keys(this.defaultFlowView[this.renamingRevisionGroup.flowId]?.revisions);
            const newName = this.renamingRevisionGroup.newName.trim().toLowerCase();
            flowNames.forEach(name => {
                if (name.trim().toLowerCase() === newName) errors.push("Please enter a unique name.");
            });

            if (errors.length) {
                this.errorMessage = errors.join("\n");
                return false;
            }
            else return true;
        },

        undeleteRevision(flowId, revisionId) {
            this.saving = true;
            this.flowApi.undeleteRevision(flowId, revisionId).then(resp => {
                if (resp.data?.data?.status) this.getFlows();
                else this.errorMessage = resp.response?.data?.message ?? resp.data?.message ?? 'An unknown error has occurred.'
            }).catch(err => {
                this.errorMessage = err.response?.data?.message ?? err.data?.message ?? 'An unknown error has occurred.'
            }).finally(() => {
                this.saving = false;
                this.setRevisionGroupVisibility();
            });
        },

        hardDeleteRevision(flowId, revisionId) {
            this.deletingRevision = {
                flowId,
                revisionId,
                hardDelete: true,
            };

            this.showDeleteModal = true;
        },

        getRevisionName(flowId, revisionGroupName, revisionId) {
            const target = this.defaultFlowView[flowId]?.revisions[revisionGroupName]?.versions?.find(version => version.id === revisionId);
            return target
                ? `"${revisionGroupName}" v${target.version}`
                : 'this Revision version';
        },

        getSortedRevisions(flow) {
            return Object.entries(flow.revisions ?? {}).sort((a, b) =>
                a[0].localeCompare(b[0])
            );
        }
    },
    watch: {
        showTrashed() {
            this.setRevisionGroupVisibility();
        },
    }
}
</script>
