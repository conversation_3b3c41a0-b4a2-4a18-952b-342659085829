import {BaseApiService} from './base.js'

export class DummyApiService extends BaseApiService {
    constructor() {
        super("DummyApiService")
        this.defaultHeaders = {
            'content-type': 'application/json',
        };
        this.delay = 500
        this.configurableEngineVariablesData = [
            {
                id: 1,
                state: "California",
                state_initials: "CA",
                county: "Los Angeles",
                engine: "V8",
                variable_key: "emissions",
                value: 123.45,
                created_at: "2025-05-10T10:00:00Z",
                updated_at: "2025-05-10T12:00:00Z",

            },
            {
                id: 2,
                state: "Texas",
                state_initials: "TX",
                county: "Harris",
                engine: "V6",
                variable_key: "fuel_efficiency",
                value: 29.1,
                created_at: "2025-05-09T09:15:00Z",
                updated_at: "2025-05-09T11:10:00Z",

            },
            {
                id: 3,
                state: "Florida",
                state_initials: "FL",
                county: "Miami-Dade",
                engine: "Electric",
                variable_key: "battery_range",
                value: 310,
                created_at: "2025-05-08T08:45:00Z",
                updated_at: "2025-05-08T09:30:00Z",

            },
            {
                id: 4,
                state: "New York",
                state_initials: "NY",
                county: "Queens",
                engine: "Hybrid",
                variable_key: "co2_output",
                value: 85.2,
                created_at: "2025-05-07T07:30:00Z",
                updated_at: "2025-05-07T08:45:00Z",

            },
            {
                id: 5,
                state: "Illinois",
                state_initials: "IL",
                county: "Cook",
                engine: "V6",
                variable_key: "fuel_efficiency",
                value: 26.7,
                created_at: "2025-05-06T06:20:00Z",
                updated_at: "2025-05-06T07:00:00Z",

            },
            {
                id: 6,
                state: "Georgia",
                state_initials: "GA",
                county: "Fulton",
                engine: "Electric",
                variable_key: "charging_speed",
                value: 120,
                created_at: "2025-05-05T05:10:00Z",
                updated_at: "2025-05-05T06:20:00Z",

            },
            {
                id: 7,
                state: "Ohio",
                state_initials: "OH",
                county: "Franklin",
                engine: "V8",
                variable_key: "emissions",
                value: 140.3,
                created_at: "2025-05-04T04:00:00Z",
                updated_at: "2025-05-04T04:45:00Z",

            },
            {
                id: 8,
                state: "Michigan",
                state_initials: "MI",
                county: "Wayne",
                engine: "Hybrid",
                variable_key: "fuel_efficiency",
                value: 33.5,
                created_at: "2025-05-03T03:30:00Z",
                updated_at: "2025-05-03T04:15:00Z",

            },
            {
                id: 9,
                state: "Arizona",
                state_initials: "AZ",
                county: "Maricopa",
                engine: "Electric",
                variable_key: "battery_range",
                value: 280,
                created_at: "2025-05-02T02:45:00Z",
                updated_at: "2025-05-02T03:20:00Z",

            },
            {
                id: 10,
                state: "Pennsylvania",
                state_initials: "PA",
                county: "Philadelphia",
                engine: "V6",
                variable_key: "co2_output",
                value: 91.7,
                created_at: "2025-05-01T01:15:00Z",
                updated_at: "2025-05-01T02:00:00Z",

            }
        ]
        this.engineOptions = [
            {id: "v8", name: "V8"},
            {id: "v6", name: "V6"},
            {id: "electric", name: "Electric"},
            {id: "hybrid", name: "Hybrid"},
        ]
        this.stateCountyOptions = [
            {
                "id": "CA",
                "name": "California",
                "counties": [
                    {"id": "los-angeles", "name": "Los Angeles"},
                    {"id": "san-diego", "name": "San Diego"},
                    {"id": "orange", "name": "Orange"}
                ]
            },
            {
                "id": "TX",
                "name": "Texas",
                "counties": [
                    {"id": "harris", "name": "Harris"},
                    {"id": "dallas", "name": "Dallas"},
                    {"id": "tarrant", "name": "Tarrant"}
                ]
            },
            {
                "id": "FL",
                "name": "Florida",
                "counties": [
                    {"id": "miami-dade", "name": "Miami-Dade"},
                    {"id": "broward", "name": "Broward"},
                    {"id": "palm-beach", "name": "Palm Beach"}
                ]
            },
            {
                "id": "NY",
                "name": "New York",
                "counties": [
                    {"id": "queens", "name": "Queens"},
                    {"id": "kings", "name": "Kings"},
                    {"id": "new-york-county", "name": "New York County"}
                ]
            },
            {
                "id": "IL",
                "name": "Illinois",
                "counties": [
                    {"id": "cook", "name": "Cook"},
                    {"id": "dupage", "name": "DuPage"},
                    {"id": "lake", "name": "Lake"}
                ]
            },
            {
                "id": "GA",
                "name": "Georgia",
                "counties": [
                    {"id": "fulton", "name": "Fulton"},
                    {"id": "dekalb", "name": "DeKalb"},
                    {"id": "gwinnett", "name": "Gwinnett"}
                ]
            },
            {
                "id": "OH",
                "name": "Ohio",
                "counties": [
                    {"id": "franklin", "name": "Franklin"},
                    {"id": "cuyahoga", "name": "Cuyahoga"},
                    {"id": "hamilton", "name": "Hamilton"}
                ]
            },
            {
                "id": "MI",
                "name": "Michigan",
                "counties": [
                    {"id": "wayne", "name": "Wayne"},
                    {"id": "oakland", "name": "Oakland"},
                    {"id": "macomb", "name": "Macomb"}
                ]
            },
            {
                "id": "AZ",
                "name": "Arizona",
                "counties": [
                    {"id": "maricopa", "name": "Maricopa"},
                    {"id": "pima", "name": "Pima"},
                    {"id": "pinal", "name": "Pinal"}
                ]
            },
            {
                "id": "PA",
                "name": "Pennsylvania",
                "counties": [
                    {"id": "philadelphia", "name": "Philadelphia"},
                    {"id": "allegheny", "name": "Allegheny"},
                    {"id": "montgomery", "name": "Montgomery"}
                ]
            }
        ];
    }

    getStateCountyOptions() {
        return this.mockResponse({
            data: this.stateCountyOptions
        })
    }

    getEngineOptions() {
        return this.mockResponse({
            data: this.engineOptions
        })
    }

    getConfigurableEngineVariables(filters = {}) {
        const {state, county, engine, variable_key: variableKey} = filters;

        const filtered = this.configurableEngineVariablesData.filter(item => {
            const match = (field, pattern) => {
                if (!pattern) return true;
                const regex = new RegExp(pattern, 'i');
                return regex.test(item[field] || '');
            };

            return (
                match('state_initials', state) &&
                match('county', county) &&
                match('engine', engine) &&
                match('variable_key', variableKey)
            );
        });

        return this.mockResponse({
            data: filtered
        });
    }


    deleteConfigurableEngineVariable(id) {
        const index = this.configurableEngineVariablesData.findIndex(e => e.id === id)

        if (index < 0) {
            throw this.mockResponse({
                message: 'Not found'
            }, 404)
        }

        this.configurableEngineVariablesData.splice(index, 1)
        return this.mockResponse()
    }

    saveConfigurableEngineVariable(payload) {
        if (payload.id) {
            this.updateConfigurableEngineVariable(payload.id, payload)
        } else {
            this.configurableEngineVariablesData.push({
                ...payload,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                id: this.configurableEngineVariablesData.length + 1,
            })
        }
    }

    mockResponse(data, status = 200, config = {}) {
        return new Promise((resolve) => {
            setTimeout(() => resolve({
                data,
                status,
                statusText: status === 200 ? 'OK' : 'Error',
                headers: this.defaultHeaders,
                config,
            }), this.delay);
        });
    }

    updateConfigurableEngineVariable(id, payload) {
        const index = this.configurableEngineVariablesData.findIndex(e => e.id === id)

        if (index < 0) {
            throw this.mockResponse({
                message: 'Not found'
            }, 404)
        }

        this.configurableEngineVariablesData[index] = {
            ...this.configurableEngineVariablesData[index],
            ...payload,
            updated_at: new Date().toISOString(),
        }

        return this.mockResponse({})
    }
}
