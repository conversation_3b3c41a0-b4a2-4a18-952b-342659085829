import axios from 'axios';
import {BaseApiService} from "./base";

export class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getConfigurableEngineVariables(params) {
        return this.axios().get('/', {
            params
        })
    }

    deleteConfigurableEngineVariable(id) {
        return this.axios().delete(`${id}`)
    }

    saveConfigurableEngineVariable(payload) {
        return this.axios().post('/', payload)
    }
}
