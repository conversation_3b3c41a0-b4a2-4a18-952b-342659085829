import {DummyApiService} from "./dummy";
import {ApiService} from "./api"

class ApiFactory {
    static makeApiService(driver = 'dummy') {
        switch(driver) {
            case 'api':
                return new ApiService('internal-api', 'flow-engines/configurable-variables', 2);
            case 'dummy':
            default:
                return new DummyApiService();
        }
    }
}

export { ApiFactory };
