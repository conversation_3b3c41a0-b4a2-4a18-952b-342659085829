
<template>
    <div class="w-full flex-auto pt-3 relative"
         :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
        <div :class="[darkMode ? 'text-white' : 'text-slate-900']">
            <div class="lg:col-span-4 border rounded-lg"
                 :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                <div class="p-5 flex items-center justify-between">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Sent Notifications</h5>
                    <div class="flex">
                        <button
                            @click="showFilters = !showFilters"
                            class="flex justify-between mr-3 text-sm font-medium py-2 px-5 transition duration-200 focus:outline-none rounded-md "
                            :class="{'hover:bg-primary-100 bg-light-background text-slate-800': !darkMode, 'bg-grey-600 hover:bg-grey-500 text-white': darkMode}"
                        >
                            Filters
                            <FilterIcon class="ml-3 h-5 w-5"/>
                        </button>
                    </div>
                </div>
                <div>
                    <NotificationsSearch v-show="showFilters" :dark-mode="darkMode" />
                </div>
                <div class="grid grid-cols-10 mb-2 px-5 pt-3 gap-3">
                    <p class="col-span-2 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                       :class="opportunityNotificationsStore.notificationFilters.sort_col === 'company_name' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                       @click="opportunityNotificationsStore.sortNotifications('company_name')">
                        Company Name
                        <span
                            v-if="opportunityNotificationsStore.notificationFilters.sort_col === 'company_name'"
                            class="ml-2 transform transition-all duration-200 w-6 fill-current"
                            :class="{
                                    'text-blue-550 rotate-180': opportunityNotificationsStore.notificationFilters.sort_col === 'company_name' && opportunityNotificationsStore.notificationFilters.sort_dir === 'asc',
                                    'text-blue-550': opportunityNotificationsStore.notificationFilters.sort_col === 'company_name' && opportunityNotificationsStore.notificationFilters.sort_dir !== 'asc',
                                    'hover:text-cyan-400': opportunityNotificationsStore.notificationFilters.sort_col !== 'company_name'
                                }">
                                <ChevronDownIcon />
                            </span>
                    </p>
                    <p class="col-span-2 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                       :class="opportunityNotificationsStore.notificationFilters.sort_col === 'config_name' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                       @click="opportunityNotificationsStore.sortNotifications('config_name')">
                        Configuration
                        <span
                            v-if="opportunityNotificationsStore.notificationFilters.sort_col === 'config_name'"
                            class="ml-2 transform transition-all duration-200 w-6 fill-current"
                            :class="{
                                    'text-blue-550 rotate-180': opportunityNotificationsStore.notificationFilters.sort_col === 'config_name' && opportunityNotificationsStore.notificationFilters.sort_dir === 'asc',
                                    'text-blue-550': opportunityNotificationsStore.notificationFilters.sort_col === 'config_name' && opportunityNotificationsStore.notificationFilters.sort_dir !== 'asc',
                                    'hover:text-cyan-400': opportunityNotificationsStore.notificationFilters.sort_col !== 'config_name'
                                }">
                                <ChevronDownIcon />
                            </span>
                    </p>
                    <p class="col-span-3 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                       :class="opportunityNotificationsStore.notificationFilters.sort_col === 'recipients' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                       @click="opportunityNotificationsStore.sortNotifications('recipients')">
                        Recipients
                        <span
                            v-if="opportunityNotificationsStore.notificationFilters.sort_col === 'recipients'"
                            class="ml-2 transform transition-all duration-200 w-6 fill-current"
                            :class="{
                                    'text-blue-550 rotate-180': opportunityNotificationsStore.notificationFilters.sort_col === 'recipients' && opportunityNotificationsStore.notificationFilters.sort_dir === 'asc',
                                    'text-blue-550': opportunityNotificationsStore.notificationFilters.sort_col === 'recipients' && opportunityNotificationsStore.notificationFilters.sort_dir !== 'asc',
                                    'hover:text-cyan-400': opportunityNotificationsStore.notificationFilters.sort_col !== 'recipients'
                                }">
                                <ChevronDownIcon />
                            </span>
                    </p>
                    <p class="col-span-2 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                       :class="opportunityNotificationsStore.notificationFilters.sort_col === 'sent_at' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                       @click="opportunityNotificationsStore.sortNotifications('sent_at')">
                        Sent At
                        <span
                            v-if="opportunityNotificationsStore.notificationFilters.sort_col === 'sent_at'"
                            class="ml-2 transform transition-all duration-200 w-6 fill-current"
                            :class="{
                                    'text-blue-550 rotate-180': opportunityNotificationsStore.notificationFilters.sort_col === 'sent_at' && opportunityNotificationsStore.notificationFilters.sort_dir === 'asc',
                                    'text-blue-550': opportunityNotificationsStore.notificationFilters.sort_col === 'sent_at' && opportunityNotificationsStore.notificationFilters.sort_dir !== 'asc',
                                    'hover:text-cyan-400': opportunityNotificationsStore.notificationFilters.sort_col !== 'sent_at'
                                }">
                                <ChevronDownIcon />
                            </span>
                    </p>
                    <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs justify-center">Actions</p>
                </div>
                <div class="flex items-center justify-center h-100" v-if="opportunityNotificationsStore.loadingNotifications">
                    <loading-spinner></loading-spinner>
                </div>
                <div class="border-t border-b h-100 overflow-y-auto" v-else
                     :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                    <div>
                        <div
                            v-if="!opportunityNotificationsStore.notifications.length"
                            class="w-full flex rounded-lg p-4 mb-3 justify-center"
                            :class="[darkMode ? 'text-gray-400' : 'bg-blue-100 text-blue-800']"
                        >
                            <InformationCircleIcon style="width:20px; height:20px;" />
                            <p class="ml-4 text-sm font-medium">
                                No notifications to show
                            </p>
                        </div>
                        <div v-for="notification in opportunityNotificationsStore.notifications" :key="notification.id"
                             class="grid grid-cols-10 pt-3 px-5 py-3 gap-3 border-b"
                             :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                            <p class="text-sm col-span-2">
                                <a
                                    class="font-medium"
                                    :class="{'text-cyan-600': !darkMode, 'text-cyan-400': darkMode}"
                                    :href="'/companies/' + notification.company_id">
                                    {{ notification.company_name }} : {{ notification.company_id }}
                                </a>
                            </p>
                            <p class="text-sm col-span-2">
                                {{ notification.config_name }}
                            </p>
                            <p :title="notification.recipients" class="text-sm col-span-3 truncate">
                                {{ notification.recipients }}
                            </p>
                            <p class="text-sm col-span-2">
                                {{ notification.sent_at ? $filters.dateFromTimestamp(notification.sent_at, 'usWithTime') : '' }}
                            </p>
                            <p class="ml-2 col-span-1 text-center">
                                <button @click="viewNotification(notification)">
                                    <EyeIcon class="w-6 h-6 text-primary-500" />
                                </button>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="p-3">
                    <Pagination
                        v-if="opportunityNotificationsStore.notificationPaginationData"
                        :dark-mode="darkMode"
                        :show-pagination="true"
                        :pagination-data="opportunityNotificationsStore.notificationPaginationData"
                        @change-page="opportunityNotificationsStore.handleNotificationPaginationEvent"></Pagination>
                </div>
                <NotificationModal
                    :dark-mode="darkMode"
                    :show-modal="showModal"
                    :notification="viewingNotification"
                    @close="closeModal"
                />
            </div>
        </div>
    </div>
</template>

<script>
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import {CheckCircleIcon, ChevronDownIcon, InformationCircleIcon, PencilIcon, XCircleIcon, EyeIcon} from "@heroicons/vue/solid";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import Pagination from "../../Shared/components/Pagination.vue";
import {FilterIcon} from "@heroicons/vue/outline";
import {useOpportunityNotificationStore} from "../../../../stores/opportunity-notification.store.js";
import NotificationsSearch from "./NotificationsSearch.vue";
import BundleInvoiceModal from "../../BundleManagement/Components/BundleInvoiceModal.vue";
import NotificationModal from "./NotificationModal.vue";

export default {
    name: "OpportunityNotifications",
    components: {
        BundleInvoiceModal,
        EyeIcon,
        FilterIcon,
        Pagination,
        XCircleIcon,
        ChevronDownIcon,
        LoadingSpinner,
        CheckCircleIcon,
        InformationCircleIcon,
        AlertsContainer,
        NotificationsSearch,
        NotificationModal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            opportunityNotificationsStore: useOpportunityNotificationStore(),
            showFilters: false,
            showModal: false,
            viewingNotification: {},
        }
    },
    mounted() {
    },
    created() {
    },
    methods: {
        viewNotification(notification) {
            this.viewingNotification = {...notification};
            this.showModal = true;
        },
        closeModal() {
            this.showModal = false;
        },
    }
}
</script>

<style scoped>

</style>
