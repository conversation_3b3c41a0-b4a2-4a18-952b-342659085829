<template>
    <Modal
        v-if="showModal"
        :container-classes="'overflow-scroll max-h-[70vh] p-8'"
        @close="closeModal"
        :hide-confirm="true"
        :close-text="'Close'"
        :dark-mode="darkMode"
        :small="false">
        <template v-slot:header>
            <h4 class="text-xl">View Opportunity Notification</h4>
        </template>
        <template v-slot:content>
            <div class="notification--edit-modal" :class="darkMode ? 'darkmode' : ''">
                <div class="w-full flex rounded-lg bg-blue-100 p-4 mb-3 text-blue-800">
                    <InformationCircleIcon class="h-5 w-5"/>
                    <p class="ml-4 text-sm font-medium">
                        Opportunity Notification fields are read-only.
                    </p>
                </div>
                <div>
                    <div class="mb-4">
                        <div class="grid grid-cols-2 gap-x-3">
                            <div class="mb-4">
                                <label class="block text-sm font-medium leading-6"
                                       :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                    Recipient Company
                                </label>
                                <input
                                    disabled
                                    :value="notification.company_name"
                                    type="text"
                                    placeholder="Company name"
                                    class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                />
                            </div>
                            <div class="mb-4">
                                <label for="notification-recipients"
                                       class="block text-sm font-medium leading-6"
                                       :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                    Recipient Addresses
                                </label>
                                <input
                                    id="notification-recipients"
                                    name="notification-recipients"
                                    :value="notification.recipients"
                                    disabled
                                    class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="text"
                                />
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium leading-6"
                                       :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                    Notification Config
                                </label>
                                <input
                                    disabled
                                    :value="notification.config_name"
                                    type="text"
                                    placeholder="Config Name"
                                    class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                />
                            </div>
                            <div class="mb-4">
                                <label for="notification-sent-at"
                                       class="block text-sm font-medium leading-6"
                                       :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                    Sent At
                                </label>
                                <input
                                    id="notification-sent-at"
                                    name="notification-sent-at"
                                    :value="$filters.dateFromTimestamp(notification.sent_at, 'usWithTime', 'US/Mountain')"
                                    disabled
                                    class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="text"
                                />
                            </div>
                        </div>
                        <label for="content"
                               class="block text-sm font-medium leading-6"
                               :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                            Content
                        </label>
                        <iframe width="100%" height="500px" :srcdoc="notification.content"></iframe>
                    </div>
                    <div v-if="modalError" class="lg:col-span-4 rounded-lg bg-red-100 p-4 mb-3">
                        <p class="text-sm font-medium text-red-800">{{ modalError }}</p>
                    </div>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import {dateFromTimestamp} from "../../../../modules/helpers";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import Tab from "../../Shared/components/Tab.vue";
import {InformationCircleIcon} from "@heroicons/vue/solid";
import {useOpportunityNotificationStore} from "../../../../stores/opportunity-notification.store.js";


export default {
    name: "OpportunityNotificationModal",
    components: {
        InformationCircleIcon,
        Autocomplete,
        Dropdown,
        Modal,
        Tab,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        showModal: {
            type: Boolean,
            default: false
        },
        notification: {
            type: Object,
            default: {}
        }
    },
    emits: ['close'],
    data() {
        return {
            modalError: null,
            notificationStore: useOpportunityNotificationStore(),
        }
    },
    created() {
    },
    watch: {
        notification(newVal, oldVal) {
            console.log(newVal)
            console.log(oldVal)
        }
    },
    methods: {
        dateFromTimestamp,
        closeModal() {
            this.$emit('close');
            this.activeModalTab = 'General';
        },
    }
}
</script>

<style lang="postcss" scoped>
.notification--edit-modal input:disabled {
    @apply bg-slate-50 text-slate-500 border-slate-200 shadow-none;
}
.notification--edit-modal input {
    @apply border-grey-350 bg-light-module;
}
.notification--edit-modal.darkmode input {
    @apply border-grey-350 bg-light-module;
}
.notification--edit-modal.darkmode input:disabled {
    @apply bg-dark-background text-blue-100 border-blue-800 shadow-none;
}
</style>
