<template>
    <div>
        <alerts-container v-if="notificationStore.notificationConfigError" :alert-type="'error'" :text="notificationStore.notificationConfigError" :dark-mode="darkMode"/>

        <div class="border-y"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div>
                <div class="p-5">
                    <div class="grid grid-cols-4 gap-3">
                        <div>
                            <input
                                id="config-search-name"
                                name="config-search-name"
                                v-model="notificationStore.notificationConfigFilters.name"
                                class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                placeholder="Name"
                                type="text"
                                :class="{
                                'border-grey-350 bg-light-module': !this.darkMode,
                                'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                            }"
                            />
                        </div>
                        <div>
                            <select
                                id="config-search-frequency"
                                name="config-search-frequency"
                                v-model="notificationStore.notificationConfigFilters.frequency"
                                class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                                :class="{
                                    'text-gray-900': !this.darkMode,
                                    'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                                }">
                                <option :value="''">All Frequencies</option>
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="annually">Annually</option>
                            </select>
                        </div>
                        <div>
                            <select
                                id="config-search-send-day"
                                name="config-search-send-day"
                                v-model="notificationStore.notificationConfigFilters.send_day"
                                class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                                :class="{
                                    'text-gray-900': !this.darkMode,
                                    'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                                }">
                                <option  :value="''">All Days</option>
                                <option value="Monday">Monday</option>
                                <option value="Tuesday">Tuesday</option>
                                <option value="Wednesday">Wednesday</option>
                                <option value="Thursday">Thursday</option>
                                <option value="Friday">Friday</option>
                                <option value="Saturday">Saturday</option>
                                <option value="Sunday">Sunday</option>
                            </select>
                        </div>
                        <div>
                            <select
                                id="config-search-active"
                                name="config-search-active"
                                v-model="notificationStore.notificationConfigFilters.active"
                                class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                                :class="{
                                    'text-gray-900': !this.darkMode,
                                    'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                                }">
                                <option :value="null">Active & Inactive</option>
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                        </div>
                        <div class="flex flex-row justify-start col-span-2">
                            <button
                                type="button"
                                @click="notificationStore.searchNotificationConfigs(1)"
                                class="px-4 py-2 h-full ml-1 mr-4 rounded-lg text-white font-medium bg-primary-500 hover:bg-blue-500 transition duration-200 inline-flex items-center">
                                <SearchIcon class="h-5 w-5"/>
                            </button>
                            <button
                                type="button"
                                @click="notificationStore.resetNotificationConfigFilters()"
                                :class="[!darkMode ? 'bg-light-background hover:bg-light-background text-slate-900' : 'bg-grey-600 hover:bg-grey-500 text-slate-100']"
                                class="px-4 py-2 h-full mr-5 rounded-lg text-white font-medium  transition duration-200 inline-flex items-center">
                                <RefreshIcon class="h-5 w-5"/>
                            </button>
                            <select
                                id="config-search-per-page"
                                name="config-search-per-page"
                                v-model="notificationStore.notificationConfigFilters.per_page"
                                class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                                :class="{
                                'text-gray-900': !this.darkMode,
                                'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                            }">
                                <option value="25">25/page</option>
                                <option value="50">50/page</option>
                                <option value="100">100/page</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import {RefreshIcon, SearchIcon} from "@heroicons/vue/solid";
import {useOpportunityNotificationStore} from "../../../../stores/opportunity-notification.store.js";

export default {
    name: "OpportunityNotificationConfigsSearch",
    components: {
        RefreshIcon,
        SearchIcon,
        AlertsContainer
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data: function() {
        return {
            notificationStore: useOpportunityNotificationStore(),
        };
    },
    created: function() {
        this.notificationStore.resetNotificationConfigFilters();
    }
}
</script>
