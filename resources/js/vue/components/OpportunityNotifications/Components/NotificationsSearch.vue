<template>
    <div>
        <alerts-container v-if="notificationStore.notificationError" :alert-type="'error'" :text="notificationStore.notificationError" :dark-mode="darkMode"/>

        <div class="border-y"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div>
                <div class="p-5">
                    <div class="grid grid-cols-6 gap-3">
                        <div>
                            <autocomplete
                                :dark-mode="darkMode"
                                :inputClass="[
                                    'block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6',
                                    !darkMode ? 'border-grey-350 bg-light-module' : 'border-blue-400 bg-dark-background text-blue-400'
                                ]"
                                v-model="notificationStore.notificationFilters.company_name"
                                :options="notificationStore.companyNameResults"
                                placeholder="Company Name"
                                :create-user-input-option="false"
                                @search="notificationStore.searchCompanyNames('name', $event)" />
                        </div>
                        <div>
                            <autocomplete
                                :dark-mode="darkMode"
                                :inputClass="[
                                    'block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6',
                                    !darkMode ? 'border-grey-350 bg-light-module' : 'border-blue-400 bg-dark-background text-blue-400'
                                ]"
                                v-model="notificationStore.notificationFilters.configuration_id"
                                :options="notificationStore.configurationOptions"
                                placeholder="Configuration"
                                :create-user-input-option="false"
                                @search="notificationStore.searchConfigurations($event)" />
                        </div>
                        <div>
                            <input
                                id="notification-search-recipients"
                                name="notification-search-recipients"
                                v-model="notificationStore.notificationFilters.recipients"
                                class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                :class="{
                                    'border-grey-350 bg-light-module': !this.darkMode,
                                    'border-blue-400 bg-dark-background text-blue-400': this.darkMode,
                                }"
                                placeholder="Recipients"
                                type="text"
                            />
                        </div>
                        <div>
                            <Datepicker
                                placeholder="Sent At"
                                v-model="notificationStore.notificationFilters.sent_at"
                                :range="true"
                                :input-class-name="'notification--filter-date ' + (this.darkMode ? 'dark' : '')"
                                :enable-time-picker="false" />
                        </div>
                        <div class="flex flex-row justify-start col-span-2">
                            <button
                                type="button"
                                @click="notificationStore.searchNotifications(1)"
                                class="px-4 py-2 h-full mr-3 text-sm rounded-lg text-white font-medium bg-primary-500 hover:bg-blue-500 transition duration-200 inline-flex items-center">
                                <SearchIcon class="mr-3 h-5 w-5"/>
                                Search
                            </button>
                            <button
                                type="button"
                                @click="notificationStore.resetNotificationFilters()"
                                :class="[!darkMode ? 'bg-light-background hover:bg-light-background text-slate-900' : 'bg-grey-600 hover:bg-grey-500 text-slate-100']"
                                class="px-4 py-2 h-full mr-3 text-sm rounded-lg text-white font-medium  transition duration-200 inline-flex items-center">
                                <RefreshIcon class="mr-3 h-5 w-5"/>
                                Reset
                            </button>
                            <select
                                id="notifications-search-per-page"
                                name="notifications-search-per-page"
                                v-model="notificationStore.notificationFilters.per_page"
                                class="z-30 truncate cursor-pointer uppercase block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6"
                                :class="{
                                    'text-gray-900': !this.darkMode,
                                    'hover:border-blue-400 border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                                }">
                                <option value="25">25/page</option>
                                <option value="50">50/page</option>
                                <option value="100">100/page</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import {RefreshIcon, SearchIcon} from "@heroicons/vue/solid";
import {useOpportunityNotificationStore} from "../../../../stores/opportunity-notification.store.js";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import Datepicker from "@vuepic/vue-datepicker";

export default {
    name: "OpportunityNotificationsSearch",
    components: {
        Datepicker, Autocomplete,
        RefreshIcon,
        SearchIcon,
        AlertsContainer
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data: function() {
        return {
            notificationStore: useOpportunityNotificationStore(),
        };
    },
    created: function() {
        this.notificationStore.resetNotificationFilters();
    }
}
</script>

<style lang="postcss" scoped>

:deep(.notification--filter-date) {
    @apply block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6;
}

:deep(.notification--filter-date.dark) {
    @apply bg-dark-background text-blue-400;
}

</style>
