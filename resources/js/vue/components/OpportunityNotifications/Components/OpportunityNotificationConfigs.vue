<template>
    <alerts-container v-if="opportunityNotificationsStore.notificationConfigError" alert-type="error" :text="opportunityNotificationsStore.notificationConfigError" :dark-mode="darkMode"/>
    <div class="w-full flex-auto pt-3 relative"
         :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
        <div :class="[darkMode ? 'text-white' : 'text-slate-900']">
            <div class="lg:col-span-4 border rounded-lg"
                 :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                <div class="p-5 flex items-center justify-between">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Notification Configs</h5>
                    <div class="flex">
                        <button
                            @click="showFilters = !showFilters"
                            class="flex justify-between mr-3 text-sm font-medium py-2 px-5 transition duration-200 focus:outline-none rounded-md "
                            :class="{'hover:bg-primary-100 bg-light-background text-slate-800': !darkMode, 'bg-grey-600 hover:bg-grey-500 text-white': darkMode}"
                        >
                            Filters
                            <FilterIcon class="ml-3 h-5 w-5"/>
                        </button>
                        <button
                            @click="createConfig"
                            class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                        >
                            Create Config
                        </button>
                    </div>
                </div>
                <div>
                    <ConfigsSearch v-show="showFilters" :dark-mode="darkMode" />
                </div>
                <div class="grid grid-cols-8 mb-2 px-5 pt-3 gap-3">
                    <p class="col-span-2 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                       :class="opportunityNotificationsStore.notificationConfigFilters.sort_col === 'name' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                       @click="opportunityNotificationsStore.sortNotificationConfigs('name')">
                        Name
                        <span
                            v-if="opportunityNotificationsStore.notificationConfigFilters.sort_col === 'name'"
                            class="ml-2 transform transition-all duration-200 w-6 fill-current"
                            :class="{
                                'text-blue-550 rotate-180': opportunityNotificationsStore.notificationConfigFilters.sort_col === 'name' && opportunityNotificationsStore.notificationConfigFilters.sort_dir === 'asc',
                                'text-blue-550': opportunityNotificationsStore.notificationConfigFilters.sort_col === 'name' && opportunityNotificationsStore.notificationConfigFilters.sort_dir !== 'asc',
                                'hover:text-cyan-400': opportunityNotificationsStore.notificationConfigFilters.sort_col !== 'name'
                            }">
                            <ChevronDownIcon />
                        </span>
                    </p>
                    <p class="flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                       :class="opportunityNotificationsStore.notificationConfigFilters.sort_col === 'name' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                       @click="opportunityNotificationsStore.sortNotificationConfigs('name')">
                        Type
                    </p>
                    <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                       :class="opportunityNotificationsStore.notificationConfigFilters.sort_col === 'frequency' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                       @click="opportunityNotificationsStore.sortNotificationConfigs('frequency')">
                        Max Frequency
                        <span
                            v-if="opportunityNotificationsStore.notificationConfigFilters.sort_col === 'frequency'"
                            class="ml-2 transform transition-all duration-200 w-6 fill-current"
                            :class="{
                                'text-blue-550 rotate-180': opportunityNotificationsStore.notificationConfigFilters.sort_col === 'frequency' && opportunityNotificationsStore.notificationConfigFilters.sort_dir === 'asc',
                                'text-blue-550': opportunityNotificationsStore.notificationConfigFilters.sort_col === 'frequency' && opportunityNotificationsStore.notificationConfigFilters.sort_dir !== 'asc',
                                'hover:text-cyan-400': opportunityNotificationsStore.notificationConfigFilters.sort_col !== 'frequency'
                            }">
                            <ChevronDownIcon />
                        </span>
                    </p>
                    <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                       :class="opportunityNotificationsStore.notificationConfigFilters.sort_col === 'send_day' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                       @click="opportunityNotificationsStore.sortNotificationConfigs('send_day')">
                        Send Day
                        <span
                            v-if="opportunityNotificationsStore.notificationConfigFilters.sort_col === 'send_day'"
                            class="ml-2 transform transition-all duration-200 w-6 fill-current"
                            :class="{
                                    'text-blue-550 rotate-180': opportunityNotificationsStore.notificationConfigFilters.sort_col === 'send_day' && opportunityNotificationsStore.notificationConfigFilters.sort_dir === 'asc',
                                    'text-blue-550': opportunityNotificationsStore.notificationConfigFilters.sort_col === 'send_day' && opportunityNotificationsStore.notificationConfigFilters.sort_dir !== 'asc',
                                    'hover:text-cyan-400': opportunityNotificationsStore.notificationConfigFilters.sort_col !== 'send_day'
                                }">
                                <ChevronDownIcon />
                            </span>
                    </p>
                    <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs"
                       :class="opportunityNotificationsStore.notificationConfigFilters.sort_col === 'activated_at' ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400'"
                       @click="opportunityNotificationsStore.sortNotificationConfigs('activated_at')">
                        Active
                        <span
                            v-if="opportunityNotificationsStore.notificationConfigFilters.sort_col === 'activated_at'"
                            class="ml-2 transform transition-all duration-200 w-6 fill-current"
                            :class="{
                                    'text-blue-550 rotate-180': opportunityNotificationsStore.notificationConfigFilters.sort_col === 'activated_at' && opportunityNotificationsStore.notificationConfigFilters.sort_dir === 'asc',
                                    'text-blue-550': opportunityNotificationsStore.notificationConfigFilters.sort_col === 'activated_at' && opportunityNotificationsStore.notificationConfigFilters.sort_dir !== 'asc',
                                    'hover:text-cyan-400': opportunityNotificationsStore.notificationConfigFilters.sort_col !== 'activated_at'
                                }">
                                <ChevronDownIcon />
                            </span>
                    </p>
                    <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs">Actions</p>
                    <p class="col-span-1 flex items-center text-slate-400 font-medium tracking-wide uppercase text-xs">Total sent</p>
                </div>
                <div class="flex items-center justify-center h-100" v-if="opportunityNotificationsStore.loadingNotificationConfigs">
                    <loading-spinner></loading-spinner>
                </div>
                <div class="border-t border-b h-100 overflow-y-auto" v-else
                     :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                    <div>
                        <div v-if="!opportunityNotificationsStore.notificationConfigs.length" class="w-full flex rounded-lg bg-blue-100 p-4 mb-3 text-blue-800 justify-center">
                            <InformationCircleIcon style="width:20px; height:20px;" />
                            <p class="ml-4 text-sm font-medium">
                                No configs to show
                            </p>
                        </div>
                        <div v-for="config in opportunityNotificationsStore.notificationConfigs" :key="config.id"
                             class="grid grid-cols-8 pt-3 px-5 py-3 gap-3 border-b"
                             :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                            <p class="text-sm col-span-2">
                                {{ config.name }}
                            </p>
                            <p class="text-sm">
                                {{ config.type_display ?? "Unknown" }}
                            </p>
                            <p class="text-sm col-span-1 capitalize">
                                {{ config.maximum_send_frequency }}
                            </p>
                            <p class="text-sm col-span-1">
                                {{ (config.days_display ?? []).join(', ') }}
                            </p>
                            <p class="text-sm col-span-1">
                                <CheckCircleIcon v-if="config.active" class="w-6 h-6 text-green-500" />
                            </p>
                            <p class="ml-2 col-span-1">
                                <button class="mr-3" @click="editConfig(config)">
                                    <PencilIcon class="w-6 h-6 text-primary-500" />
                                </button>

                                <button @click="showDeleteConfirmation(config)">
                                    <TrashIcon class="w-6 h-6 text-primary-500" />
                                </button>
                            </p>
                            <p class="col-span-1 text-center">
                                {{config?.total_sent ? config?.total_sent : 0}}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="p-3">
                    <Pagination
                        v-if="opportunityNotificationsStore.notificationConfigPaginationData"
                        :dark-mode="darkMode"
                        :show-pagination="true"
                        :pagination-data="opportunityNotificationsStore.notificationConfigPaginationData"
                        @change-page="opportunityNotificationsStore.handleNotificationConfigPaginationEvent"></Pagination>
                </div>
                <ConfigModal
                    v-if="showModal"
                    :dark-mode="darkMode"
                    :initial-config="editingConfig"
                    @close="closeModal"
                />
                <Modal
                    v-if="showDeleteConfirmModal"
                    :container-classes="'overflow-scroll max-h-[90vh] p-8'"
                    @close="closeDeleteConfirmModal"
                    @confirm="deleteConfig"
                    close-text="No, keep it"
                    confirmText="Yes, remove it"
                    :dark-mode="darkMode"
                    :small="true">
                    <template v-slot:header>
                        <h4 class="text-xl">Delete Config</h4>
                    </template>
                    <template v-slot:content>
                        <div :class="darkMode ? 'darkmode' : ''">
                            <p>Are you sure you wish to delete this notification configuration?</p>
                        </div>
                    </template>
                </Modal>
            </div>
        </div>
    </div>
</template>

<script>
import {useOpportunityNotificationStore} from "../../../../stores/opportunity-notification.store.js";
import {CheckCircleIcon, ChevronDownIcon, InformationCircleIcon, PencilIcon, TrashIcon} from "@heroicons/vue/solid";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import Pagination from "../../Shared/components/Pagination.vue";
import {FilterIcon} from "@heroicons/vue/outline";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import ConfigsSearch from "./ConfigsSearch.vue";
import NotificationModal from "./NotificationModal.vue";
import ConfigModal from "./OpportunityNotificationModal.vue";
import Modal from "../../Shared/components/Modal.vue";

export default {
    name: "OpportunityNotificationConfigs",
    components: {
        Modal,
        NotificationModal,
        ConfigsSearch,
        CheckCircleIcon,
        PencilIcon,
        TrashIcon,
        FilterIcon,
        Pagination,
        ChevronDownIcon,
        LoadingSpinner,
        InformationCircleIcon,
        AlertsContainer,
        ConfigModal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            opportunityNotificationsStore: useOpportunityNotificationStore(),
            showFilters: false,
            showDeleteConfirmModal: false,
            showModal: false,
            editingConfig: null,
            deleteConfigId: null,
        }
    },
    mounted() {
    },
    created() {
    },
    methods: {
        createConfig() {
            this.editingConfig = null;
            this.showModal = true;
        },
        editConfig(config) {
            this.editingConfig = {...config};
            this.showModal = true;
        },
        showDeleteConfirmation(config) {
            this.showDeleteConfirmModal = true;
            this.deleteConfigId = config.id;
        },
        deleteConfig() {
            this.opportunityNotificationsStore.deleteNotificationConfig(this.deleteConfigId);
            this.showDeleteConfirmModal = false;
        },
        closeModal() {
            this.showModal = false;
        },
        closeDeleteConfirmModal() {
            this.showDeleteConfirmModal = false;
        },
    }
}
</script>
