<template>
    <Modal
        :container-classes="'overflow-y-auto max-h-[80vh] p-8'"
        @close="closeModal"
        @confirm="saveConfig"
        :hide-confirm="false"
        :close-text="'Close'"
        :dark-mode="darkMode"
        :disable-confirm="!canCreate || saving"
        :small="false">
        <template v-slot:header>
            <h4 class="text-xl">{{ editingConfig.id ? 'Edit' : 'Add' }} Configuration</h4>
        </template>
        <template v-slot:content>
            <div class="config--edit-modal" :class="darkMode ? 'darkmode' : ''">
                <div
                    class="w-full flex rounded-lg p-4 mb-3"
                    :class="[darkMode ? 'text-blue-400 border border-blue-400' : 'bg-blue-100 text-blue-800']"
                >
                    <InformationCircleIcon class="h-5 w-5"/>
                    <p class="ml-4 text-sm font-medium"
                        :class="[!canCreate ? 'text-red-600' : '']"
                    >
                        {{ canCreate ? 'Changes to existing configurations will take effect immediately' : 'Only one BDM Configuration can be created. Please edit the existing config.'}}
                    </p>
                </div>

                <div class="relative">
                    <div class="mb-4"
                        :class="[saving ? 'opacity-50' : '']"
                    >
                        <div class="grid grid-cols-2 gap-x-3">
                            <div class="mb-4">
                                <CustomInput
                                    label="Name"
                                    :dark-mode="darkMode"
                                    v-model="editingConfig.name"
                                    placeholder="Name"
                                />
                            </div>
                            <div class="gap-x-3">
                                <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                    Type
                                </p>
                                <Dropdown
                                    class="mt-1"
                                    :class="[editingConfig.id ? 'pointer-events-none' : '']"
                                    :dark-mode="darkMode"
                                    :options="getConfigOptions()"
                                    v-model="editingConfig.type"
                                    :disabled="editingConfig.id"
                                    @change="(newOption) => setCurrentConfig(newOption.id)"
                                />
                            </div>
                            <div class="mb-4"
                                v-if="fieldIsEnabled('attempt_on_days')"
                            >
                                <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                    Attempt sending on days
                                </p>
                                <MultiSelect
                                    :dark-mode="darkMode"
                                    :selected-ids="editingConfig.attempt_on_days"
                                    :options="[
                                        { id: 0, name: 'Sunday' },
                                        { id: 1, name: 'Monday' },
                                        { id: 2, name: 'Tuesday' },
                                        { id: 3, name: 'Wednesday' },
                                        { id: 4, name: 'Thursday' },
                                        { id: 5, name: 'Friday' },
                                        { id: 6, name: 'Saturday' },
                                    ]"
                                    @input="handleMultiSelect"
                                />
                            </div>
                            <div class="mb-4 relative"
                                 v-if="fieldIsEnabled('send_time')"
                            >
                                <div class="flex items-center gap-x-2">
                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                        Send at
                                    </p>
                                    <InformationCircleIcon
                                        v-on:mouseleave="showSendTimeTooltip = false"
                                        v-on:mouseenter="showSendTimeTooltip = true"
                                        class="h-4 w-4"
                                    />
                                </div>
                                <div v-if="showSendTimeTooltip"
                                     class="text-xs bg-primary-50 text-slate-900 top-5 shadow-xl absolute transition-all duration-150 p-2 rounded border-grey-200 z-20 mt-2">
                                    Setting this field to 9:00am ensures that companies on the east coast receive emails at 9:00am in their timezone, while those on the west coast receive them 3 hours later.
                                </div>
                                <CustomInput
                                    :dark-mode="darkMode"
                                    name="config-send-time"
                                    type="time"
                                    v-model="editingConfig.send_time"
                                />
                                <span class="text-xs">
                                    Recipient company timezone, defaulting to Mountain Time.
                                </span>
                            </div>
                            <div class="mb-4">
                                <CustomInput
                                    :dark-mode="darkMode"
                                    v-model="editingConfig.maximum_send_frequency"
                                    type="number"
                                    label="Maximum send frequency in days (per company)"
                                />
                            </div>
                            <div class="mb-4 relative">
                                <div class="flex items-center gap-x-2">
                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                        Missed product threshold
                                    </p>
                                    <InformationCircleIcon
                                        v-on:mouseleave="showLeadThresholdTooltip = false"
                                        v-on:mouseenter="showLeadThresholdTooltip = true"
                                        class="h-4 w-4"
                                    />
                                </div>
                                <div v-if="showLeadThresholdTooltip"
                                     class="text-xs bg-primary-50 text-slate-900 top-5 shadow-xl absolute transition-all duration-150 p-2 rounded border-grey-200 z-20 mt-2"
                                >
                                    Define the quantity of leads created since the last notification was sent to inform companies about fresh missed leads
                                </div>
                                <CustomInput
                                    :dark-mode="darkMode"
                                    v-model="editingConfig.lead_threshold"
                                    type="number"
                                    placeholder="Product threshold"
                                />
                            </div>
                            <div class="mb-4 relative"
                                 v-if="fieldIsEnabled('campaign_threshold')"
                            >
                                <div class="flex items-center gap-x-2">
                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                        Campaign threshold
                                    </p>
                                    <InformationCircleIcon
                                        v-on:mouseleave="showCampaignThresholdTooltip = false"
                                        v-on:mouseenter="showCampaignThresholdTooltip = true"
                                        class="h-4 w-4"
                                    />
                                </div>
                                <div v-if="showCampaignThresholdTooltip"
                                     class="text-xs bg-primary-50 text-slate-900 top-5 shadow-xl absolute transition-all duration-150 p-2 rounded border-grey-200 z-20 mt-2"
                                >
                                    Define the number of products an individual campaign must have missed in order to qualify for email summary
                                </div>
                                <CustomInput
                                    :dark-mode="darkMode"
                                    v-model="editingConfig.campaign_threshold"
                                    type="number"
                                    placeholder="Campaign threshold"
                                />
                            </div>
                            <div class="mb-4"
                                 v-if="fieldIsEnabled('filter_preset_id')"
                            >
                                <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                    Companies filter preset *
                                </p>
                                <Dropdown
                                    :dark-mode="darkMode"
                                    :options="notificationStore.filterCompanyPresets"
                                    placeholder="Choose Option"
                                    v-model="editingConfig.filter_preset_id"
                                    :selected="editingConfig.filter_preset_id"
                                    @update:model-value="fetchPreviewCount()"
                                />
                            </div>
                            <div class="mb-4"
                                 v-if="fieldIsEnabled('expires_at')"
                            >
                                <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                    Expires at
                                </p>
                                <DatePicker
                                    :enable-time-picker="false"
                                    :dark="darkMode"
                                    v-model="editingConfig.expires_at"
                                    model-type="yyyy-MM-dd'T'HH:mm:ssX"
                                    auto-apply
                                    placeholder="mm-dd-yy"
                                    format="MMM do yyyy"
                                />
                            </div>
                            <div class="mb-4 relative"
                                 v-if="fieldIsEnabled('maximum_promo_products')"
                            >
                                <div class="flex items-center gap-x-2">
                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                        Maximum free products per company
                                    </p>
                                    <InformationCircleIcon
                                        v-on:mouseleave="showMaxProductsTooltip = false"
                                        v-on:mouseenter="showMaxProductsTooltip = true"
                                        class="h-4 w-4"
                                    />
                                </div>
                                <div v-if="showMaxProductsTooltip"
                                     class="text-xs bg-primary-50 text-slate-900 top-5 shadow-xl absolute transition-all duration-150 p-2 rounded border-grey-200 z-20 mt-2"
                                >
                                    The maximum number of free missed products a single Company may receive (cumulative)
                                </div>
                                <CustomInput
                                    :dark-mode="darkMode"
                                    v-model="editingConfig.maximum_promo_products"
                                    type="number"
                                    placeholder="Maximum free products"
                                />
                            </div>
                            <div class="mb-4 relative"
                                 v-if="fieldIsEnabled('maximum_days_since_last_lead')"
                            >
                                <div class="flex items-center gap-x-2">
                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                        Maximum days since last product assignment
                                    </p>
                                    <InformationCircleIcon
                                        v-on:mouseleave="showMaxDaysSinceLastLeadTooltip = false"
                                        v-on:mouseenter="showMaxDaysSinceLastLeadTooltip = true"
                                        class="h-4 w-4"
                                    />
                                </div>
                                <div v-if="showMaxDaysSinceLastLeadTooltip"
                                     class="text-xs bg-primary-50 text-slate-900 top-5 shadow-xl absolute transition-all duration-150 p-2 rounded border-grey-200 z-20 mt-2"
                                >
                                    Companies with no product assignments since this many days ago will be excluded from notifications
                                </div>
                                <CustomInput
                                    :dark-mode="darkMode"
                                    v-model="editingConfig.maximum_days_since_last_lead"
                                    type="number"
                                    placeholder="Maximum free products"
                                />
                            </div>
                            <div class="mb-4 relative"
                                 v-if="fieldIsEnabled('days_to_query')"
                            >
                                <div class="flex items-center gap-x-2">
                                    <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                        Number of days to query for missed products
                                    </p>
                                    <InformationCircleIcon
                                        v-on:mouseleave="showDaysToQueryTooltip = false"
                                        v-on:mouseenter="showDaysToQueryTooltip = true"
                                        class="h-4 w-4"
                                    />
                                </div>
                                <div v-if="showDaysToQueryTooltip"
                                     class="text-xs bg-primary-50 text-slate-900 top-5 shadow-xl absolute transition-all duration-150 p-2 rounded border-grey-200 z-20 mt-2"
                                >
                                    How many days' worth of missed products to query and summarise for each notification
                                </div>
                                <CustomInput
                                    :dark-mode="darkMode"
                                    v-model="editingConfig.days_to_query"
                                    type="number"
                                    placeholder="Maximum free products"
                                />
                            </div>
                            <div class="gap-x-3"
                                 v-if="fieldIsEnabled('active')"
                            >
                                <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                                    Active
                                </p>
                                <ToggleSwitch
                                    class="mt-1"
                                    :dark-mode="darkMode"
                                    v-model="editingConfig.active"
                                />
                            </div>
                            <div v-if="!loadingPreviewCompaniesCount && previewCompaniesCount" class="col-span-2">
                                <label for="config-email-template"
                                       class="block text-sm font-medium leading-6"
                                       :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                    Email preview - {{previewCompaniesCount ? `${previewCompaniesCount?.total_companies} company(ies) might receive this notification` : ''}}
                                </label>
                            </div>
                            <div class="col-span-2 mb-4" v-if="previewCompaniesCount">
                                <div class="grid grid-cols-3 gap-2">
                                    <div>
                                        <dropdown
                                            v-model="previewSelectedCompany"
                                            :options="previewCompaniesCount?.preview_data"
                                            placeholder="Select Company"
                                            :dark-mode="darkMode"
                                            placement="top"
                                        />
                                    </div>
                                    <div>
                                        <dropdown
                                            v-model="previewSelectedCompanyUser"
                                            :disabled="!previewSelectedCompany"
                                            :options="companyUserOptions"
                                            placeholder="Select Company User"
                                            :dark-mode="darkMode"
                                            placement="top"
                                        />
                                    </div>
                                    <div>
                                        <custom-button :disabled="!previewSelectedCompanyUser" @click="generatePreviewTemplate">
                                            Preview email
                                        </custom-button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-span-2 mb-4 flex items-center" v-if="loadingPreviewCompaniesCount || loadingPreviewEmail">
                                <loading-spinner />
                            </div>

                            <div class="col-span-2 my-4" v-if="notificationStore?.emailTemplatePreview && !loadingPreviewEmail">
                                <iframe width="100%" height="300px" :srcdoc="notificationStore?.emailTemplatePreview"></iframe>
                            </div>

                        </div>
                    </div>
                    <div v-if="modalError" class="lg:col-span-4 rounded-lg bg-red-100 p-4 mb-3">
                        <p class="text-sm font-medium text-red-800">{{ modalError }}</p>
                    </div>
                </div>
                <div class="absolute top-0 left-0 w-full mt-[16rem] flex items-center justify-center"
                     v-if="saving"
                >
                    <LoadingSpinner />
                </div>
            </div>
            <div v-if="editingConfig.type === 1"
                class="mt-12"
            >
                <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1">
                    Select a company for test data
                </p>
                <CompanySearchAutocomplete
                    :dark-mode="darkMode"
                    v-model="testCompanyId"
                />
            </div>
        </template>
        <template v-slot:buttons>
            <button class="transition duration-200 text-white font-medium focus:outline-none py-2 rounded-md px-5 bg-primary-500 hover:bg-blue-500"
                    @click.stop="confirmTest">
                Send Test Email
            </button>
        </template>
    </Modal>
    <Modal
        v-if="showConfirmTest"
        :container-classes="'overflow-scroll max-h-[70vh] p-8'"
        @close="showConfirmTest = false"
        @confirm="dispatchEmails"
        confirm-text="Confirm Test"
        :hide-confirm="false"
        :close-text="'Cancel'"
        :dark-mode="darkMode"
        :small="false">
        <template v-slot:header>
            <h4 class="text-xl">Confirm Test</h4>
        </template>
        <template v-slot:content>
            <div class="config--confirm-test" :class="darkMode ? 'darkmode' : ''">
                Are you sure you want to send a test? An email will be sent to {{ notificationStore.testEmailRecipients }}
            </div>
        </template>
    </Modal>

</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import Tab from "../../Shared/components/Tab.vue";
import {InformationCircleIcon} from "@heroicons/vue/solid";
import {useOpportunityNotificationStore} from "../../../../stores/opportunity-notification.store.js";
import CustomButton from "../../Shared/components/CustomButton.vue";
import HoverTooltip from "../../Shared/components/HoverTooltip.vue";
import {DialogOverlay} from "@headlessui/vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import Toggle from "../../Inputs/Toggle/Toggle.vue";
import DropdownSelector from "../../Shared/components/DropdownSelector.vue";
import MultiSelect from "../../Shared/components/MultiSelect.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import DatePicker from "@vuepic/vue-datepicker";
import CompanySearchAutocomplete from "../../Shared/components/Company/CompanySearchAutocomplete.vue";

const getDefaultConfig = () => ({
    name: '',
    type: 2,
    active: true,
    attempt_on_days: [1,4],
    send_time: '10:00',
    maximum_send_frequency: 5,
    lead_threshold: 15,
    filter_preset_id: null,
    expires_at: (new Date(Date.now() + 1000 * 3600 * 24 * 28)).toISOString(),
    maximum_promo_products: 10,
    campaign_threshold: 10,
    maximum_days_since_last_lead: 90,
    days_to_query: 7,
});

export default {
    name: "OpportunityNotificationModal",
    components: {
        CompanySearchAutocomplete,
        ToggleSwitch,
        MultiSelect,
        DropdownSelector,
        Toggle,
        CustomInput,
        LoadingSpinner,
        DialogOverlay,
        HoverTooltip,
        CustomButton,
        InformationCircleIcon,
        Autocomplete,
        Dropdown,
        Modal,
        Tab,
        DatePicker,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        initialConfig: {
            type: Object,
            default: null,
        },
    },
    emits: ['close'],
    data() {
        return {
            modalError: null,
            notificationStore: useOpportunityNotificationStore(),
            previewCompaniesCount: null,
            loadingPreviewCompaniesCount: false,
            showConfirmTest: false,
            showSendTimeTooltip: false,
            showLeadThresholdTooltip: false,
            showCampaignThresholdTooltip: false,
            showMaxProductsTooltip: false,
            showMaxDaysSinceLastLeadTooltip: false,
            showDaysToQueryTooltip: false,
            previewSelectedCompany: null,
            previewSelectedCompanyUser: null,
            loadingPreviewEmail: false,
            currentConfigType: null,
            editingConfig: {
                name: '',
                type: 2,
                active: true,
                attempt_on_days: [1,2,3,4,5],
                send_time: '10:00',
                maximum_send_frequency: 5,
                lead_threshold: 15,
                campaign_threshold: 10,
                maximum_days_since_last_lead: 90,
                days_to_query: 7,
                filter_preset_id: null,
                expires_at: (new Date(Date.now() + 1000 * 3600 * 24 * 28)).toISOString(),
            },
            testCompanyId: null,
            saving: false,
        }
    },
    created() {
        this.editingConfig = this.initialConfig
            ? this.initialConfig
            : getDefaultConfig();
        this.fetchCompanyFilterPresets();
        this.fetchTestEmailRecipients();
        this.setCurrentConfig();
        if (this.editingConfig.type !== 1 && this.editingConfig.id && this.editingConfig.filter_preset_id)
            this.fetchPreviewCount();
    },
    computed: {
        companyUserOptions(){
            if (this.previewSelectedCompany) {
                return this.previewCompaniesCount
                    ?.preview_data
                    ?.find(company => company.id === this.previewSelectedCompany)
                    ?.company_users ?? []
            }

            return []
        },
        canCreate() {
            return !(this.notificationStore.bdmConfigExists() && this.editingConfig.type === 1 && this.initialConfig === null);
        }
    },
    methods: {
        async fetchPreviewCount(){
            // Prevent trigger multiple times
            if (!this.editingConfig?.id || this.loadingPreviewCompaniesCount) return;

            this.loadingPreviewCompaniesCount = true

            try {
                const res = await this.notificationStore.getPreviewCompanyCount(this.editingConfig.filter_preset_id)
                this.previewCompaniesCount = res.data.data
            } catch (err) {
                console.error(err)
            }

            this.loadingPreviewCompaniesCount = false
        },
        saveConfig() {
            if (this.saving)
                return;
            this.saving = true;
            (this.editingConfig.id
                ? this.notificationStore.updateNotificationConfig(this.editingConfig)
                : this.notificationStore.createNotificationConfig(this.editingConfig)
            ).then(res => {
                if (res) this.closeModal();
                this.saving = false;
            });
        },
        closeModal() {
            this.notificationStore.resetTemplate()
            this.previewCompaniesCount = null;
            this.previewSelectedCompany = null;
            this.previewSelectedCompanyUser = null;
            this.$emit('close');
        },
        fetchCompanyFilterPresets() {
            this.notificationStore.getCompanyFilterPresets();
        },
        fetchTestEmailRecipients() {
            this.notificationStore.getTestEmailRecipients()
        },
        dispatchEmails() {
            this.showConfirmTest = false;
            this.notificationStore.sendOpNotificationEmails(this.editingConfig.id, this.testCompanyId);
        },

        async generatePreviewTemplate(){
            this.loadingPreviewEmail = true
            await this.notificationStore.getEmailTemplatePreview(this.previewSelectedCompanyUser, this.editingConfig.id)
            this.loadingPreviewEmail = false
        },
        handleMultiSelect(newValue) {
            this.editingConfig.attempt_on_days = newValue;
        },
        setCurrentConfig(type) {
            type = type ?? this.editingConfig.type;
            if (type != null)
                this.currentConfigType = this.notificationStore.getConfigTypeById(type);
        },
        getConfigOptions() {
            return Object.values(this.notificationStore.notificationConfigTypes).map(config => ({ id: config.id, name: config.title }));
        },
        fieldIsEnabled(fieldName) {
            return this.currentConfigType
                ? !(this.currentConfigType.disabled ?? []).includes(fieldName)
                : false;
        },
        confirmTest() {
            if (this.editingConfig.type === 1 && !this.testCompanyId) {
                this.notificationStore.notificationConfigError = "A test company must be selected for BDM queue testing."
                return;
            }
            this.showConfirmTest = true
        }
    },

    watch: {
        previewSelectedCompany(){
            this.notificationStore.resetTemplate()
            this.previewSelectedCompanyUser = null
        },
    }
}
</script>
