
<template>
<!--    TODO - Genericise/generify this alerts  -->
    <alerts-container v-if="notificationStore.notificationError" alert-type="error" :text="notificationStore.notificationError" :dark-mode="darkMode"/>
    <alerts-container v-if="notificationStore.notificationSuccess" alert-type="success" :text="notificationStore.notificationSuccess" :dark-mode="darkMode"/>
    <alerts-container v-if="notificationStore.notificationConfigError" alert-type="error" :text="notificationStore.notificationConfigError" :dark-mode="darkMode"/>
    <alerts-container v-if="notificationStore.notificationConfigSuccess" alert-type="success" :text="notificationStore.notificationConfigSuccess" :dark-mode="darkMode"/>

    <alerts-container v-if="genericAlert.isVisible" :alert-type="genericAlert.type" :text="genericAlert.text" :dark-mode="darkMode"/>

    <div class="w-full flex-auto pt-3 relative"
         :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
        <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
            <div class="flex items-center justify-between flex-wrap py-4">
                <div class="flex items-center py-1">
                    <h3 class="text-xl font-medium pb-0 mr-4">Opportunity Notifications</h3>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="col-span-1">
                    <Configs :dark-mode="darkMode"></Configs>
                </div>
                <div class="col-span-1">
                    <Notifications :dark-mode="darkMode"></Notifications>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Notifications from "./Components/Notifications.vue";
import Configs from "./Components/OpportunityNotificationConfigs.vue";
import {useOpportunityNotificationStore} from "../../../stores/opportunity-notification.store.js";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";

export default {
    name: "OpportunityNotificationsContainer",
    components: {
        AlertsContainer,
        Notifications,
        Configs
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        currentUserRoles: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            notificationStore: useOpportunityNotificationStore(),
            isMissedProductsConfigModalVisible: false,

            genericAlert: {
                isVisible: false,
                message: '',
                type: '',
            }
        }
    },
    methods: {
        toggleProductsConfigModal(visibility){
            this.isMissedProductsConfigModalVisible = visibility
        },

        showGenericAlert({ type, text }, timeout = 2000){
            this.genericAlert.isVisible = true
            this.genericAlert.text = text
            this.genericAlert.type = type

            setTimeout(() => {
                this.genericAlert.isVisible = false
                this.genericAlert.text = ''
                this.genericAlert.type = ''
            }, timeout)
        },

        handleMissedProductsConfigModalClose({ saved }){
            this.toggleProductsConfigModal(false)

            if (saved) {
                this.showGenericAlert({
                    text: 'Configuration saved successfully',
                    type: 'success',
                })
            }
        }
    }
}
</script>

<style scoped>

</style>
