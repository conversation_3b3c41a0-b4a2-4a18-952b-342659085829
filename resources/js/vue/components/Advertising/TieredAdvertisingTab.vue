<template>
    <div>
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>

        <div v-if="initializing === 2">
            <loading-spinner :label="'Initializing'" :dark-mode="darkMode" />
        </div>
        <div v-else-if="initializing === 1"
            class="text-center text-red-400 my-10">
            Initialization error
        </div>
        <div v-else-if="initializing === 0"
             class="w-full flex-auto pt-3 relative"
             :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
            <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                <div class="border rounded-lg"
                     :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                    <div class="p-5">
                        <div class="flex items-center pb-3">
                            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Tiered Advertising</h5>
                            <h5 class="text-sm font-semibold leading-tight ml-2" v-if="instanceName !== ''"> -  {{ instanceName }}</h5>
                        </div>
                        <div class="grid grid-cols-5 gap-5 justify-between">
                            <div class="col-span-3 flex flex-col gap-3">
                                <div class="grid grid-cols-6 gap-3">
                                    <Dropdown :disabled="editing" class="col-span-2" :dark-mode="darkMode" :placeholder="'Choose a platform'" :options="platformOptions" v-model="selectedPlatform" @change="handleChangePlatform($event.id)"/>
                                    <Dropdown :disabled="editing" class="col-span-2" :dark-mode="darkMode" :placeholder="'Choose an industry'" :options="industryOptions" v-model="selectedIndustry" @change="handleChangeIndustry($event.id)"/>
                                    <div v-if="!editing" class="inline-flex gap-6">
                                        <CustomButton
                                            class="justify-center w-20"
                                            color="primary-outline"
                                            :disabled="!isAdvertisingAdmin"
                                            :dark-mode="darkMode"
                                            @click="editing = true">
                                            Edit
                                        </CustomButton>
                                        <svg @click="getTieredAdsAccounts(this.selectedPlatform, this.selectedIndustry)" class="cursor-pointer mt-2" fill="#3A7EF9" height="20px" width="20px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 489.698 489.698" xml:space="preserve">
                                            <g stroke-width="0"></g><g stroke-linecap="round" stroke-linejoin="round"></g> <g> <g> <path d="M468.999,227.774c-11.4,0-20.8,8.3-20.8,19.8c-1,74.9-44.2,142.6-110.3,178.9c-99.6,54.7-216,5.6-260.6-61l62.9,13.1 c10.4,2.1,21.8-4.2,23.9-15.6c2.1-10.4-4.2-21.8-15.6-23.9l-123.7-26c-7.2-1.7-26.1,3.5-23.9,22.9l15.6,124.8 c1,10.4,9.4,17.7,19.8,17.7c15.5,0,21.8-11.4,20.8-22.9l-7.3-60.9c101.1,121.3,229.4,104.4,306.8,69.3 c80.1-42.7,131.1-124.8,132.1-215.4C488.799,237.174,480.399,227.774,468.999,227.774z"></path> <path d="M20.599,261.874c11.4,0,20.8-8.3,20.8-19.8c1-74.9,44.2-142.6,110.3-178.9c99.6-54.7,216-5.6,260.6,61l-62.9-13.1 c-10.4-2.1-21.8,4.2-23.9,15.6c-2.1,10.4,4.2,21.8,15.6,23.9l123.8,26c7.2,1.7,26.1-3.5,23.9-22.9l-15.6-124.8 c-1-10.4-9.4-17.7-19.8-17.7c-15.5,0-21.8,11.4-20.8,22.9l7.2,60.9c-101.1-121.2-229.4-104.4-306.8-69.2 c-80.1,42.6-131.1,124.8-132.2,215.3C0.799,252.574,9.199,261.874,20.599,261.874z"></path> </g> </g>
                                        </svg>
                                    </div>
                                    <CustomButton
                                        v-if="editing"
                                        class="justify-center"
                                        color="primary-outline"
                                        :dark-mode="darkMode"
                                        @click="addCampaign()"
                                    >
                                        Add Campaign
                                    </CustomButton>
                                </div>
                            </div>
                            <div class="flex flex-col items-start justify-end">
                            </div>
                            <div class="flex-inline flex-col items-end justify-end">
                                <div v-if="editing" class="inline-flex gap-2">
                                    <CustomButton
                                        class="justify-center"
                                        color="slate-outline"
                                        :dark-mode="darkMode"
                                        @click="cancelUpdates()"
                                    >
                                        Cancel Updates
                                    </CustomButton>
                                    <CustomButton
                                        class="justify-center"
                                        color="primary"
                                        :disabled="!saveUpdatesEnabled"
                                        :dark-mode="darkMode"
                                        @click="saveUpdates()"
                                    >
                                        <span v-if="saving" class="flex flex-row gap-5 items-center">
                                            Saving <loading-spinner :size="'w-4 h-4'" :dark-mode="darkMode" :border-color="'border-white'" />
                                        </span>
                                        <span v-else-if="saveUpdatesEnabled">
                                            Save Updates
                                        </span>
                                        <span-hover-tooltip v-else mobile-right extra-large>
                                            <template #title>
                                                Save Updates
                                            </template>
                                            Errors:
                                            <div v-for="error in saveUpdatesErrors">
                                                {{ error }}
                                            </div>
                                        </span-hover-tooltip>
                                    </CustomButton>
                                </div>
                                <CustomButton
                                    v-if="!editing"
                                    class="justify-center w-30"
                                    color="primary"
                                    :disabled="!isAdvertisingAdmin"
                                    :dark-mode="darkMode"
                                    @click="showLocationUpdateModal = true">
                                    Update Platform Campaign Locations
                                </CustomButton>
                            </div>
                        </div>
                        <div class="grid grid-cols-5">
                            <div class="ml-5 mt-5 grid grid-cols-3">
                                <p class="uppercase font-semibold text-xs w-max mr-3"
                                   :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                    Enabled
                                </p>
                                <p class="uppercase font-semibold text-xs w-max mr-3"
                                   :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                    ROAS %
                                </p>
                                <p class="uppercase font-semibold text-xs w-max mr-3"
                                   :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                    Update Frequency (Mins)
                                </p>
                                <ToggleSwitch v-model="enabled" :disabled="!editing" :dark-mode="darkMode" />
                                <input type="number"
                                       v-model="roas"
                                       :disabled="!editing"
                                       class="rounded text-sm font-medium w-20 h-9 border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                       :class="{
                                        'text-slate-100 bg-dark-background border-dark-border': darkMode,
                                        'text-slate-900 bg-light-background border-light-border': !darkMode,
                                   }">
                                <input type="number"
                                   v-model="updateFrequency"
                                   :disabled="!editing"
                                   class="rounded text-sm font-medium w-20 h-9 border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                   :class="{
                                        'text-slate-100 bg-dark-background border-dark-border': darkMode,
                                        'text-slate-900 bg-light-background border-light-border': !darkMode,
                                   }">
                            </div>
                            <div class="ml-20 mt-5 grid grid-cols-1">
                                <p class="uppercase font-semibold text-xs w-max mr-3"
                                   :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                    Advertiser
                                </p>
                                <Dropdown
                                    v-if="editing"
                                    :dark-mode="darkMode"
                                    :placeholder="'Advertiser'"
                                    :options="advertisers"
                                    v-model="selectedAdvertiser"
                                    class="max-w-32"/>
                                <input v-else
                                       :disabled="true"
                                       type="text"
                                       v-model="advertiserName"
                                       class="rounded text-sm font-medium w-20 h-9 border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                       :class="{
                                        'text-slate-100 bg-dark-background border-dark-border': darkMode,
                                        'text-slate-900 bg-light-background border-light-border': !darkMode,
                                   }">
                            </div>
                            <div></div>
                            <div></div>
                            <div v-if="selectedInstance" class="mt-5 grid grid-cols-2 ml-5 mr-10">
                                <p class="uppercase font-semibold text-xs w-max mr-3"
                                   :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                    Instance
                                </p>
                                <div></div>
                                <Dropdown
                                    :disabled="editing"
                                    :dark-mode="darkMode"
                                    :placeholder="'Choose an Instance'"
                                    :options="instanceOptions"
                                    v-model="selectedInstance"
                                    @change="handleChangeInstance($event.id)"/>
                                <CustomButton
                                    v-if="!editing"
                                    class="justify-center ml-3"
                                    color="primary-outline"
                                    :disabled="!isAdvertisingAdmin"
                                    :dark-mode="darkMode"
                                    @click="showCreateInstanceModal = true">
                                    Add New Instance
                                </CustomButton>
                                <CustomButton
                                    v-else
                                    class="justify-center ml-3"
                                    color="red-outline"
                                    :disabled="!isAdvertisingAdmin"
                                    :dark-mode="darkMode"
                                    @click="showDeleteInstanceModal = true">
                                    Delete Instance
                                </CustomButton>
                            </div>
                            <div v-else class="mt-5 ml-5 mr-10">
                                <CustomButton
                                    v-if="!editing"
                                    class="justify-center"
                                    color="primary-outline"
                                    :disabled="!isAdvertisingAdmin"
                                    :dark-mode="darkMode"
                                    @click="showCreateInstanceModal = true">
                                    Assign Instance
                                </CustomButton>
                            </div>
                        </div>
                    </div>
                    <div v-if="loadingCampaigns">
                        <div class="border-t border-b h-100 overflow-y-auto justify-center items-center"
                             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                            <loading-spinner class="h-full" :label="'Loading'" :dark-mode="darkMode"/>
                        </div>
                        <div class="p-3"></div>
                    </div>
                    <simple-table v-else-if="campaigns?.length > 0"
                        :dark-mode="darkMode"
                        v-model="tableFilter"
                        :table-filters="tableFilters"
                        :data="campaigns"
                        :headers="tableHeaders"
                        :loading="loading"
                        :no-pagination="true"
                    >
                        <template v-slot:row.col.accountPlatformId="{item, value}">
                            <div class="flex items-center text-center text-sm gap-2">
                                <div v-if="!editing">{{ value }}</div>
                                <div v-else>
                                    <input type="text"
                                           v-model="campaigns[getKey(item.id)].accountPlatformId"
                                           class="rounded text-sm font-medium w-full h-9 border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                           :class="{
                                                'text-slate-100 bg-dark-background border-dark-border': darkMode,
                                                'text-slate-900 bg-light-background border-light-border': !darkMode,
                                           }">
                                </div>
                            </div>
                        </template>
                        <template v-slot:row.col.campaignPlatformId="{item, value}">
                            <div class="flex items-center text-center text-sm gap-2">
                                <div v-if="!editing">{{ value }}</div>
                                <div v-else>
                                    <input type="text"
                                           v-model="campaigns[getKey(item.id)].campaignPlatformId"
                                           class="rounded text-sm font-medium w-full h-9 border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                           :class="{
                                                'text-slate-100 bg-dark-background border-dark-border': darkMode,
                                                'text-slate-900 bg-light-background border-light-border': !darkMode,
                                           }">
                                </div>
                            </div>
                        </template>
                        <template v-slot:row.col.tier="{item, value}" class="text-center">
                            <div class="flex items-center text-center text-sm gap-2">
                                <div>
                                    <Badge :color="getTierColor(value)" :dark-mode="darkMode">
                                        {{ value }}
                                    </Badge>
                                </div>
                            </div>
                        </template>
                        <template v-slot:row.col.tcpaBid="{item, value}">
                            <div class="flex items-center text-center text-sm gap-2">
                                <div v-if="!editing">{{ currencyFormat(value) }}</div>
                                <div v-else>
                                    <input type="number"
                                           v-model="campaigns[getKey(item.id)].tcpaBid"
                                           class="rounded text-sm font-medium w-20 h-9 border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                           :class="{
                                                'text-slate-100 bg-dark-background border-dark-border': darkMode,
                                                'text-slate-900 bg-light-background border-light-border': !darkMode,
                                           }">
                                </div>
                            </div>
                        </template>
                        <template v-slot:row.col.lowerBound="{item, value}">
                            <div class="flex items-center text-center text-sm gap-2">
                                <div v-if="!editing">
                                    {{ currencyFormat(value) }}
                                    <br>
                                    <p class="font-medium text-2xs text-gray-500">RPL: {{ currencyFormat(value * (roas / 100)) }} </p>
                                </div>
                                <div v-else>
                                    <input type="number"
                                           v-model="campaigns[getKey(item.id)].lowerBound"
                                           class="rounded text-sm font-medium w-20 h-9 border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                           :class="{
                                                'text-slate-100 bg-dark-background border-dark-border': darkMode,
                                                'text-slate-900 bg-light-background border-light-border': !darkMode,
                                           }">
                                </div>
                            </div>
                        </template>
                        <template v-slot:row.col.upperBound="{item, value}">
                            <div class="flex items-center text-center text-sm gap-2">
                                <div v-if="!editing">
                                    <div v-if="value !== null">
                                        {{ currencyFormat(value) }}
                                        <br>
                                        <p class="font-medium text-2xs text-gray-500">RPL: {{ currencyFormat(value * (roas / 100)) }} </p>
                                    </div>
                                    <div v-else>&infin;</div>
                                </div>
                                <div v-else>
                                    <input type="number"
                                           v-model="campaigns[getKey(item.id)].upperBound"
                                           class="rounded text-sm font-medium w-20 h-9 border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                           :class="{
                                                'text-slate-100 bg-dark-background border-dark-border': darkMode,
                                                'text-slate-900 bg-light-background border-light-border': !darkMode,
                                           }">
                                </div>
                            </div>
                        </template>
                        <template v-slot:row.col.coveredPopulation="{item, value}">
                            <div class="flex items-center text-center text-sm gap-2">
                                <div>{{ countFormat(value) }}</div>
                            </div>
                        </template>
                        <template v-slot:row.col.edit="{item, value}">
                            <div @click="selectedCampaignId = item.id" class="flex items-center text-center text-sm text-primary-500 gap-2 cursor-pointer">
                                <div>Edit Campaign</div>
                                <simple-icon
                                    :dark-mode="darkMode"
                                    :icon="simpleIcon.icons.ARROW_TOP_RIGHT_ON_SQUARE"
                                />
                            </div>
                        </template>
                        <template v-slot:row.col.lastLocationUpdate="{item, value}">
                            <div class="flex items-center text-center text-sm gap-2">
                                <div>{{ dateFormat(value) }}</div>
                            </div>
                        </template>
                        <template v-slot:row.col.actions="{item, idx}" class="items-center">
                            <div v-if="!editing">
                                <simple-icon color="blue" :icon="simpleIcon.icons.EYE" class="cursor-pointer" @click="viewCampaign(item.id)"></simple-icon>
                            </div>
                            <div v-else>
                                <simple-icon color="red" :icon="simpleIcon.icons.X_MARK" class="cursor-pointer" @click="deleteCampaign(item.id)"></simple-icon>
                            </div>
                        </template>
                    </simple-table>
                    <div v-else>
                        <div
                            class="border-t border-b h-100 overflow-y-auto flex justify-center items-center"
                            :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                            <div>No campaigns</div>
                        </div>
                        <div class="p-3"></div>
                    </div>

                    <Modal
                        v-if="selectedCampaignId"
                        :container-classes="'overflow-scroll max-h-[90vh] p-8'"
                        :dark-mode="darkMode"
                        :no-buttons="true"
                        @close="closeCampaignModal"
                        :small="true">
                        <template v-slot:header>
                            <h4 class="text-xl">{{selectedCampaign.name}} ({{selectedCampaign.campaignPlatformId}})</h4>
                            <p>Account: {{selectedCampaign.accountName}} ({{selectedCampaign.accountPlatformId}})</p>
                            <p>Counties: {{ selectedCampaignLocations.length }}</p>
                        </template>
                        <template v-slot:content>
                            <simple-table
                                v-if="!loadingCampaignLocations"
                                :dark-mode="darkMode"
                                :data="selectedCampaignLocations"
                                :no-pagination="true"
                                body-classes="flex items-center justify-left"
                                row-classes="gap-5 grid items-center rounded px-5 h-20"
                                :headers="campaignLocationsHeaders">
                                <template class="" v-slot:row.col.negativeZips="{item, value}">
                                    <div class="max-h-16 overflow-y-auto">{{ value }}</div>
                                </template>
                                <template class="" v-slot:row.col.tcpa="{item, value}">
                                    <div >
                                        {{ currencyFormat(value) }}
                                    </div>
                                </template>
                                <template class="" v-slot:row.col.data="{item, value}">
                                    <div class="overflow-y-auto">
                                        ARPL: {{ value['arpl'] }} <br>
                                        ERPL: {{ value['erpl'] }} <br>
                                        GRPL: {{ value['grpl'] }}
                                    </div>
                                </template>
                            </simple-table>
                            <div v-else>
                                <loading-spinner class="h-full" :label="'Loading'" :dark-mode="darkMode"/>
                            </div>
                        </template>
                    </Modal>

                    <Modal
                        v-if="showLocationUpdateModal"
                        :container-classes="'overflow-scroll max-h-[90vh] p-8'"
                        @close="showLocationUpdateModal = false"
                        @confirm="updateLocations()"
                        close-text="Cancel"
                        confirmText="Update Locations"
                        :dark-mode="darkMode"
                        :small="true">
                        <template v-slot:header>
                            <h4 class="text-xl">Confirm Platform Location Update</h4>
                        </template>
                        <template v-slot:content>
                            <div :class="darkMode ? 'darkmode' : ''">
                                <p>Selecting 'Update Locations' will use the {{ unslugify(selectedPlatform, '_') }} API to update the positive and negative locations
                                    in the tiered ads campaigns for Instance: {{ instanceName}}, Industry: {{ unslugify(selectedIndustry, '_') }}. </p>
                                <br>
                                <p>This is already done automatically every X minutes (Update Frequency) for this instance if it is enabled.
                                    Using this will manually trigger the location update and reset the timer for the next update.</p>
                                <br>
                                <p>It may take a few minutes for the time stamps to update in the table, as this will put the update in a queue.</p>
                                <br>
                                <p>If tiered advertising is not enabled for this instance, this will only re-assign counties
                                    in our A2 database and re-calculate covered population - it will not communicate with any platform APIs
                                    to change campaign positive and negative locations.</p>
                                <br>
                                <p>This will only force the locations update for the selected instance: '{{ instanceName }}'. It will not update locations on any other instances or platforms.</p>
                                <br>
                                <p>Are you sure?</p>
                            </div>
                        </template>
                    </Modal>

                    <Modal
                        v-if="showCreateInstanceModal"
                        :container-classes="'overflow-scroll max-h-[90vh] p-8'"
                        @close="showCreateInstanceModal = false"
                        @confirm="createInstance()"
                        close-text="Cancel"
                        confirmText="Create Instance"
                        :dark-mode="darkMode"
                        :disableConfirm="newInstanceName === ''"
                        :small="true">
                        <template v-slot:header>
                            <h4 class="text-xl">Create Instance</h4>
                        </template>
                        <template v-slot:content>
                            <div :class="darkMode ? 'darkmode' : ''">
                                <p v-if="!selectedInstance">Assigning an instance will allow you to create additional tiered advertising
                                    instances for {{ unslugify(selectedPlatform, '_') }} - {{ unslugify(selectedIndustry, '_') }}.</p>
                                <p v-else>Creating another instance for {{ unslugify(selectedPlatform, '_') }} - {{ unslugify(selectedIndustry, '_') }}
                                    will allow you to define a separate set of tiered advertising logic for this platform and industry. </p>
                                <br>
                                <p v-if="!selectedInstance">These instances will operate independently and can be set with their own enabled status, ROAS, and update frequency.</p>
                                <p v-else>This instance will operate independently and can be set with its own enabled status, ROAS, update frequency, and tier parameters.</p>
                                <br>
                                Instance Name:
                                <input type="text"
                                       v-model="newInstanceName"
                                       class="rounded text-sm font-medium w-full h-9 border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10"
                                       :class="{
                                                'text-slate-100 bg-dark-background border-dark-border': darkMode,
                                                'text-slate-900 bg-light-background border-light-border': !darkMode,
                                           }">
                            </div>
                        </template>
                    </Modal>

                    <Modal
                        v-if="showDeleteInstanceModal"
                        :container-classes="'overflow-scroll max-h-[90vh] p-8'"
                        @close="showDeleteInstanceModal = false"
                        @confirm="deleteInstance()"
                        close-text="Cancel"
                        confirmText="Delete Instance"
                        :dark-mode="darkMode"
                        :small="true">
                        <template v-slot:header>
                            <h4 class="text-xl">Delete Instance '{{ instanceName }}'</h4>
                        </template>
                        <template v-slot:content>
                            <div :class="darkMode ? 'darkmode' : ''">
                                <p>Are you sure you want to delete instance '{{ instanceName }}' for
                                    {{ unslugify(selectedPlatform, '_') }} - {{ unslugify(selectedIndustry, '_') }}?</p>
                                <br>
                                <p>Deleting this instance will clear all associated campaign and location update information in Admin 2,
                                    if will not affect the campaigns on the platform itself and it will not affect any other instances for this
                                    platform and industry.</p>
                                <br>
                                <p>IMPORTANT: Deleting an instance will not pause the campaigns on the platform, if you are looking to disable the campaigns
                                    entirely you must do so on the platform itself.</p>
                                <br>
                            </div>
                        </template>
                    </Modal>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import Pagination from "../Shared/components/Pagination.vue";
import AlertsMixin from '../../mixins/alerts-mixin';
import Api from './services/api';
import SharedApiService from "../Shared/services/api";
import Modal from "../Shared/components/Modal.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import AccountManagement from "./components/AccountManagement.vue";
import ToggleSwitch from "../Shared/components/ToggleSwitch.vue";
import PlatformAuthenticationManagement from "./components/PlatformAuthenticationManagement.vue";
import AdCostManagement from "./components/AdCostManagement.vue";
import {unslugify} from "../Shared/services/strings";
import Badge from "../Shared/components/Badge.vue";
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import CustomButton from "../Shared/components/CustomButton.vue";
import Tooltip from "../Shared/components/Tooltip.vue";
import SpanHoverTooltip from "../Shared/components/SpanHoverTooltip.vue";
const simpleIcon = useSimpleIcon()

export default {
    name: "TieredAdvertisingTab",
    components: {
        SpanHoverTooltip,
        Tooltip,
        CustomButton,
        SimpleIcon, SimpleTable, Badge,
        ToggleSwitch,
        Dropdown,
        AlertsContainer,
        LoadingSpinner,
        Pagination,
        Modal,
        AccountManagement,
        PlatformAuthenticationManagement,
        AdCostManagement
    },
    mixins: [
        AlertsMixin
    ],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data: function () {
        return {
            api: Api.make(),
            sharedApi: SharedApiService.make(),
            simpleIcon,

            selectedPlatform: '',
            selectedIndustry: '',
            selectedAdvertiser: 1,
            industryOptions: [],
            platformOptions: [],
            selectedInstance: null,
            instanceOptions: [],
            advertisers: [],

            defaultPlatform: 'google_ads',
            defaultIndustry: 'solar',

            loadingCampaigns: false,

            selectedCampaignId: null,
            selectedCampaign: null,
            selectedCampaignLocations: [],
            createCampaign: false,
            enabled: false,
            updateFrequency: 0,
            roas: 200.00,
            showLocationUpdateModal: false,
            loadingCampaignLocations: false,
            showDeleteInstanceModal: false,
            showCreateInstanceModal: false,
            newInstanceName: '',


            tableFilter: {},
            headers: [
                {title: "Campaign Name", field: "name", cols: 2},
                {title: "Account Id", field: "accountPlatformId", cols: 2},
                {title: "Campaign Id", field: "campaignPlatformId", cols: 2},
                {title: "Tier", field: "tier", cols: 1},
                {title: "Lower Bound", field: "lowerBound", cols: 1},
                {title: "TCPA Bid", field: "tcpaBid", cols: 1},
                {title: "Upper Bound", field: "upperBound", cols: 1},
                {title: "Covered Population", field: "coveredPopulation", cols: 1},
                {title: "Last Location Update", field: "lastLocationUpdate", cols: 1},
                {title: "Actions", field: "actions", cols: 1}
            ],
            metaHeaders: [
                {title: "Ad Set Name", field: "name", cols: 2},
                {title: "Account Id", field: "accountPlatformId", cols: 2},
                {title: "Ad Set Id", field: "campaignPlatformId", cols: 2},
                {title: "Tier", field: "tier", cols: 1},
                {title: "Lower Bound", field: "lowerBound", cols: 1},
                {title: "TCPA Bid", field: "tcpaBid", cols: 1},
                {title: "Upper Bound", field: "upperBound", cols: 1},
                {title: "Covered Population", field: "coveredPopulation", cols: 1},
                {title: "Last Location Update", field: "lastLocationUpdate", cols: 1},
                {title: "Actions", field: "actions", cols: 1}
            ],
            tableFilters: [],

            campaignLocationsHeaders: [
                {title: "County", field: "county", cols: 1},
                {title: "State", field: "state", cols: 1},
                {title: "TCPA", field: "tcpa", cols: 1},
                {title: "Data", field: "data", cols: 1},
                {title: "Negative Zips", field: "negativeZips", cols: 1},
            ],

            campaigns: [],

            editing: false,

            userTimeZone: null,
            saveUpdatesEnabled: true,
            saveUpdatesErrors: [],

            isAdvertisingAdmin: false,

            unslugify: unslugify,

            loading: false,
            saving: false,
            initializing: 2
        };
    },
    computed: {
        simpleIcon() {
            return simpleIcon
        },
        instanceName() {
            const instance = this.instanceOptions.find(instance => instance.id === this.selectedInstance);
            return instance ? instance.name : '';
        },
        tableHeaders() {
            if (this.selectedPlatform === 'meta_ads') {
                return this.metaHeaders;
            }
            return this.headers;
        },
        advertiserName() {
            const advertiserName = this.advertisers.find(advertiser => advertiser.id === this.selectedAdvertiser);
            return advertiserName ? advertiserName.name : '';
        },
    },
    created: function () {
        this.initReferenceLists();
        this.userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    },
    methods: {
        initReferenceLists() {
            this.api.tieredAdsOptions().then(res => {
                if(res.data.data.status === true) {
                    this.isAdvertisingAdmin = res.data.data.is_advertising_admin;
                    this.industryOptions = res.data.data.industries;
                    this.advertisers = res.data.data.advertisers;

                    this.platformOptions = res.data.data.platforms.map((platform) => {
                        return {
                            id: platform,
                            name: this.unslugify(platform, '_')
                        }
                    });

                    // Default to google solar on load
                    this.selectedPlatform = this.defaultPlatform;
                    this.selectedIndustry = this.defaultIndustry;
                    this.getTieredAdsAccounts(this.selectedPlatform, this.selectedIndustry);

                    this.initializing = 0;
                }
                else {
                    this.showAlert('error', 'Failed to initialize');

                    this.initializing = 1;
                }
            }).catch(err => {
                this.showAlert('error', 'Initialization error');

                this.initializing = 1;
            })
        },
        handleChangePlatform(platform) {
            this.getTieredAdsAccounts(platform, this.selectedIndustry, null);
        },
        handleChangeIndustry(industry) {
            this.getTieredAdsAccounts(this.selectedPlatform, industry, null);
        },
        handleChangeInstance(instance) {
            this.getTieredAdsAccounts(this.selectedPlatform, this.selectedIndustry, instance);
        },
        getTieredAdsAccounts(platform, industry, instance = this.selectedInstance) {
            this.loadingCampaigns = true;
            return this.api.getTieredAdsCampaigns(platform, industry, instance).then(res => {
                if(res.data.data.status === true) {
                    this.campaigns = res.data.data.campaigns;
                    this.enabled = res.data.data.enabled;
                    this.updateFrequency = res.data.data.update_frequency;
                    this.roas = res.data.data.roas * 100;
                    this.selectedInstance = res.data.data.instance;
                    this.instanceOptions = res.data.data.instance_options;
                    this.selectedAdvertiser = res.data.data.advertiser;
                }
                else {
                    this.showAlert('error', 'Failed to retrieve tiered ads campaigns');
                }
            }).catch(err => {
                this.showAlert('error', 'Error retrieving tiered ads campaigns');
            }).finally(() => {
                this.loadingCampaigns = false;
            })
        },
        getTierColor(tier) {
            switch (tier) {
                case 1:
                    return 'green';
                case 2:
                    return 'indigo';
                case 3:
                    return 'blue';
                case 4:
                    return 'amber';
                case 5:
                    return 'orange';
                default:
                    return 'red';
            }
        },
        getKey(itemId) {
            return this.campaigns.findIndex(campaign => campaign.id === itemId);
        },
        deleteCampaign(campaignId) {
            this.campaigns = this.campaigns.filter(campaign => campaign.id !== campaignId);
        },
        viewCampaign(campaignId) {
            this.selectedCampaignId = campaignId;
            this.selectedCampaign = this.campaigns[this.getKey(campaignId)];
            this.getCampaignLocations(campaignId);
        },
        getCampaignLocations(campaignId) {
            this.loadingCampaignLocations = true;
            return this.api.getTieredAdsCampaignLocations(campaignId).then(res => {
                if(res.data.data.status === true) {
                    this.selectedCampaignLocations = res.data.data.locations;
                }
                else {
                    this.showAlert('error', 'Failed to retrieve tiered ads campaign locations');
                    this.loadingCampaignLocations = false;
                }
            }).catch(err => {
                this.showAlert('error', 'Error retrieving tiered ads campaign locations');
                this.loadingCampaignLocations = false;
            }).finally(() => {
                this.loadingCampaignLocations = false;
            });
        },
        closeCampaignModal() {
            this.selectedCampaignId = null;
            this.selectedCampaign = null;
        },
        addCampaign() {
            const newCampaign = {
                id: this.campaigns.reduce((max, item) => (item.id > max ? item.id : max), 0) + 1,
                industry: this.selectedIndustry,
                platform: this.selectedPlatform,
                name: 'New Campaign',
                tier: this.campaigns.reduce((max, item) => (item.tier > max ? item.tier : max), 0) + 1,
                lowerBound: null,
                upperBound: null,
                tcpaBid: null,
                accountPlatformId: null,
                campaignPlatformId: null,
                newCampaign: true,
            };

            this.campaigns.push(newCampaign);
        },
        updateLocations() {
            const payload = {
                instance: this.selectedInstance,
            }

            return this.api.triggerPlatformLocationsUpdate(this.selectedPlatform, this.selectedIndustry, payload).then(res => {
                if(res.data.data.status === true) {
                    this.showAlert('success', 'Location Update triggered successfully for '+this.instanceName+'. It may take a few minutes for the update to complete.');
                }
                else {
                    this.showAlert('error', 'Failed to trigger locations update for '+this.instanceName+'.');
                }
            }).catch(err => {
                this.showAlert('error', 'Error triggering locations update for '+this.instanceName+'.');
            }).finally(() => {
                this.showLocationUpdateModal = false;
            })
        },
        createInstance() {
            const payload = {
                instance: this.newInstanceName,
                assignExisting: !this.selectedInstance,
            }
            return this.api.assignTieredAdsInstance(this.selectedPlatform, this.selectedIndustry, payload).then(res => {
                if(res.data.data.status === true) {
                    this.showCreateInstanceModal = false;
                    this.showAlert('success', 'Instance '+this.newInstanceName+' created successfully.');
                    this.getTieredAdsAccounts(this.selectedPlatform, this.selectedIndustry, res.data.data.instance_id);
                }
                else {
                    this.showAlert('error', 'Failed to create instance '+this.newInstanceName+'. '+res.data.data.message);
                }
            }).catch(err => {
                this.showAlert('error', 'Error creating instance.');
            }).finally(() => {
                this.newInstanceName = '';
            });
        },
        deleteInstance() {
            const payload = {
                instance: this.selectedInstance,
            }

            return this.api.deleteTieredAdsInstance(this.selectedPlatform, this.selectedIndustry, payload).then(res => {
                if(res.data.data.status === true) {
                    this.showDeleteInstanceModal = false;
                    this.showAlert('success', 'Instance '+this.instanceName+' deleted successfully.');
                    this.editing = false;
                    this.getTieredAdsAccounts(this.selectedPlatform, this.selectedIndustry, null);
                }
                else {
                    this.showAlert('error', 'Failed to delete instance '+this.selectedInstance+'. '+res.data.data.message);
                }
            }).catch(err => {
                this.showAlert('error', 'Error deleting instance.');
            });
        },
        cancelUpdates() {
            this.editing = false;
            this.getTieredAdsAccounts(this.selectedPlatform, this.selectedIndustry);
        },
        saveUpdates() {
            this.saving = true;

            const payload = {
                campaigns: this.campaigns,
                roas: this.roas / 100.0,
                updateFrequency: this.updateFrequency,
                enabled: this.enabled,
                instanceId: this.selectedInstance,
                advertiser: this.selectedAdvertiser,
            };

            return this.api.saveTieredAdsUpdates(this.selectedPlatform, this.selectedIndustry, payload).then(res => {
                if(res.data.data.status === true) {
                    this.campaigns = res.data.data.campaigns;
                    this.enabled = res.data.data.enabled;
                    this.updateFrequency = res.data.data.update_frequency;
                    this.roas = res.data.data.roas * 100;
                    this.selectedAdvertiser = res.data.data.advertiser;
                    this.showAlert('success', 'Update Successful.');
                }
                else {
                    this.showAlert('error', 'Failed to update ads campaigns. Error: '+res.data.data.message);
                    this.saving = false;
                    this.getTieredAdsAccounts(this.selectedPlatform, this.selectedIndustry);
                }
            }).catch(err => {
                this.showAlert('error', 'Error updating ads campaigns.');
                this.saving = false;
                this.getTieredAdsAccounts(this.selectedPlatform, this.selectedIndustry);
            }).finally(() => {
                this.saving = false;
                this.editing = false;
            })
        },
        countFormat(value) {
            if (!value)
                return '';
            return new Intl.NumberFormat().format(value);
        },
        currencyFormat(value) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(value);
        },
        dateFormat(value) {
            if (!value)
                return '';
            const date = new Date(value);

            const options = {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: this.userTimeZone,
                timeZoneName: 'short'
            };

            return date.toLocaleString('en-US', options);
        },
        validateBoundsInputs() {
            this.saveUpdatesErrors = [];

            if (this.enabled && this.campaigns.length === 0) {
                this.saveUpdatesErrors.push("Cannot enable tiered advertising with no campaigns defined.");
            }

            if (this.updateFrequency < 1 || this.updateFrequency === '') {
                this.saveUpdatesErrors.push("Invalid update frequency.");
            }

            if (this.roas < 1 || this.roas === '') {
                this.saveUpdatesErrors.push("Invalid ROAS.");
            }

            let bounds = [];
            this.campaigns.forEach(campaign => bounds.push([campaign.lowerBound, campaign.upperBound, campaign.id]));
            bounds.sort((a, b) => b[0] - a[0]);

            let index = 0;

            bounds.forEach(bound => {
                this.campaigns[this.getKey(bound[2])].tier = index + 1;

                if (bounds.length > index + 1) {
                    if (bound[0] !== bounds[index + 1][1]) {
                        this.saveUpdatesErrors.push("Lower bound of each tier must match the upper bound of the tier below it.");
                    }
                }

                index++;
            });

            this.campaigns.forEach(campaign => {
                if (campaign.lowerBound < 0 || campaign.upperBound < 0 || campaign.tcpaBid < 0) {
                    this.saveUpdatesErrors.push("No negative bids or bounds allowed.");
                }
                if (campaign.tcpaBid === 0 || campaign.tcpaBid === '' || !campaign.tcpaBid) {
                    this.saveUpdatesErrors.push("Each campaign needs a TCPA Bid value.");
                }
                if (campaign.lowerBound >= campaign.upperBound && campaign.upperBound !== '' && campaign.upperBound) {
                    this.saveUpdatesErrors.push("Upper bounds must be greater than lower bounds.");
                }
                if (campaign.campaignPlatformId === '' || !campaign.campaignPlatformId) {
                    this.saveUpdatesErrors.push("Campaign ID cannot be empty.");
                }
                if (campaign.accountPlatformId === '' || !campaign.accountPlatformId) {
                    this.saveUpdatesErrors.push("Account ID cannot be empty.");
                }
            });

            if (this.saveUpdatesErrors.length > 0) {
                this.saveUpdatesEnabled = false;
            } else {
                this.saveUpdatesEnabled = true;
            }
        },
    },
    watch: {
        campaigns: {
            handler(newValue) {
                this.validateBoundsInputs();
            },
            deep: true
        },
        enabled: {
            handler(newValue) {
                this.validateBoundsInputs();
            }
        },
        updateFrequency: {
            handler(newValue) {
                this.validateBoundsInputs();
            }
        },
        roas: {
            handler(newValue) {
                this.validateBoundsInputs();
            }
        }
    }
}
</script>
