<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div class="flex flex-col px-5 border-b gap-4"
                         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                        <div class="flex justify-between">
                            <Tab
                                :dark-mode="darkMode"
                                :tabs="tabTitles"
                                @selected="selectTab"
                                tab-style="fit"
                                background-color="light"
                                :tab-type="'Normal'"
                            />
                        </div>
                    </div>
                    <component :is="currentTabComponent" :dark-mode="darkMode"/>
                </div>
            </div>
        </div>
        <AlertsContainer
            v-if="alertActive"
            :alert-type="alertType"
            :text="alertText"
            :dark-mode="darkMode"
        />
    </div>
</template>

<script>

import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import Tab from "../Shared/components/Tab.vue";
import {useRolesPermissions} from "../../../stores/roles-permissions.store.js";
import useQueryParams from "../../../composables/useQueryParams.js";
import {markRaw} from "vue";
import CampaignsTab from "./CampaignsTab.vue";
import TieredAdvertisingTab from "./TieredAdvertisingTab.vue";

const DEFAULT_SELECTED_TAB = "Campaigns";
export default {
    name: "AdvertisingManagement",
    components: {Tab, AlertsContainer},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    mixins: [AlertsMixin],
    created() {
        const paramsObject = this.queryParamsHelper.getCurrentParams();

        if ('tab' in paramsObject) {
            this.setSelectedTab(paramsObject['tab']);
        }
    },
    data() {
        return {
            permissionStore: useRolesPermissions(),
            tabTitles: [
                { name: DEFAULT_SELECTED_TAB, current: true, component: markRaw(CampaignsTab)},
                { name: "Tiered Advertising", current: false, component: markRaw(TieredAdvertisingTab)},
            ],
            selectedTab: null,
            queryParamsHelper: useQueryParams()
        }
    },
    computed: {
        currentTabComponent() {
            return this.tabTitles.find(e => e.current)?.component
        }
    },
    methods: {
        setSelectedTab(tab) {
            this.selectedTab = tab;
            this.tabTitles.forEach(e => {
                e.current = e.name === this.selectedTab
            })
        },
        selectTab(tab) {
            this.queryParamsHelper.setQueryParamsOnCurrentUrl({tab})
            this.setSelectedTab(tab)
        },
    }
}
</script>

<style scoped>

</style>
