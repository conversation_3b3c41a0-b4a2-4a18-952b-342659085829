import axios from 'axios';

export default class Api {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseUrl = baseUrl;
        this.baseEndpoint = baseEndpoint;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new Api('internal-api', 'advertising', 1);
    }

    getPlatformAccounts(platform) {
        return this.axios().get(`accounts/${platform}`);
    }

    getPaginatedPlatformAccounts(platform, page = 1) {
        return this.axios().get(`accounts-paginated/${platform}`, {
            params: {
                page: page
            }
        });
    }

    initReferenceLists() {
        return this.axios().get('/init-reference-lists');
    }

    getPlatformReferenceLists(platform) {
        return this.axios().get(`/reference-lists/${platform}`);
    }

    saveAccount(platform, id, name, industry, websites, tracksConversions, uploadIntervalHours, advertiser) {
        return this.axios().post(`accounts/${platform}`, {
            platform_account_id: id,
            name: name,
            industry: industry,
            websites: websites,
            tracks_conversions: tracksConversions,
            upload_conversions_interval_hours: uploadIntervalHours,
            advertiser: advertiser
        });
    }

    getCampaignsPaginated(
        platform,
        accountId,
        page = 1,
        perPage = 10,
        state = null,
        costMetric = null,
        campaignStatus = null,
        campaignName = null,
        pageTokens = null
    )
    {
        return this.axios().get(`campaigns-paginated/${platform}/${accountId}`, {
            params: {
                page: page,
                per_page: perPage,
                search_state_location_id: state,
                cost_metric: costMetric,
                campaign_status: campaignStatus,
                campaign_name: campaignName,
                page_tokens: pageTokens
            }
        });
    }

    updateCampaigns(
        platform,
        accountId,
        campaigns
    ) {
        return this.axios().patch(`campaigns/${platform}/${accountId}`, {
            campaigns: campaigns
        });
    }

    deleteAccount(platform, accountId) {
        return this.axios().delete(`accounts/${platform}/${accountId}`);
    }

    getAuthenticationPlatforms() {
        return this.axios().get('token-auth-platforms');
    }

    getPlatformTokenAuthEndpoint(platform) {
        return this.axios().get(`token-auth-endpoint/${platform}`);
    }

    getAllPlatforms() {
        return this.axios().get('all-platforms');
    }

    getAdvertisers() {
        return this.axios().get('advertisers');
    }

    getAdCostParameters() {
        return this.axios().get('ad-cost-parameters');
    }

    runBackfill(payload) {
        return this.axios().post('ad-cost-backfill', payload);
    }

    tieredAdsOptions() {
        return this.axios().get('/tiered-ads/options');
    }

    getTieredAdsCampaigns(platform, industry, instance = null) {
        return this.axios().get(`tiered-ads/campaigns/${platform}/${industry}`, {
            params: {
                instance: instance,
            }
        });
    }

    saveTieredAdsUpdates(platform, industry, payload) {
        return this.axios().post(`tiered-ads/${platform}/${industry}`, payload);
    }

    triggerPlatformLocationsUpdate(platform, industry, payload) {
        return this.axios().post(`tiered-ads/locations-update/${platform}/${industry}`, payload);
    }

    assignTieredAdsInstance(platform, industry, payload) {
        return this.axios().post(`tiered-ads/assign-instance/${platform}/${industry}`, payload);
    }

    deleteTieredAdsInstance(platform, industry, payload) {
        return this.axios().post(`tiered-ads/delete-instance/${platform}/${industry}`, payload);
    }

    getTieredAdsCampaignLocations(campaignId) {
        return this.axios().get(`tiered-ads/campaign-locations/${campaignId}`);
    }
}
