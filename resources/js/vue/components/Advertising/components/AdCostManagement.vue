<template>
    <div>
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>

        <modal v-if="true" :no-buttons="true" :dark-mode="darkMode" :full-width="true" @close="closeAdCostModal(false)">
            <template v-slot:header>
                Ad Cost Management
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-2 gap-5">
                    <div>
                        <p class="font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                            Date Range
                        </p>
                        <Datepicker
                            v-model="timePeriod"
                            :enable-time-picker="false"
                            :auto-apply="true"
                            :range="{partialRange: false, showLastInRange: false}"
                            :model-type="datePickerFormat"
                            :clearable="false"
                            :year-range="yearRange"
                            :dark="darkMode">
                        </Datepicker>
                    </div>
                    <div></div>
                    <div>
                        <p class="font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                            Platforms
                        </p>
                        <multi-select :options="platforms"
                                      :dark-mode="darkMode"
                                      :show-search-box="false"
                                      :text-place-holder="'Select Platforms'"
                                      :selected-ids="selectedPlatforms"
                                      :classes="'w-full'"/>
                    </div>
                    <div>
                        <p class="font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                            Industries
                        </p>
                        <multi-select :options="industries"
                                      :dark-mode="darkMode"
                                      :show-search-box="false"
                                      :text-place-holder="'Select Industries'"
                                      :selected-ids="selectedIndustries"
                                      :classes="'w-full'"/>
                    </div>
                    <div>
                        <p class="font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                            Advertisers
                        </p>
                        <multi-select :options="advertisers"
                                      :dark-mode="darkMode"
                                      :show-search-box="false"
                                      :text-place-holder="'Select Advertisers'"
                                      :selected-ids="selectedAdvertisers"
                                      :classes="'w-full'"/>
                    </div>
                    <div>
                        <p class="font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                            Resolutions
                        </p>
                        <multi-select :options="resolutions"
                                      :dark-mode="darkMode"
                                      :show-search-box="false"
                                      :text-place-holder="'Select Resolutions'"
                                      :selected-ids="selectedResolutions"
                                      :classes="'w-full'"/>
                    </div>
                    <div>
                        <p class="font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                            Clear Matching Data
                        </p>
                        <toggle-switch
                            v-model="clearSection"
                            :dark-mode="darkMode" />
                    </div>
                </div>
                <div class="mt-10 flex flex-row gap-5 justify-end items-center">
                    <button
                        type="button"
                        @click="closeAdCostModal(false)"
                        class="px-5 py-2 h-9 w-32 rounded bg-grey-300 hover:bg-grey-600 text-white text-sm font-medium">
                        Cancel
                    </button>

                    <div v-if="loading" class="text-center px-5 py-2 h-9 w-32">
                        Running...
                    </div>
                    <button
                        v-else
                        type="button"
                        @click="runBackfill()"
                        class="px-5 py-2 h-9 w-32 rounded bg-primary-500 hover:bg-primary-600 text-white text-sm font-medium">
                        Run Backfill
                    </button>
                </div>
            </template>
        </modal>
        <Modal
            v-if="showConfirmModal"
            :container-classes="'overflow-scroll max-h-[90vh] p-8'"
            @close="closeConfirmModal"
            @confirm="runBackfill()"
            close-text="No, dont run it"
            confirmText="Yes, run it"
            :dark-mode="darkMode"
            :small="true">
            <template v-slot:header>
                <h4 class="text-xl">Are you Sure?</h4>
            </template>
            <template v-slot:content>
                <div :class="darkMode ? 'darkmode' : ''">
                    <p>Are you sure want to run this ad cost back fill with the clear matching data option?</p>
                    <p>This will clear all ad cost data within the selected parameters before re-retrieving.</p>
                    <p>For Example, running this with Google Ads and Solar selected will clear all ad cost data within the selected time frame that match Google Ads, Solar industry, and either advertiser and resolution.</p>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
    import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
    import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
    import AlertsMixin from '../../../mixins/alerts-mixin';
    import Api from './../services/api';
    import Modal from "../../Shared/components/Modal.vue";
    import Dropdown from "../../Shared/components/Dropdown.vue";
    import Pagination from "../../Shared/components/Pagination.vue";
    import SharedApiService from "../../Shared/services/api";
    import MultiSelect from "../../Shared/components/MultiSelect.vue";
    import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
    import {unslugify} from "../../Shared/services/strings";
    import Datepicker from "@vuepic/vue-datepicker";
    import '@vuepic/vue-datepicker/dist/main.css';
    import {DateTime} from "luxon";

    export default {
        name: "AdCostManagement",
        components: {
            Datepicker,
            ToggleSwitch,
            Pagination,
            Dropdown,
            AlertsContainer,
            LoadingSpinner,
            Modal,
            MultiSelect
        },
        mixins: [
            AlertsMixin
        ],
        props: {
            darkMode: {
                type: Boolean,
                default: false
            }
        },
        emits: [
            'close'
        ],
        data: function() {
            return {
                api: Api.make(),
                sharedApi: SharedApiService.make(),

                timePeriod: [],
                platforms: [],
                selectedPlatforms: [],
                industries: [],
                selectedIndustries: [],
                resolutions: [],
                selectedResolutions: [],
                advertisers: [],
                selectedAdvertisers: [],
                clearSection: false,

                datePickerFormat: 'yyyy-M-d',

                showConfirmModal: false,

                loading: false,

                unslugify: unslugify
            };
        },
        computed: {
            luxonTimezone() {
                return `UTC${DateTime.local().toFormat('Z')}`;
            },
            now() {
                return DateTime.now().setZone(this.luxonTimezone.value).startOf('day');
            },
            yearRange() {
                return [2020, this.now.year + 1];
            },
        },
        created: function() {
            this.api.getAdCostParameters().then(res => {
                if(res.data.data.status === true) {
                    this.platforms = res.data.data.platforms.map((platform) => {
                        return {
                            id: platform,
                            name: this.unslugify(platform, '_')
                        };
                    });
                    this.selectedPlatforms = res.data.data.platforms;
                    this.advertisers = Object.entries(res.data.data.advertisers).map(([key, value]) => {
                        return {
                            id: key,
                            name: value
                        };
                    });
                    this.selectedAdvertisers = Object.entries(res.data.data.advertisers).map(([key, value]) => {
                        return key;
                    });
                    this.resolutions = res.data.data.resolutions.map((resolution) => {
                        return {
                            id: resolution,
                            name: this.unslugify(resolution, '_')
                        };
                    });
                    this.selectedResolutions = res.data.data.resolutions;
                    this.industries = res.data.data.industries.map((industry) => {
                        return {
                            id: industry,
                            name: this.unslugify(industry, '_')
                        };
                    });
                    this.selectedIndustries = res.data.data.industries;
                }
                else {
                    this.showAlert('error', 'Problem retrieving parameters for ad cost backfill');
                }
            }).catch(err => {
                this.showAlert('error', 'Error retrieving parameters for ad cost backfill');
            });
        },
        methods: {
            runBackfill() {
                const errorMsg = this.validate();

                if(errorMsg.length > 0) {
                    this.showAlert('error', errorMsg);
                    return;
                }

                if (this.clearSection && !this.showConfirmModal) {
                    this.showConfirmModal = true;
                    return;
                }
                this.showConfirmModal = false;

                const payload = {
                    'date_range': this.timePeriod,
                    'platforms': this.selectedPlatforms,
                    'industries': this.selectedIndustries,
                    'advertisers': this.selectedAdvertisers,
                    'resolutions': this.selectedResolutions,
                    'clear_section': this.clearSection,
                }

                this.loading = true;

                this.api.runBackfill(payload).then(res => {
                    if(res.data.data.status === true) {
                        this.showAlert('success', 'Backfill process started.');
                        this.closeAdCostModal()
                    } else {
                        this.showAlert('error', 'Failed to start backfill process.');
                    }
                }).catch(err => {
                    this.showAlert('error', 'Error starting backfill process.');
                })
                this.loading = false;
            },
            validate() {
                const errors = [];

                if(!this.timePeriod.length) {
                    errors.push("Need to select time period");
                }

                if(!this.selectedPlatforms.length) {
                    errors.push("Need to select a platform");
                }

                if(!this.selectedIndustries.length) {
                    errors.push("Need to select an industry");
                }

                if(!this.selectedAdvertisers.length) {
                    errors.push("Need to select an advertiser");
                }

                if(!this.selectedResolutions.length) {
                    errors.push("Need to select a resolution");
                }

                return errors.join('. ');
            },
            closeAdCostModal() {
                this.$emit('close');
            },
            closeConfirmModal() {
                this.showConfirmModal = false;
            }
        }
    }
</script>
