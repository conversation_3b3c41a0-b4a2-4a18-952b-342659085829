import RingCentralService from "../../LeadProcessing/services/ring-central/ring-central-service";
import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin";
import {waitForCallbackToReturnTrue} from "../../../../utilities/loading-utilities";

export default {
    data: function() {
        return {
            ringCentralService: null,
            webPhoneActive: false,
            rcCall: null,

            // Call Related Data
            session: null,
            incomingWebPhoneSession: null,
            incomingSession: null,
            muted: false,
            onHold: false,

            callActive: false,
            minimizeCall: false,
            contactPhone: null,
            contactName: null,
            contactId: null,
            incomingPhone: null,
            incomingName: null,
            incomingId: null,
            communicationId: null,
            incomingCommunicationId: null,
            data: {
                lead: null,
                recordings: [],
                voicemails: []
            },
            lastId: null
        }
    },
    props: {
        ringCentralClientId: {
            type: String,
            default: null,
        },
        ringCentralClientSecret: {
            type: String,
            default: null,
        },
        ringCentralSandbox: {
            type: String,
            default: null,
        },
        ringCentralWebhookUrl: {
            type: String,
            default: null,
        },
        ringCentralWebhookVerificationToken: {
            type: String,
            default: null,
        }
    },
    mixins: [DispatchesGlobalEventsMixin],
    mounted() {
        if(this.ringCentralClientId !== null && this.ringCentralClientSecret !== null)
            this.initializeRingCentral();

        this.listenToGlobalRingCentralEvents();

        this.ringCentralService.isUserLoggedIn().then(resp => {
            if(resp)
                this.loadWebPhone();
        });
    },
    methods: {
        listenToGlobalRingCentralEvents() {
            document.addEventListener('ring-central-authentication', () => this.authenticateRingCentral());
            document.addEventListener('ring-central-logout', () => this.logoutRingCentral());

            this.listenForGlobalEvent("new-lead", (data) => {
                this.data.lead = data;

                if(this.lastId !== this.data.lead.basic.id)
                    this.handleNewLead();

                this.lastId = this.data.lead ? this.data.lead.basic.id : null;
            });

            this.listenForGlobalEvent("request-service", () => {
               this.dispatchGlobalEvent('service', {service: this.ringCentralService});
            });
        },

        handleNewLead() {
            this.data.recordings = [];
            this.data.voicemails = [];

            if(this.data.lead !== null && this.data.lead.basic && this.data.lead.basic.phone && this.data.lead.basic.has_communicated == true) {
                this.ringCentralService.getRecordings(this.data.lead.basic.phone).then(resp => this.data.recordings = resp).catch(e => {
                    console.error(e);
                    this.data.recordings = [];
                });

                this.ringCentralService.getVoicemails(this.data.lead.basic.phone).then(resp => this.data.voicemails = resp).catch(e => {
                    console.error(e);
                    this.data.voicemails = [];
                });
            }
        },

        initializeRingCentral() {
            this.ringCentralService = new RingCentralService(this.ringCentralClientId, this.ringCentralClientSecret, this.ringCentralWebhookUrl, this.ringCentralWebhookVerificationToken);

            if(Number(this.ringCentralSandbox) === 1)
                this.ringCentralService.enableSandboxMode();

            this.ringCentralService.isUserLoggedIn().then(resp => {
                if(resp)
                    setTimeout(() => this.dispatchGlobalEvent('active'), 500);
                else
                    setTimeout(() => this.dispatchGlobalEvent('inactive'), 500);

                if((window.location.hash && window.location.hash.includes('code')) || (window.location.search && window.location.search.includes('code'))) {
                    this.ringCentralService.finalizeLogin().then(resp => {
                        if(resp)
                            setTimeout(() => this.dispatchGlobalEvent('active'), 500);
                        else
                            setTimeout(() => this.dispatchGlobalEvent('inactive'), 500);
                    });
                }
            });
        },

        authenticateRingCentral() {
            this.ringCentralService.authenticate().then(resp => {
                if(resp) {
                    setTimeout(() => this.dispatchGlobalEvent('active'), 500);
                    this.loadWebPhone();
                } else {
                    setTimeout(() => this.dispatchGlobalEvent('inactive'), 500);
                }
            });
        },

        logoutRingCentral() {
            this.ringCentralService.logout().then(resp => {
                setTimeout(() => this.dispatchGlobalEvent('inactive'), 500);
            });
        },

        endCall(hangup = true) {
            this.callActive = false;
            this.minimizeCall = false;
            this.contactName = null;
            this.contactPhone = null;
            this.contactId = null;
            this.muted = false;
            this.onHold = false;
            this.incomingSession = null;
            this.incomingWebPhoneSession = null;

            if(this.session !== null) {
                this.session.hangup();
                this.session = null;
            }

            this.communicationId = null;
        },

        toggleMute() {
            this.muted = !this.muted

            if(this.session !== null)
                this.muted ? this.session.mute() : this.session.unmute();
        },

        toggleHold() {
            this.onHold = !this.onHold

            if(this.session !== null)
                this.onHold ? this.session.hold() : this.session.unhold();
        },

        toggleMinimizeCall() {
            this.minimizeCall = !this.minimizeCall;
        },

        attemptCall(phone, name, id) {
            this.contactName = name;
            this.contactPhone = phone;
            this.contactId = id;
            this.callActive = true;

            if(this.webPhoneActive)
                this.call(phone);
            else
                this.waitForWebPhone().then(() => this.call(phone));
        },

        answerCall() {
            this.incomingWebPhoneSession.accept().then(() => {
                this.contactName = this.incomingName;
                this.contactPhone = this.incomingPhone;
                this.contactId = this.incomingId;
                this.communicationId = this.incomingCommunicationId;
                this.callActive = true;

                const session = this.incomingSession;
                this.session = session;
                this.session.startRecord();
                this.session.on('status', (status) => {
                    if(status.status.toLocaleLowerCase() === "disconnected") {
                        this.api.updateInboundCall(this.communicationId, true);
                        this.endCall(false);
                    }
                });

                if(this.contactId !== "Unknown" && this.contactId !== "Unknown (looking up)")
                    this.dispatchGlobalEvent("next-lead", {lead_id: this.contactId});

                this._clearIncoming();
            });
        },

        declineCall() {
            this.incomingWebPhoneSession.reject();
            this._clearIncoming();
        },

        call(phone) {
            if(this.webPhoneActive && this.rcCall !== null) {
                this.ringCentralService.getUserPhoneNumber().then(userPhoneNumber => {
                    this.rcCall.makeCall({
                        type: 'webphone',
                        toNumber: phone,
                        fromNumber: userPhoneNumber
                    }).then((session) => {
                        this.session = session;
                        this.session.startRecord();
                        let commId = null;

                        waitForCallbackToReturnTrue(() => session._telephonySession !== undefined, 500, 20).then(() => {
                            this.api.createOutboundCall(this.contactId, userPhoneNumber, phone, this.session.telephonySessionId).then(resp => {
                                this.communicationId = resp.data.data.lead_processing_communication_id.id;
                                commId = this.communicationId;
                            });
                        });

                        session.on('status', (status) => {
                            if(status.status.toLocaleLowerCase() === "disconnected" || (status.status.toLocaleLowerCase() === "answered" && status.party.status.code.toLocaleLowerCase() === "disconnected")) {
                                this.api.updateOutboundCall(commId, true, 'answered');
                                this.endCall(false);
                            } else if(status.status.toLocaleLowerCase() === "voicemail") {
                                this.api.updateOutboundCall(commId, true, 'voicemail');
                                this.endCall(false);
                            } else {}
                        });
                    }).catch(e => console.error(e));
                });
            } else {
                this.loadWebPhone();
                this.waitForWebPhone().then(resp => this.call(phone));
            }
        },

        waitForWebPhone() {
            return new Promise((resolve) => {
                waitForCallbackToReturnTrue(() => this.webPhoneActive = true, 500, 20).then(() => resolve(true)).catch(() => resolve(false));
            });
        },

        loadWebPhone() {
            if(this.rcCall === null)
                this.ringCentralService.getCallInstance().then(resp => {
                    this.rcCall = resp;
                    this.ringCentralService.registerAndMaintainWebPhone();

                    this.rcCall.on('webphone-registered', () => this.webPhoneActive = true);
                    this.rcCall.on('webphone-unregistered', () => this.webPhoneActive = false);
                    this.rcCall.on('webphone-registration-failed', () => this.webPhoneActive = false);
                    this.rcCall.on('webphone-invite', (session) => this._incomingCall(session));
                })
        },

        _incomingCall(session) {
            this.incomingWebPhoneSession = session;
            const lookup = this.rcCall._getSessionFromWebphoneSession(session);
            session = lookup ? lookup : session;

            this.incomingSession = lookup ? lookup : session;
            this.incomingName = "Unknown (looking up)";
            this.incomingPhone = session.from.phoneNumber.replace('+1', '');
            console.log(this.incomingPhone);
            this.incomingId = "Unknown (looking up)";

            session.on('status', (status) => {
                if(status.status.toLocaleLowerCase() === "disconnected") {
                    this._clearIncoming();
                } else {
                    console.log(status);
                }
            });

            this._lookupDetailsByPhone(this.incomingPhone);
        },

        _clearIncoming() {
            this.incomingSession = null;
            this.incomingName = null;
            this.incomingPhone = null;
            this.incomingId = null;
            this.incomingCommunicationId = null;
        },

        _lookupDetailsByPhone(phone) {
            this.api.lookupLead(phone).then(resp => {
                if(resp.data.data.status === true && resp.data.data.lead !== null) {
                    if(this.incomingSession !== null) {
                        this.incomingName = resp.data.data.lead.name;
                        this.incomingId = resp.data.data.lead.lead_id;
                        this.incomingCommunicationId = resp.data.data.lead.communication_id;
                    } else if(this.contactId === "Unknown (looking up)") {
                        this.contactName = resp.data.data.lead.name;
                        this.contactId = resp.data.data.lead.lead_id;
                        this.communicationId = resp.data.data.lead.communication_id;

                        if(resp.lead.lead_id !== "Unknown")
                            this.dispatchGlobalEvent("next-lead", {lead_id: resp.data.data.lead.lead_id})
                    }
                }
            })
        }
    }
}
