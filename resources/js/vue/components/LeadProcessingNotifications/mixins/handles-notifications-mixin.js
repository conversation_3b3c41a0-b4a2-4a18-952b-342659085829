import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin";
import { NotificationPusherService } from "../services/notificationPusher";

export default {
    data: function() {
        return {
            pusher: null,
            notificationPusherService: null,
        }
    },
    props: {
        pusherAppKey: {
            type: String,
            default: null
        },
        pusherAppCluster: {
            type: String,
            default: null
        },
        userId: {
            type: String,
            default: null
        }
    },
    mixins: [DispatchesGlobalEventsMixin],
    created() {
        this.notificationPusherService = NotificationPusherService.generate(this.pusherAppKey, this.pusherAppCluster, this.userId);

        if(this.userId !== null)
            this.initializePusherEvents();
    },
    methods: {
        initializePusherEvents() {
            this.notificationPusherService.subscribe(this.userId)

            this.notificationPusherService.bindOnNewMessage((data) => this.newMessage(data))
            this.notificationPusherService.bindOnNewNotification((data) => this.newNotification(data))
        },
        newNotification(data) {
            if(this.$refs.notificationWrapper)
                this.$refs.notificationWrapper.addNotification(data);
        },
        newMessage(data) {
            this.dispatchGlobalEvent("new-message", data.direction, data.first_name, data.last_name, data.message_body, data.timestamp);
        },
        addMessage(direction, firstName, lastName, messageBody, timestamp) {}
    },
}
