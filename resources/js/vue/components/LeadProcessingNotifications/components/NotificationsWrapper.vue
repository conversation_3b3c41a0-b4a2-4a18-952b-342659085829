<template>
    <div class="relative">
        <notification-bell :title="unreadCount > 0 ? unreadCount + ' unread notifications' : ''" :unreadCount="unreadCount" :dark-mode="darkMode" @toggle-notifications-list="toggleNotificationsList" :notifications-list="notificationsList" :notifications-badge="notificationsBadge"/>
        <notifications-badge :dark-mode="darkMode" @toggle-notifications-list="toggleNotificationsList" :notification="newNotification" :notifications-badge="notificationsBadge"/>
        <notifications-list :unreadCount="unreadCount" @get-recent-notifications="getRecentNotifications" @notification-clicked="notificationClicked" :dark-mode="darkMode" :api="api" v-if="notificationsList" :notifications="data.notifications"/>
    </div>
</template>

<script>
import NotificationBell from "./NotificationBell.vue";
import NotificationsBadge from "./NotificationsBadge.vue";
import NotificationsList from "./NotificationsList.vue";
import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin";
import {CommunicationRelationTypes} from "../../Communications/enums/communication.js";

export default {
    name: "NotificationsWrapper",
    components: {NotificationBell, NotificationsBadge, NotificationsList},
    mixins: [DispatchesGlobalEventsMixin],
    props: {
        api: null,
        darkMode: {
            type: Boolean,
            default: false,
        },
        notificationsList: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            notificationsBadge: false,
            newNotification: null,
            data: {
                notifications: [],
                count: null,
            },
            timeout: null,
            notificationTypes: {lead: 3, company: 4},
            notificationLinkTypes: {lead: "lead", company: "company", task: "task"}
        }
    },
    created() {
        this.getRecentNotifications();
    },
    methods: {
        getRecentNotifications() {
            this.api.getRecentNotifications().then(resp => {
                this.data.notifications = resp.data.data.notifications;
                this.data.count = resp.data.data.unread_count;
            });
        },
        toggleNotificationsList() {
            this.$emit('toggle-notifications-list');
        },
        notificationClicked(notification) {
            this.markNotificationAsRead(notification);
        },
        markNotificationAsRead(notification) {
            this.data.notifications = this.data.notifications.map(noti => {
                if(noti.id === notification.id && noti.type === notification.type)
                    noti.read = true;

                return noti;
            });

            let notiType = 'lead-processor';
            if(typeof notification.type === 'number') {
                notiType = 'user';
            }

            this.api.markNotificationAsRead(notification.id, notiType).then((response) => {
                this.data.count = response.data.data.unread_count;

                this.performNotificationRedirection(notification);
            });

        },
        performNotificationRedirection(notification) {
            if(notification.type_id !== null && notification.type_id > 0) {
                switch (notification.type) {
                    case this.notificationTypes.lead:
                        if(notification.link !== null && notification.link > 0
                            && notification.link_type !== null && notification.link_type === this.notificationLinkTypes.task)
                            window.location.href = `/tasks/queue?tasks=${notification.link}`;
                        else {
                            window.location.href = `/consumer-product/?consumer_id=${notification.type_id}`;
                        }
                        break;
                    case this.notificationTypes.company:
                        if(notification.payload && notification.payload.sms && notification.payload.phone_number) {
                            this.dispatchGlobalEvent('sms', {
                                phone: notification.payload.phone_number,
                                name: notification.payload?.name ?? "",
                                id: notification.type_id,
                                relType: CommunicationRelationTypes.COMPANY,
                                relId: notification.type_id
                            });
                        } else if(notification.link !== null && notification.link > 0
                        && notification.link_type !== null && notification.link_type === this.notificationLinkTypes.company) {
                            window.location.href = `/companies/${notification.link}`;
                        }
                        break;
                    default:
                        if(notification.payload && notification.payload.sms && notification.payload.phone_number) {
                            this.dispatchGlobalEvent('sms', {
                                phone: notification.payload.phone_number,
                                name: notification.payload?.name ?? ""
                            });
                        }
                }
            } else if(notification.payload !== undefined && notification.payload !== null) {
                if(notification.payload && notification.payload.sms && notification.payload.phone_number) {
                    this.dispatchGlobalEvent('sms', {
                        phone: notification.payload.phone_number,
                        name: notification.payload?.name ?? ""
                    });
                }
            }
        },
        addNotification(notification) {
            this.newNotification = notification;
            this.notificationsBadge = true;
            this.data.notifications.unshift(notification);

            if(this.timeout !== null)
                clearTimeout(this.timeout);

            this.timeout = setTimeout(() => this.clearNewNotification(), 5000);
        },
        clearNewNotification() {
            this.notificationsBadge = false;
            this.newNotification = null;
        },
    },

    computed: {
        unreadCount() {
            if(this.data.notifications.length === 0)
                return 0;

            return this.data.count
        }
    }
}
</script>

<style scoped>

</style>
