<template>
    <span ref="copy" @click="handleCopy" class="cursor-copy">
        {{ content }}
    </span>
</template>

<script>
export default {
    name: "SpanToClipboard",

    props: {
        content: {
            type: String,
            required: true
        }
    },


    methods: {
        async handleCopy() {
            await navigator.clipboard.writeText(this.content);
        }
    }
}
</script>

<style scoped>

</style>
