<template>
    <modal small @close="handleClose" :dark-mode="darkMode" @confirm="handleSave"
           :close-text="'Cancel'" :confirm-text="loading ? 'Saving...' : 'Save'">
        <template v-slot:header>
            <h4>{{modalName}}</h4>
        </template>
        <template v-slot:content>
            <div class="grid gap-3">
                <div class="flex flex-col">
                    <p class="uppercase font-semibold text-xs w-20 pb-1"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Name
                    </p>
                    <TextField
                        v-model="localApiKey.name"
                        placeholder="Key name"
                        :dark-mode="darkMode"
                        :error-message="errors['name']"
                        :disabled="'id' in localApiKey"
                        @update:model-value="errors['name'] = ''"
                    >
                    </TextField>
                </div>
                <div class="flex flex-col gap-1">
                    <p class="uppercase font-semibold text-xs w-20"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Origins ({{ localApiKey.origins.length }})
                    </p>

                    <div id="originList" class="max-h-64 overflow-y-scroll" :class="{'pr-6': localApiKey.origins.length > 5}">
                        <div v-for="(origin, idx) in localApiKey.origins" :key="idx" class="grid mb-2">
                            <TextField
                                v-model="origin.origin"
                                :placeholder="`Origin ${idx + 1}`"
                                :dark-mode="darkMode"
                                :error-message="errors[`origins.${idx}.origin`]"
                                :disabled="'id' in origin"
                                @update:model-value="errors[`origins.${idx}.origin`] = ''"
                            >
                                <template v-slot:prepend-slot>
                                    <confirm-delete-button
                                        @confirm="handleRemoveOrigin(idx);resetInputError(`origins.${idx}.origin`)"></confirm-delete-button>
                                </template>
                            </TextField>
                        </div>

                    </div>
                    <div>
                        <custom-button :dark-mode="darkMode" color="primary" @click="handleAddOrigin">
                            Add origin
                        </custom-button>
                    </div>
                </div>

            </div>
        </template>
    </modal>
</template>

<script>
import Modal from "../../../../Shared/components/Modal.vue";
import ApiService from "../../services/api";
import CustomButton from "../../../../Shared/components/CustomButton.vue";
import TextField from "./TextField.vue";
import ConfirmDeleteButton from "./ConfirmDeleteButton.vue";

// Prevents the modification of existing property attributes and values
const ORIGIN_DEFAULT_SHAPE = Object.freeze({
    origin: ''
})

export default {
    name: "ManageKeyModal",
    components: {ConfirmDeleteButton, CustomButton, Modal, TextField},
    props: {
        darkMode: {
            type: Boolean,
            required: true
        },
        website: {
            type: Object,
            required: true
        },

        apiKey: {
            type: Object,
            required: false
        }
    },
    emits: ['close', 'error'],

    mounted() {
        this.localApiKey = this.apiKey ?? this.localApiKey
        if (this.localApiKey.origins.length === 0) this.handleAddOrigin()
    },

    computed: {
        modalName(){
            const title = this.localApiKey['id'] ? 'Edit' : 'Add';

            return title + ' Key'
        }
    },

    data() {
        return {
            api: ApiService.make(),
            loading: false,
            localApiKey: {
                name: '',
                origins: []
            },
            errors: {}
        }
    },

    methods: {
        handleClose() {
            this.$emit('close')
        },

        getCleanPayload(){
            // Trim values before sending to server
            const origins = this.localApiKey.origins.map(o => ({...o, origin: o.origin.trim()}))
            const name = this.localApiKey.name.trim()

            return {
                origins,
                name
            }
        },

        async handleSave() {
            this.loading = true;

            const { name, origins } = this.getCleanPayload()

            try {
                if ('id' in this.localApiKey) await this.updateApiKey({name, origins})
                else await this.createApiKey({name, origins})

                this.handleClose()
            } catch (error) {
                this.loading = false
                console.error(error)
                this.errors = error.response.data.errors ?? {}
                this.$emit('error', error)
            }
        },

        async createApiKey({ name, origins }) {
            await this.api.addKey(this.website.id, {
                name,
                origins
            })
        },

        async updateApiKey({ origins }) {
            await this.api.updateKey(this.website.id, this.apiKey.id, {
                origins
            })
        },

        handleAddOrigin() {
            this.localApiKey.origins.push({...ORIGIN_DEFAULT_SHAPE})

            this.scrollDivToBottomById('originList')
        },

        scrollDivToBottomById(){
            // Scroll div bottom to show new fields added
            const originListDiv = document.getElementById("originList");
            setTimeout(() => originListDiv.scrollTop = originListDiv.scrollHeight, 100)
        },

        handleRemoveOrigin(idx) {
            this.localApiKey.origins.splice(idx, 1)

            if (this.localApiKey.origins.length === 0) this.handleAddOrigin()
        },

        resetInputError(path) {
            this.errors[path] = ''
        }
    }
}
</script>

<style scoped>

</style>
