<template>
    <div class="flex gap-2 w-full">
        <input
            class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
            :class="style.input"
            :placeholder="placeholder"
            :value="modelValue"
            @input="$emit('update:modelValue', $event.target.value)"
            :disabled="disabled"
            :type="type"
        >
        <slot name="prepend-slot"></slot>
    </div>
    <span v-if="errorMessage" class="mt-1 ml-1 text-sm text-red-600 dark:text-red-500 font-medium">
        {{ typeof errorMessage === 'object' ? errorMessage[0] : errorMessage }}
    </span>
</template>

<script>
export default {
    name: "TextField",

    props: {
        darkMode: {
            type: Boolean,
            required: true
        },

        modelValue: {
            type: String,
            required: false
        },

        placeholder: {
            type: String,
            required: false
        },

        errorMessage: {
            type: [String, Array],
            required: false
        },

        disabled: {
            type: Boolean,
            default: false
        },

        height: {
            type: String,
            default: null
        },
        type: {
            type: String,
            default: 'text'
        }
    },
    emits: ['update:modelValue'],

    computed: {
        style() {
            return {
                input: {
                    'border-grey-200 bg-grey-50': !this.darkMode,
                    'border-blue-700 bg-dark-background text-blue-400': this.darkMode,
                    'border-red-500': this.errorMessage,
                    'cursor-not-allowed': this.disabled,
                    [this.height]: true
                }
            }
        }
    },
}
</script>

<style scoped>

</style>
