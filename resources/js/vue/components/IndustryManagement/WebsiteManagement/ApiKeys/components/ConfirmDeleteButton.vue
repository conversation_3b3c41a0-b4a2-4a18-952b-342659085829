<template>
    <div class="flex items-center"
        :class="[darkMode ? 'text-red-500' : 'text-red-700']"
    >
        <simple-icon v-if="flag" @click="handleClick" icon="bin" color="red" clickable/>
        <div v-else class="flex flex-col">
            <span class="font-bold text-base">
                Delete?
            </span>
            <div class="flex justify-evenly">
                <simple-icon @click="$emit('confirm');handleClick()" icon="check" color="red" clickable/>
                <simple-icon @click="handleClick" icon="x-mark" color="red" clickable/>
            </div>
        </div>
    </div>
</template>

<script>
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";

export default {
    name: "ConfirmDeleteButton",
    components: {SimpleIcon},

    emits: ['confirm'],

    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        small: {
            type: Boolean,
            default: false,
        },
    },

    data() {
        return {
            flag: true
        }
    },


    methods: {
        handleClick() {
            this.flag = !this.flag
        }
    },
}
</script>

<style scoped>
</style>
