<template>
    <div class="p-5">
        <div class="flex items-center justify-between mb-3">
            <h5 class="text-primary-500 font-semibold leading-tight">Keys for {{ website?.name }}</h5>
            <button
                class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5" @click="openModal">
                Add New Key
            </button>
        </div>
        <div class="grid grid-cols-9 gap-x-3 mb-2 px-5" v-if="!loading">
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Name</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-3">Key</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Origins</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs">Status</p>
        </div>
        <div class="border-t border-b" v-if="!loading"
             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}">
            <div>
                <div class="grid grid-cols-9 gap-x-3 border-b px-5 py-3 items-center"
                     v-for="key in apiKeys" :key="key.id"
                     :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                    <p class="text-sm col-span-2 truncate">
                        {{ key.name }}
                    </p>
                    <p class="text-sm col-span-3 truncate">
                        <SpanToClipboard :content="key.key"></SpanToClipboard>
                    </p>
                    <div class="text-sm col-span-2 truncate">
                        <span v-for="origin in key.origins">{{origin.origin}} <br></span>
                    </div>
                    <toggle v-model="key.status" @click="updateKeyStatus(key)"></toggle>
                    <ActionsHandle :dark-mode="darkMode" no-delete-button @edit="handleEditKey(key)"/>
                </div>
            </div>
        </div>
        <div class="h-32 flex items-center justify-center" v-if="loading">
            <loading-spinner></loading-spinner>
        </div>
        <manage-key-modal v-if="showModal" @close="closeModal" @error="handleError" :dark-mode="darkMode"
                          :website="website" :api-key="selectedApiKey"></manage-key-modal>
        <alerts-container :dark-mode="darkMode" :alert-type="'error'" :text="error" v-if="error !== null"></alerts-container>
    </div>
</template>

<script>
import ApiService from "../services/api";
import loadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import Modal from "../../../Shared/components/Modal.vue";
import Toggle from "../../../Inputs/Toggle/Toggle.vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import ManageKeyModal from "./components/ManageKeyModal.vue";
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";
import SpanToClipboard from "./components/SpanToClipboard.vue";

export default {
    name: "ApiKeys",
    components: {SpanToClipboard, ActionsHandle, loadingSpinner, Modal, Toggle, AlertsContainer, ManageKeyModal},
    props: {
        website: {
            type: Object,
            default: null
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            apiKeys: [],
            name: null,
            api: ApiService.make(),
            loading: false,
            error: null,
            showModal: false,
            selectedApiKey: null
        }
    },
    created() {
        if (this.website) this.getKeys()
    },
    methods: {
        getKeys() {
            this.apiKeys = [];
            this.loading = true;
            this.resetError();
            this.api.getKeys(this.website.id)
                .catch(e => this.error = e.response.data.message)
                .then(resp => this.apiKeys = resp.data.data.api_keys)
                .finally(() => this.loading = false);
        },
        updateKeyStatus(key) {
            let response = null;
            this.loading = true;
            this.resetError();

            if (key.status) response = this.api.enableKey(this.website.id, key.id);
            else response = this.api.disableKey(this.website.id, key.id);

            response.catch(e => {
                this.error = e.response.data.message;
                key.status = !key.status;
            }).finally(() => this.loading = false);
        },
        openModal() {
            this.resetError();
            this.showModal = true;
        },
        closeModal() {
            this.resetInput();
            this.showModal = false;
            this.getKeys()
        },

        handleEditKey(key){
            // Avoid data binding when inserting/removing a origin from the list
            this.selectedApiKey = {...key, origins: [...key.origins]}

            this.openModal()
        },

        handleError(error){
            this.error = error.response.data.message;
        },

        resetInput(){
            this.selectedApiKey = null
        },

        resetError() {
            this.error = null;
        }
    }
}
</script>
