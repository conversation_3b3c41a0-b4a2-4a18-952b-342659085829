<template>
    <div>
        <div class="flex items-center justify-between p-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Industry List</h5>
            <button
                class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5" @click="openModal">
                Add New Industry
            </button>
        </div>
        <div class="border-t border-b p-5 mb-5">
            <div class="inline-flex gap-5 items-center" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
                <div class="text-slate-500 font-semibold tracking-wide text-xs uppercase whitespace-nowrap">Default Lead Email Template:</div>
                <dropdown :options="defaultLeadEmailTemplateOptions" :placeholder="'Select Template'" v-model="defaultLeadEmailTemplate" class="min-w-[20rem]" v-if="editDefaultTemplate">
                </dropdown>
                <div class="text-sm col-span-2 truncate text-primary-500 cursor-pointer" v-else @click="editDefaultTemplate = true">
                    {{ defaultLeadEmailTemplate ? emailTemplates.find(template => template.id === defaultLeadEmailTemplate)?.name : 'Add A Default Template' }}
                </div>
                <div v-if="editDefaultTemplate">
                    <button class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5" @click="saveDefaultLeadEmailTemplate">
                        Save
                    </button>
                </div>
            </div>
        </div>
        <div class="grid grid-cols-10 gap-x-3 mb-2 px-5 pt-5" v-if="!loading">
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-3">Name</p>
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Lead Delivery Email Template</p>
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-5">Attributes</p>
        </div>
        <div class="border-t border-b h-[22rem] overflow-y-auto" v-if="!loading"
             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}">
            <div>
                <div class="grid grid-cols-10 gap-x-3 border-b px-5 py-3 items-center"
                     v-for="industry in industries" :key="industry.id"
                     :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                    <p class="text-sm col-span-2 truncate">
                        {{ industry.name }}
                    </p>
                    <div class="text-xs italic"
                         :class="[darkMode ? 'text-slate-400 ' : 'text-slate-600']"
                    >
                        <p v-if="industry.configuration?.future_campaigns_active">Using Future Campaigns</p>
                    </div>

                    <div class="col-span-2">
                        <p class="text-primary-500 cursor-pointer text-sm" v-if="industry.deliveryEmailTemplate === null" @click="showEmailTemplateModal(industry)">
                            Select Template
                        </p>
                        <div v-else>
                            <p class="text-xs col-span-2 truncate mb-1">
                                {{ industry.deliveryEmailTemplate.name }}
                            </p>
                            <p class="text-primary-500 cursor-pointer text-xs" @click="showEmailTemplateModal(industry)">
                                Update
                            </p>
                        </div>
                    </div>

                    <p class="text-primary-500 cursor-pointer text-sm" @click="viewWebsites(industry)">
                        Websites
                    </p>
                    <p class="text-primary-500 cursor-pointer text-sm" @click="viewServices(industry)">
                        Services
                    </p>
                    <p class="text-primary-500 cursor-pointer text-sm" @click="viewCompanyFields(industry)">
                        Company Fields
                    </p>
                    <p class="text-primary-500 cursor-pointer text-sm" @click="viewConsumerFields(industry)">
                        Consumer Fields
                    </p>

                    <ActionsHandle :dark-mode="darkMode" @edit="editIndustry(industry)" @delete="deleteIndustry(industry)"/>
                </div>
            </div>
        </div>
        <div class="h-32 flex items-center justify-center" v-if="loading">
            <loading-spinner></loading-spinner>
        </div>
        <div class="p-3">

        </div>
        <modal
            :small="true"
            v-if="showModal"
            @close="closeModal"
            :dark-mode="darkMode"
            @confirm="saveIndustry"
            :close-text="'Close'"
            :confirm-text="confirmText"
        >
            <template v-slot:header>
                <h4>{{ industry.id ? 'Update' : 'Create' }} Industry</h4>
                <p class="text-red-500 text-sm">Please notify a developer after {{ industry.id ? 'any updates to name' : 'creation' }}.</p>
            </template>
            <template v-slot:content>
                <div class="flex items-center pb-3 ">
                    <p class="uppercase font-semibold text-xs w-20"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Name
                    </p>
                    <input class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                           placeholder="Industry Name"
                           v-model="industry.name"
                           :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                </div>
                <div v-if="loading || saving" class="absolute w-full h-full">
                    <LoadingSpinner />
                </div>
                <div v-else>
                    <div class="grid grid-cols-3 gap-3">
                        <div class="flex items-center">
                            <p class="uppercase font-semibold text-xs w-50 mr-3"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Dark Mode Color
                            </p>
                            <input
                                id="dark_mode_color"
                                type="color"
                                @change="hasChangedDarkColor"
                                v-model="industry.dark_mode_color">
                        </div>
                        <div class="flex items-center">
                            <p class="uppercase font-semibold text-xs w-50 mr-3"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Light Mode Color
                            </p>
                            <input
                                id="light_mode_color"
                                type="color"
                                @change="hasChangedLightColor"
                                v-model="industry.light_mode_color">
                        </div>
                        <div class="flex items-center">
                            <label for="use_default_colors"
                                class="uppercase font-semibold text-xs w-50 mr-3"
                                :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                                Reset to Default Colors
                            </label>
                            <input
                                id="use_default_colors"
                                type="checkbox"
                                v-model="useDefaultColors">
                        </div>
                    </div>
                    <div class="grid grid-cols-4 gap-x-3 gap-y-6 mt-16 items-center"
                         :class="[ darkMode ? 'text-blue-400 ' : 'text-grey-600']"
                         v-if="permissionStore.hasPermission(PERMISSIONS.PERMISSION_INDUSTRY_CONFIGURATION)"
                    >
                        <p class="uppercase font-semibold text-xs">Future Campaigns</p>
                        <div class="justify-self-center">
                            <CustomButton
                                v-if="!industry.configuration?.future_campaigns_active"
                                :dark-mode="darkMode"
                                color="primary"
                                @click="confirmFutureCampaignsChange(true)"
                            >
                                Enable
                            </CustomButton>
                            <CustomButton
                                v-else
                                :dark-mode="darkMode"
                                color="red"
                                @click="confirmFutureCampaignsChange(false)"
                            >
                                Disable
                            </CustomButton>
                        </div>
                        <div class="col-span-2">
                            <p class="text-sm italic">Enable or disable using the new CompanyCampaign model for all Campaign operations and product allocation. Only disable as a last resort.</p>
                        </div>
                        <p class="uppercase font-semibold text-xs">
                            Allow Custom Floor Pricing
                        </p>
                        <div class="justify-self-center">
                            <CustomButton
                                v-if="!industry.configuration?.allow_custom_floor_prices"
                                :dark-mode="darkMode"
                                color="primary"
                                @click="toggleCustomFloorPricing(industry.id, true)"
                            >
                                Enable
                            </CustomButton>
                            <CustomButton
                                v-else
                                :dark-mode="darkMode"
                                color="red"
                                @click="toggleCustomFloorPricing(industry.id, false)"
                            >
                                Disable
                            </CustomButton>
                        </div>
                        <div  class="text-sm col-span-2 italic">
                            <p>
                                Account Managers may set custom state or county floor prices per Campaign
                                to allow companies to bid under the floor price in that area.
                            </p>
                        </div>
                        <p class="uppercase font-semibold text-xs">
                            Reviews Enabled
                        </p>
                        <Toggle
                            :model-value="industry.configuration?.consumer_reviews_active"
                            :dark-mode="darkMode"
                            @change="(newValue) => toggleConsumerReviews(industry.id, newValue)"
                        />
                        <p class="col-span-2 text-sm italic">
                            Controls whether the Reviews section is active for companies in Fixr Dashboard
                        </p>
                    </div>
                </div>
            </template>
        </modal>
        <modal :small="true" v-if="showFutureCampaignConfirmation" @close="closeConfirmationModal" :dark-mode="darkMode" @confirm="handleFutureCampaignsChange" :close-text="'Cancel'" confirm-text="OK">>
            <template v-slot:header>
                <h4>{{enablingFutureCampaigns ? 'Enable' : 'Disable' }} Future Campaigns</h4>
            </template>
            <template v-slot:content>
                <p v-if="enablingFutureCampaigns">
                    Are you sure you wish to enable Future Campaigns? This will switch all product processing and allocation in the industry to updated Admin2.0 logic.
                </p>
                <p v-else>
                    Are you sure you wish to disable Future Campaigns? This will restore all products received in this industry to Legacy processing and allocation.
                </p>
            </template>
        </modal>
        <alerts-container :dark-mode="darkMode" :alert-type="alertType || 'error'" :text="alertText || error" v-if="alertActive || error"></alerts-container>
        <modal v-if="showEmailTemplateSelectionModal" @close="closeEmailTemplateModal" @confirm="saveDeliveryEmailTemplate" :disable-confirm="!selectedEmailTemplate">
            <template v-slot:header>Select Delivery Email Template</template>
            <template v-slot:content>
                <dropdown :options="emailTemplateOptions" v-model="selectedEmailTemplate" :placeholder="'Select Template'"></dropdown>
            </template>
        </modal>
    </div>

</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import ApiService from "../services/api";
import Api from "../../../EmailTemplates/API/api";
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import {DateTime} from "luxon";
import { PERMISSIONS, useRolesPermissions } from "../../../../../stores/roles-permissions.store.js";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import Toggle from "../../../Inputs/Toggle/Toggle.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin.js";

const getDefaultConfiguration = () => {
    return {
        future_campaigns_active: null,
        allow_custom_floor_prices: 0,
        consumer_reviews_active: false,
    }
}

export default {
    name: "Industries",
    components: { Toggle, CustomButton, Dropdown, Modal, ActionsHandle, LoadingSpinner, AlertsContainer},
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            api: ApiService.make(),
            emailTemplateApi: Api.make(),
            showModal: false,
            industries: [],
            hasSetLightColor: false,
            hasSetDarkColor: false,
            useDefaultColors: false,
            industry: {
                name: null,
                light_mode_color: null,
                dark_mode_color: null,
                configuration: getDefaultConfiguration(),
            },
            emailTemplates: [],
            saving: false,
            loading: false,
            error: null,
            showEmailTemplateSelectionModal: false,
            selectedEmailTemplate: null,
            selectedIndustry: null,
            defaultLeadEmailTemplate: null,
            editDefaultTemplate: false,

            permissionStore: useRolesPermissions(),
            PERMISSIONS,
            showFutureCampaignConfirmation: false,
            enablingFutureCampaigns: true,
        }
    },
    created() {
        this.getIndustries();
        this.loadEmailTemplates();
    },
    computed: {
        confirmText: function () {
            if (this.saving) return 'Saving...';
            if (this.industry.id) return 'Update Colors';

            return 'Create';
        },
        emailTemplateOptions: function () {
            return [
                {id: 'no_template', name: 'No Template'},
                ...this.defaultLeadEmailTemplateOptions
            ];
        },
        defaultLeadEmailTemplateOptions: function () {
            return this.emailTemplates.map(template => {
                return {
                    id: template.id,
                    name: template.name
                }
            })
        },
    },
    methods: {
        hasChangedLightColor() {
            this.hasSetLightColor = true;
        },
        hasChangedDarkColor() {
            this.hasSetDarkColor = true;
            this.industry.light_mode_color = this.adjustColor(this.industry.dark_mode_color, 85);
            this.hasSetLightColor = true;
        },
        openModal() {
            this.showModal = true;
        },
        closeModal () {
            this.showModal = false;
            this.industry = {
                name: null,
                light_mode_color: null,
                dark_mode_color: null,
                configuration: getDefaultConfiguration(),
            };
            this.error = null;
        },
        getIndustries() {
            this.error = null;
            this.loading = true;
            this.api.getIndustries().then(resp => this.industries = resp.data.data.industries)
                .catch(e => this.error = e.response.data.message).finally(() => this.loading = false);
        },
        saveIndustry() {
            if (this.saving) return;
            this.saving = true;
            this.error = null;

            if (this.useDefaultColors) {
                this.industry.dark_mode_color = null;
                this.industry.light_mode_color = null;
                this.useDefaultColors = false;
            }

            // Prevent the colour picker's default #000000 from being set
            // on the industry when no colours have been selected
            if (!this.hasSetDarkColor) this.industry.dark_mode_color = null;
            if (!this.hasSetLightColor) this.industry.light_mode_color = null;

            if (this.industry.id) {
                this.updateIndustry();
                return;
            }
            this.createIndustry();
        },
        createIndustry() {
            this.api.createIndustry(this.industry).then(() => this.refreshList(true))
                .catch(e => this.error = e.response.data.message).finally(() => this.saving = false);
        },
        editIndustry(industry) {
            Object.assign(this.industry, industry);
            this.industry.configuration = this.industry.configuration ?? getDefaultConfiguration();

            this.openModal();
        },
        updateIndustry() {
            this.industry.save_configuration = true;
            this.api.updateIndustry(this.industry.id, this.industry).then(() => this.refreshList(true))
                .catch(e => this.error = e.response.data.message).finally(() => this.saving = false);
        },
        deleteIndustry(industry) {
            this.error = null;
            this.api.deleteIndustry(industry.id).then(() => this.getIndustries())
                .catch(e => this.error = e.response.data.message);
        },
        formatDate(date) {
            return date ? DateTime.fromISO(date).toLocaleString(DateTime.DATETIME_SHORT) : null
        },
        viewWebsites(industry) {
            this.$emit('industry-websites-requested', industry);
        },
        viewCompanies(industry) {
            this.$emit('industry-companies-requested', industry);
        },
        viewServices(industry) {
            this.$emit('industry-services-requested', industry);
        },
        viewCompanyFields(industry) {
            this.$emit('industry-company-fields-requested', industry);
        },
        viewConsumerFields(industry) {
            this.$emit('industry-consumer-fields-requested', industry);
        },
        viewTypes(industry) {
            this.$emit('industry-types-requested', industry);
        },
        refreshList(closeModal = false) {
            this.getIndustries();
            if(closeModal) this.closeModal();
        },
        // Lighten or darken a HEX colour Eg:
        // adjust('#ffffff', -20) => "#ebebeb"
        // adjust('000000', 20) => "#141414"
        adjustColor(color, amount) {
            return '#' + color.replace(/^#/, '').replace(/../g, color => ('0'+Math.min(255, Math.max(0, parseInt(color, 16) + amount)).toString(16)).substr(-2));
        },

        loadEmailTemplates() {
            this.emailTemplateApi.getAllEmailTemplates()
                .then(resp => {
                    this.emailTemplates = resp.data.data.templates;
                    this.defaultLeadEmailTemplate = this.emailTemplates.find(template => !!template.default_lead_email)?.id ?? null;
                })
                .catch(() => this.error = 'Failed to load email templates');
        },

        showEmailTemplateModal(industry) {
            this.selectedIndustry = industry;
            this.selectedEmailTemplate = industry.deliveryEmailTemplate?.id ?? null
            this.showEmailTemplateSelectionModal = true;
        },

        closeEmailTemplateModal() {
            this.selectedIndustry = null;
            this.selectedEmailTemplate = null;
            this.showEmailTemplateSelectionModal = false;
        },

        saveDeliveryEmailTemplate() {
            const template = this.selectedEmailTemplate === 'no_template' ? null : this.selectedEmailTemplate;

            this.api.saveDeliveryEmailTemplate(this.selectedIndustry.id, template)
                .then(() => {
                    this.selectedIndustry.deliveryEmailTemplate = this.emailTemplates.find(temp => temp.id === template) ?? null;
                    this.closeEmailTemplateModal();
                })
                .catch(() => this.error = `Failed to save delivery email template for industry: ${this.selectedIndustry.name}`)
                .finally(() => this.closeEmailTemplateModal());
        },

        saveDefaultLeadEmailTemplate() {
            if (!this.defaultLeadEmailTemplate) return;

            this.api.saveDefaultLeadEmailTemplate(this.defaultLeadEmailTemplate)
                .then(() => this.editDefaultTemplate = false)
                .catch(() => this.error = 'Failed to save default lead email delivery template.');
        },

        enableFutureCampaigns() {
            if (this.saving) return;

            this.saving = true;
            this.api.enableFutureCampaigns(this.industry.id).then(resp => {
                if (resp.data?.data?.status)
                    this.getIndustries();
                else
                    this.error = "Could not enable Future Campaigns for this industry - has it been migrated?"
            }).catch(e => this.error = e)
                .finally(() => {
                    this.showModal = false;
                    this.saving = false;
                    this.showFutureCampaignConfirmation = false;
                });
        },

        disableFutureCampaigns() {
            if (this.saving) return;

            this.saving = true;
            this.api.disableFutureCampaigns(this.industry.id).then(resp => {
                if (resp.data?.data?.status)
                    this.getIndustries();
                else
                    this.error = "Could not disable Future Campaigns for this industry"
            }).catch(e => this.error = e)
                .finally(() => {
                    this.saving = false;
                    this.showFutureCampaignConfirmation = false;
                    this.showModal = false;
                });
        },

        toggleCustomFloorPricing(industryId, newValue) {
            this.saving = true;
            this.api.toggleCustomFloorPricingFlag(industryId, newValue).then(resp => {
                if (resp.data.data.status) {
                    this.showAlert('success', `Industry custom floor pricing was ${newValue ? 'enabled' : 'disabled'}.`);
                    this.industry.configuration.allow_custom_floor_prices = newValue;
                }
                else
                    this.showAlert('error', resp.response.data.message);
            }).catch(e => {
                this.error = (e.message);
            }).finally(() => {
                this.saving = false;
            });
        },

        toggleConsumerReviews(industryId, newValue) {
            this.saving = true;
            this.api.updateConsumerReviewsFlag(industryId, newValue).then(resp => {
                if (resp.data.data?.status)
                    this.industry.configuration.consumer_reviews_active = newValue;
                else
                    this.showAlert('error', resp.response.data.message ?? 'Something went wrong.');
            }).catch(e => void this.showAlert('error', e.message ?? e ?? 'Something went wrong')
            ).finally(() => this.saving = false);
        },

        closeConfirmationModal() {
            this.error = null;
            this.showFutureCampaignConfirmation = false;
            this.enablingFutureCampaigns = null;
        },

        confirmFutureCampaignsChange(enable = true) {
            this.enablingFutureCampaigns = enable;
            this.showFutureCampaignConfirmation = true;
        },

        handleFutureCampaignsChange() {
            if (this.enablingFutureCampaigns === true)
                this.enableFutureCampaigns();
            else if (this.enablingFutureCampaigns === false)
                this.disableFutureCampaigns();
        }
    }
}
</script>
