<template>
    <div>
        <div class="flex items-center justify-between p-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Industry - Company List</h5>
            <button
                class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5" @click="openModal">
                Add New Company
            </button>
        </div>
        <div class="grid grid-cols-7 gap-x-3 mb-2 px-5" v-if="!loading">
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Industry</p>
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Company</p>
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Created At</p>
        </div>
        <div class="border-t border-b h-[22rem] overflow-y-auto" v-if="!loading"
             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}">
            <div>
                <div class="grid grid-cols-7 gap-x-3 border-b px-5 py-3 items-center"
                     v-for="industryCompany in industryCompanies" :key="industryCompany.id"
                     :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                    <p class="text-sm col-span-2 truncate">
                        {{ industryCompany.industry }}
                    </p>
                    <p class="text-sm col-span-2 truncate">
                        {{ industryCompany.company.name }}
                    </p>
                    <p class="text-sm col-span-2">
                        {{ formatDate(industryCompany.created_at) }}
                    </p>
                    <ActionsHandle :dark-mode="darkMode" @delete="deleteIndustryCompany(industryCompany)" :no-edit-button="true"/>
                </div>
            </div>
        </div>
        <div class="h-32 flex items-center justify-center" v-if="loading">
            <loading-spinner></loading-spinner>
        </div>
        <div class="p-3"></div>
        <modal :small="true" v-if="showModal" @close="closeModal" :dark-mode="darkMode" @confirm="saveIndustryCompany" :close-text="'Cancel'" :confirm-text="confirmText">
            <template v-slot:header>
                <h4>{{ 'Add' }} Company</h4>
            </template>
            <template v-slot:content>
                <div class="grid gap-3">
                    <div v-if="!industryCompany.id" class="flex items-center">
                        <p class="uppercase font-semibold text-xs w-20"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Company
                        </p>
                        <Dropdown :dark-mode="darkMode" v-model="industryCompany.company.id" :options="companies" placeholder="Company" :selected="industryCompany.company.id"></Dropdown>
                    </div>
                </div>
            </template>
        </modal>
        <alerts-container :dark-mode="darkMode" :alert-type="'error'" :text="error" v-if="error"></alerts-container>
    </div>

</template>

<script>
import Dropdown from "../../../Shared/components/Dropdown.vue";
import Modal from "../../../Shared/components/Modal.vue";
import ApiService from "../services/api";
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import {DateTime} from "luxon";

export default {
    name: "IndustryCompanies",
    components: {Dropdown, Modal, ActionsHandle, LoadingSpinner, AlertsContainer},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        industry: {
            type: Number,
            default: 0
        }
    },
    data () {
        return {
            api: ApiService.make(),
            showModal: false,
            industryCompanies: [],
            industryCompany: {company: {id: null, name: null}},
            companies: [],
            saving: false,
            loading: false,
            error: null
        }
    },
    created() {
        this.getIndustryCompanies();
        this.getSystemCompanies();
    },
    computed: {
        confirmText: function () {
            if (this.saving) return 'Saving...';

            return 'Create';
        }
    },
    methods: {
        openModal() {
            this.showModal = true;
        },
        closeModal () {
            this.showModal = false;
            this.industryCompany = {company: {id: null, name: null}};
        },
        getIndustryCompanies() {
            if(!this.industry) return;
            if(!this.loading) this.loading = true;
            this.error = null;

            this.api.getIndustryCompanies(this.industry).then(resp => this.industryCompanies = resp.data.data.companies)
                .catch(e => this.error = e.response.data.message).finally(() => this.loading = false);
        },
        saveIndustryCompany() {
            if(this.saving) return;

            this.saving = true;
            this.error  = null;

            this.createIndustryCompany();
        },
        createIndustryCompany() {
            this.api.addIndustryCompany(this.industry, {company_id : this.industryCompany.company.id}).then(() => this.refreshList(true, true))
              .catch(e => this.error = e.response.data.message).finally(() => this.saving = false);
        },
        deleteIndustryCompany(industryCompany) {
            this.error = null;
            this.api.deleteIndustryCompany(industryCompany.id).then(() => this.refreshList(false, true))
                .catch(e => this.error = e.response.data.message);
        },
        formatDate(date) {
            return date ? DateTime.fromISO(date).toLocaleString(DateTime.DATETIME_SHORT) : null
        },
        getSystemCompanies() {
            this.api.getNonAddedCompanies(this.industry).then(resp => this.companies = resp.data.data.companies)
                .catch(e => this.error = e.response.data.message);
        },
        refreshList(closeModal = false, fetchCompanies = false) {
            this.getIndustryCompanies();

            if(closeModal) this.closeModal();
            if(fetchCompanies) this.getSystemCompanies();
        }
    }
}
</script>
