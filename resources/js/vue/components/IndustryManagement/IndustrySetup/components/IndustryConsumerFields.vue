<template>
    <div>
        <div class="flex items-center justify-between p-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Industry - Consumer Field List</h5>
            <button
                class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5" @click="openModal">
                Add New Consumer Field
            </button>
        </div>
        <div class="grid grid-cols-11 gap-x-3 mb-2 px-5" v-if="!loading">
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Industry</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Name</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Key</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Type</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Category</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Send To Company</p>
            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Created At</p>
        </div>
        <div class="border-t border-b h-[22rem] overflow-y-auto" v-if="!loading"
             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}">
            <div>
                <div class="grid grid-cols-11 gap-x-3 border-b px-5 py-3 items-center"
                     v-for="industryConsumerField in industryConsumerFields" :key="industryConsumerField.id"
                     :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                    <p class="text-sm col-span-2 truncate">
                        {{ industryConsumerField.industry }}
                    </p>
                    <p class="text-sm col-span-2 truncate">
                        {{ industryConsumerField.name }}
                    </p>
                    <p class="text-sm col-span-1 truncate">
                        {{ industryConsumerField.key }}
                    </p>
                    <p class="text-sm col-span-1 truncate">
                        {{ industryConsumerField.type?.label }}
                    </p>
                    <p class="text-sm col-span-1 truncate">
                        {{ industryConsumerField.category?.name }}
                    </p>
                    <p class="text-sm col-span-2">
                        {{ industryConsumerField.send_to_company }}
                    </p>
                    <p class="text-sm col-span-1">
                        {{ formatDate(industryConsumerField.created_at) }}
                    </p>
                    <ActionsHandle :dark-mode="darkMode" @edit="editIndustryConsumerField(industryConsumerField)" @delete="deleteIndustryConsumerField(industryConsumerField)"/>
                </div>
            </div>
        </div>
        <div class="h-32 flex items-center justify-center" v-if="loading">
            <loading-spinner></loading-spinner>
        </div>
        <div class="p-3"></div>
        <modal :small="true" v-if="showModal" @close="closeModal" :dark-mode="darkMode" @confirm="saveIndustryConsumerField" :close-text="'Cancel'" :confirm-text="confirmText">
            <template v-slot:header>
                <h4>{{ industryConsumerField.id ? 'Update' : 'Create' }} Consumer Field</h4>
            </template>
            <template v-slot:content>
                <div class="grid gap-3">
                    <div v-if="!industryConsumerField.id" class="flex items-center">
                        <p class="uppercase font-semibold text-xs w-20"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Type
                        </p>
                        <Dropdown :dark-mode="darkMode" v-model="industryConsumerField.type.id" :options="fieldTypes" placeholder="Type" :selected="industryConsumerField.type.id"></Dropdown>
                    </div>
                    <div class="flex items-center">
                        <p class="uppercase font-semibold text-xs w-20"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Name
                        </p>
                        <input class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                               placeholder="Name"
                               v-model="industryConsumerField.name"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                    </div>
                    <div class="flex items-center">
                        <p class="uppercase font-semibold text-xs w-20"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Key
                        </p>
                        <input class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                               placeholder="Key"
                               v-model="industryConsumerField.key"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                    </div>
                    <div class="flex items-center">
                        <p class="uppercase font-semibold text-xs w-20"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Category
                        </p>
                        <Dropdown :dark-mode="darkMode" v-model="industryConsumerField.category_id" :options="consumerConfigurableFieldCategories" placeholder="Type" :selected="industryConsumerField.category_id"></Dropdown>
                    </div>
                    <div class="flex items-center">
                        <p class="uppercase font-semibold text-xs w-32"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Send To Company
                        </p>
                        <toggle v-model="industryConsumerField.send_to_company"></toggle>
                    </div>
                    <div class="flex items-center">
                        <p class="uppercase font-semibold text-xs w-32"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Show on Dashboard
                        </p>
                        <toggle v-model="industryConsumerField.show_on_dashboard"></toggle>
                    </div>

                </div>
            </template>
        </modal>
        <alerts-container :dark-mode="darkMode" :alert-type="'error'" :text="error" v-if="error"></alerts-container>
    </div>

</template>

<script>
import Dropdown from "../../../Shared/components/Dropdown.vue";
import Toggle from "../../../Inputs/Toggle/Toggle.vue";
import Modal from "../../../Shared/components/Modal.vue";
import ApiService from "../services/api";
import FieldsApiService from "../../ConfigurableFields/services/api";
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import {DateTime} from "luxon";
import * as ConsumerConfigurableFieldCategory
    from "../../ConfigurableFields/services/consumerConfigurableFieldCategory";


export default {
    name: "IndustryConsumerFields",
    components: {Dropdown, Toggle, Modal, ActionsHandle, LoadingSpinner, AlertsContainer},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        industry: {
            type: Number,
            default: 0
        }
    },
    data () {
        return {
            api: ApiService.make(),
            fieldApi: FieldsApiService.make(),
            showModal: false,
            industryConsumerFields: [],
            industryConsumerField: {type: {id: null, name: null}, name: null, key: null, send_to_company: false, category_id: '', show_on_dashboard: false},
            fieldTypes: [],
            consumerConfigurableFieldCategories: [],
            saving: false,
            loading: false,
            error: null
        }
    },
    created() {
        this.getIndustryConsumerFields();
        this.getFieldTypes();
        this.getConsumerConfigurableFieldCategories()
    },
    computed: {
        confirmText: function () {
            if (this.saving) return 'Saving...';
            if (this.industryConsumerField.id) return 'Update';

            return 'Create';
        },
        defaultConsumerConfigurableFieldCategoryId(){
            return ConsumerConfigurableFieldCategory.getDefaultCategoryId(this.consumerConfigurableFieldCategories)
        }
    },
    methods: {
        setDefaultValueToIndustryConsumerField(){
            this.industryConsumerField = {
                type: {id: null, name: null},
                name: null,
                key: null,
                send_to_company: false,
                category_id: this.defaultConsumerConfigurableFieldCategoryId,
                show_on_dashboard: false,
            }
        },
        getConsumerConfigurableFieldCategories() {
            this.fieldApi.getConsumerConfigurableFieldCategories()
                .then(resp => {
                    this.consumerConfigurableFieldCategories = resp.data.data.categories
                    this.setDefaultValueToIndustryConsumerField()
                })
                .catch(() => this.showAlert('error', 'Problem loading consumer configurable field categories'));
        },
        openModal() {
            this.showModal = true;
        },
        closeModal () {
            this.showModal = false;
            this.setDefaultValueToIndustryConsumerField()
        },
        getIndustryConsumerFields() {
            if(!this.industry) return;
            if(!this.loading) this.loading = true;
            this.error = null;

            this.api.getIndustryConsumerFields(this.industry).then(resp => this.industryConsumerFields = resp.data.data.fields)
                .catch(e => this.error = e.response.data.message).finally(() => this.loading = false);
        },
        saveIndustryConsumerField() {
            if(this.saving) return;
            this.saving = true;
            this.error = null;

            if (this.industryConsumerField.id) {
                this.updateIndustryConsumerField();
                return;
            }
            this.createIndustryConsumerField();
        },
        getParamsToMakeRequest() {
            return {
                name              : this.industryConsumerField.name,
                key               : this.industryConsumerField.key,
                type              : this.industryConsumerField.type?.id,
                send_to_company   : this.industryConsumerField.send_to_company === true ?? false,
                category_id       : this.industryConsumerField.category_id,
                show_on_dashboard : this.industryConsumerField.show_on_dashboard === true ?? false,
            }
        },
        createIndustryConsumerField() {
            this.api.createIndustryConsumerField(this.industry, this.getParamsToMakeRequest()).then(() => this.refreshList(true))
                .catch(e => this.error = e.response.data.message).finally(() => this.saving = false);
        },
        editIndustryConsumerField(industryConsumerField) {
            this.industryConsumerField = {...industryConsumerField};
            this.industryConsumerField.send_to_company = this.industryConsumerField.send_to_company === 'Yes';
            this.industryConsumerField.show_on_dashboard = this.industryConsumerField.show_on_dashboard === 'Yes';
            this.openModal();
        },
        updateIndustryConsumerField() {
            this.api.updateIndustryConsumerField(this.industryConsumerField.id, this.industry, this.getParamsToMakeRequest()).then(() => this.refreshList(true))
                .catch(e => this.error = e.response.data.message).finally(() => this.saving = false);
        },
        deleteIndustryConsumerField(industryConsumerField) {
            this.error = null;
            this.api.deleteIndustryConsumerField(industryConsumerField.id).then(() => this.refreshList(false))
                .catch(e => this.error = e.response.data.message);
        },
        formatDate(date) {
            return date ? DateTime.fromISO(date).toLocaleString(DateTime.DATETIME_SHORT) : null
        },
        getFieldTypes() {
            this.fieldApi.getFieldTypes().then(resp => {
                resp = resp.data.data?.field_types;
                let types = [];
                resp.map(function(item) {
                    types.push({id: item['id'], name: item['label']})
                });
                this.fieldTypes = types;
            }).catch(e => this.error = e.response.data.message);
        },
        refreshList(closeModal = false) {
            this.getIndustryConsumerFields();
            if(closeModal) this.closeModal();
        }
    }
}
</script>
