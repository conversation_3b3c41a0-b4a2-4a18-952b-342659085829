<template>
    <div class="max-h-[85vh] overflow-y-auto p-5 rounded">
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>
        <div class="flex items-center justify-between mb-4">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Default Profitability Configuration</h5>
            <div class="flex items-center gap-x-4">
                <custom-button v-if="editingAll" :dark-mode="darkMode" color="green-outline" @click="updateAll">
                    Update All
                </custom-button>
                <custom-button v-if="editingAll" :dark-mode="darkMode" color="red-outline" @click="cancelEditAll">
                    Cancel
                </custom-button>
                <custom-button :dark-mode="darkMode" color="primary" @click="editingAll = true" :disabled="editingAll">
                    Edit All
                </custom-button>
            </div>
        </div>
        <hr :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}" />
        <div v-if="loading || saving" class="flex flex-col h-80 items-center justify-center w-full">
            <loading-spinner :label="saving ? 'Saving' : 'Loading'" />
        </div>
        <div v-else>
            <div v-for="[ industryName, industry ] in Object.entries(industryServices)"
                 class="flex flex-col py-5 border-b"
                 :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}"
                 :key="industryName"
            >
                <button @click="toggleIndustry(industryName)"
                        class="cursor-pointer flex items-center text-primary-500 font-semibold"
                >
                    <svg v-if="expandedIndustries.includes(industryName)" class="w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M18 12H6" />
                    </svg>
                    <svg v-else class="w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>

                    <p class="ml-2">{{ industryName }}</p>
                </button>
                <div v-if="expandedIndustries.includes(industryName)"
                    class="pt-3"
                >
                    <div v-for="service in industry"
                        class="py-3 mx-4 mt-2 border-t"
                        :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}"
                    >
                        <div class="flex items-center gap-x-2 mb-3 text-primary-300">
                            <p class="font-semibold">{{ service.name }}</p>
                            <svg v-if="!editingAll" class="w-4 cursor-pointer" @click="editServiceProfitability(service.num_id)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M21.731 2.269a2.625 2.625 0 00-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 000-3.712zM19.513 8.199l-3.712-3.712-12.15 12.15a5.25 5.25 0 00-1.32 2.214l-.8 2.685a.75.75 0 00.933.933l2.685-.8a5.25 5.25 0 002.214-1.32L19.513 8.2z" />
                            </svg>
                            <svg @click="updateServiceProfitability(service.num_id)"
                                v-if="editingService.id === service.num_id"
                                 class="w-6 cursor-pointer text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <svg @click="cancelServiceUpdate(service.num_id)"
                                 v-if="editingService.id === service.num_id"
                                 class="w-6 cursor-pointer text-red-700"  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="grid grid-cols-4 items-center ml-4 gap-x-8 max-w-[80rem]">
                            <div class="text-grey-400">
                                <custom-input
                                    :dark-mode="darkMode"
                                    v-model="defaultProfitabilityConfigurations[service.num_id].percentage_leads_successful"
                                    label="Percentage Leads Successful:"
                                    :disabled="this.editingService.id !== service.num_id && !this.editingAll"
                                    type="number"
                                />
                            </div>
                            <div>
                                <custom-input
                                    :dark-mode="darkMode"
                                    v-model="defaultProfitabilityConfigurations[service.num_id].average_lead_revenue"
                                    label="Average Lead Revenue:"
                                    :disabled="this.editingService.id !== service.num_id && !this.editingAll"
                                    type="number"
                                />
                            </div>
                            <div>
                                <custom-input
                                    :dark-mode="darkMode"
                                    v-model="defaultProfitabilityConfigurations[service.num_id].average_job_cost"
                                    label="Average Job Cost:"
                                    :disabled="this.editingService.id !== service.num_id && !this.editingAll"
                                    type="number"
                                />
                            </div>
                            <div>
                            <custom-input
                                :dark-mode="darkMode"
                                v-model="defaultProfitabilityConfigurations[service.num_id].labour_materials_cost"
                                label="Labor Materials Cost:"
                                :disabled="this.editingService.id !== service.num_id && !this.editingAll"
                                type="number"
                            />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../../mixins/alerts-mixin.js";
import SharedApiService from "../../Shared/services/api.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import DefaultProfitabilityApiService from "./services/api.js";
import CustomInput from "../../Shared/components/CustomInput.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";

export default {
    name: "DefaultProfitability",
    components: {
        CustomButton,
        CustomInput,
        LoadingSpinner,
        AlertsContainer
    },
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    mounted() {
        this.loading = true;
        this.getIndustryServices(true).then(() => {
            this.getDefaultProfitabilityAssumptions(true).then(() => {
                this.loading = false;
            });
        });
    },
    data () {
        return {
            api: DefaultProfitabilityApiService.make(),
            sharedApi: SharedApiService.make(),
            loading: false,
            saving: false,
            industryServices: {},
            defaultProfitabilityConfigurations: {},
            expandedIndustries: [],
            editingService: {
                id: null,
                percentage_leads_successful: 0,
                average_lead_revenue: 0,
                average_job_cost: 0,
                labour_materials_cost: 0
            },
            editingAll: false,
        }
    },
    methods: {
        async getIndustryServices(initialLoad = false) {
            if (!initialLoad && this.loading) return;
            this.loading = true;
            await this.sharedApi.getIndustryServices().then(resp => {
                if (resp.data?.data?.status) {
                    resp.data.data.industry_services.forEach(service => {
                        this.industryServices[service.industry] = this.industryServices[service.industry] ?? [];
                        this.industryServices[service.industry].push(service);
                        this.defaultProfitabilityConfigurations[service.num_id] = {
                            percentage_leads_successful: 0,
                            average_lead_revenue: 0,
                            average_job_cost: 0,
                            labour_materials_cost: 0,
                            industry_service_id: service.num_id,
                        }
                    });
                }
                else {
                    this.showAlert('error', `An error occurred fetching Industry data.`)
                }
            }).catch(err => {
                this.showAlert('error', err || `An error occurred fetching Industry data.`);
            }).finally(() => {
                if (!initialLoad) this.loading = false;
            });
        },
        async getDefaultProfitabilityAssumptions(initialLoad = false) {
            if (!initialLoad && this.loading) return;
            this.loading = true;
            await this.api.getDefaultProfitabilityConfigurations().then(resp => {
                if (resp.data?.data?.status) {
                    resp.data.data.default_profitability_configurations.forEach(configuration => {
                        if (configuration.industry_service_id) {
                            this.defaultProfitabilityConfigurations[configuration.industry_service_id] = configuration;
                        }
                    });
                }
                else {
                    this.showAlert('error', `An error occurred fetching Profitability data.`)
                }
            }).catch(err => {
                this.showAlert('error', err || `An error occurred fetching Profitability data.`);
            }).finally(() => {
                if (!initialLoad) this.loading = false;
            });
        },
        toggleIndustry(industryName) {
            if (this.expandedIndustries.includes(industryName)) {
                this.expandedIndustries = this.expandedIndustries.filter(industry => industry !== industryName);
            }
            else {
                this.expandedIndustries.push(industryName);
            }
        },
        editServiceProfitability(serviceId) {
            const targetService = this.defaultProfitabilityConfigurations[serviceId];
            if (targetService) {
                Object.assign(this.editingService, targetService);
                this.editingService.id = serviceId;
            }
        },
        cancelServiceUpdate() {
            if (this.editingService.id) {
                Object.assign(this.defaultProfitabilityConfigurations[this.editingService.id], this.editingService);
            }
            this.clearEditingService();
        },
        clearEditingService() {
            Object.assign(this.editingService, {
                id: null,
                percentage_leads_successful: 0,
                average_lead_revenue: 0,
                average_job_cost: 0,
                labour_materials_cost: 0,
            });
        },
        updateServiceProfitability(serviceId) {
            if (this.saving) return;
            this.saving = true;
            const targetProfitability = this.defaultProfitabilityConfigurations[serviceId];
            if (targetProfitability) {
                this.api.updateOrCreateDefaultProfitabilityConfiguration(serviceId, targetProfitability).then(resp => {
                    if (resp.data?.data?.status) {
                         this.clearEditingService();
                    }
                    else {
                        this.showAlert('error', 'There was an error updating the profitability configuration.');
                    }
                }).catch(err => {
                    this.showAlert('error', err.response?.data?.message || err.message || 'There was an error updating the profitability configuration.');
                }).finally(() => {
                    this.saving = false;
                });
            }
        },
        updateAll() {
            if (this.saving) return;
            this.saving = true;
            this.api.updateOrCreateAllDefaultProfitabilityConfigurations(this.defaultProfitabilityConfigurations).then(resp => {
                if (resp.data?.data?.status) {
                    this.editingAll = false;
                }
            }).catch(err => {
                this.showAlert('error', err.response?.data?.message || err.message || 'There was an error updating the profitability configuration.');
            }).finally(() => {
                this.saving = false;
            });
        },
        cancelEditAll() {
            this.editingAll = false;
            this.getDefaultProfitabilityAssumptions();
        }
    },
}

</script>
