import axios from 'axios';

export default class DefaultProfitabilityApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new DefaultProfitabilityApiService('internal-api', 'industries/default-profitability', 1);
    }


    getDefaultProfitabilityConfigurations() {
        return this.axios().get('/');
    }

    updateOrCreateDefaultProfitabilityConfiguration(industryServiceId, payload) {
        return this.axios().put(`/${industryServiceId}`, payload);
    }

    updateOrCreateAllDefaultProfitabilityConfigurations(payload) {
        return this.axios().put('/update-all', payload);
    }

    deleteDefaultProfitabilityConfiguration(industryServiceId) {
        return this.axios().delete(`/${industryServiceId}`);
    }

}
