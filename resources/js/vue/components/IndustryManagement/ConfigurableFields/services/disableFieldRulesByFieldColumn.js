
export const FIELD_COLUMNS = {
    KEY: 'key'
}

// Export some rules to disable fields from user input
// New rules must be registered inside rules variable and return a boolean value
export default function (column) {
    const rules = {
        // Verify if field has already been saved into database
        // !!field.id means that id should exist and be greater than 0
        [FIELD_COLUMNS.KEY]: (field) => !!field.id
    }

    // Default validation rule when the given column doesn't have a rule
    const fn = () => false

    return rules[column] ?? fn
}
