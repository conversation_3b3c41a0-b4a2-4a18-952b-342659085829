export const determineInputType = (col) => {
    if(col.includes('show_on') || col === 'send_to_company') {
        return 'boolean';
    }
    if(col.includes('hidden')) {
        return 'boolean';
    }
    else if(col === 'type') {
        return 'field_type'
    }
    else if (col === 'category') {
        return 'companyFieldCategory'
    }
    else if (col === 'category_id') {
        // Consumer Configurable Field Category Id
        return 'fieldCategoryId'
    }
    else if(['name', 'key'].includes(col)) {
        return 'text';
    }
    else if(col === 'payload') {
        return 'json';
    }
    else if(['created_at', 'updated_at'].includes(col)) {
        return 'datetime';
    }
    else if(['industry_id', 'industry_service_id', 'id'].includes(col)) {
        return 'id';
    }
}
