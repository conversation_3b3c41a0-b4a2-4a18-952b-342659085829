<template>
    <modal
        confirm-text="Yes"
        :dark-mode="darkMode"
        close-text="No"
        @confirm="$emit('choice', true)"
        @close="$emit('choice', false)"
        :small="true"
    >
        <template  v-slot:header>
            <span class="font-bold">Confirm Deletion</span>
        </template>
        <template v-slot:content>
            <div class="text-red-500">Are you sure you want to delete <strong> {{ selectedFieldName }} </strong>?</div>
        </template>
    </modal>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";

export default {
    name: "FieldDeletionConfirmationModal",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },

        selectedFieldName: {
            type: String,
            required: true
        }
    },

    components: {
        Modal
    },

    emits: ['choice']
}
</script>
