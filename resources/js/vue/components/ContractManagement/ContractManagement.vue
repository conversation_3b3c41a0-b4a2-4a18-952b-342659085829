<template>
    <div class="border rounded-lg"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
    >
        <AlertsContainer
            class="z-100"
            v-if="alertActive"
            :alert-type="alertType"
            :text="alertText"
            :dark-mode="darkMode"
        />
        <ContractManagementTableHeader
            :dark-mode="darkMode"
            @create-contract="showCreateContractModal = true"
        />
        <LoadingSpinner v-if="loading"/>
        <ContractManagementTableBody
            v-else
            :dark-mode="darkMode"
            :contact-templates="contractTemplates"
            :can-edit="canEdit"
            @update-contract="updateContract"
            @activate-contract="activateContract"
            @delete-contract="deleteContract"
        />
    </div>
</template>

<script>
import contractApiService from './Services/api'
import Dropdown from "../LeadProcessing/components/Dropdown.vue";
import ConsumerSearchTableBody from "../ConsumerSearch/components/ConsumerSearchTableBody.vue";
import AlertsContainer from "../LeadProcessing/components/AlertsContainer.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import Pagination from "../Shared/components/Pagination.vue";
import LoadingSpinner from "../LeadProcessing/components/LoadingSpinner.vue";
import ContractManagementTableHeader from "./Components/ContractManagementTableHeader.vue";
import ContractManagementTableBody from "./Components/ContractManagementTableBody.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store";
import AlertsMixin from "../../mixins/alerts-mixin";
import Modal from "../Shared/components/Modal.vue";
import FileUpload from "../Inputs/FileUpload.vue";

export default {
    name: 'ContractManagement',
    components: {
        FileUpload,
        Modal,
        ContractManagementTableBody,
        ContractManagementTableHeader,
        LoadingSpinner,
        Pagination,
        CustomInput,
        CustomButton,
        AlertsContainer,
        ConsumerSearchTableBody,
        Dropdown
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    mixins: [AlertsMixin],
    data () {
        return {
            contractTemplates: null,
            loading: false,
            permissionStore: useRolesPermissions(),
            showCreateContractModal: false,
            contract: {
                file: null,
                title: null,
                subject: null,
                message: null
            }

        }
    },
    mounted() {
        this.getContracts()
    },
    computed: {
        canEdit() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_CONTRACT_MANAGEMENT_EDIT);
        }
    },
    methods: {
        getContracts() {
            this.loading = true
            contractApiService.getContracts().then(resp => {
                this.contractTemplates = resp.data.data.contracts;
            }).finally(() => this.loading = false);
        },
        updateContract(contractId, payload) {
            contractApiService.updateContract(contractId, payload).then(resp => {
                this.getContracts()
            }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
        },
        activateContract(contractId) {
            contractApiService.activateContract(contractId).then(resp => {
                this.getContracts()
            }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
        },
        deleteContract(contractId) {
            contractApiService.deleteContract(contractId).then(resp => {
                this.showAlert('success', 'The contract was deleted');
                this.getContracts()
            }).catch(e => this.showAlert('error', e?.response?.data?.message || this.message.error))
        },
        getFile(payload) {
            this.contract.file = payload[0];
        },
    }
}
</script>
