<template>
    <div class="p-5 flex items-center justify-between">
        <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Contracts</h5>
    </div>
    <div
        class="grid grid-cols-6 gap-x-3 mb-2 px-5 mt-4"
        :class="[darkMode ? 'text-slate-400' : 'text-slate-500']"
    >
        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs">Contract Type</p>
        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Status</p>
        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs">Served On</p>
        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs">Description</p>
        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs">Created</p>
        <p class="text-slate-400 font-medium tracking-wide uppercase text-xs text-center">Actions</p>
    </div>
</template>

<script>
import CustomButton from "../../Shared/components/CustomButton.vue";

export default {
    name: "ContractManagementTableHeader",
    components: {CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        testLeads: {
            type: Array,
            default: []
        },
    },
    methods: {
        createContract() {
            this.$emit('create-contract');
        }
    }
}
</script>
