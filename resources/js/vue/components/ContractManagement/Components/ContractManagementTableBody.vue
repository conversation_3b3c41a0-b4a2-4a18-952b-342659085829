<template>
    <div :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}"
         class="border-t border-b">
        <div v-for="contract in contactTemplates"
             :key="contract.template_id"
             :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
             class="grid grid-cols-6 gap-x-3 border-b px-5 py-3 items-center">
            <p class="text-sm truncate">{{ contract.contract_key?.name ?? 'N/A' }}</p>
            <div class="text-sm truncate flex justify-center">
                <div v-if="contract.active"
                     :class="[darkMode ? 'border-green-400 text-green-600 bg-transparent' : 'border-transparent bg-green-100 text-green-600']"
                     class="px-5 inline-flex items-center py-1 text-sm font-semibold rounded-full border space-x-2">
                    <svg fill="none" height="10" viewBox="0 0 12 10" width="12"
                         xmlns="http://www.w3.org/2000/svg">
                        <g id="priority_high_24px">
                            <path
                                d="M4.77585 6.10601L2.34865 3.5623L0.85791 5.13175L4.77796 9.23828L11.8579 1.80552L10.3651 0.238281L4.77585 6.10601Z"
                                fill="#00AE07"/>
                        </g>
                    </svg>
                    <p class="text-sm truncate">Active</p>
                </div>
                <div v-else
                     :class="[darkMode ? 'border-yellow-400 text-yellow-600 bg-transparent' : 'border-transparent bg-yellow-100 text-yellow-600']"
                     class="px-5 inline-flex items-center py-1 text-sm font-semibold rounded-full border space-x-2">
                    <svg fill="none" height="17" viewBox="0 0 17 17" width="17"
                         xmlns="http://www.w3.org/2000/svg">
                        <g id="priority_high_24px">
                            <path id="icon/notification/priority_high_24px" clip-rule="evenodd"
                                  d="M7.11816 3.73128C7.11816 2.99795 7.71816 2.39795 8.4515 2.39795C9.18483 2.39795 9.78483 2.99795 9.78483 3.73128V9.06462C9.78483 9.79795 9.18483 10.3979 8.4515 10.3979C7.71816 10.3979 7.11816 9.79795 7.11816 9.06462V3.73128ZM7.11816 13.0646C7.11816 12.3282 7.71512 11.7313 8.4515 11.7313C9.18788 11.7313 9.78483 12.3282 9.78483 13.0646C9.78483 13.801 9.18788 14.3979 8.4515 14.3979C7.71512 14.3979 7.11816 13.801 7.11816 13.0646Z"
                                  fill="#D97706"
                                  fill-rule="evenodd"/>
                        </g>
                    </svg>
                    <p class="text-sm truncate text-yellow-600">Not Active</p>
                </div>

            </div>
            <p class="text-sm truncate"> {{ contract.website?.name ?? 'N/A'}} </p>

            <p class="text-sm truncate"> {{ contract.description }} </p>
            <p class="text-sm truncate"> {{ contract.created_at }} </p>
            <div class="flex flex-col justify-start gap-1">
                <ActionsHandle v-if="canEdit" :dark-mode="darkMode" :custom-actions="customActions" :noCustomAction="false"
                               @activate="showActivateContractModal(contract)"
                               @delete="deleteContract(contract.id)" @edit="editContract(contract)" no-delete-button />
            </div>
        </div>
        <Modal
            v-if="editContractModal"
            :small="true"
            :confirm-text="'Save'"
            :close-text="'Cancel'"
            :dark-mode="darkMode"
            @close="handleModalClosure"
            @confirm="updateContract"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium">Update Contract</h4>
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                        <label class="block uppercase tracking-wide text-xs font-bold mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400': darkMode}">
                            <hover-tooltip :dark-mode="darkMode">
                                <template v-slot:title>
                                    Key
                                </template>
                                <template v-slot:default>
                                    <ul>
                                        <li>Lead Buying Agreement: Bundles</li>
                                        <li>Terms and Conditions: Registration</li>
                                        <li>Site Access Agreement: Dashboard</li>
                                        <li>Credit Card consent: Adding new card</li>
                                    </ul>
                                </template>
                            </hover-tooltip>
                        </label>

                        <Dropdown v-model="editContractData.contract_key_id" :selected="editContractData.contract_key_id" :options="contractKeyOptions" :dark-mode="darkMode" class="w-full" placeholder="Select Key"></Dropdown>
                    </div>
                    <div>
                        <label class="block uppercase tracking-wide text-xs font-bold mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400': darkMode}">
                            Origin
                        </label>
                        <Dropdown v-model="editContractData.website_id" :selected="editContractData.website_id" :options="websiteOptions" :dark-mode="darkMode" class="w-full" placeholder="Select Origin"></Dropdown>
                    </div>
                    <div>
                        <label class="block uppercase tracking-wide text-xs font-bold mb-2"
                               :class="{'text-grey-600': !darkMode, 'text-blue-400': darkMode}">
                            Description
                        </label>
                        <input class="w-full border rounded px-3 py-2 focus:outline-none focus:border focus:border-primary-500"
                               placeholder="Description of contract"
                               v-model="editContractData.description"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                    </div>
                </div>
            </template>
        </Modal>
        <Modal
            v-if="activateContractModal"
            :small="true"
            :confirm-text="'Activate'"
            :close-text="'Cancel'"
            :dark-mode="darkMode"
            @close="handleModalClosure"
            @confirm="activateContract"
        >
            <template v-slot:header>
                <h4 class="text-xl font-medium">Are you sure you wish to activate this contract?</h4>
            </template>
            <template v-slot:content>
                <div class="space-y-4">
                    <p class="text-red-500 font-semibold">When activated, this contract will serve as a legally binding agreement.</p>
                    <div>
                        <p>This contract will appear on <span class="font-bold">{{ editContractData.website_name }}</span>.</p>
                        <p>This contract is of type: <span class="font-bold">{{ editContractData.contract_type }}</span>.</p>
                        <p>Activating this contract will deactivate other contracts of the same type.</p>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import SimpleAlert from "../../Shared/components/SimpleAlert.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import Modal from "../../Shared/components/Modal.vue";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import SharedApiService from "../../Shared/services/api";
import HoverTooltip from "../../Shared/components/HoverTooltip.vue";
export default {
    name: 'ContractManagementTableBody',
    components: {
        HoverTooltip,
        LoadingSpinner, Autocomplete, Modal, ToggleSwitch, Dropdown, SimpleAlert, ActionsHandle, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        contactTemplates: {
            type: Array,
            default: []
        },
        canEdit: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            sharedApiService: SharedApiService.make(),
            editContractModal: false,
            activateContractModal: false,
            editContractData: {
                id: null,
                description: null,
                website_id: null,
                website_name: null,
                contract_key_id: null,
                contract_type: null,
                active: null
            },
            websiteOptions: null,
            contractKeyOptions: null,
            customActions: [
                {event: 'activate', name: 'Activate'},
                {event: 'delete', name: 'Delete'}
            ]
        }
    },
    created() {
        this.getWebsites();
        this.getContractKeys();
    },
    methods: {
        getWebsites() {
            this.sharedApiService.getWebsites().then(resp => {
                if (resp.data.data.status === true)
                    this.websiteOptions = resp.data.data.websites.map((website) => {
                        return {
                            id: website.id,
                            name: website.name
                        };
                    });
                else
                    this.showAlert('error', 'Problem loading websites');
            });
        },
        getContractKeys() {
            this.sharedApiService.getContractKeys().then(resp => {
                if (resp.data.data.status === true)
                    this.contractKeyOptions = resp.data.data.contract_keys.map((key) => {
                        return {
                            id: key.id,
                            name: key.name
                        };
                    });
                else
                    this.showAlert('error', 'Problem loading contract keys');
            });
        },
        deleteContract(contractId) {
            this.$emit('delete-contract', contractId);
        },
        showActivateContractModal(contract) {
            this.activateContractModal = true;
            this.editContract(contract)
        },
        activateContract() {
            this.$emit('activate-contract', this.editContractData.id)
        },
        editContract(contract) {
            this.editContractModal = true;
            this.editContractData.id = contract.id;
            this.editContractData.contract_key_id = contract.contract_key_id;
            this.editContractData.contract_type = contract.contract_key?.name
            this.editContractData.description = contract.description;
            this.editContractData.website_id = contract.website_id;
            this.editContractData.website_name = contract.website?.name;
            this.editContractData.active = contract.active;
        },
        createContract() {
            this.$emit('create-contract')
        },
        handleModalClosure() {
            this.editContractModal = false
            this.activateContractModal = false
            this.editContractData = {
                id: null,
                description: null,
                website_id: null,
                website_name: null,
                contract_key_id: null,
                contract_type: null,
                active: null
            }
        },
        updateContract() {
            this.$emit('update-contract', this.editContractData.id, this.editContractData)
        }
    }
}

</script>
