class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    static make() {
        return new ApiService('internal-api', 'contract-management', 1);
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }
    getContracts(payload = {}) {
        return this.axios().get('/', {
            params: {
                ...payload,
            }
        })
    }

    updateContract(contractId, payload) {
        return this.axios().patch(`/contracts/${contractId}/update`, payload);
    }

    activateContract(contractId) {
        return this.axios().post(`/contracts/${contractId}/activate`)
    }

    deleteContract(contractId) {
        return this.axios().delete(`/contracts/${contractId}`)
    }

    sendContractSignatureRequest(contractId, companyUserId) {
        return this.axios().post(`/contracts/${contractId}/company-users/${companyUserId}/send-signature-request`);
    }

    uploadCompanyContract(companyUserId, file) {
        return this.axios().post(`/contracts/company-users/${companyUserId}/upload`, file, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    }
}

const contractApiService = ApiService.make();
export default contractApiService;
