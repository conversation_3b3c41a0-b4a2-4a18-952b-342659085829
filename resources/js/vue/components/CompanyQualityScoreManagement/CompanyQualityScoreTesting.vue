<template>
    <div class="px-3 py-3 flex flex-col gap-y-4">
        <loading-spinner v-if="loading" />
        <div v-show="!loading">
            <div v-if="testRule && testRule.name" class="w-full flex rounded-lg bg-blue-100 p-4 mb-3 text-blue-800">
                <information-circle-icon class="h-5 w-5"/>
                <p class="ml-4 text-sm font-medium">
                    You're currently testing the `{{ testRule.name }}`.
                </p>
            </div>
            <div
                class="flex items-center cursor-pointer text-primary-500"
                @click="toggleCompanyConfig"
            >
                <svg class="w-4 mr-2 transition-all" :class="[showCompanyConfig ? 'rotate-90' : '']" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
                <h5 class="">
                    Test Companies ({{ testCompanies.length }})
                </h5>
            </div>
            <div v-if="showCompanyConfig" class="p-2 mb-4">
                <div class="flex items-center w-[95%] py-2 mx-auto">
                    <p class="whitespace-nowrap mr-2">Query companies from: </p>
                    <dropdown
                        :dark-mode="darkMode"
                        :options="industryOptions"
                        v-model="selectedIndustryId"
                        @change="getTestCompanies"
                    />
                </div>
                <!-- Company Search -->
                <div class="flex items-center gap-x-4 w-full">
                    <Autocomplete
                        class="w-full"
                        :dark-mode="darkMode"
                        v-model="selectedCompanyId"
                        :options="companyOptions"
                        @search="searchCompany"
                        :placeholder="'Enter Name or Company ID'"
                    >
                    </Autocomplete>
                    <custom-button
                        class="w-min ml-auto"
                        @click="addCompany"
                    >
                        Add
                    </custom-button>
                </div>
                <div class="mt-4 border rounded p-2 flex items-center gap-y-1 flex-wrap"
                     :class="[darkMode ? 'border-dark-border bg-dark-175' : 'border-light-border bg-gray-200']"
                 >
                    <div v-if="!testCompanies.length" class="italic">No companies selected</div>
                    <div
                        v-else
                        v-for="(company, index) in testCompanies"
                        class="whitespace-nowrap cursor-pointer flex items-center mr-3"
                        @mouseover="hoverIndex = index"
                        @mouseout="hoverIndex = null"
                    >
                        <p>({{ company.id }}) {{ company.name }}</p>
                        <button
                            class="text-red-500 hover:text-red-400 ml-2"
                            @click="removeCompany(company.id)"
                        >
                            <svg class="w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" >
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </button>
                        <p v-if="index !== (testCompanies.length - 1)">, </p>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <div
                class="flex items-center cursor-pointer text-primary-500"
                @click="toggleTestResults"
            >
                <svg class="w-4 mr-2" :class="[showTestResults ? 'rotate-90' : '']" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
                <h5 class="">
                    Test Results
                </h5>
            </div>
            <div
                v-if="showTestResults"
                class="mt-4 border rounded p-4 flex items-center gap-3"
                :class="[darkMode ? 'border-dark-border bg-dark-175' : 'border-light-border bg-gray-200']"
            >
                <div v-if="!testResults">No results to show</div>
                <div v-else class="w-full">
                    <div v-for="[companyId, results] in Object.entries(testResults)" class="mb-4 w-full">
                        <div class="flex items-center gap-x-2 text-primary-500 hover:text-primary-400 cursor-pointer"
                             @click="toggleCompany(companyId)"
                        >
                            <div>
                                <svg v-if="expandedCompanies[companyId]" class="w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" >
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M18 12H6" />
                                </svg>
                                <svg v-else  class="w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m6-6H6" />
                                </svg>
                            </div>
                            <p>({{ companyId }}) {{ getCompanyName(companyId) }}</p>
                            <p class="ml-auto">{{ showPercentage(results.result.total_score_in_percentage) }}</p>
                        </div>
                        <div v-if="expandedCompanies[companyId]" class="py-2">
                            <p v-if="results.error" :class="[darkMode ? 'text-red-500' : 'text-red-700']">{{ results.error }}</p>
                            <div v-else class="ml-4 text-sm">
                                <p>Total Points: {{ results.result.total_score_in_points }} / {{ results.result.total_points_available }}</p>
                                <p>Total Percentage: {{ showPercentage(results.result.total_score_in_percentage) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import Autocomplete from "../Shared/components/Autocomplete.vue";
    import SharedApiService from "../Shared/services/api.js";
    import CustomButton from "../Shared/components/CustomButton.vue";
    import Dropdown from "../Shared/components/Dropdown.vue";
    import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
    import AlertsMixin from "../../mixins/alerts-mixin.js";
    import { nextTick } from "vue";
    import { InformationCircleIcon } from "@heroicons/vue/solid";

    export default {
        name: "CompanyQualityScoreTesting",
        components: {
            LoadingSpinner,
            Dropdown,
            CustomButton,
            Autocomplete,
            InformationCircleIcon,
        },
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            testResults: {
                type: Object,
                default: null,
            },
            api: {
                type: Object,
                required: true,
            },
            testRule: {
                type: Object,
                default: null,
            },
        },
        mixins: [AlertsMixin],
        emits: ['update:testCompanies'],
        data() {
            return {
                sharedApi: SharedApiService.make(),
                testCompanies: [],
                showCompanyConfig: true,
                showTestResults: true,
                companyOptions: [],
                selectedCompanyId: null,
                hoverIndex: null,
                expandedCompanies: {},
                industryOptions: [],
                loading: false,
                selectedIndustryId: 0,
            }
        },
        mounted() {
            this.getIndustryOptions();
        },
        methods: {
            getIndustryOptions() {
                this.loading = true;
                this.sharedApi.getOdinIndustries().then(resp => {
                    if (resp.data?.data?.status) {
                        this.industryOptions = [
                            { id: 0, name: 'Global' },
                            ...resp.data.data.industries
                        ];
                    }
                    else this.showAlert('error', resp.message || resp.data?.message || "An unknown error occurred.");
                })
                .catch(e => this.showAlert('error', e.response.data.message))
                .finally(() => {
                    this.loading = false;
                    if (this.selectedIndustryId != null) this.getTestCompanies();
                });
            },
            async getTestCompanies() {
                if (this.loading) return;
                this.loading = true;

                this.selectedCompanyId = null

                await nextTick();
                this.api.getTestCompanies(this.selectedIndustryId).then(resp => {
                    if (resp.data?.data?.status) {
                        this.testCompanies = resp.data.data.test_companies;
                    }
                    else this.showAlert('error', resp.message || resp.data?.message || "An unknown error occurred.");
                })
                .catch(e => this.showAlert('error', e.response.data.message))
                .finally(() => this.loading = false);
            },
            toggleCompanyConfig() {
                this.showCompanyConfig = !this.showCompanyConfig;
            },
            toggleTestResults() {
                this.showTestResults = !this.showTestResults;
            },
            searchCompany(query) {
                const additionalQuery = {}
                if (this.selectedIndustryId > 0) {
                    Object.assign(additionalQuery, { industry_id: this.selectedIndustryId })
                }

                this.sharedApi.getAdminCompanies(query, additionalQuery).then(resp => this.companyOptions = resp.data.data.companies);
            },
            addCompany() {
                if (this.selectedCompanyId && !this.testCompanies.find(company => company.id === this.selectedCompanyId)) {
                    const { id, name } = this.companyOptions.find(company => company.id === this.selectedCompanyId);
                    this.testCompanies = [ ...this.testCompanies, { id, name } ];
                }
            },
            removeCompany(companyId) {
                for (let i = this.testCompanies.length - 1; i >= 0; i--) {
                    if (this.testCompanies[i].id === companyId) {
                        this.testCompanies.splice(i, 1);
                        return;
                    }
                }
            },
            toggleCompany(companyId) {
                this.expandedCompanies[companyId] = !this.expandedCompanies[companyId];
            },
            getCompanyName(companyId) {
                return this.testCompanies.find(company => company.id === parseInt(companyId))?.name ?? 'Unknown Company';
            },
            showPercentage(percentageValue) {
                return `${parseFloat(percentageValue).toFixed(2)}%`;
            }
        },
        watch: {
            testCompanies(newVal) {
                this.$emit('update:testCompanies', newVal.map(company => company.id));
            }
        }
    }

</script>


