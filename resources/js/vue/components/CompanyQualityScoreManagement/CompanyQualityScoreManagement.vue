<template>
<!--    <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>-->

    <div class="w-full flex-auto pt-3 relative" :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
        <div class="" :class="[darkMode ? 'text-white' : 'text-slate-900']">
            <div class="border rounded-lg" :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                <div>
                    <tab :tabs="tabs" :dark-mode="darkMode" :tabs-classes="'xl:w-1/2 md:w-2/3'" :show-total="false" @selected="tabSelected" :default-tab-index="selectedTabIndex"></tab>
                </div>

                <div>
                    <company-quality-score-rules
                        v-show="selectedTabName === 'Quality Score Rules'"
                        :dark-mode="darkMode"
                        :api="api"
                    />
<!--                    <company-quality-score-industry-configuration-->
<!--                        v-if="selectedTabName === 'Industry Configuration'"-->
<!--                        :dark-mode="darkMode"-->
<!--                        :api="api"-->
<!--                    />-->
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import Tab from "../Shared/components/Tab.vue";
import CompanyQualityScoreRules from "./CompanyQualityScoreRules.vue";
import { ApiFactory } from "./services/factory.js";
import CompanyQualityScoreIndustryConfiguration from "./CompanyQualityScoreIndustryConfiguration.vue";

export default {
    name: "CompanyQualityScoreManagement",
    components: {
        CompanyQualityScoreIndustryConfiguration,
        CompanyQualityScoreRules,
        Tab
    },
    props: {
        darkMode: {
            type: Boolean,
            required: true
        },
        apiDriver: {
            type: String,
            default: 'api',
        },
    },
    data() {
        return {
            api: ApiFactory.makeApiService(this.apiDriver),
            tabs: [
                {name: 'Quality Score Rules', current: true},
                // {name: 'Industry Configuration', current: false},
            ],
            selectedTabName: 'Quality Score Rules',
            selectedTabIndex: 0,
        }
    },
    methods: {
        tabSelected(tabName) {
            this.selectedTabName = tabName;
        },
    },
}

</script>
