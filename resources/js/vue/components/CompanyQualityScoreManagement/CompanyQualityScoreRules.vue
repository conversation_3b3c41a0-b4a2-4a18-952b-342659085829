<template>
    <div class="p-5"
        :class="[savingQualityScoreRule ? 'pointer-events-none grayscale-[50%]' : '']"
    >
        <div class="flex items-center gap-x-12 mb-5 justify-between">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Quality Score Rules</h5>
            <button
                class="transition duration-200 font-semibold bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                @click="showDataInputModal = true"
            >
                + Add New Rule
            </button>
        </div>
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>
        <loading-spinner v-if="loading" />
        <div v-else>
            <div class="grid md:grid-cols-1 gap-x-6">
                <div class="border rounded" :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
<!--                    <div class="mb-3">-->
<!--                        <tab :tabs="tabs" tabs-classes="w-full" :dark-mode="darkMode" :show-total="false" @selected="tabSelected" :default-tab-index="selectedTabIndex" />-->
<!--                    </div>-->
                    <!-- Rules list -->
                    <div v-if="selectedTabName === 'Rules'">
                        <div class="grid grid-cols-7 gap-3 mb-2 p-5 pb-0" v-if="!loading">
                            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Name</p>
                            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Production</p>
<!--                            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Industry</p>-->
                            <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Ruleset</p>
                            <p class=""></p>
                        </div>
                        <div v-if="qualityScoreRules.length < 1" class="ml-4 py-4">
                            No Rules found.
                        </div>
                        <div v-else
                             class="border" v-if="!loading"
                             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}"
                        >
                            <div v-for="qualityScoreRule in qualityScoreRules"
                                :key="qualityScoreRule.id"
                                class="grid grid-cols-7 gap-x-3 px-5 py-3 border-b last:border-b-0 items-center"
                                :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                            >
                                <p class="text-sm col-span-2 overflow-x-hidden" :title="qualityScoreRule.name">
                                    {{ qualityScoreRule.name }}
                                </p>
                                <p class="text-sm col-span-2">
                                    {{ qualityScoreRule.is_production ? 'Yes' : 'No' }}
                                </p>
<!--                                <p class="text-sm col-span-2 whitespace-pre-wrap">-->
<!--                                    {{ qualityScoreRule.industry?.name }}-->
<!--                                </p>-->
                                <p class="text-sm col-span-2 whitespace-pre-wrap">
                                    {{ qualityScoreRule.ruleset?.name }}
                                </p>
                                <actions-handle
                                    :dark-mode="darkMode"
                                    :no-custom-action="false"
                                    :custom-actions="customActions"
                                    @edit="gearUpToEditQualityScoreRule(qualityScoreRule.id)"
                                    @delete="gearUpToDeleteQualityScoreRule(qualityScoreRule.id)"
                                    @test="gearUpToTestQualityScoreRule(qualityScoreRule.id)"
                                    @trigger-process="handleTriggerProcess(qualityScoreRule.id)"
                                />
                            </div>
                        </div>
                    </div>
                    <!-- Test Results -->
<!--                    <div v-show="selectedTabName === 'Testing'">-->
<!--                        <loading-spinner v-if="runningTest" />-->
<!--                        <company-quality-score-testing-->
<!--                            v-show="!runningTest"-->
<!--                            :dark-mode="darkMode"-->
<!--                            :test-results="testResults"-->
<!--                            :test-rule="editingQualityScoreRule"-->
<!--                            :api="api"-->
<!--                            @update:test-companies="updateTestCompanies"-->
<!--                        />-->
<!--                    </div>-->
                </div>
            </div>
        </div>
        <!-- Data input modal -->
        <modal
            v-if="showDataInputModal"
            :confirm-text="confirmTextForDataInput"
            close-text="Cancel"
            :small="true"
            :dark-mode="darkMode"
            @confirm="confirmDataInput"
            @close="cancelDataInput"
        >
            <template v-slot:header>
                <h4 class="text-xl">{{ editingQualityScoreRule.id ? 'Update' : 'Create' }} Quality Score Rule</h4>
            </template>
            <template v-slot:content>
                <div class="grid gap-3">
                    <div class="flex items-center">
                        <p class="uppercase font-semibold text-xs w-20"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Name
                        </p>
                        <input class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                               placeholder="Name"
                               v-model="editingQualityScoreRule.name"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                    </div>
<!--                    <div class="flex items-center">-->
<!--                        <p class="uppercase font-semibold text-xs w-20"-->
<!--                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">-->
<!--                            Industry-->
<!--                        </p>-->
<!--                        <Dropdown :dark-mode="darkMode"-->
<!--                                  v-model="editingQualityScoreRule.industry.id"-->
<!--                                  :options="industryOptions"-->
<!--                                  placeholder="Industry"-->
<!--                                  :selected="editingQualityScoreRule.industry.id"-->
<!--                        ></Dropdown>-->
<!--                    </div>-->
                    <div class="flex items-center">
                        <p class="uppercase font-semibold text-xs w-20"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Ruleset
                        </p>
                        <Dropdown v-if="editingQualityScoreRule.ruleset" :dark-mode="darkMode"
                                  v-model="editingQualityScoreRule.ruleset.id"
                                  :options="rulesetOptions"
                                  placeholder="Ruleset"
                                  :selected="editingQualityScoreRule.ruleset.id"
                        ></Dropdown>
                    </div>
                    <div class="flex items-center">
                        <p class="uppercase font-semibold text-xs w-40"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Is Production
                        </p>
                        <toggle v-model="editingQualityScoreRule.is_production"/>
                    </div>
                </div>
            </template>
        </modal>
        <!-- Confirmation modal -->
        <modal
            v-if="showConfirmationModal"
            confirm-text="Yes, continue"
            close-text="No, get back"
            :small="true"
            :dark-mode="darkMode"
            @confirm="confirmToProceed"
            @close="confirmToCancel"
        >
            <template v-slot:header>
                <h4 class="text-xl">Caution...</h4>
            </template>
            <template v-slot:content>
                <p v-if="deletingQualityScoreRule" class="p-4">Are you sure you wish to delete rule "{{ getQualityScoreRuleName(deletingQualityScoreRule) }}"?</p>
                <div v-else>
                    <p class="p-2 text-red-600">
                        WARNING: You are currently editing a production-flagged Ruleset which may be in live use for calculating Company Quality Scores.
                    </p>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import ActionsHandle from "../Shared/components/ActionsHandle.vue";
import Modal from "../Shared/components/Modal.vue";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import Tab from "../Shared/components/Tab.vue";
import CompanyQualityScoreTesting from "./CompanyQualityScoreTesting.vue";
import {REQUEST} from "../../../constants/APIRequestKeys";
import Toggle from "../Inputs/Toggle/Toggle.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import SharedApiService from "../Shared/services/api";

export default {
    name: "CompanyQualityScoreRules",
    components: {
        Dropdown,
        Toggle,
        Tab,
        AlertsContainer,
        Modal,
            ActionsHandle,
            CustomButton,
            LoadingSpinner,
            CompanyQualityScoreTesting
        },
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            api: {
                type: Object,
                required: true,
            }
        },
        mixins: [AlertsMixin],
        data() {
            return {
                sharedApi: SharedApiService.make(),
                loading: true,
                saving: false,
                loadingQualityScoreRule: false,
                savingQualityScoreRule: false,
                qualityScoreRules: [],
                editingQualityScoreRule: this.resetQualityScoreRule(),
                deletingQualityScoreRule: null,
                showConfirmationModal: false,
                showDataInputModal: false,
                tabs: [
                    {name: 'Rules', current: true},
                    {name: 'Testing', current: false},
                ],
                industryOptions: [],
                rulesetOptions: [],
                selectedTabName: 'Rules',
                selectedTabIndex: 0,

                runningTest: false,
                testCompanyIds: [],
                testResults: null,
                customActions: [
                    { event: 'trigger-process', name: 'Trigger process' },
                ],

                genericErrorMessage: "An unknown error occurred.",
            }
        },
        mounted() {
            this.getQualityScoreRules();
            this.get();
            this.getRulesetOptions();
        },
        computed: {
            confirmTextForDataInput: function () {
                if (this.savingQualityScoreRule) {
                    return 'Saving...';
                }

                if (this.editingQualityScoreRule.id) {
                    return 'Update';
                }

                return 'Create';
            }
        },
        methods: {
            getQualityScoreRules() {
                this.loading = true;
                this.api.getQualityScoreRules()
                    .then(resp => {
                        if (resp.data?.data?.status) {
                            this.qualityScoreRules = resp.data.data[REQUEST.COMPANY_QUALITY_SCORE_RULES];
                        } else {
                            this.showAlert('error', resp.message || resp.data?.message || this.genericErrorMessage);
                        }
                    })
                    .catch(e => this.showAlert('error', e.response?.data?.message || this.genericErrorMessage))
                    .finally(() => this.loading = false);
            },
            getIndustryOptions() {
                this.loading = true;

                this.sharedApi.getOdinIndustries()
                    .then(resp => {
                        if (resp.data?.data?.status) {
                            this.industryOptions = resp.data.data[REQUEST.INDUSTRIES] ?? [];
                        } else {
                            this.showAlert('error', resp.message || resp.data?.message || this.genericErrorMessage);
                        }
                    })
                    .catch(e => this.showAlert('error', e.response?.data?.message || this.genericErrorMessage))
                    .finally(() => this.loading = false);
            },
            getRulesetOptions() {
                this.loading = true;

                this.sharedApi.getRulesets('ranking')
                    .then(resp => {
                        if (resp.data?.data?.status) {
                            this.rulesetOptions = resp.data.data[REQUEST.RULESETS] ?? [];
                        } else {
                            this.showAlert('error', resp.message || resp.data?.message || this.genericErrorMessage);
                        }
                    })
                    .catch(e => this.showAlert('error', e.response?.data?.message || this.genericErrorMessage))
                    .finally(() => this.loading = false);
            },
            gearUpToEditQualityScoreRule(qualityScoreRuleId) {
                this.showDataInputModal = true;

                this.editingQualityScoreRule = this.getQualityScoreRule(qualityScoreRuleId);
            },
            updateQualityScoreRule() {
                if (this.savingQualityScoreRule) {
                    return;
                }

                this.savingQualityScoreRule = true;
                this.api.updateQualityScoreRule(this.getRequestPayload())
                    .then(async (resp) => {
                        if (resp.data?.data?.status) {
                            await this.getQualityScoreRules();
                            this.showAlert('success', `Successfully updated rule "${this.editingQualityScoreRule[REQUEST.NAME]}"`);
                        }
                    })
                    .catch(e => this.showAlert('error', e.response?.data?.message || e.response?.message || this.genericErrorMessage))
                    .finally(() => {
                        this.savingQualityScoreRule = false;
                        this.showConfirmationModal = false;
                        this.showDataInputModal = false;
                        this.editingQualityScoreRule = this.resetQualityScoreRule();
                    });
            },
            saveQualityScoreRule() {
                if (this.savingQualityScoreRule || this.loadingQualityScoreRule || this.checkQualityScoreRuleNameInUse()) {
                    return;
                }

                this.savingQualityScoreRule = true;
                this.api.createQualityScoreRule(this.getRequestPayload())
                    .then(async (resp) => {
                        if (resp.data?.data?.status) {
                            await this.getQualityScoreRules();
                            this.editingQualityScoreRule = resp.data.data[REQUEST.COMPANY_QUALITY_SCORE_RULE];
                            this.showAlert('success', `Successfully saved new ruleset "${this.editingQualityScoreRule[REQUEST.NAME]}"`);
                        }
                    })
                    .catch(e => this.showAlert('error', e.response?.data?.message || this.genericErrorMessage))
                    .finally(() => {
                        this.savingQualityScoreRule = false
                        this.showDataInputModal = false;
                    });
            },
            getRequestPayload() {
                return {
                    [REQUEST.ID]             : this.editingQualityScoreRule[REQUEST.ID] ?? null,
                    [REQUEST.NAME]           : this.editingQualityScoreRule[REQUEST.NAME],
                    [REQUEST.INDUSTRY_ID]    : this.editingQualityScoreRule[REQUEST.INDUSTRY]?.[REQUEST.ID],
                    [REQUEST.RULE_ID]        : this.editingQualityScoreRule[REQUEST.RULESET]?.[REQUEST.ID],
                    [REQUEST.IS_PRODUCTION]  : this.editingQualityScoreRule[REQUEST.IS_PRODUCTION],
                    [REQUEST.TOTAL_POINTS]   : this.getTotalPoints(),
                }
            },
            getQualityScoreRuleName(qualityScoreRuleId) {
                return this.qualityScoreRules.find(qualityScoreRule => qualityScoreRule.id === qualityScoreRuleId)?.name || 'Unknown Rule';
            },
            deleteQualityScoreRule() {
                if (this.savingQualityScoreRule) {
                    return;
                }

                this.savingQualityScoreRule = true;
                this.api.deleteQualityScoreRule(this.deletingQualityScoreRule)
                    .then(async (resp) => {
                        if (resp.data?.data?.status) {
                            await this.getQualityScoreRules();
                        } else {
                            this.showAlert('error', resp.message || resp.data.message || this.genericErrorMessage);
                        }
                    })
                    .catch(e => this.showAlert('error', e.response?.data?.message || this.genericErrorMessage))
                    .finally(() => {
                        this.deletingQualityScoreRule = null;
                        this.savingQualityScoreRule = false;
                        this.showConfirmationModal = false;
                    });
            },
            confirmDataInput() {
                if (this.editingQualityScoreRule.id) {
                    const targetQualityScoreRule = this.qualityScoreRules.find(qualityScoreRule => qualityScoreRule.id === this.editingQualityScoreRule.id);
                    if (targetQualityScoreRule && targetQualityScoreRule[REQUEST.IS_PRODUCTION]) {
                        this.showConfirmationModal = true;
                    } else {
                        this.updateQualityScoreRule();
                    }
                } else {
                    this.saveQualityScoreRule();
                }
            },
            cancelDataInput() {
                this.savingQualityScoreRule = null;
                this.showDataInputModal = false;
                this.editingQualityScoreRule = this.resetQualityScoreRule();
            },
            confirmToProceed() {
                if (this.deletingQualityScoreRule) {
                    this.deleteQualityScoreRule();
                } else {
                    this.updateQualityScoreRule();
                }
            },
            confirmToCancel() {
                this.showConfirmationModal = false;

                if (this.deletingQualityScoreRule) {
                    this.deletingQualityScoreRule = null;
                }
            },
            gearUpToDeleteQualityScoreRule(qualityScoreRuleId) {
                this.deletingQualityScoreRule = qualityScoreRuleId;
                this.showConfirmationModal = true;
            },
            gearUpToTestQualityScoreRule(qualityScoreRuleId) {
                if (this.savingQualityScoreRule || this.runningTest) {
                    return;
                }

                if (!this.testCompanyIds.length) {
                    this.showAlert('error', "Oops, seems like you've not added any test company IDs.");
                    return;
                }

                this.editingQualityScoreRule = this.getQualityScoreRule(qualityScoreRuleId);
                this.runRuleTest();
            },
            runRuleTest() {
                this.runningTest = true;
                this.testResults = null;

                if (this.selectedTabName !== 'Testing') {
                    this.selectedTabName = 'Testing';
                    this.tabs.forEach(tab => { tab.current = tab.name === 'Testing' });
                }

                this.api.testRule(this.editingQualityScoreRule[REQUEST.RULESET]?.[REQUEST.ID], this.testCompanyIds)
                    .then(resp => {
                        if (resp.data?.data?.status) {
                            this.testResults = resp.data.data[REQUEST.RESULT] ?? [];
                        }
                    })
                    .catch(e => this.showAlert('error', e.response?.data?.message || this.genericErrorMessage))
                    .finally(() => this.runningTest = false);
            },
            tabSelected(tabName) {
                this.selectedTabName = tabName;
            },
            checkQualityScoreRuleNameInUse() {
                if (this.qualityScoreRules.find(qualityScoreRule =>
                    qualityScoreRule[REQUEST.NAME]?.trim().toLowerCase() === this.editingQualityScoreRule[REQUEST.NAME]?.trim().toLowerCase()
                    && qualityScoreRule[REQUEST.ID] !== this.editingQualityScoreRule[REQUEST.ID])) {
                    this.showAlert('error', 'The name is already in use, please rename the current Quality Score Rule before saving.');
                    return true;
                }
                return false;
            },
            updateTestCompanies(companyIds) {
                this.testCompanyIds = companyIds;
            },
            getTotalPoints() {
                return this.editingQualityScoreRule[REQUEST.RULESET]?.[REQUEST.RULES]?.reduce((output, rule) => rule.is_active ? output + parseInt(rule.max_points) : output, 0)
            },
            resetQualityScoreRule() {
              return {
                  [REQUEST.ID]            : null,
                  [REQUEST.NAME]          : null,
                  [REQUEST.IS_PRODUCTION] : null,
                  [REQUEST.INDUSTRY]      : {[REQUEST.ID]: null, [REQUEST.NAME]: null},
                  [REQUEST.RULESET]       : {[REQUEST.ID]: null, [REQUEST.NAME]: null, [REQUEST.RULES]: []},
                  [REQUEST.TOTAL_POINTS]  : null,
              };
            },
            getQualityScoreRule(qualityScoreRuleId) {
                const targetQualityScoreRule = this.qualityScoreRules.find(qualityScoreRule => qualityScoreRule.id === parseInt(qualityScoreRuleId));
                return JSON.parse(JSON.stringify(targetQualityScoreRule));
            },
            async handleTriggerProcess(companyQualityScoreRuleId){
                try {
                    await this.api.triggerCompanyQualityScore(companyQualityScoreRuleId)
                    this.showAlert('success', 'Success triggering the configuration')
                }catch{
                    this.showAlert('error', 'Error triggering the configuration')
                }
            }
        },
    }
</script>


