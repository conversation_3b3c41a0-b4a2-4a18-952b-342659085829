<template>
    <div class="p-5"
         :class="[saving ? 'pointer-events-none grayscale-[50%] opacity-70' : '']"
    >
        <div class="flex items-center gap-x-12 mb-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Industry Configuration</h5>
        </div>
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>
        <loading-spinner v-if="loading || saving" />
        <div v-if="!loading">
            <div v-if="!Object.keys(globalConfiguration).length">
                No configurations found
            </div>
            <div v-else>
                <div class="grid grid-cols-7 gap-x-3 mb-2 px-5">
                    <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Industry</p>
                    <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Production Rule</p>
                    <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-4">Default Test Companies</p>
                </div>
                <div class="grid grid-cols-7 gap-x-3 px-5 py-3 border-b last:border-b-0 items-center"
                    :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                >
                    <p>Global</p>
                    <dropdown
                        class="col-span-2"
                        :dark-mode="darkMode"
                        :options="ruleOptions"
                        v-model="globalConfiguration.rule_id"
                        placeholder="None"
                        @change="saveRuleConfiguration(globalConfiguration)"
                    />
                    <div class="flex items-start col-span-4 gap-x-4">
                        <div class="border rounded p-2 flex items-center w-full flex-wrap"
                             :class="[darkMode ? 'border-dark-border bg-dark-175' : 'border-light-border bg-gray-200']"
                        >
                            <div v-if="!globalConfiguration.test_companies?.length" class="italic">No test companies</div>
                            <div v-else
                                 v-for="(company, index) in globalConfiguration.test_companies"
                                 :key="index"
                                 class="whitespace-nowrap cursor-pointer flex items-center pr-3"
                             >
                                <p>({{ company.id }}) {{ company.name }}{{ index === (globalConfiguration.test_companies?.length - 1) ? '' : ', ' }}</p>
                            </div>
                        </div>
                        <custom-button
                            @click="updateTestCompanies(globalConfiguration)"
                            class="whitespace-nowrap"
                        >
                            Update Test Companies
                        </custom-button>
                    </div>
                </div>
                <div
                    v-for="[slug, industry] in Object.entries(industryConfigurations)"
                    :key="slug"
                    class="grid grid-cols-7 gap-x-3 px-5 py-3 border-b last:border-b-0 items-center"
                    :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                >
                    <p>{{ industry.name }}</p>
                    <dropdown
                        class="col-span-2"
                        :dark-mode="darkMode"
                        :options="ruleOptions"
                        v-model="industry.rule_id"
                        placeholder="None"
                        @change="saveRuleConfiguration(industry)"
                    />
                    <div class="flex items-start col-span-4 gap-x-4">
                        <div class="border rounded p-2 flex items-center w-full flex-wrap"
                             :class="[darkMode ? 'border-dark-border bg-dark-175' : 'border-light-border bg-gray-200']"
                        >
                            <div v-if="!industry.test_companies?.length" class="italic">No test companies</div>
                            <div v-else
                                 v-for="(company, index) in industry.test_companies"
                                 :key="index"
                                 class="whitespace-nowrap cursor-pointer flex items-center pr-3"
                            >
                                <p>({{ company.id }}) {{ company.name }}{{ index === (industry.test_companies?.length - 1) ? '' : ', ' }}</p>
                            </div>
                        </div>
                        <custom-button
                            @click="updateTestCompanies(industry)"
                            class="whitespace-nowrap"
                        >
                            Update Test Companies
                        </custom-button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Test Companies modal -->
    <modal
        v-if="showAddCompaniesModal"
        :dark-mode="darkMode"
        @close="cancelTestCompanies"
        @confirm="saveTestCompanies"
        @cancel="cancelTestCompanies"
    >
        <template v-slot:header>
            Add Test Companies
        </template>
        <template v-slot:content>
            <div>
                <p>Adding Test Companies to {{ editingIndustry.name }}.</p>
            </div>
            <div class="flex items-center gap-x-4 w-full pt-4">
                <Autocomplete
                    class="w-full"
                    :dark-mode="darkMode"
                    v-model="selectedCompanyId"
                    :options="companyOptions"
                    @search="searchCompany"
                    :placeholder="'Enter Name or Company ID'">
                </Autocomplete>
                <custom-button
                    class="w-min ml-auto py-2"
                    @click="addCompany"
                >
                    Add
                </custom-button>
            </div>
            <div class="mt-4 border rounded p-2 flex items-center gap-y-1 flex-wrap pb-4"
                 :class="[darkMode ? 'border-dark-border bg-dark-175' : 'border-light-border bg-gray-200']"
            >
                <div v-if="!editingCompanies.length" class="italic">No companies selected</div>
                <div
                    v-else
                    v-for="(company, index) in editingCompanies"
                    class="whitespace-nowrap cursor-pointer flex items-center"
                    @mouseover="hoverIndex = index"
                    @mouseout="hoverIndex = null"
                >
                    <p>({{ company.id }}) {{ company.name }}</p>
                    <button
                        class="text-red-500 hover:text-red-400 ml-2"
                        @click="removeCompany(company.id)"
                    >
                        <svg class="w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" >
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                    <p v-if="index !== (editingCompanies.length - 1)" class="mr-3">, </p>
                </div>
            </div>
        </template>
    </modal>
</template>

<script>
    import AlertsContainer from "../Shared/components/AlertsContainer.vue";
    import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
    import AlertsMixin from "../../mixins/alerts-mixin.js";
    import Dropdown from "../Shared/components/Dropdown.vue";
    import Autocomplete from "../Shared/components/Autocomplete.vue";
    import CustomButton from "../Shared/components/CustomButton.vue";
    import Modal from "../Shared/components/Modal.vue";
    import SharedApiService from "../Shared/services/api.js";
    import { nextTick } from "vue";

    export default {
        name: "CompanyQualityScoreIndustryConfiguration",
        components: { Modal, CustomButton, Autocomplete, Dropdown, LoadingSpinner, AlertsContainer },
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            api: {
                type: Object,
                required: true,
            },
        },
        mixins: [AlertsMixin],
        data() {
            return {
                sharedApi: SharedApiService.make(),
                globalConfiguration: {},
                industryConfigurations: [],
                ruleOptions: [],
                loading: false,
                saving: false,
                showAddCompaniesModal: false,
                editingIndustry: {
                    id: null,
                    name: null,
                },
                editingCompanies: null,
                editingGlobalConfig: null,
                selectedCompanyId: null,
                companyOptions: [],
                hoverIndex: null,

            }
        },
        mounted() {
            this.getConfigurations()
        },
        methods: {
            getConfigurations() {
                if (this.loading) return;
                this.loading = true;

                this.api.getIndustryConfigurations().then(resp => {
                    if (resp.data?.data?.status) {
                        this.globalConfiguration = resp.data.data.configurations.global;
                        delete resp.data.data.configurations.global;
                        this.industryConfigurations = resp.data.data.configurations;
                        this.ruleOptions = [
                            { name: 'None', id: 0 },
                            ...resp.data.data.rule_options,
                        ];
                    }
                    else this.showAlert('error', resp.message || resp.data?.message || "An unknown error occurred.");
                })
                .catch(e => this.showAlert('error', e.response.data.message))
                .finally(() => this.loading = false);
            },
            async saveRuleConfiguration(config) {
                if (this.saving) return;
                this.saving = true;

                await nextTick();

                this.api.updateIndustryRule(config.rule_id, config.global, config.id).then(resp => {
                    if (!resp.data?.data?.status)
                        this.showAlert('error', resp.message || resp.data?.message || "An unknown error occurred.");
                })
                .catch(e => this.showAlert('error', e.response.data.message))
                .finally(() => this.saving = false);
            },
            updateTestCompanies(configuration) {
                this.editingIndustry = {
                    id: configuration.id,
                    name: configuration.name,
                };
                this.editingGlobalConfig = !!configuration.global;
                this.editingCompanies = [...(configuration.test_companies ?? [])];
                this.showAddCompaniesModal = true;
            },
            cancelTestCompanies() {
                this.showAddCompaniesModal = false;
                this.editingIndustry = { id: null, name: ''};
                this.editingCompanies = [];
            },
            saveTestCompanies() {
                if (this.saving) return;
                this.saving = true;
                this.api.saveTestCompanies(this.editingCompanies.map(company => company.id), this.editingGlobalConfig, this.editingIndustry.id).then(resp => {
                    if (resp.data?.data?.status) {
                        this.cancelTestCompanies();
                        this.getConfigurations();
                    }
                    else this.showAlert('error', resp.message || resp.data?.message || "An unknown error occurred.");
                })
                .catch(e => this.showAlert('error', e.response.data.message))
                .finally(() => this.saving = false);
            },
            searchCompany(query) {
                this.sharedApi.getAdminCompanies(query).then(resp => this.companyOptions = resp.data.data.companies);
            },
            addCompany() {
                if (this.selectedCompanyId && !this.editingCompanies.find(company => company.id === this.selectedCompanyId)) {
                    const { id, name } = this.companyOptions.find(company => company.id === this.selectedCompanyId);
                    this.editingCompanies.push({ id, name });
                }
            },
            removeCompany(companyId) {
                for (let i = this.editingCompanies.length - 1; i >= 0; i--) {
                    if (this.editingCompanies[i].id === companyId) {
                        this.editingCompanies.splice(i, 1);
                        return;
                    }
                }
            },
        },
    }

</script>


