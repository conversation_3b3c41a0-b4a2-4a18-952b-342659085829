import axios from 'axios';
import { BaseApiService } from "./base";
import { REQUEST } from "../../../../constants/APIRequestKeys";

export class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    /**
     * Fetches a list of quality score rules.
     *
     * @returns {Promise<AxiosResponse<any>>}
     */
    getQualityScoreRules() {
        return this.axios().get('/');
    }

    /**
     * <PERSON>les creating a new quality score rule.
     *
     * @param qualityScoreRulePayload
     * @returns {Promise<AxiosResponse<any>>}
     */
    createQualityScoreRule(qualityScoreRulePayload) {
        return this.axios().post('/', qualityScoreRulePayload);
    }

    /**
     * <PERSON>les updating an existing quality score rule.
     *
     * @param qualityScoreRulePayload
     * @returns {Promise<AxiosResponse<any>>}
     */
    updateQualityScoreRule(qualityScoreRulePayload) {
        return this.axios().put(`/${qualityScoreRulePayload.id}`, qualityScoreRulePayload);
    }

    /**
     * Removes the requested quality score rule from the system.
     *
     * @param qualityScoreRuleId
     * @returns {Promise<AxiosResponse<any>>}
     */
    deleteQualityScoreRule(qualityScoreRuleId) {
        return this.axios().delete(`${qualityScoreRuleId}`);
    }

    /**
     * Handles testing a given rule for the requested set of companies.
     *
     * @param ruleset
     * @param companyIds
     * @returns {Promise<AxiosResponse<any>>}
     */
    testRule(ruleset, companyIds = []) {
        return this.axios().post('/test', {
            [REQUEST.COMPANY_IDS] : companyIds,
            [REQUEST.RULESET]     : ruleset,
        });
    }

    /**
     * Fetches a list of industry configurations.
     *
     * @returns {Promise<AxiosResponse<any>>}
     */
    getIndustryConfigurations() {
        return this.axios().get('/industry-configuration');
    }

    /**
     * Handles saving test companies against the request industry ID. Otherwise, stored as globally (for all industries).
     *
     * @param testCompanyIds
     * @param global
     * @param industryId
     * @returns {Promise<AxiosResponse<any>>}
     */
    saveTestCompanies(testCompanyIds, global = false, industryId) {
        return this.axios().put('/industry-configuration/update-test-companies', {
            [REQUEST.INDUSTRY_ID]       : industryId,
            [REQUEST.TEST_COMPANY_IDS]  : testCompanyIds,
            [REQUEST.GLOBAL]            : global,
        });
    }

    /**
     * Handles saving a rule for the given industry ID. Otherwise, stored as globally (for all industries).
     *
     * @param ruleId
     * @param global
     * @param industryId
     * @returns {Promise<AxiosResponse<any>>}
     */
    updateIndustryRule(ruleId, global = false, industryId) {
        return this.axios().put('/industry-configuration/update-rule-id', {
            [REQUEST.INDUSTRY_ID] : industryId,
            [REQUEST.GLOBAL]      : global,
            [REQUEST.RULE_ID]     : ruleId,
        });
    }

    /**
     * Fetches a list of test companies defined for the requested industry ID.
     *
     * @param industryId
     * @returns {Promise<AxiosResponse<any>>}
     */
    getTestCompanies(industryId) {
        return this.axios().get(`industry-configuration/test-companies/${industryId}`);
    }

    triggerCompanyQualityScore(companyQualityScoreRuleId){
        return this.axios().post(`trigger/${companyQualityScoreRuleId}`);
    }
}
