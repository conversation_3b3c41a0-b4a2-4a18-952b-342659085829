import { BaseApiService } from "./base";
import { REQUEST } from "../../../../constants/APIRequestKeys";

export class DummyApiService extends BaseApiService {
    constructor(delay = 150) {
        super("DummyApiService");

        this.delay = delay;
    }

    _makeResponse(data) {
        return new Promise((resolve) => {
            setTimeout(() => resolve({data: {data}}), this.delay);
        });
    }

    getQualityScoreRules() {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : 'Dummy API only, nothing to get.'
        });
    }

    createQualityScoreRule(_qualityScoreRulePayload) {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : 'Dummy API only, nothing to save.'
        });
    }

    updateQualityScoreRule(_qualityScoreRulePayload) {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : 'Dummy API only, nothing to save.'
        });
    }

    deleteQualityScoreRule(_qualityScoreRuleId) {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : 'Dummy API only, nothing deleted.'
        });
    }

    testRule(_rule, _companyIds = []) {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : 'Dummy API only, nothing tested.'
        });
    }

    getIndustryConfigurations() {
        return this._makeResponse(
            this.configurations.data,
        )
    }

    saveTestCompanies(_testCompanyIds, _global = false, _industryId) {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : 'Dummy API only, nothing saved.'
        });
    }

    updateIndustryRule(_ruleId, _global = false, _industryId) {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : 'Dummy API only, nothing updated.'
        });
    }

    getTestCompanies(_industryId) {
        return this._makeResponse({
            [REQUEST.STATUS]  : false,
            [REQUEST.MESSAGE] : 'Dummy API only, nothing companies to get.'
        });
    }

    configurations={"data":{"status":true,"configurations":{"solar":{"id":1,"slug":"solar","name":"Solar","ruleset_id":1,"ruleset_name":"Solarruleset","test_companies":[],"global":0},"roofing":{"id":2,"slug":"roofing","name":"Roofing","ruleset_id":null,"ruleset_name":null,"test_companies":[],"global":false},"bathrooms":{"id":3,"slug":"bathrooms","name":"Bathrooms","ruleset_id":null,"ruleset_name":null,"test_companies":[{"id":100417,"name":"1stColoradoRoofing","users":[]},{"id":101198,"name":"AcademyRoofingSystems,Inc."},{"id":101267,"name":"AceRoofing&Construction"}],"global":0},"jetpacks-and-hoverboards":{"id":4,"slug":"jetpacks-and-hoverboards","name":"JetpacksandHoverboards","ruleset_id":10,"ruleset_name":"Rulestemplate222","test_companies":[],"global":0},"global":{"id":null,"name":"Global","slug":"global","global":1,"ruleset_id":1,"ruleset_name":"Solarruleset","test_companies":[{"id":39,"name":"All-SolInc."},{"id":120,"name":"AdvancedSolarNC"}]}},"ruleset_options":[{"name":"Solarruleset","id":1},{"name":"Rulestemplate222","id":10},{"name":"Rulestemplate2222","id":12},{"name":"Rulestemplate123","id":13}]}}

}
