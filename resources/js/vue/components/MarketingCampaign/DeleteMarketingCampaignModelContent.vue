<template>
    <div class="flex flex-col gap-4 relative">
        <div>
            Are you sure you want to delete
            <span class="text-primary-500 font-semibold">
                {{name}}
            </span>
            ?
        </div>
        <div class="flex flex-1 justify-end sticky bottom-0 bg-light-module pt-5">
            <custom-button @click="handleDelete" :dark-mode="darkMode" color="red">Delete</custom-button>
        </div>
    </div>
</template>
<script>
import CustomButton from "../Shared/components/CustomButton.vue";
import ApiService from "./services/api.js";

export default {
    name: "DeleteMarketingCampaignModelContent",
    components: {
        CustomButton,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        marketingCampaignId: {
            type: Number,
            required: true,
        },
        name: {
            type: String,
            default: '',
        }
    },
    data() {
        return {
            api: ApiService.make(),
        }
    },
    emits: ['delete'],
    methods: {
        async handleDelete() {
            await this.api.deleteMarketingCampaign(this.marketingCampaignId)
            this.$emit('delete')
        }
    }
}
</script>
