<template>
    <modal
        :dark-mode="darkMode"
        no-buttons
        small
        @close="$emit('close')"
    >
        <template #content>
            <div class="flex flex-col gap-4">
                <div class="flex flex-col rounded-md  p-4" :class="[darkMode ? 'bg-dark-background' : 'bg-grey-50']">
                    <div class="flex justify-between">
                        <h4 class="text-xl font-medium mb-2" :class="[darkMode ? 'text-white' : '']">Summary</h4>
                    </div>
                    <div v-if="relationType === marketingLog.relationTypes.MARKETING_CAMPAIGN_CONSUMER" class="grid grid-cols-5 gap-2">
                        <labeled-value label="ID">
                            {{data.id}}
                        </labeled-value>
                        <labeled-value label="Name">
                            {{data.name}}
                        </labeled-value>
                        <labeled-value class="col-span-2" label="Status">
                            <marketing-campaign-consumer-status-badge
                                :dark-mode="darkMode"
                                :status="data.status"
                            />
                        </labeled-value>
                    </div>
                    <div v-else class="grid grid-cols-5 gap-2">
                        <labeled-value label="ID">
                            {{data.id}}
                        </labeled-value>
                        <labeled-value class="col-span-2" label="Name">
                            {{data.name}}
                        </labeled-value>
                        <labeled-value class="col-span-2" label="Status">
                            <marketing-campaign-badge
                                :dark-mode="darkMode"
                                :status="data.status"
                            />
                        </labeled-value>
                    </div>
                </div>
                <card-wrapper
                    title="Logs"
                    :dark-mode="darkMode"
                >
                    <event-log
                        :data="logs"
                        :dark-mode="darkMode"
                        date-key="created_at"
                        title-key="namespace"
                    >
                        <template #event-icon="{event}">
                            <simple-icon
                                clickable
                                :dark-mode="darkMode"
                                :icon="openedLog.id === event.id ? simpleIcon.icons.MINUS_CIRCLE : simpleIcon.icons.PLUS_CIRCLE"
                                :color="simpleIcon.colors.BLUE"
                                @click="showDetails(event)"
                            />
                        </template>
                        <template #title="{event}">
                            <div class="flex flex-col">
                                <div class="flex items-center gap-2 font-semibold">
                                    {{ event.namespace }}
                                    <log-level-badge :level="event.level" :dark-mode="darkMode"/>
                                </div>
                            </div>
                        </template>
                        <template #payload="{event}">
                            <div v-if="openedLog.id === event.id" class="flex flex-col mr-2">
                                <labeled-value label="Message" class="text-xs">
                                    {{openedLog.message}}
                                </labeled-value>
                                <labeled-value v-if="openedLog?.context"  label="Context" class="text-xs">
                                    <div class="flex bg-sky-100 rounded-md p-2">
                                        <pre class=" whitespace-pre-wrap text-sm">{{openedLog.context}}</pre>
                                    </div>
                                </labeled-value>
                                <labeled-value v-if="openedLog?.stack_trace" label="Trace" class="text-xs">
                                    <div class="flex bg-sky-100 rounded-md p-2">
                                        <pre class=" whitespace-pre-wrap text-sm">{{openedLog.stack_trace}}</pre>
                                    </div>
                                </labeled-value>
                            </div>
                        </template>
                    </event-log>
                </card-wrapper>
            </div>
        </template>
    </modal>
</template>
<script>
import useMarketingLog from "../../../composables/useMarketingLog.js";
import ApiService from "./services/api.js";
import Modal from "../Shared/components/Modal.vue";
import EventLog from "../Companies/components/Territory/components/shared/EventLog.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import Badge from "../Shared/components/Badge.vue";
import CardWrapper from "../Companies/components/Territory/components/shared/CardWrapper.vue";
import LabeledValue from "../Shared/components/LabeledValue.vue";
import MarketingCampaignBadge from "./MarketingCampaignBadge.vue";
import MarketingCampaignConsumerStatusBadge from "./MarketingCampaignConsumerStatusBadge.vue";
import LogLevelBadge from "./LogLevelBadge.vue";
const simpleIcon = useSimpleIcon();

const marketingLog = useMarketingLog();
export default {
    name: "ViewLogsModal",
    components: {
        LogLevelBadge,
        MarketingCampaignConsumerStatusBadge,
        MarketingCampaignBadge, LabeledValue, CardWrapper, Badge, LoadingSpinner, SimpleIcon, EventLog, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        relationType: {
            type: String,
            default: marketingLog.relationTypes.MARKETING_CAMPAIGN,
        },
        relationId: {
            type: Number,
            required: true,
        },
        data: {
            type: Object,
            default: {},
        }
    },
    emits: ['close'],
    data() {
        return {
            marketingLog,
            simpleIcon,
            api: ApiService.make(),
            loadingLogs: false,
            logs: [],
            relation: {},
            openedLog: {},
        }
    },
    created() {
        this.getLogs();
    },
    methods: {
        async getLogs() {
            this.loadingLogs = true;
            const response = await this.api.listMarketingCampaignLogs({
                relationId: this.relationId,
                relationType: this.relationType,
                all: true
            });
            this.logs = response.data.data;
            this.loadingLogs = false;
        },
        async showDetails(log) {
            //already opened
            if (log.id === this.openedLog.id) {
                this.openedLog = {}
            } else {
                const response = await this.api.getMarketingLog(log.id);
                this.openedLog = response.data.data;
            }
        }
    }
}
</script>
