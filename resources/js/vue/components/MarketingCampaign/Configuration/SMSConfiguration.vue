<template>
    <simple-card :dark-mode="darkMode" class="grid grid-cols-3 gap-4 p-4">
        <labeled-value label="SMS Content" class="col-span-2">
            <shortcode-input-cached
                v-model="modelValue.message"
                :all-shortcodes="shortcodes"
                :dark-mode="darkMode"
                input-type="textarea"
                placeholder="Hello"
            />
        </labeled-value>
        <labeled-value label="Send Test SMS">
            <div class="flex flex-col gap-2">
                <custom-input
                    class="flex flex-1"
                    input-classes="bg-light-module"
                    :dark-mode="darkMode"
                    v-model="toPhone"
                    placeholder="Phone Number"
                />
                <div>
                    <custom-button
                        :dark-mode="darkMode"
                        @click="sendTestSMS"
                    >
                        <div class="flex gap-2 items-center">
                            <div>
                                Send
                            </div>
                            <simple-icon
                                :icon="simpleIcon.icons.PAPER_AIRPLANE"
                                :dark-mode="darkMode"
                                :color="simpleIcon.colors.WHITE"
                            />
                        </div>
                    </custom-button>
                </div>
            </div>
        </labeled-value>
        <div class="col-span-full grid grid-cols-2 gap-2">
            <labeled-value>
                <template #label>
                    <div class="flex gap-2 items-center font-semibold">
                        <div>Send SMS Between</div>
                        <simple-icon
                            :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                            :color="simpleIcon.colors.BLUE"
                            tooltip="SMS will be sent at random point within the given range"
                        />
                    </div>
                </template>
                <div class="flex flex-col justify-between gap-2">
                    <Datepicker
                        :dark-mode="darkMode"
                        v-model="modelValue.send_time.start"
                        :dark="darkMode"
                        teleport="body"
                        time-picker
                        :clearable="false"
                        :max-time="modelValue.send_time.end"
                        :min-time="{hours: 8, minutes: 0, seconds: 0}"
                    />
                    <Datepicker
                        :dark-mode="darkMode"
                        v-model="modelValue.send_time.end"
                        :dark="darkMode"
                        teleport="body"
                        time-picker
                        :clearable="false"
                        :min-time="modelValue.send_time.start"
                        :max-time="{hours: 21, minutes: 0, seconds: 0}"
                    />
                </div>
            </labeled-value>
            <labeled-value>
                <template #label>
                    <div class="flex gap-2 items-center font-semibold">
                        <div>Send At</div>
                    </div>
                </template>
                <Datepicker
                    :dark-mode="darkMode"
                    v-model="modelValue.sent_at"
                    :min-date="new Date()"
                    :dark="darkMode"
                    teleport="body"
                    :enable-time-picker="false"
                />
            </labeled-value>
        </div>
    </simple-card>
</template>
<script>
import SimpleCard from "../SimpleCard.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import ShortcodeInputCached from "../../Shared/components/ShortcodeInputCached.vue";
import ApiService from "../services/api.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import {useToastNotificationStore} from "../../../../stores/billing/tost-notification.store.js";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import Datepicker from "@vuepic/vue-datepicker";
const simpleIcon = useSimpleIcon();

export default {
    name: "SMSConfiguration",
    components: {Datepicker, CustomInput, CustomButton, SimpleIcon, ShortcodeInputCached, LabeledValue, SimpleCard},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            api: ApiService.make(),
            shortcodes: [],
            loading: false,
            toastNotification: useToastNotificationStore(),
            toPhone: '',
            simpleIcon,
        }
    },
    computed: {
        validSMS() {
            return /^\+?[1-9]\d{7,14}$/.test(this.toPhone);
        },

    },
    created() {
        this.getMarketingCampaignShortcodes();
    },
    methods: {
        async getMarketingCampaignShortcodes() {
            this.loading = true;
            const response = await this.api.getMarketingCampaignShortcodes();
            this.shortcodes = response.data.data.shortcodes;
            this.loading = false;
        },
        async sendTestSMS() {
            this.loading = true;
            try {
                await this.api.sendTestSMS(
                    this.toPhone,
                    this.modelValue.message
                )
                this.toastNotification.notifySuccess(`Test SMS Sent!`)
            } catch (err) {
                this.toastNotification.notifyError(`Failed to Send SMS`)
            }

            this.loading = false;
        }
    }
}
</script>
