<template>
    <div class="flex flex-col gap-1">
        <labeled-value label="SMS Content">
            <shortcode-input-cached
                v-model="modelValue.message"
                :all-shortcodes="shortcodes"
                :dark-mode="darkMode"
                input-type="textarea"
                placeholder="Hi <PERSON>,"
            />
        </labeled-value>
        <labeled-value label="Start At">
            <Datepicker
                :dark-mode="darkMode"
                v-model="modelValue.sent_at"
                :min-date="new Date()"
                :dark="darkMode"
                teleport="body"
                placeholder="Now"
            />
        </labeled-value>
        <labeled-value label="Anniversary / Send After">
            <div class="flex gap-2">
                <dropdown
                    :dark-mode="darkMode"
                    :options="dripCampaign.spanOptions"
                    v-model="modelValue.span_type"
                />
                <simple-stepper
                    :dark-mode="darkMode"
                    v-model="modelValue.span_value"
                    :max="dripCampaign.spanRanges[modelValue.span_type]?.max"
                    :min="dripCampaign.spanRanges[modelValue.span_type]?.min"
                    :suffix="dripCampaign.spanStyle[modelValue.span_type]?.suffix"
                />
            </div>
        </labeled-value>
        <labeled-value>
            <template #label>
                <div class="flex gap-2 items-center font-semibold">
                    <div>Send SMS Between</div>
                    <simple-icon
                        :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                        :color="simpleIcon.colors.BLUE"
                        tooltip="SMS will be sent at random point within the given range"
                    />
                </div>
            </template>
            <div class="flex flex-col justify-between gap-2">
                <Datepicker
                    :dark-mode="darkMode"
                    v-model="modelValue.send_time.start"
                    :dark="darkMode"
                    teleport="body"
                    time-picker
                    :clearable="false"
                    :max-time="modelValue.send_time.end"
                    :min-time="{hours: 8, minutes: 0, seconds: 0}"
                />
                <Datepicker
                    :dark-mode="darkMode"
                    v-model="modelValue.send_time.end"
                    :dark="darkMode"
                    teleport="body"
                    time-picker
                    :clearable="false"
                    :min-time="modelValue.send_time.start"
                    :max-time="{hours: 21, minutes: 0, seconds: 0}"
                />
            </div>
        </labeled-value>
    </div>
</template>
<script>
import ShortcodeInputCached from "../../Shared/components/ShortcodeInputCached.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import ApiService from "../services/api.js";
import SimpleStepper from "../../Affiliates/SimpleStepper.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Datepicker from "@vuepic/vue-datepicker";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import useDripCampaign from "../../../../composables/marketing/useDripCampaign.js";
const simpleIcon = useSimpleIcon();
const dripCampaign = useDripCampaign();

export default {
    name: "DripSMSConfiguration",
    components: {SimpleIcon, Datepicker, Dropdown, SimpleStepper, LabeledValue, ShortcodeInputCached},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            api: ApiService.make(),
            shortcodes: [],
            simpleIcon,
            dripCampaign
        }
    },
    created() {
        this.getMarketingCampaignShortcodes();
    },
    methods: {
        async getMarketingCampaignShortcodes() {
            this.loading = true;
            const response = await this.api.getMarketingCampaignShortcodes();
            this.shortcodes = response.data.data.shortcodes;
            this.loading = false;
        },
    }
}
</script>
