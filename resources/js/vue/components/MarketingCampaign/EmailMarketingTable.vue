<template>
    <div>
        <AlertsContainer v-if="alertActive" :text="alertText" :alert-type="alertType" :dark-mode="darkMode"/>
        <simple-table
            :title="title"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            v-model="filter"
            :table-filters="availableFilters"
            :pagination-data="paginationData"
            @update:modelValue="getEmailMarketingCampaigns"
            @search="handleSearch"
            @reset="handleReset"
            row-classes="gap-5 grid items-center py-3 rounded px-5 text-sm"
        >
            <template v-slot:custom-buttons>
                <slot name="custom-buttons">

                </slot>
            </template>
            <template v-slot:row.col.status="{item,value}">
                <div class="flex gap-3 justify-between items-center">
                    <marketing-campaign-badge
                        :dark-mode="darkMode"
                        :status="value"
                    />
                    <badge
                        @click="handleAction('view_logs',item)"
                        v-if="item.has_logs"
                        :dark-mode='darkMode'
                        class="cursor-pointer text-primary-500"
                    >
                        Logs
                    </badge>
                    <hover-tooltip v-if="item.processing">
                        <template #title>
                            <div class="circle pulse bg-primary-500 w-3 h-3"></div>
                        </template>
                        <div class="font-semibold text-xs">
                            Uploading...
                        </div>
                    </hover-tooltip>
                </div>
            </template>
            <template v-slot:row.col.sent_at="{item,value}">
                <div class="text-sm flex flex-col gap-2">
                    <div>{{value}}</div>
                    <div>
                        <badge v-if="item.schedule" :dark-mode="darkMode">{{item.schedule}}</badge>
                    </div>
                </div>
            </template>
            <template v-slot:row.col.targeted="{item,value}">
                <div>
                    {{ item.metrics.targeted ?? 'N/A' }}
                </div>
            </template>
            <template v-slot:row.col.sent="{item,value}">
                <div class="flex gap-2 items-center">
                    <div>
                        {{ item.metrics.sent?.count ?? 'N/A' }}
                    </div>
                    <badge v-if="item.metrics.sent?.percentage" :dark-mode="darkMode">{{ item.metrics.sent?.percentage }}%</badge>
                </div>
            </template>
            <template #row.col.delivered="{item,value}" >
                <div class="flex gap-2 items-center">
                    <div>{{item.metrics?.delivered?.count ?? 'N/A'}}</div>
                    <badge v-if="item.metrics?.delivered?.percentage" :dark-mode="darkMode">{{ item.metrics?.delivered?.percentage }}%</badge>
                </div>
            </template>
            <template #row.col.failed="{item,value}">
                <div class="flex gap-2 items-center">
                    <div>{{item.metrics?.failed?.count ?? 'N/A'}}</div>
                    <badge v-if="item.metrics?.failed?.percentage" :dark-mode="darkMode">{{ item.metrics?.failed?.percentage }}%</badge>
                </div>
            </template>
            <template #row.col.opened="{item,value}">
                <div class="flex gap-2 items-center">
                    <div>{{item.metrics?.opened?.count ?? 'N/A'}}</div>
                    <badge v-if="item.metrics?.opened?.percentage" :dark-mode="darkMode">{{ item.metrics?.opened?.percentage }}%</badge>
                </div>
            </template>
            <template #row.col.clicked="{item,value}">
                <div class="flex gap-2 items-center">
                    <div>{{item.metrics?.clicked?.count ?? 'N/A'}}</div>
                    <badge v-if="item.metrics?.clicked?.percentage" :dark-mode="darkMode">{{ item.metrics?.clicked?.percentage }}%</badge>
                </div>
            </template>
            <template #row.col.leads="{item,value}">
                <div class="flex gap-2 items-center">
                    {{ item.metrics.leads?.count ?? 'N/A' }}
                    <badge v-if="item.metrics?.leads?.percentage" :dark-mode="darkMode">{{ item.metrics?.leads?.percentage }}%</badge>
                </div>
            </template>
            <template v-slot:row.col.actions="{item,value}">
                <div class="flex items-center gap-2">
                    <actions-handle
                        @edit="handleAction('edit',item)"
                        @delete="handleAction('delete',item)"
                        :dark-mode="darkMode"
                    />
                    <simple-icon
                        tooltip="Refresh Metrics"
                        clickable
                        @click="handleRefresh(item)"
                        :color="simpleIcon.colors.BLUE"
                        :icon="simpleIcon.icons.ARROW_PATH"
                        :dark-mode="darkMode"
                    />
                    <simple-icon
                        v-if="item.pausable"
                        clickable
                        :tooltip="item.status !== marketingCampaign.statuses.PAUSED ? 'Pause Campaign' : 'Resume Campaign'"
                        :icon="item.status !== marketingCampaign.statuses.PAUSED ? simpleIcon.icons.PAUSE_CIRCLE : simpleIcon.icons.PLAY_CIRCLE"
                        :color="simpleIcon.colors.BLUE"
                        :dark-mode="darkMode"
                        @click="toggleCampaign(item.id)"
                    />
                </div>
            </template>
        </simple-table>
        <modal
            v-if="selectedMarketingCampaign && (selectedMarketingCampaignAction === 'edit' || selectedMarketingCampaignAction === 'delete')"
            :dark-mode="darkMode"
            @close="handleClose"
            no-buttons
            small
            no-min-height
        >
            <template #header>
                {{
                    selectedMarketingCampaignAction === 'edit' ? `Editing Marketing Campaign ID: ${selectedMarketingCampaign.id}` : `Delete Marketing Campaign ID ${selectedMarketingCampaign.id}`
                }}
            </template>
            <template #content>
                <edit-marketing-campaign-model-content
                    v-if="selectedMarketingCampaignAction === 'edit'"
                    :dark-mode="darkMode"
                    :marketing-campaign-id="selectedMarketingCampaign.id"
                    @save="handleUpdate('Updated')"
                />
                <delete-marketing-campaign-model-content
                    v-else
                    :dark-mode="darkMode"
                    :marketing-campaign-id="selectedMarketingCampaign.id"
                    @delete="handleUpdate('Deleted')"
                    :name="selectedMarketingCampaign.name"
                />
            </template>
        </modal>
        <view-logs-modal
            @close="handleClose"
            v-else-if="selectedMarketingCampaignAction === 'view_logs'"
            :relation-id="selectedMarketingCampaign.id"
            :relation-type="marketingLog.relationTypes.MARKETING_CAMPAIGN"
            :data="selectedMarketingCampaign"
        />
    </div>
</template>
<script>
import ApiService from "./services/api.js";
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {SimpleTableHiddenFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import useMarketingCampaign from "../../../composables/useMarketingCampaign.js";
import Badge from "../Shared/components/Badge.vue";
import ActionsHandle from "../Shared/components/ActionsHandle.vue";
import Modal from "../Shared/components/Modal.vue";
import EditMarketingCampaignModelContent from "./EditMarketingCampaignModelContent.vue";
import MarketingCampaignBadge from "./MarketingCampaignBadge.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import Alert from "../Shared/components/Alert.vue";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import HoverTooltip from "../Shared/components/HoverTooltip.vue";
import {useToastNotificationStore} from "../../../stores/billing/tost-notification.store.js";
import useErrorHandler from "../../../composables/useErrorHandler.js";
import DeleteMarketingCampaignModelContent from "./DeleteMarketingCampaignModelContent.vue";
import ViewLogsModal from "./ViewLogsModal.vue";
import useMarketingLog from "../../../composables/useMarketingLog.js";

const marketingCampaign = useMarketingCampaign();
const simpleIcon = useSimpleIcon();
const marketingLog = useMarketingLog();
const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 10,
}
export default {
    name: "EmailMarketingTable",
    components: {
        ViewLogsModal,
        DeleteMarketingCampaignModelContent,
        HoverTooltip,
        MarketingCampaignBadge,
        EditMarketingCampaignModelContent,
        Modal,
        ActionsHandle,
        Badge,
        SimpleTable,
        AlertsContainer,
        Alert,
        SimpleAlert,
        SimpleIcon
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        baseFilters: {
            type: Object,
            default: {}
        },
        title: {
            type: String,
            default: "Email Marketing Campaigns"
        },
        headers: {
            type: Array,
            default: [
                {title: 'Name', field: 'name'},
                {title: 'Status', field: 'status'},
                {title: 'Sent At / Scheduled', field: 'sent_at', cols: 2},
                {title: 'Targeted', field: 'targeted'},
                {title: 'Sent', field: 'sent'},
                {title: 'Delivered', field: 'delivered'},
                {title: 'Failed', field: 'failed'},
                {title: 'Opened', field: 'opened'},
                {title: 'Clicked', field: 'clicked'},
                {title: 'Leads Generated', field: 'leads'},
                {title: 'Revenue', field: 'revenue'},
                {title: 'Actions', field: 'actions'},
            ]
        }
    },
    mixins: [AlertsMixin],
    data() {
        return {
            marketingLog,
            loading: false,
            api: ApiService.make(),
            data: [],
            filter: {},
            availableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'name',
                    title: "Enter Email Campaign Name"
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'status',
                    title: "Status",
                    options: [
                        {id: 'draft', name: 'Draft'},
                        {id: 'sent', name: 'Sent'},
                        {id: 'active', name: 'Active'},
                        {id: 'paused', name: 'Paused'}
                    ]
                },
            ],
            paginationData: {},
            DEFAULT_TABLE_FILTER,
            marketingCampaign,
            selectedMarketingCampaign: null,
            selectedMarketingCampaignAction: null,
            simpleIcon,
            toastNotification: useToastNotificationStore(),
            errorHandler: useErrorHandler(),
        }
    },
    created() {
        this.handleSearch();
    },
    methods: {
        async getEmailMarketingCampaigns() {
            this.loading = true;
            const response = await this.api.listMarketingCampaigns(this.filter);
            const {data, links, meta} = response.data
            this.data = data
            this.paginationData = {links, ...meta}
            this.loading = false;
        },
        async handleSearch() {
            this.filter = {...this.filter, ...DEFAULT_TABLE_FILTER, ...this.baseFilters};
            await this.getEmailMarketingCampaigns();
        },
        async handleReset() {
            this.filter = {...DEFAULT_TABLE_FILTER, ...this.baseFilters};
            await this.getEmailMarketingCampaigns();
        },
        async handleRefresh(campaign) {
            try {
                await this.api.updateCampaignMetrics(campaign.id);
                this.toastNotification.notifySuccess(`Updating Metrics, may take a couple minutes`)
            } catch (err) {
                this.errorHandler.handleError(err)
                this.toastNotification.notifyError(`Something went wrong. ${this.errorHandler.message}`)
            }
        },
        async toggleCampaign(campaignId) {
            this.api.toggleCampaign(campaignId).then(() => this.getEmailMarketingCampaigns());
        },
        handleAction(action, campaign) {
            this.selectedMarketingCampaign = campaign;
            this.selectedMarketingCampaignAction = action;
        },
        handleClose() {
            this.selectedMarketingCampaignAction = null;
            this.selectedMarketingCampaign = null;
        },
        handleUpdate(action) {
            this.toastNotification.notifySuccess(`Campaign ${this.selectedMarketingCampaign.name} ${action}`);
            this.handleClose();
            this.getEmailMarketingCampaigns();
        },
    }
}
</script>
<style scoped>
.circle {
    border-radius: 50%;
    box-shadow: 0px 0px 1px 1px #0000001a;
}

.pulse {
    animation: pulse-animation 2s infinite;
}

@keyframes pulse-animation {
    0% {
        box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.2);
    }
    100% {
        box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
    }
}
</style>
