<template>
    <labeled-value label="From Email">
        <div class="flex items-center gap-1">
            <custom-input :dark-mode="darkMode" v-model="modelValue.sender_local"/>
            <p class="text-lg">@</p>
            <div class="flex flex-col gap-2">
                <div v-if="loading" class="flex justify-start">
                    <loading-spinner size="w-4 h-4" margin="my-2" wrapper-style="flex" />
                </div>
                <dropdown
                    class="min-w-[10rem]"
                    v-else
                    :dark-mode="darkMode"
                    :options="emailDomainOptions"
                    v-model="modelValue.sender_domain"
                    option-style="text-sm font-medium"
                />
            </div>
        </div>
    </labeled-value>
</template>
<script>
import Dropdown from "../Shared/components/Dropdown.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import LabeledValue from "../Shared/components/LabeledValue.vue";
import ApiService from "./services/api.js";
import useMarketingDomain from "../../../composables/useMarketingDomain.js";
const marketingDomain = useMarketingDomain();

export default {
    name: "FromEmailAddress",
    components: {LabeledValue, CustomInput, LoadingSpinner, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            api: ApiService.make(),
            loading: false,
            emailDomainOptions: [],
            marketingDomain,
        }
    },
    created() {
        this.listEmailDomains();
    },
    methods: {
        async listEmailDomains() {
            this.loading = true;
            const response = await this.api.getMarketingDomains({all: true, status: marketingDomain.status.VERIFIED});
            this.emailDomainOptions = response.data.data.map(domain => ({id: domain.name, name: domain.name}));
            this.loading = false;
        },
    }
}
</script>
