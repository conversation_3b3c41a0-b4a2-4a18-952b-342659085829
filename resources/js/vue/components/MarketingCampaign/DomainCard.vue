<template>
    <BillingCardWrapper
        heading="Domains"
        :dark-mode="darkMode"
        :loading="loading"
        variant="light"
    >
        <template #headerAction>
            <SimpleIcon
                :dark-mode="darkMode"
                :icon="simpleIcon.icons.ARROW_PATH"
                :color="simpleIcon.colors.BLUE"
                tooltip="Sync Domains"
                clickable
                @click="sync"
            />
        </template>
        <BillingCardPaginatedContent
            v-if="!loading"
            :dark-mode="darkMode"
            :headers="headers"
            :data="data"
            :pagination-data="pagination"
            @page-change="handlePageChange"
        >
            <template #status="{item, value}">
                <badge
                    :dark-mode="darkMode"
                    class="capitalize"
                    :color="marketingDomain.statusStyle[value].color"
                >
                    {{ value }}
                </badge>
            </template>
            <template #sent_count="{item,value}">
                {{value}} Sent
            </template>
        </BillingCardPaginatedContent>
    </BillingCardWrapper>
</template>
<script>
import BillingCardWrapper from "../BillingManagement/components/BillingCardBuilders/BillingCardWrapper.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import ApiService from "./services/api.js";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import BillingCardPaginatedContent
    from "../BillingManagement/components/BillingCardBuilders/BillingCardPaginatedContent.vue";
import Badge from "../Shared/components/Badge.vue";
import useMarketingDomain from "../../../composables/useMarketingDomain.js";
const simpleIcon = useSimpleIcon();
const marketingDomain = useMarketingDomain();

export default {
    name: "DomainCard",
    components: {Badge, BillingCardPaginatedContent, SimpleIcon, BillingCardWrapper},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            simpleIcon,
            marketingDomain,
            loading: false,
            api: ApiService.make(),
            headers: [
                {name: 'Id', field: 'id'},
                {name: 'Name', field: 'name', cols: 2},
                {name: 'Status', field: 'status'},
                {name: 'Last 24 Hours', field: 'sent_count'},
            ],
            data: [],
            pagination: {
                page: 1,
                perPage: 5,
            },
        }
    },
    created() {
        this.list();
    },
    methods: {
        async handlePageChange(newPage) {
            this.pagination.page = newPage
            await this.list();
        },
        async list() {
            this.loading = true;
            const sub24hours = Math.floor((Date.now() - (24 * 60 * 60 * 1000)) / 1000);
            const response = await this.api.getMarketingDomains({...this.pagination, send_count_cutoff: sub24hours});

            this.pagination = {
                page: response.data.meta.current_page,
                total: response.data.meta.total,
                perPage: response.data.meta.per_page,
            }

            this.data = response.data.data;

            this.loading = false;
        },
        async sync() {
            await this.api.syncMarketingDomains();
            await this.list();
        }
    }
}
</script>
