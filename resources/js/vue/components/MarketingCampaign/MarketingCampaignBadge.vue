<template>
    <badge
        :dark-mode="darkMode"
        :color="styles[status].badgeColor"
    >
        {{titles[status]}}
    </badge>
</template>

<script>
import Badge from "../Shared/components/Badge.vue";
import useMarketingCampaign from "../../../composables/useMarketingCampaign.js";
const {styles, titles} = useMarketingCampaign()

export default {
    name: "MarketingCampaignBadge" ,
    components: {Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        status: {
            type: String,
        }
    },
    data() {
        return {
            styles,
            titles
        }
    }
}
</script>