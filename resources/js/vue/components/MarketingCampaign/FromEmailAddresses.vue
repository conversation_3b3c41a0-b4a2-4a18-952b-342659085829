<template>
    <labeled-value>
        <div class="flex flex-col gap-2 mb-2">
            <div v-for="(email, index) in modelValue" class="flex items-center gap-1">
                <custom-input :dark-mode="darkMode" v-model="email.sender_local"/>
                <p class="text-lg">@</p>
                <div class="flex flex-col gap-2">
                    <div v-if="loading" class="flex justify-start">
                        <loading-spinner size="w-4 h-4" margin="my-2" wrapper-style="flex" />
                    </div>
                    <dropdown
                        class="min-w-[10rem]"
                        v-else
                        :dark-mode="darkMode"
                        :options="emailDomainOptions"
                        v-model="email.sender_domain"
                        option-style="text-sm font-medium"
                    />
                </div>
                <simple-icon @click="removeEmail(index)" :icon="simpleIcon.icons.X_CIRCLE" tooltip="Remove Email" />
            </div>
        </div>
        <CustomButton @click="addEmail" :dark-mode="darkMode">+ Email</CustomButton>
    </labeled-value>
</template>
<script>
import Dropdown from "../Shared/components/Dropdown.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import LabeledValue from "../Shared/components/LabeledValue.vue";
import ApiService from "./services/api.js";
import useMarketingDomain from "../../../composables/useMarketingDomain.js";
import CustomButton from "../Shared/components/CustomButton.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
const marketingDomain = useMarketingDomain();
const simpleIcon = useSimpleIcon();

export default {
    name: "FromEmailAddresses",
    components: {SimpleIcon, CustomButton, LabeledValue, CustomInput, LoadingSpinner, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Array,
            default: {},
        }
    },
    data() {
        return {
            api: ApiService.make(),
            loading: false,
            emailDomainOptions: [],
            marketingDomain,
            emailCount: 2,
            simpleIcon,
        }
    },
    created() {
        this.listEmailDomains();
    },
    methods: {
        async listEmailDomains() {
            this.loading = true;
            const response = await this.api.getMarketingDomains({all: true, status: marketingDomain.status.VERIFIED});
            this.emailDomainOptions = response.data.data.map(domain => ({id: domain.name, name: domain.name}));
            this.loading = false;
        },
        removeEmail(index) {
            this.modelValue.splice(index, 1);
        },
        addEmail() {
            this.modelValue.push({sender_local: '', sender_domain: ''})
        }
    }
}
</script>
