<template>
    <div class="flex flex-col rounded-md" :class="[darkMode ? darkStyle : lightStyle]">
        <slot name="title">
            <div class="text-xl font-medium mb-2" v-if="title">{{title}}</div>
        </slot>
        <loading-spinner v-if="loading" :dark-mode="darkMode"/>
        <slot v-else>
            <div class="flex justify-center items-center text-slate-500 h-20">
                No Data
            </div>
        </slot>
    </div>
</template>
<script>
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";

export default {
    name: "SimpleCard",
    components: {LoadingSpinner},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: null,
        },
        variant: {
            type: String,
            default: 'default'
        },
        loading: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        lightStyle() {
            return {
                default: 'bg-light-background',
                light: 'border rounded-lg bg-light-module border-light-border'
            }[this.variant] ?? this.variant
        },
        darkStyle() {
            return {
                default: 'bg-dark-background',
                light: 'border rounded-lg bg-dark-module border-dark-border'
            }[this.variant] ?? this.variant
        }
    }
}
</script>
