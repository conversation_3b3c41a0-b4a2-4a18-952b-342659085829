<template>
    <dropdown
        :dark-mode="darkMode"
        :options="options"
        v-model="modelValue"
        @update:modelValue="handleUpdate"
        :loading="loading"
        :disabled="loading"
    />
</template>
<script>
import Dropdown from "../Shared/components/Dropdown.vue";
import Api from "../EmailTemplates/API/api.js";

export default {
    name: "EmailTemplateDropdown",
    components: {Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: String,
            default: null,
        },
        filters: {
            type: Object,
            default: {},
        },
        emitObject: {
            type: Boolean,
            default: false,
        }
    },
    emits: ['update:modelValue'],
    data() {
        return {
            api: Api.make(),
            options: [],
            loading: false,
        }
    },
    created() {
        this.listEmailTemplates();
    },
    methods: {
        handleUpdate(args) {
            args = this.emitObject ? this.options.find((option) => (option.id === args)) : args;
            this.$emit('update:modelValue', args)
        },
        async listEmailTemplates() {
            this.loading = true;
            const response = await this.api.retrieveEmailTemplateList({filters: this.filters});
            this.options = response.data.data;
            this.loading = false;
        },
    }
}
</script>
