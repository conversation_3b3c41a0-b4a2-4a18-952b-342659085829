<template>
    <div>
        <alerts-container :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
        <Modal
            @close="$emit('close')"
            :dark-mode="darkMode"
            confirm-text="Create"
            no-min-height
            @confirm="toggleShowConfirmModal"
        >
            <template #header>
                <h3 class="font-medium">Create Drip Campaign</h3>
            </template>
            <template #content>
                <div class="grid grid-cols-7 gap-3">
                    <div class="col-span-5 grid grid-cols-6 gap-3">
                        <simple-card class="p-4 col-span-3" :dark-mode="darkMode" title="Basic Information">
                            <template #title>
                                <div class="text-primary-500 font-semibold">1. Basic Details</div>
                            </template>
                            <div class="flex flex-col gap-2 flex-1">
                                <labeled-value label="Name">
                                    <custom-input
                                        class="w-full"
                                        placeholder="Campaign Name"
                                        :dark-mode="darkMode"
                                        v-model="campaign.name"
                                        input-classes="bg-light-module"
                                    />
                                </labeled-value>
                                <labeled-value label="Description" class="flex-1" content-style="flex flex-1">
                                    <custom-input
                                        class="flex-1"
                                        placeholder="Campaign Description"
                                        :dark-mode="darkMode"
                                        v-model="campaign.description"
                                        type="textarea"
                                        input-classes="bg-light-module h-full"
                                    />
                                </labeled-value>
                            </div>
                        </simple-card>
                        <simple-card class="p-4 col-span-3" :dark-mode="darkMode">
                            <template #title>
                                <div class="text-primary-500 font-semibold">2. Configuration</div>
                            </template>
                            <div v-if="type === marketingCampaign.types.DRIP_EMAIL" class="flex flex-col gap-2">
                                <labeled-value label="Email Template">
                                    <email-template-dropdown
                                        :dark-mode="darkMode"
                                        :filters="{'email-template-type': [5]}"
                                        @update:modelValue="handleTemplateSelected"
                                        emit-object

                                    />
                                </labeled-value>
                                <from-email-address :dark-mode="darkMode" v-model="campaign.configuration" />
                                <labeled-value label="Anniversary / Send After">
                                    <div class="flex gap-2">
                                        <dropdown
                                            :dark-mode="darkMode"
                                            :options="marketingCampaign.dripSpanOptions"
                                            v-model="campaign.configuration.span_type"
                                        />
                                        <simple-stepper
                                            :dark-mode="darkMode"
                                            v-model="campaign.configuration.span_value"
                                            :max="marketingCampaign.dripSpanRanges[campaign.configuration.span_type]?.max"
                                            :min="marketingCampaign.dripSpanRanges[campaign.configuration.span_type]?.min"
                                            :suffix="marketingCampaign.dripSpanStyle[campaign.configuration.span_type]?.suffix"
                                        />
                                    </div>
                                </labeled-value>
                                <labeled-value label="Start At">
                                    <Datepicker
                                        :dark-mode="darkMode"
                                        v-model="campaign.configuration.sent_at"
                                        :min-date="new Date()"
                                        :dark="darkMode"
                                        teleport="body"
                                        placeholder="Now"
                                    />
                                </labeled-value>
                                <labeled-value>
                                    <template #label>
                                        <div class="flex gap-2 items-center font-semibold">
                                            <div>Send Emails Between</div>
                                            <simple-icon
                                                :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                                                :color="simpleIcon.colors.BLUE"
                                                tooltip="Email will be sent at random point within the given range"
                                            />
                                        </div>
                                    </template>
                                    <div class="flex flex-col gap-2">
                                        <Datepicker
                                            :dark-mode="darkMode"
                                            v-model="campaign.configuration.send_time.start"
                                            :dark="darkMode"
                                            teleport="body"
                                            time-picker
                                            :clearable="false"
                                            :max-time="campaign.configuration.send_time.end"
                                        />
                                        <Datepicker
                                            :dark-mode="darkMode"
                                            v-model="campaign.configuration.send_time.end"
                                            :dark="darkMode"
                                            teleport="body"
                                            time-picker
                                            :clearable="false"
                                            :min-time="campaign.configuration.send_time.start"
                                        />
                                    </div>
                                </labeled-value>
                            </div>
                            <component
                                v-else
                                :is="marketingCampaign.typeConfigurationComponentMap[type]"
                                :dark-mode="darkMode"
                                :model-value="campaign.configuration"
                            />
                        </simple-card>
                        <simple-card class="p-4 col-span-full" :dark-mode="darkMode">
                            <template #title>
                                <div class="text-primary-500 font-semibold">4. Verification Type</div>
                            </template>
                            <marketing-campaign-verification-type
                                :dark-mode="darkMode"
                                v-model="campaign"
                            />
                        </simple-card>
                    </div>
                    <simple-card class="p-4 col-span-2" :dark-mode="darkMode">
                        <template #title>
                            <div class="text-primary-500 font-semibold flex justify-between">
                                <div>3. Filters</div>
                                <filterable
                                    :dark-mode="darkMode"
                                    v-model="filters"
                                    :filters="filterOptions"
                                    button-color="primary"
                                />
                            </div>
                        </template>
                        <FilterableActivePills
                            class="flex-wrap gap-2 mt-2"
                            pill-style="text-blue-600 bg-blue-100 border border-blue-200"
                            v-if="filters"
                            :filters="filterOptions"
                            :active-filters="filters"
                            :dark-mode="darkMode"
                            @reset-filter="clearFilter"
                        />
                    </simple-card>
                </div>
            </template>
        </Modal>
        <Modal
            v-if="showConfirm"
            :dark-mode="darkMode"
            no-min-height
            small
            custom-width="max-w-screen-sm"
            confirm-text="Create Campaign"
            @close="toggleShowConfirmModal"
            @confirm="handleConfirm"
            :disable-confirm="invalidCampaign"
        >
            <template #header>
                Review Details
            </template>
            <template #content>
                <simple-alert v-if="invalidCampaign" :dark-mode="darkMode">
                    <template #content>
                        <div>Required Fields Missing:</div>
                        <div class="grid grid-cols-3">
                            <div v-if="!campaign.name"> - Campaign Name</div>
                            <div v-if="!campaign.configuration.email_template_id"> - Email Template</div>
                            <div v-if="!campaign.configuration.send_time"> - Send Time</div>
                            <div v-if="!campaign.validation_type"> - Verification Type</div>
                        </div>
                    </template>
                </simple-alert>
                <div class="grid grid-cols-2 gap-2">
                    <simple-card class="p-2 gap-2">
                        <template #title>
                            <div class="text-primary-500 font-semibold">Campaign Summary</div>
                        </template>
                        <labeled-value label="Start Date">
                            <badge>{{ campaign.configuration.sent_at ? $filters.dateFromTimestamp(campaign.configuration.sent_at) : 'Now' }}</badge>
                        </labeled-value>
                        <labeled-value label="Consumer Anniversary">
                            <badge>{{ campaign.configuration.span_value + ' ' + marketingCampaign.dripSpanStyle[campaign.configuration.span_type].suffix }}</badge>
                        </labeled-value>
                        <labeled-value label="Email Template">
                            <badge>{{ campaign.configuration.email_template_name ?? 'None Selected'}}</badge>
                        </labeled-value>
                        <labeled-value label="Verification Type">
                            <badge>{{campaign.validation_type ? toTitleCase(campaign.validation_type) : 'None Selected'}}</badge>
                        </labeled-value>
                        <labeled-value label="Filters">
                            <FilterableActivePills
                                class="flex-wrap gap-2 mt-2"
                                pill-style="text-blue-600 bg-blue-100 border border-blue-200"
                                v-if="filters"
                                :filters="filterOptions"
                                :active-filters="filters"
                                :dark-mode="darkMode"
                                @reset-filter="clearFilter"
                            />
                            <badge v-else>None Selected</badge>
                        </labeled-value>
                    </simple-card>
                    <simple-card>
                        <template #title>
                            <div class="text-primary-500 font-semibold px-4 pt-2">Preview</div>
                        </template>
                        <div class="flex flex-col gap-2">
                            <div class="flex justify-between px-4 text-sm font-semibold">
                                <div>Date</div>
                                <div>Send Count</div>
                            </div>
                            <div v-if="!estimate.length" class="flex text-center justify-center text-sm mb-4 text-slate-500">
                                No Consumers In Estimate
                            </div>
                            <div v-else class="flex justify-between px-4 pb-2 text-sm" v-for="day in estimate">
                                <div>{{day.date}}</div>
                                <div>{{day.count}}</div>
                            </div>
                        </div>
                    </simple-card>
                </div>
            </template>
        </Modal>
    </div>
</template>
<script>
import Modal from "../Shared/components/Modal.vue";
import SimpleCard from "./SimpleCard.vue";
import LabeledValue from "../Shared/components/LabeledValue.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import MarketingCampaignVerificationType from "./MarketingCampaignVerificationType.vue";
import ApiService from "./services/api.js";
import Filterable from "../Shared/components/Filterables/Filterable.vue";
import EmailTemplateDropdown from "./EmailTemplateDropdown.vue";
import Datepicker from "@vuepic/vue-datepicker";
import SimpleStepper from "../Affiliates/SimpleStepper.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import useMarketingCampaign from "../../../composables/useMarketingCampaign.js";
import FilterableActivePills from "../Shared/components/Filterables/FilterableActivePills.vue";
import Badge from "../Shared/components/Badge.vue";
import {toTitleCase} from "../../../modules/helpers.js";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import ShortcodeInputCached from "../Shared/components/ShortcodeInputCached.vue";
import FromEmailAddress from "./FromEmailAddress.vue";
const marketingCampaign = useMarketingCampaign();
const simpleIcon = useSimpleIcon();
export default {
    name: "DripCampaignModal",
    components: {
        FromEmailAddress,
        SimpleIcon,
        AlertsContainer,
        SimpleAlert,
        Badge,
        FilterableActivePills,
      Dropdown,
      SimpleStepper,
      Datepicker,
      EmailTemplateDropdown,
      Filterable, MarketingCampaignVerificationType, CustomInput, LabeledValue, SimpleCard, Modal},
    emits: ['close', 'confirm'],
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: marketingCampaign.types.DRIP_EMAIL,
        }
    },
    data() {
        return {
            simpleIcon,
            api: ApiService.make(),
            campaign: {
                id: null,
                name: null,
                description: null,
                type: this.type,
                validation_type: null,
                validation_type_inputs: {},
                configuration: {},
            },
            filterOptions: [],
            estimate: {},
            marketingCampaign,
            showConfirm: false,
            filters: {},
        }
    },
    computed: {
        invalidCampaign() {
            const required = {
                name: this.campaign.name,
                template: this.campaign.configuration.email_template_id,
                anniversary: this.campaign.configuration.span_type,
                send_time: this.campaign.configuration.send_time,
                verification: this.campaign.validation_type
            };

            return Object.values(required).some(value => value === null);
        }
    },
    created() {
        this.getFilterOptions();

        this.campaign.configuration = this.marketingCampaign.typeConfigurationDefaults[this.type];
    },
    methods: {
        toTitleCase,
        async handleConfirm() {
            const params = {
                campaign: this.campaign,
                filters: this.filters,
            }

            await this.api.createMarketingCampaign(params).then(resp => {
                this.toggleShowConfirmModal();
                this.$emit('confirm');
            }).catch(error => {
                this.showAlert("error", error.response.data.message)
            });
        },
        toggleShowConfirmModal() {
            this.showConfirm = !this.showConfirm;
        },
        async getFilterOptions() {
            const response = await this.api.filterOptions();
            this.filterOptions = response.data.data.filter_options;
        },
        async updateEstimate() {
            const response = await this.api.getEstimate({...this.campaign, filters: this.filters})
            this.estimate = response.data.data.estimate;
        },
        clearFilter(filterId) {
            delete this.filters[filterId];
        },
        handleTemplateSelected(args) {
            this.campaign.configuration.email_template_id = args.id;
            this.campaign.configuration.email_template_name = args.name;
        }
    },
    watch: {
        'campaign.configuration': {
            handler() {
                this.updateEstimate();
            },
            deep: true,
        },
        'filters': {
            handler() {
                this.updateEstimate();
            },
            deep: true,
        }
    }
}
</script>
