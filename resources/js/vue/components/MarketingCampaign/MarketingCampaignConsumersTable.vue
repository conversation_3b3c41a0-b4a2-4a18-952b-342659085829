<template>
    <div>
        <simple-table
            title="Marketing Campaign Consumers"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            v-model="filter"
            :table-filters="availableFilters"
            :pagination-data="paginationData"
            :loading="loading"
            @search="handleSearch"
            @reset="handleReset"
            @update:modelValue="listCampaignConsumers"
            :current-per-page="paginationData.per_page"
            row-classes="gap-5 grid items-center py-3 rounded px-5"
        >
            <template #row.col.status="{item,value}" class="gap-2">
                <div class="flex items-center gap-2">
                    <marketing-campaign-consumer-status-badge
                        :dark-mode="darkMode"
                        :status="value"
                    />
                    <badge
                        @click="toggleShowLogs(item)"
                        v-if="item.has_logs"
                        :dark-mode='darkMode'
                        class="cursor-pointer text-primary-500"
                    >
                        Logs
                    </badge>
                </div>
            </template>
            <template #row.col.revalidated="{item,value}">
                <div class="flex gap-2 items-center text-sm">
                    <a v-if="item?.cloned_consumer_product_id" target="_blank" :href="item.link" class="text-primary-500 font-bold flex items-center">
                        <p>ID #{{ item.cloned_consumer_product_id }}</p>
                    </a>
                    <p class="text-slate-500">({{ item.revalidated }})</p>
                </div>
            </template>
            <template #row.col.email="{item,value}">
                <div class="flex text-sm truncate">{{value}}</div>
            </template>
        </simple-table>
        <view-logs-modal
            v-if="selectedMarketingCampaignConsumer"
            @close="toggleShowLogs()"
            :relation-type="marketingLog.relationTypes.MARKETING_CAMPAIGN_CONSUMER"
            :relation-id="selectedMarketingCampaignConsumer.id"
            :dark-mode="darkMode"
            :data="selectedMarketingCampaignConsumer"
        />
    </div>
</template>
<script>
import ApiService from "./services/api.js";
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {SimpleTableHiddenFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import useMarketingCampaignConsumer from "../../../composables/useMarketingCampaignConsumer.js";
import MarketingCampaignConsumerStatusBadge from "./MarketingCampaignConsumerStatusBadge.vue";
import Badge from "../Shared/components/Badge.vue";
import ViewLogsModal from "./ViewLogsModal.vue";
import useMarketingLog from "../../../composables/useMarketingLog.js";
const marketingCampaignConsumer = useMarketingCampaignConsumer();
const marketingLog = useMarketingLog();

const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 10,
}
export default {
    name: "MarketingCampaignConsumersTable",
    components: {ViewLogsModal, Badge, MarketingCampaignConsumerStatusBadge, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            loading: false,
            api: ApiService.make(),
            headers: [
                {title: "ID", field: "id"},
                {title: "Name", field: "name"},
                {title: "Status", field: "status", cols: 2},
                {title: "Email", field: "email", cols: 2},
                {title: "Campaign", field: "campaign"},
                {title: "Sent", field: "sent_at"},
                {title: "Delivered", field: "delivered_at"},
                {title: "Opened", field: "opened_at"},
                {title: "Clicked", field: "clicked_at"},
                {title: "Revalidated", field: "revalidated", cols: 2},
                {title: "Revenue", field: "revenue"},
            ],
            data: [],
            filter: {},
            availableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'name',
                    title: "Enter Consumer Name"
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'campaign_id',
                    title: "Marketing Campaign",
                    options: []
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'status',
                    title: "Status",
                    options: marketingCampaignConsumer.statusList
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'revalidated',
                    title: "Is Revalidated",
                    options: [
                        {id: true, name: 'Yes'},
                        {id: false, name: 'No'},
                    ]
                },
            ],
            paginationData: {},
            marketingCampaignConsumer,
            selectedMarketingCampaignConsumer: null,
            marketingLog,
        }
    },
    created() {
        this.listCampaignConsumers();
        this.getFilterOptions();
    },
    methods: {
        async listCampaignConsumers() {
            this.loading = true;
            const response = await this.api.listMarketingCampaignConsumers(this.filter);
            const { data, links, meta } = response.data
            this.data = data
            this.paginationData = { links, ...meta}
            this.loading = false;
        },
        async getFilterOptions() {
            this.loading = true;
            const campaignFilterIndex = this.availableFilters.findIndex(filter => filter.field === 'campaign_id');
            const response = await this.api.getMarketingCampaignOptions()
            this.availableFilters[campaignFilterIndex].options = response.data.data.data
            this.loading = false;
        },
        async handleSearch() {
            this.filter = {...this.filter, ...DEFAULT_TABLE_FILTER};
            await this.listCampaignConsumers();
        },
        async handleReset() {
            this.filter = {...DEFAULT_TABLE_FILTER};
            await this.listCampaignConsumers();
        },
        toggleShowLogs(item = null) {
            this.selectedMarketingCampaignConsumer = item;
        }
    }

}
</script>
