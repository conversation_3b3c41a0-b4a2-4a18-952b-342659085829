<template>
    <div>
        <email-marketing-table
            title="Drip Campaigns"
            :base-filters="{type: [marketingCampaign.types.DRIP_EMAIL, marketingCampaign.types.DRIP_SMS]}"
            :dark-mode="darkMode"
            :headers="headers"
            ref="emailMarketingTable"
        >
            <template v-slot:custom-buttons>
                <custom-button @click="toggleCreateDripCampaign(marketingCampaign.types.DRIP_EMAIL)" :dark-mode="darkMode" class="gap-2">
                    <simple-icon
                        :dark-mode="darkMode"
                        :icon="simpleIcon.icons.ENVELOPE"
                    />
                    Drip Email
                </custom-button>
                <custom-button @click="toggleCreateDripCampaign(marketingCampaign.types.DRIP_SMS)" :dark-mode="darkMode" class="gap-2">
                    <simple-icon
                        :dark-mode="darkMode"
                        :icon="simpleIcon.icons.CHAT_BUBBLE_BOTTOM_CENTER_TEXT"
                    />
                    Drip SMS
                </custom-button>
            </template>
        </email-marketing-table>
        <drip-campaign-modal
            v-if="showModal"
            :type="showModal"
            :dark-mode="darkMode"
            @close="toggleCreateDripCampaign"
            @confirm="handleConfirm"
        />
    </div>
</template>
<script>
import EmailMarketingTable from "./EmailMarketingTable.vue";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import useMarketingCampaign from "../../../composables/useMarketingCampaign.js";
import CustomButton from "../Shared/components/CustomButton.vue";
import DripCampaignModal from "./DripCampaignModal.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
const marketingCampaign = useMarketingCampaign();
const simpleIcon = useSimpleIcon();

export default {
    name: "DripEmailMarketingCampaignsTab",
    components: {SimpleIcon, DripCampaignModal, CustomButton, SimpleAlert, EmailMarketingTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            showModal: false,
            marketingCampaign,
            simpleIcon,
            headers: [
                {title: 'Name', field: 'name'},
                {title: 'Status', field: 'status', cols: 2},
                {title: 'Sent At / Scheduled', field: 'sent_at', cols: 2},
                {title: 'Cadence', field: 'cadence'},
                {title: 'Targeted', field: 'targeted'},
                {title: 'Sent', field: 'sent'},
                {title: 'Delivered', field: 'delivered'},
                {title: 'Failed', field: 'failed'},
                {title: 'Opened', field: 'opened'},
                {title: 'Clicked', field: 'clicked'},
                {title: 'Leads Generated', field: 'leads'},
                {title: 'Revenue', field: 'revenue'},
                {title: 'Actions', field: 'actions'},
            ],

        }
    },
    methods: {
        toggleCreateDripCampaign(type) {
            this.showModal = type;
        },
        handleConfirm() {
            this.showModal = false;
            this.$refs.emailMarketingTable?.handleSearch()
        }
    }
}
</script>
