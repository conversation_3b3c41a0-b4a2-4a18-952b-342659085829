<template>
    <div class="grid grid-cols-3 gap-4">
        <div class="flex flex-col gap-2">
            <div class="font-semibold text-sm">Campaign Verification Type</div>
            <dropdown
                :dark-mode="darkMode"
                placeholder="Select Callback Type"
                class="mb-2"
                :options="campaignCallbackTypes"
                v-model="modelValue.validation_type"
                placement="top"
            />
        </div>
        <div class="col-span-2 text-slate-500 text-sm flex justify-center items-center" v-if="newCampaignInputs === null">
            Select a Campaign Verification Type
        </div>
        <div
            v-else-if="newCampaignInputs.length > 0"
            v-for="input in newCampaignInputs"
            class="flex flex-col gap-2"
            :class="marketingCampaign.inputComponentStyle[input.type]"
        >
            <div class="font-semibold text-sm">{{ input.label }}</div>
            <component
                v-if="shortcodes.length !== 0"
                v-model="modelValue.validation_type_inputs[input.field]"
                :is="marketingCampaign.inputComponentMap[input.type]"
                :inputType="input.type"
                :allShortcodes="shortcodes"
                :placeholder="input?.placeholder"
            />
            <loading-spinner v-else :dark-mode="darkMode"/>
        </div>
        <div v-else class="col-span-2 text-slate-500 text-sm flex justify-center items-center">
            Selected Campaign Type has no user Inputs
        </div>
    </div>
</template>
<script>
import Dropdown from "../Shared/components/Dropdown.vue";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import useMarketingCampaign from "../../../composables/useMarketingCampaign.js";
import ApiService from "./services/api.js";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
const marketingCampaign = useMarketingCampaign()

export default {
    name: "MarketingCampaignVerificationType" ,
    components: {LoadingSpinner, SimpleAlert, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object
        }
    },
    data() {
        return {
            api: ApiService.make(),
            campaignCallbackTypes: [],
            shortcodes: [],
            marketingCampaign,
        }
    },
    emits: ['update:modelValue'],
    created() {
        this.listMarketingCampaignCallbackTypes()
        this.getMarketingCampaignShortcodes();
    },
    computed: {
        newCampaignInputs() {
            if (this.modelValue.validation_type === null) {
                return null
            }

            return this.campaignCallbackTypes.find(campaignType => campaignType.id === this.modelValue.validation_type)?.payload?.inputs ?? null
        }
    },
    methods: {
        async listMarketingCampaignCallbackTypes() {
            this.loading = true;
            const response = await this.api.getMarketingCampaignCallbackTypes();
            this.campaignCallbackTypes = response.data.data.types;
            this.loading = false;
        },
        async getMarketingCampaignShortcodes() {
            this.loading = true;
            const response = await this.api.getMarketingCampaignShortcodes();
            this.shortcodes = response.data.data.shortcodes;
            this.loading = false;
        }
    },
    watch: {
        newCampaignInputs: function () {
            this.newCampaignInputs.forEach((input) => {
                if (!this.modelValue.validation_type_inputs[input.field] && input?.placeholder) {
                    this.modelValue.validation_type_inputs[input.field] = JSON.parse( `"${input.placeholder}"`)
                }
            })
        }
    }
}
</script>
