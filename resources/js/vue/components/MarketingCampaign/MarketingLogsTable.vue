<template>
    <div>
        <simple-table
            title="Email Marketing Logs"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            :pagination-data="paginationData"
            :table-filters="availableFilters"
            v-model="filter"
            @search="handleSearch"
            @reset="handleReset"
            has-row-click
            @click-row="args => marketingLog = args.id"
            @update:modelValue="getMarketingLogs"
            row-classes="gap-5 grid items-center py-3 rounded px-5 text-sm"
        >
            <template #row.col.message="{item,value}">
                <div class="text-sm truncate">{{value}}</div>
            </template>
            <template #row.col.level="{item,value}">
                <div class="flex">
                    <log-level-badge
                        :dark-mode="darkMode"
                        :level="value"
                    />
                </div>
            </template>
            <template #row.col.relations="{item,value}">
                <limited-list :list-items="value">
                    <template #list-item="{item}">
                        <badge :dark-mode="darkMode">
                            {{item.name + ' ('+ item.type + ' ID: ' + item.id + ')' }}
                        </badge>
                    </template>
                </limited-list>
            </template>
        </simple-table>
        <marketing-logs-modal
            v-if="marketingLog"
            :dark-mode="darkMode"
            :marketing-log-id="marketingLog"
            @close="marketingLog = null"
        />
    </div>
</template>
<script>
import ApiService from "./services/api.js";
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {SimpleTableHiddenFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import useLogLevel from "../../../composables/useLogLevel.js";
import Badge from "../Shared/components/Badge.vue";
import MarketingLogsModal from "./MarketingLogsModal.vue";
import LimitedList from "../Shared/components/Simple/LimitedList.vue";
import LogLevelBadge from "./LogLevelBadge.vue";
const logLevel = useLogLevel()
const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 100,
}
export default {
    name: "MarketingLogsTable",
    components: {LogLevelBadge, LimitedList, MarketingLogsModal, Badge, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            loading: false,
            api: ApiService.make(),
            headers: [
                {title: 'Id', field: 'id'},
                {title: 'Level', field: 'level'},
                {title: 'Namespace', field: 'namespace'},
                {title: 'Message', field: 'message', cols:2},
                {title: 'Relations', field: 'relations', cols:2},
                {title: 'Created At', field: 'created_at'},
            ],
            data: [],
            availableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'message',
                    title: "Enter Log Message"
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    field: 'levels',
                    title: "Log Level",
                    options: logLevel.options
                },
            ],
            filter: {},
            paginationData: {},
            marketingLog: null,
            DEFAULT_TABLE_FILTER,
            logLevel,
        }
    },
    created() {
        this.filter = {...this.DEFAULT_TABLE_FILTER}
        this.getMarketingLogs();
    },
    methods: {
        async getMarketingLogs() {
            this.loading = true;
            const response = await this.api.listMarketingCampaignLogs(this.filter);
            const { data, links, meta } = response.data
            this.data = data
            this.paginationData = { links, ...meta}
            this.loading = false;
        },
        async handleSearch() {
            this.filter = {...this.filter, ...DEFAULT_TABLE_FILTER};
            await this.getMarketingLogs();
        },
        async handleReset() {
            this.filter = {...DEFAULT_TABLE_FILTER};
            await this.getMarketingLogs();
        },
    }
}
</script>
