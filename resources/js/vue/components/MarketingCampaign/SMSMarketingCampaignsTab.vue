<template>
    <div>
        <email-marketing-table
            :base-filters="{type: [marketingCampaign.types.SMS]}"
            :dark-mode="darkMode"
        />
    </div>
</template>
<script>
import EmailMarketingTable from "./EmailMarketingTable.vue";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import useMarketingCampaign from "../../../composables/useMarketingCampaign.js";
const marketingCampaign = useMarketingCampaign();
export default {
    name: "SMSMarketingCampaignsTab",
    components: {SimpleAlert, EmailMarketingTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            marketingCampaign,
        }
    }
}
</script>
