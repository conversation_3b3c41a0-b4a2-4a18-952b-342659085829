<template>
    <badge
        :color="logLevel.styles[level].badgeColor"
        :dark-mode="darkMode"
    >
        {{logLevel.titles[level]}}
    </badge>
</template>
<script>
import Badge from "../Shared/components/Badge.vue";
import useLogLevel from "../../../composables/useLogLevel.js";
const logLevel = useLogLevel();

export default {
    name: "LogLevelBadge",
    components: {Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        level: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            logLevel,
        }
    }
}
</script>
