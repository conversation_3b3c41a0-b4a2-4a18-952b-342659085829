<template>
    <Modal
        :dark-mode="darkMode"
        @close="$emit('close')"
        small
        :confirm-text="isNewCampaign ? 'Create New Campaign' : 'Add to Campaign'"
        @confirm="handleSaveCampaign"
        :disable-confirm="!validCampaign"
    >
        <template #header>
            <h3 class="font-medium">Add / Create Marketing Campaign</h3>
        </template>
        <template #content>
            <div class="flex flex-col gap-4">
                <simple-card :dark-mode="darkMode">
                    <div class="grid grid-cols-2 p-4 gap-2">
                        <div class="font-semibold text-sm">Add To Existing Campaign</div>
                        <div class="font-semibold text-sm">Create New Campaign</div>
                        <dropdown
                            :dark-mode="darkMode"
                            placeholder="Select a Marketing Campaign"
                            v-model="campaign.id"
                            :options="campaigns"
                            :disabled="campaigns.length === 0"
                            class="mb-2"
                        />
                        <custom-input
                            placeholder="Create new Campaign"
                            v-model="campaign.name"
                            :dark-mode="darkMode"
                        />
                    </div>
                </simple-card>
                <simple-card v-if="isNewCampaign" :dark-mode="darkMode">
                    <div class="grid grid-cols-3 gap-4 p-4">
                        <div class="flex flex-col gap-2">
                            <div class="font-semibold text-sm">Campaign Type</div>
                            <dropdown
                                :dark-mode="darkMode"
                                placeholder="Select Campaign Type"
                                v-model="campaign.type"
                                :options="campaignTypes"
                                class="mb-2"
                            />
                        </div>
                        <div class="flex flex-col col-span-2 gap-2">
                            <div class="font-semibold text-sm">Description</div>
                            <textarea
                                :disabled="!isNewCampaign"
                                class="flex flex-1 w-full border rounded pl-4  focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                                placeholder="Enter a brief description..." type="text" v-model="campaign.description"
                                :class="{'border-grey-200': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"
                            />
                        </div>
                    </div>
                </simple-card>
                <component
                    v-if="marketingCampaign.typeConfigurationComponentMap[campaign.type]"
                    :is="marketingCampaign.typeConfigurationComponentMap[campaign.type]"
                    :dark-mode="darkMode"
                    v-model="campaign.configuration"
                />
                <simple-card v-if="isNewCampaign" :dark-mode="darkMode">
                   <marketing-campaign-verification-type
                       class="gap-4 p-4"
                       :dark-mode="darkMode"
                       v-model="campaign"
                   />
                </simple-card>
            </div>
        </template>
        <template #buttons>
            <div class="flex flex-1 gap-2 text-sm items-center">
                <div class="text-slate-500">Targeted Consumers Count:</div>
                <div class="font-medium">{{ consumerCount ?? 'N/A' }}</div>
            </div>
        </template>
    </Modal>
</template>
<script>
import Modal from "../Shared/components/Modal.vue";
import ApiService from "./services/api.js";
import Dropdown from "../Shared/components/Dropdown.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import useMarketingCampaign from "../../../composables/useMarketingCampaign.js";
import CustomButton from "../Shared/components/CustomButton.vue";
import {ApiFactory} from "../ConsumerSearch/services/api/factory.js";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import TextBox from "../TaskManagement/components/ResultTypes/TextBox.vue";
import SimpleCard from "./SimpleCard.vue";
import MarketingCampaignVerificationType from "./MarketingCampaignVerificationType.vue";
const marketingCampaign = useMarketingCampaign()

export default {
    name: "MarketingCampaignModal",
    components: {
        MarketingCampaignVerificationType,
        SimpleCard, TextBox, SimpleAlert, CustomButton, CustomInput, Dropdown, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        filters: {
            type: Object,
            default: []
        },
        consumerCount: {
            type: Number,
            default: null,
        },
        typeScope: {
            type: Array,
            default: [
                marketingCampaign.types.INTERNAL_EMAIL,
                marketingCampaign.types.SMS,
            ],
        }
    },
    emits: ['close'],
    data() {
        return {
            marketingCampaign,
            api: ApiService.make(),
            consumerSearchApi: ApiFactory.makeApiService('api'),
            loading: false,
            campaigns: [],
            selectedCampaign: null,
            newCampaign: null,
            campaign: {
                id: null,
                name: null,
                description: null,
                validation_type: null,
                validation_type_inputs: {},
                configuration: {},
            },
            campaignTypes: [],

        }
    },
    created() {
        this.listMarketingCampaigns();
        this.listMarketingCampaignTypes();
    },
    computed: {
        isNewCampaign() {
            return !!this.campaign.name;
        },
        validCampaign() {
            if (this.isNewCampaign) {
                return this.consumerCount > 0 && this.campaign.name && this.campaign.validation_type && this.campaign.type && this.isValidConfiguration();
            } else {
                return this.consumerCount > 0 && this.campaign.id !== null;
            }
        },
    },
    methods: {
        async listMarketingCampaigns() {
            this.loading = true;
            const response = await this.api.getMarketingCampaignOptions({
                statuses: [marketingCampaign.statuses.DRAFT, marketingCampaign.statuses.ACTIVE],
                type: [
                    marketingCampaign.types.INTERNAL_EMAIL,
                    marketingCampaign.types.MAILCHIMP_EMAIL,
                    marketingCampaign.types.SMS,
                ]
            });
            this.campaigns = response.data.data.data;
            this.loading = false;
        },
        async listMarketingCampaignTypes() {
            this.loading = true;
            const response = await this.api.getMarketingCampaignTypes({type: this.typeScope});
            this.campaignTypes = response.data.data.types;
            this.loading = false;
        },
        async handleSaveCampaign() {
            this.loading = true;

            const params = {
                campaign: this.campaign,
                filters: this.filters,
            }

            const response = this.isNewCampaign ? await this.api.createMarketingCampaign(params) : await this.api.updateMarketingCampaignTargets(params);

            if (response.data.data.status) {
                this.$emit('close', this.isNewCampaign ? 'Marketing Campaign Created' : 'Consumers Added to Marketing Campaign')
            }

            this.loading = false;
        },
        isValidConfiguration() {
            if (this.campaign.type === marketingCampaign.types.INTERNAL_EMAIL) {
                const requiredFields = [
                    'email_template_id',
                    'sender_domain',
                    'sender_local',
                    'sender_name',
                    'sent_at',
                ]

                return requiredFields.every(key => this.campaign.configuration[key] != null)
            } else {
                return true;
            }
        }
    },
    watch: {
        'campaign.type'(newValue) {
            this.campaign.configuration = this.marketingCampaign.typeConfigurationDefaults[newValue];
        }
    }
}
</script>
