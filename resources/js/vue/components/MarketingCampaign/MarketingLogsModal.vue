<template>
    <div>
        <modal
            :dark-mode="darkMode"
            no-buttons
            @close="$emit('close')"
        >
            <template #header>
                View Marketing Log #{{marketingLogId}}
            </template>
            <template #content>
                <div class="flex flex-col rounded-md  p-4" :class="[darkMode ? 'bg-dark-background' : 'bg-grey-50']">
                    <div class="flex justify-between">
                        <h4 class="text-xl font-medium mb-2" :class="[darkMode ? 'text-white' : '']">Summary</h4>
                    </div>
                    <div v-if="!loading" class="grid grid-cols-4 gap-3">
                        <div class="flex flex-col gap-1">
                            <div class="uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-500' : 'text-grey-600']">ID</div>
                            <div class="text-sm">{{ log?.id }}</div>
                        </div>
                        <div class="flex flex-col gap-1">
                            <div class="uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-500' : 'text-grey-600']">Level</div>
                            <div class="flex">
                                <badge
                                    :color="logLevel.styles[log.level].badgeColor"
                                    :dark-mode="darkMode"
                                >
                                    {{logLevel.titles[log.level]}}
                                </badge>
                            </div>
                        </div>
                        <div class="flex flex-col gap-1">
                            <div class="uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-500' : 'text-grey-600']">Namespace</div>
                            <div class="text-sm">{{ log?.namespace }}</div>
                        </div>
                        <div class="flex flex-col gap-1">
                            <div class="uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-500' : 'text-grey-600']">Created At</div>
                            <div class="text-sm">{{ log?.created_at }}</div>
                        </div>
                        <div class="flex flex-col gap-1 col-span-4">
                            <div class="uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-500' : 'text-grey-600']">Message</div>
                            <div class="text-sm">{{ log?.message }}</div>
                        </div>
                    </div>
                    <div v-else class="flex justify-center items-center h-24">
                        <loading-spinner :dark-mode="darkMode"/>
                    </div>
                </div>
                <div class="mt-4" v-if="log?.context">
                    <h4 class="text-xl font-medium mb-2" :class="[darkMode ? 'text-white' : '']">Context</h4>
                    <div class="col-span-3 bg-sky-100 rounded-md p-2">
                        <pre class=" whitespace-pre-wrap text-sm">{{log.context}}</pre>
                    </div>
                </div>
                <div class="mt-4" v-if="log?.stack_trace">
                    <h4 class="text-xl font-medium mb-2" :class="[darkMode ? 'text-white' : '']">Stack Trace</h4>
                    <div class="col-span-3 bg-sky-100 rounded-md p-2">
                        <pre class=" whitespace-pre-wrap text-sm">{{log.stack_trace}}</pre>
                    </div>
                </div>
            </template>
        </modal>
    </div>
</template>
<script>
import Modal from "../Shared/components/Modal.vue";
import ApiService from "./services/api.js";
import useLogLevel from "../../../composables/useLogLevel.js";
import Badge from "../Shared/components/Badge.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
const logLevel = useLogLevel()
export default {
    name: "MarketingLogsModal",
    components: {LoadingSpinner, Badge, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        marketingLogId: {
            type: Number,
            required: true,
        }
    },
    emits: ['close'],
    data() {
        return {
            loading: false,
            api: ApiService.make(),
            log: null,
            logLevel,
        }
    },
    created() {
        this.getMarketingLog();
    },
    methods: {
        async getMarketingLog() {
            this.loading = true;
            const response = await this.api.getMarketingLog(this.marketingLogId);
            this.log = response.data.data;
            this.loading = false;
        }
    }

}
</script>