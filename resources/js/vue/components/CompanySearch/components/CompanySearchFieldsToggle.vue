<template>
    <div class="relative">
        <div v-if="fieldsPopup" @click="toggleFieldsPopup" class="fixed inset-0 z-0"></div>
        <div class="relative z-10">
            <CustomButton @click="toggleFieldsPopup" :dark-mode="darkMode" color="slate-inverse">
                <svg class="fill-current mr-2" width="17" height="16" viewBox="0 0 17 16" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M2.66667 2C2.55481 2 2.40141 2.05193 2.25638 2.22597C2.10691 2.40533 2 2.6818 2 3V13C2 13.3182 2.10691 13.5947 2.25638 13.774C2.40141 13.9481 2.55481 14 2.66667 14H4.33333C4.44519 14 4.59859 13.9481 4.74362 13.774C4.89309 13.5947 5 13.3182 5 13V3C5 2.6818 4.89309 2.40533 4.74362 2.22597C4.59859 2.05193 4.44519 2 4.33333 2H2.66667ZM6 0.654839C5.54679 0.248166 4.96618 0 4.33333 0H2.66667C1.89447 0 1.20002 0.369497 0.719934 0.945602C0.244279 1.51639 0 2.25734 0 3V13C0 13.7427 0.24428 14.4836 0.719934 15.0544C1.20002 15.6305 1.89446 16 2.66667 16H4.33333C4.96618 16 5.54679 15.7518 6 15.3452C6.45321 15.7518 7.03382 16 7.66667 16H9.33333C9.96618 16 10.5468 15.7518 11 15.3452C11.4532 15.7518 12.0338 16 12.6667 16H14.3333C15.1055 16 15.8 15.6305 16.2801 15.0544C16.7557 14.4836 17 13.7427 17 13V3C17 2.25734 16.7557 1.51639 16.2801 0.945602C15.8 0.369497 15.1055 0 14.3333 0H12.6667C12.0338 0 11.4532 0.248166 11 0.654839C10.5468 0.248166 9.96618 0 9.33333 0H7.66667C7.03382 0 6.45321 0.248166 6 0.654839ZM10 3C10 2.6818 9.89309 2.40533 9.74362 2.22597C9.59859 2.05193 9.44519 2 9.33333 2H7.66667C7.55481 2 7.40141 2.05193 7.25638 2.22597C7.10691 2.40533 7 2.6818 7 3V13C7 13.3182 7.10691 13.5947 7.25638 13.774C7.40141 13.9481 7.55481 14 7.66667 14H9.33333C9.44519 14 9.59859 13.9481 9.74362 13.774C9.89309 13.5947 10 13.3182 10 13V3ZM12 13C12 13.3182 12.1069 13.5947 12.2564 13.774C12.4014 13.9481 12.5548 14 12.6667 14H14.3333C14.4452 14 14.5986 13.9481 14.7436 13.774C14.8931 13.5947 15 13.3182 15 13V3C15 2.6818 14.8931 2.40533 14.7436 2.22597C14.5986 2.05193 14.4452 2 14.3333 2H12.6667C12.5548 2 12.4014 2.05193 12.2564 2.22597C12.1069 2.40533 12 2.6818 12 3V13Z"/>
                </svg>
                Fields
            </CustomButton>
            <div v-if="fieldsPopup"
                 class="z-20 absolute rounded-lg border w-64 grid gap-2 p-3 top-10 left-0 shadow-module text-sm font-medium"
                 :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                <div>
                    <CustomButton color="slate" @click="toggleAllFieldsShown" :dark-mode="darkMode"
                                  v-if="!store.allFieldsExceptIdAreNotShown">
                        Deselect All
                    </CustomButton>
                    <CustomButton @click="toggleAllFieldsShown" :dark-mode="darkMode" v-else>Select All
                    </CustomButton>
                </div>
                <div v-for="(field, key) in store.fields" :key="field">
                    <div class="inline-flex items-center">
                        <input type="checkbox" @input="updateFieldShown(key, !field.shown)" :checked="field.shown"
                               class="mr-2 checked:bg-primary-500 rounded cursor-pointer focus:outline-none focus:ring-0">
                        {{ field.name }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useFieldsToggleStore } from '../services/service'

const store = useFieldsToggleStore()

const updateFieldShown = (key, shown) => {
    store.updateFieldShown(key, shown)
}

const toggleAllFieldsShown = () => {
    store.toggleAllFieldsShown()
}

</script>

<script>
import CustomButton from '../../Shared/components/CustomButton.vue'

export default {
    name: 'CompanySearchFieldsToggle',
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    components: { CustomButton },
    data () {
        return {
            fieldsPopup: false,
        }
    },
    methods: {
        toggleFieldsPopup () {
            this.fieldsPopup = !this.fieldsPopup
        },
    }
}
</script>

<style scoped>

</style>
