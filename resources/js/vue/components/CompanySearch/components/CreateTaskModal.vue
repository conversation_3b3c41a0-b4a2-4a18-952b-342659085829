<template>
    <modal
        v-show="show"
        :dark-mode="darkMode"
        :small="true"
        :confirm-text="misc.savingTask ? 'Saving...' : 'Create Task'"
        close-text="Cancel"
        @confirm="createTask"
        @close="close"
        :inputsDisabled="misc.inputsDisabled"
        :disableConfirm="misc.disableConfirm"
        :z-index-hundred="zIndexHundred"
    >
        <template v-slot:header>
            <h4 class="text-xl">Create Task</h4>
        </template>
        <template v-slot:content>
            <div class="mb-6" v-if="misc.alertMessage">
                <alert :text="misc.alertMessage" :alert-type="misc.alertType" :dark-mode="darkMode"></alert>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                <div class="col-span-2 task-box flex flex-wrap"
                     v-if="!createTasksForAllCompanies">
                    <FilterPill class="mr-2 mb-2" v-for="company in selectedCompanies"
                                @click="removeCompany(company.id)" :dark-mode="darkMode">
                        {{ company.name }}
                    </FilterPill>
                </div>
                <div v-else>
                    <h4 class="text-lg">Create tasks for {{ totalCompaniesCount }} companies</h4>
                </div>
                <div class="col-span-2">
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Task Note
                    </p>
                    <input
                        class="w-full border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                        placeholder="Task note"
                        v-model="form.taskNote"
                        :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Task Type
                    </p>
                    <dropdown :options="options.taskTypes" :dark-mode="darkMode" :placeholder="'Select Task Type'"
                              v-model="form.taskType"></dropdown>
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Task Category
                    </p>

                    <Dropdown v-model="form.taskCategory" :options="options.taskCategories" :dark-mode="darkMode"
                              placeholder="Select Category" :selected="form.taskCategory"></Dropdown>
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Priority
                    </p>
                    <Dropdown :dark-mode="darkMode" class="mr-2" v-model="form.priority"
                              :options="options.taskPriorities"
                              placeholder="Priority" :selected="form.priority" placement="top"></Dropdown>
                </div>
                <div>
                    <p class="uppercase font-semibold text-xs mb-2"
                       :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                        Available At
                    </p>
                    <button
                        class="transition duration-200 text-sm font-semibold focus:outline-none py-3 rounded-lg px-5"
                        @click="misc.showCalender = true"
                        :class="{'bg-grey-475 hover:bg-blue-800 text-white': !darkMode, 'bg-blue-400 hover:bg-blue-500 text-white': darkMode}">
                        Pick Date Time
                    </button>
                    {{ form.availableAt }}
                    <div v-if="misc.showCalender" class="mt-3">
                        <Datepicker :inline="true" @update:modelValue="setDateTime" :format="'yyyy-MM-dd HH:mm'"
                                    :start-time="misc.manualTaskDefaultTime" :dark="darkMode"></Datepicker>
                    </div>
                </div>
            </div>
        </template>
    </modal>
</template>

<script>
import TaskManagementApiService from '../../../components/TaskManagement/services/api'
import Dropdown from '../../Shared/components/Dropdown.vue'
import Modal from '../../Shared/components/Modal.vue'
import Alert from '../../Shared/components/Alert.vue'
import { useSelectionStore } from '../services/service'
import { mapWritableState } from 'pinia'
import FilterPill from '../../Shared/components/FilterPill.vue'
import ApiService from '../../../components/Tasks/services/api'

export default {
    name: 'CreateTaskModal',
    components: { FilterPill, Dropdown, Modal, Alert },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        zIndexHundred: {
            type: Boolean,
            default: false
        },
        show: {
            type: Boolean,
            default: false
        },
        companyData: {
            type: Object,
            default: () => {
                return {
                    all: false,
                    data: {
                        companies: []
                    }
                }
            }
        },
        payload: {
            type: Object,
            default: {}
        }
    },
    data () {
        return {
            apiService: ApiService.make(),
            taskManagementApi: TaskManagementApiService.make(),
            form: {
                availableAt: null,
                taskType: null,
                taskNote: null,
                taskCategory: null,
                priority: null,
            },
            options: {
                taskCategories: [],
                taskTypes: [],
                taskPriorities: [
                    { id: 1, name: 'Low' },
                    { id: 2, name: 'Medium' },
                    { id: 3, name: 'High' },
                    { id: 4, name: 'Urgent' }
                ],
            },
            misc: {
                showCalender: false,
                alertMessage: null,
                savingTask: false,
                alertType: 'success',
                manualTaskDefaultTime: { hours: 8, minutes: 0, seconds: 0 },
                inputsDisabled: false,
                disableConfirm: false,
            },
        }
    },
    created () {
        this.getTaskTypes()
        this.getTaskCategories()
    },
    computed: {
        ...mapWritableState(useSelectionStore, {
            selectedCompanies: 'selection',
        }),
        totalCompaniesCount () {
            return this.companyData?.paginationData?.total ?? 0
        },
        createTasksForAllCompanies () {
            return this.companyData.all
        },
        shouldExit () {
            const notCreatingTasksForAllCompanies = !this.createTasksForAllCompanies

            const selectionListIsEmpty = this.selectedCompanies.length === 0

            return notCreatingTasksForAllCompanies && selectionListIsEmpty
        },
    },
    methods: {
        createTask () {
            if (this.misc.savingTask) return

            this.misc.alertMessage = null
            this.misc.savingTask = true

            this.misc.inputsDisabled = true
            this.misc.disableConfirm = true

            let companyData = this.companyData

            if (this.createTasksForAllCompanies) {
                companyData.data.companies = [1]
            }

            this.apiService.createTasks({
                company_data: companyData,
                task_type_id: this.form.taskType,
                subject: this.form.taskNote,
                available_at: this.form.availableAt,
                priority: this.form.priority,
                task_category_id: this.form.taskCategory,
                ... this.payload
            }).then(() => {
                this.misc.alertType = 'success'
                this.misc.alertMessage = 'Tasks was created successfully'
                setTimeout(() => {
                    this.close()
                    this.misc.alertMessage = null
                    this.reset()
                    this.selectedCompanies = []
                }, 1000)
            }).catch(e => {
                this.misc.alertType = 'error'
                this.misc.alertMessage = e.response?.data?.message
            }).finally(() => {
                this.misc.savingTask = false

                this.misc.inputsDisabled = false
                this.misc.disableConfirm = false
            })
        },
        reset () {
            this.form = {
                availableAt: null,
                taskType: null,
                taskNote: null,
                taskCategory: null,
                priority: null,
            }
        },
        close () {
            this.$emit('update:show', false)
        },
        setDateTime (date) {
            this.form.availableAt = this.$filters.dateFromTimestamp(date, 'YYYY-MM-DD HH:mm')
            this.misc.showCalender = false
        },
        getTaskTypes () {
            this.taskManagementApi.getTaskTypes().then(resp => this.options.taskTypes = resp?.data?.data?.task_types)
        },
        getTaskCategories () {
            this.taskManagementApi.getTaskCategories().then(resp => this.options.taskCategories = resp?.data?.data?.categories)
        },
        removeCompany (company_id) {
            this.selectedCompanies.splice(this.selectedCompanies.findIndex(obj => obj.id === company_id), 1)
        },
    },
    watch: {
        shouldExit (to) {
            if (to) {
                this.$emit('update:show', false)
            }
        }
    }
}
</script>

<style scoped>
.task-box {
    border: 2px solid #ddd;
    max-height: 150px;
    overflow: auto;
    padding: 10px;
    border-radius: 5px;
}
</style>
