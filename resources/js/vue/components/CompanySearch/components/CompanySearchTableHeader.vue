<template>
    <thead class="text-xs text-gray-700 uppercase border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
        <tr class="pb-3 mb-4">
            <th class="p-5 table-header-text uppercase text-center">
                <input type="checkbox" class="form-checkbox rounded text-primary-500"
                       :checked="useSelectionStore().allRowsAreSelectedForPage"
                       @click="useSelectionStore().selectAll"
                />
                <div class="mt-5" v-if="useSelectionStore().selection?.length > 0">
                    {{ useSelectionStore().selection?.length }} selected
                </div>
            </th>
            <SortColumnHeader key="id" v-show="fields.id.shown" @sort="updateSortedColumns('id', $event)"
                              :precedence="getSortColumnPrecedence('id')">Id
            </SortColumnHeader>
            <SortColumnHeader key="company-name" v-show="fields.companyName.shown"
                              @sort="updateSortedColumns('name', $event)" :precedence="getSortColumnPrecedence('name')">
                Company Name
            </SortColumnHeader>
            <SortColumnHeader key="status" v-show="fields.status.shown" @sort="updateSortedColumns('status', $event)"
                              :precedence="getSortColumnPrecedence('status')">Status
            </SortColumnHeader>
            <SortColumnHeader key="sales-status" v-show="fields.salesStatus.shown"
                              @sort="updateSortedColumns('sales-status', $event)"
                              :precedence="getSortColumnPrecedence('sales-status')">Sales Status
            </SortColumnHeader>
            <SortColumnHeader key="industries" v-show="fields.industries.shown"
                              @sort="updateSortedColumns('industries', $event)"
                              :precedence="getSortColumnPrecedence('industries')"
            >
                Industries
            </SortColumnHeader>
            <SortColumnHeader key="services" v-show="fields.services.shown"
                              @sort="updateSortedColumns('services', $event)"
                              :precedence="getSortColumnPrecedence('services')"
            >
                Services
            </SortColumnHeader>
            <SortColumnHeader key="addresses" disable-sorting v-show="fields.addresses.shown">Addresses</SortColumnHeader>
            <SortColumnHeader key="state" v-show="fields.state.shown"
                              @sort="updateSortedColumns('state', $event)"
                              :precedence="getSortColumnPrecedence('state')"
            >
                State
            </SortColumnHeader>
            <SortColumnHeader key="campaigns" v-show="fields.campaigns.shown"
                              @sort="updateSortedColumns('campaigns', $event)"
                              :precedence="getSortColumnPrecedence('campaigns')"
            >
                Campaigns
            </SortColumnHeader>
            <SortColumnHeader key="lead-purchased-1" @sort="updateSortedColumns('lead-purchased-1', $event)"
                              :precedence="getSortColumnPrecedence('lead-purchased-1')" v-show="fields.leadsPurchased.shown">
                {{ leadsPurchasedColumnOne }}
            </SortColumnHeader>
            <SortColumnHeader key="leads-purchased-2" @sort="updateSortedColumns('lead-purchased-2', $event)"
                              :precedence="getSortColumnPrecedence('lead-purchased-2')"
                              v-show="fields.leadsPurchased.shown && leadsPurchasedColumnTwo">
                {{ leadsPurchasedColumnTwo }}
            </SortColumnHeader>
            <SortColumnHeader key="last-time-lead-purchased" v-show="fields.lastTimeLeadsPurchased.shown"
                              @sort="updateSortedColumns('last-time-lead-purchased', $event)"
                              :precedence="getSortColumnPrecedence('last-time-lead-purchased')">
                Last Time Lead Purchased
            </SortColumnHeader>
            <SortColumnHeader key="manual-lead-rejection-%" v-show="fields.leadRejection.shown"
                              @sort="updateSortedColumns('manual-lead-rejection-percentage', $event)"
                              :precedence="getSortColumnPrecedence('manual-lead-rejection-percentage')"

            >
                Manual Lead Rejection %
            </SortColumnHeader>
            <SortColumnHeader key="crm-lead-rejection-%" v-show="fields.leadRejection.shown"
                              @sort="updateSortedColumns('crm-lead-rejection-percentage', $event)"
                              :precedence="getSortColumnPrecedence('crm-lead-rejection-percentage')"

            >
                CRM Lead Rejection %
            </SortColumnHeader>
            <SortColumnHeader key="overall-lead-rejection-%" v-show="fields.leadRejection.shown"
                              @sort="updateSortedColumns('overall-lead-rejection-percentage', $event)"
                              :precedence="getSortColumnPrecedence('overall-lead-rejection-percentage')"

            >
                Overall Lead Rejection %
            </SortColumnHeader>
            <SortColumnHeader key="last-time-contacted" @sort="updateSortedColumns('last-time-contacted', $event)"
                              :precedence="getSortColumnPrecedence('last-time-contacted')"
                              v-show="fields.lastTimeContacted.shown">Last Time Contacted</SortColumnHeader>
            <SortColumnHeader key="last-time-called" disable-sorting
                              v-show="fields.lastTimeCalled.shown">Last Time Called</SortColumnHeader>
            <SortColumnHeader key="google-rating" v-show="fields.googleRating.shown"
                              @sort="updateSortedColumns('google-rating', $event)"
                              :precedence="getSortColumnPrecedence('google-rating')">
                {{ fields.googleRating.name }}
            </SortColumnHeader>
            <SortColumnHeader key="google-review-count" v-show="fields.googleRating.shown"
                              @sort="updateSortedColumns('google-review-count', $event)"
                              :precedence="getSortColumnPrecedence('google-review-count')">
                {{ fields.googleReviewCount.name }}
            </SortColumnHeader>
            <SortColumnHeader key="estimated-revenue" v-show="fields.estimatedRevenue.shown"
                              @sort="updateSortedColumns('estimated-revenue', $event)"
                              :precedence="getSortColumnPrecedence('estimated-revenue')">
                {{ fields.estimatedRevenue.name }}
            </SortColumnHeader>
            <SortColumnHeader key="estimated-monthly-ad-spend" v-show="fields.estimatedMonthlyAdSpend.shown"
                              @sort="updateSortedColumns('estimated-monthly-ad-spend', $event)"
                              :precedence="getSortColumnPrecedence('estimated-monthly-ad-spend')">
                {{ fields.estimatedMonthlyAdSpend.name }}
            </SortColumnHeader>
            <SortColumnHeader key="company-cadence-name" v-show="fields.cadenceName.shown"
                              @sort="updateSortedColumns('company-cadence-name', $event)"
                              :precedence="getSortColumnPrecedence('company-cadence-name')">
                {{ fields.cadenceName.name }}
            </SortColumnHeader>
            <SortColumnHeader key="lifetime-revenue" v-show="fields.lifetimeRevenue.shown"
                              @sort="updateSortedColumns('lifetime-revenue', $event)"
                              :precedence="getSortColumnPrecedence('lifetime-revenue')">
                {{ fields.lifetimeRevenue.name }}
            </SortColumnHeader>
            <SortColumnHeader key="other" disable-sorting v-show="fields.other.shown">Other</SortColumnHeader>
            <SortColumnHeader key="actions" disable-sorting v-show="fields.actions.shown">Actions</SortColumnHeader>
            <SortColumnHeader key="bdm" disable-sorting v-show="fields.bdm.shown">BDM</SortColumnHeader>
            <SortColumnHeader key="am" disable-sorting v-show="fields.am.shown">AM</SortColumnHeader>
            <SortColumnHeader key="om" disable-sorting v-show="fields.om.shown">OM</SortColumnHeader>
        </tr>
    </thead>
</template>

<script>
import { mapState } from 'pinia'
import { useFieldsToggleStore, useSelectionStore, useSortingStore } from '../services/service'
import { DateTime } from 'luxon'
import SortColumnHeader from '../shared/SortColumnHeader.vue'

const updateSortedColumns = (field, direction) => {
    const store = useSortingStore()

    store.updateSortedColumns(field, direction)
}

const getSortColumnPrecedence = (field) => {
    const store = useSortingStore()

    if (store.fields?.length <= 1) {
        return null
    }

    return store.getSortColumnPrecedence(field)
}

export default {
    name: 'CompanySearchTableHeader',
    components: { SortColumnHeader },
    methods: { updateSortedColumns, useSelectionStore, getSortColumnPrecedence, },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        data: {
            type: Array,
            default: []
        },
        showComments: {
            type: Boolean,
            default: false
        },
        leadsPurchasedFilter: {
            type: Object,
            default: {
                first_from_date: null,
                first_to_date: null,
                second_from_date: null,
                second_to_date: null,
                first_timeframe: null,
                first_timeframe_active: false,
                second_timeframe: null,
                second_timeframe_active: false,
                logical: null,
                first_input: null,
                second_input: null,
                first_operator: null,
                second_operator: null,
            }
        },
        currentPage: {
            type: Number,
            default: 1
        }
    },
    computed: {
        parentClass () {
            let result = 'px-5 grid gap-5 pb-3'

            if (this.data.length > 5) {
                result += ' mr-3'
            }

            if (this.darkMode) {
                result += ' text-slate-400'
            } else {
                result += ' text-slate-500'
            }

            result += this.totalColumnsEnabled > 12
                ? ` grid-cols-${this.totalColumnsEnabled}`
                : ' grid-cols-12';

            return result
        },
        ...mapState(useFieldsToggleStore, {
            fields: state => state.fieldsGetter,
        }),
        leadsPurchasedColumnOne () {
            let result = 'Leads Purchased Last 30 Days'

            if (!isNaN(this.leadsPurchasedFilter?.first_input) && ['greaterThan', 'equalTo', 'lessThan'].includes(this.leadsPurchasedFilter?.first_operator)) {
                result = 'Lead Cost'

                if (this.leadsPurchasedColumnTwo) {
                    result = 'Lead Cost 1'
                }

                if (this.leadsPurchasedFilter?.first_timeframe_active && this.leadsPurchasedFilter?.first_from_date instanceof DateTime && this.leadsPurchasedFilter?.first_to_date instanceof DateTime) {
                    const fromDate = this.leadsPurchasedFilter?.first_from_date?.toLocaleString(DateTime.DATE_SHORT)

                    const toDate = this.leadsPurchasedFilter?.first_to_date?.toLocaleString(DateTime.DATE_SHORT)

                    const dateRange = `${fromDate} - ${toDate}`

                    result = `Lead Cost (${dateRange})`

                    if (this.leadsPurchasedColumnTwo) {
                        result = `Lead Cost 1 (${dateRange})`
                    }

                }
            }

            return result
        },
        leadsPurchasedColumnTwo () {
            let result = null

            const firstTimeframeUsed = this.leadsPurchasedFilter?.first_timeframe_active && this.leadsPurchasedFilter?.first_from_date instanceof DateTime && this.leadsPurchasedFilter?.first_to_date instanceof DateTime

            const secondTimeframeUsed = this.leadsPurchasedFilter?.second_timeframe_active && this.leadsPurchasedFilter?.second_from_date instanceof DateTime && this.leadsPurchasedFilter?.second_to_date instanceof DateTime

            if (['and', 'or'].includes(this.leadsPurchasedFilter?.logical) && !isNaN(this.leadsPurchasedFilter?.second_input) && ['greaterThan', 'equalTo', 'lessThan'].includes(this.leadsPurchasedFilter?.second_operator) && firstTimeframeUsed && secondTimeframeUsed) {
                result = 'Lead Cost 2'

                const fromDate = this.leadsPurchasedFilter?.second_from_date?.toLocaleString(DateTime.DATE_SHORT)

                const toDate = this.leadsPurchasedFilter?.second_to_date?.toLocaleString(DateTime.DATE_SHORT)

                const dateRange = `${fromDate} - ${toDate}`

                result = `Lead Cost 2 (${dateRange})`
            }

            return result
        },
        totalColumnsEnabled() {
            return Object.values(this.fields).reduce((total, field) => field.shown ? total + 1 : total, 0) + 1;
        },
    }
}
</script>

<style scoped>
.table-header-text {
    @apply uppercase text-xs font-bold rounded;
}
</style>
