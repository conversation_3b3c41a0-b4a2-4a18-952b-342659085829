<template>
        <Modal :no-buttons="!selectedCompaniesHaveBeenConfirmed" :dark-mode="darkMode" :small="true" @confirm="" @close="closeModal">
            <template v-slot:header>
                <p class="font-semibold">Company Cadence Routines</p>
            </template>
            <template v-slot:content>
                <div>
                    <div v-if="(assignedSuccessful === false && terminateSuccessful === false)" class="grid grid-cols-1 gap-8">
                        <div>
                            <p class="mb-2 uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Which Companies</p>
                            <Dropdown
                                v-model="affectedCompanies"
                                :dark-mode="darkMode"
                                :options="affectedCompanyOptions"
                            ></Dropdown>
                        </div>
                        <div>
                            <p class="mb-2 uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Action</p>
                            <Dropdown
                                v-model="action"
                                :dark-mode="darkMode"
                                :options="actionOptions"
                            ></Dropdown>
                        </div>
                        <div v-if="action === 'assign'">

                            <p class="mb-2 uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Filter Routines</p>

                            <div class="flex gap-3 mb-3">
                                <custom-input class="flex-grow" :dark-mode="darkMode" v-model="filter" placeholder="Routine Name Or Author"></custom-input>
                                <div class="inline-flex items-center justify-start w-40 gap-2">
                                    <toggle-switch @click="" v-model="shared" :dark-mode="darkMode"></toggle-switch>
                                    <svg v-if="shared" class="fill-current text-slate-500" width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 3C12 3.79565 11.6839 4.55871 11.1213 5.12132C10.5587 5.68393 9.79565 6 9 6C8.20435 6 7.44129 5.68393 6.87868 5.12132C6.31607 4.55871 6 3.79565 6 3C6 2.20435 6.31607 1.44129 6.87868 0.87868C7.44129 0.316071 8.20435 0 9 0C9.79565 0 10.5587 0.316071 11.1213 0.87868C11.6839 1.44129 12 2.20435 12 3ZM17 5C17 5.53043 16.7893 6.03914 16.4142 6.41421C16.0391 6.78929 15.5304 7 15 7C14.4696 7 13.9609 6.78929 13.5858 6.41421C13.2107 6.03914 13 5.53043 13 5C13 4.46957 13.2107 3.96086 13.5858 3.58579C13.9609 3.21071 14.4696 3 15 3C15.5304 3 16.0391 3.21071 16.4142 3.58579C16.7893 3.96086 17 4.46957 17 5ZM13 12C13 10.9391 12.5786 9.92172 11.8284 9.17157C11.0783 8.42143 10.0609 8 9 8C7.93913 8 6.92172 8.42143 6.17157 9.17157C5.42143 9.92172 5 10.9391 5 12V15H13V12ZM5 5C5 5.53043 4.78929 6.03914 4.41421 6.41421C4.03914 6.78929 3.53043 7 3 7C2.46957 7 1.96086 6.78929 1.58579 6.41421C1.21071 6.03914 1 5.53043 1 5C1 4.46957 1.21071 3.96086 1.58579 3.58579C1.96086 3.21071 2.46957 3 3 3C3.53043 3 4.03914 3.21071 4.41421 3.58579C4.78929 3.96086 5 4.46957 5 5ZM15 15V12C15.0014 10.9833 14.7433 9.98303 14.25 9.094C14.6933 8.98054 15.1568 8.96984 15.6049 9.06272C16.053 9.1556 16.474 9.34959 16.8357 9.62991C17.1974 9.91023 17.4903 10.2695 17.6921 10.6802C17.8939 11.091 17.9992 11.5424 18 12V15H15ZM3.75 9.094C3.25675 9.98305 2.9986 10.9833 3 12V15H2.6572e-07V12C-0.000192468 11.542 0.104463 11.0901 0.305947 10.6789C0.507431 10.2676 0.800394 9.90793 1.16238 9.62742C1.52437 9.3469 1.94578 9.15298 2.39431 9.06052C2.84284 8.96806 3.30658 8.97951 3.75 9.094Z"/>
                                    </svg>
                                    <svg v-else width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.375 3.375C3.375 5.23575 4.88925 6.75 6.75 6.75C8.61075 6.75 10.125 5.23575 10.125 3.375C10.125 1.51425 8.61075 0 6.75 0C4.88925 0 3.375 1.51425 3.375 3.375ZM12.75 14.25H13.5V13.5C13.5 10.6058 11.1442 8.25 8.25 8.25H5.25C2.355 8.25 0 10.6058 0 13.5V14.25H12.75Z" fill="#64748B"/>
                                    </svg>

                                    <p class="text-sm font-semibold text-slate-500">{{shared ? 'All' : 'Mine Only'}}</p>
                                </div>
                            </div>

                            <br/>
                            <p class="mb-2 uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Select Routine</p>
                            <Dropdown
                                v-model="selectedCadenceRoutineId"
                                :dark-mode="darkMode"
                                :options="filteredRoutines"
                                placeholder="Select Routine"
                            ></Dropdown>
                            <span class="text-xs">{{filteredRoutines.length}} results</span>

                            <div class="mt-6 mb-2">
                                <span class="flex">
                                    <p class="uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Use Account Manager</p>
                                    <tooltip>
                                        If checked, email and sms will be sent from the company's <b>account manager instead of you</b>.<br/>
                                        This will also affect which user tasks are generated for.
                                    </tooltip>
                                </span>
                                <custom-checkbox v-model="useAccountManager"></custom-checkbox>
                            </div>


                        </div>
                    </div>
                    <div class="grid">
                        <button v-if="(assignedSuccessful === false && terminateSuccessful === false) && loadingConfirm === false" class="transition duration-200 text-white font-medium focus:outline-none py-2 rounded-md px-5 w-full justify-center items-center inline-flex mt-8"
                                :class="{'bg-primary-500 hover:bg-blue-500': confirmable, 'bg-grey-200 text-grey-500': !confirmable, 'cursor-not-allowed': !confirmable}"
                                @click="confirmable ? confirm() : null">
                            Confirm
                        </button>
                        <div v-if="loadingConfirm" class="border border-primary-500 py-2 rounded-md px-5 w-full inline-flex items-center justify-center mt-8">
                            <loading-spinner size="w-4 h-4"></loading-spinner>
                        </div>
                    </div>


                    <div v-if="assignedSuccessful">
                        <p class="font-medium inline-flex items-center capitalize">
                            <svg v-if="results.routines_initialized > 0" class="w-4 mr-2" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 16C10.1217 16 12.1566 15.1571 13.6569 13.6569C15.1571 12.1566 16 10.1217 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 0 5.87827 0 8C0 10.1217 0.842855 12.1566 2.34315 13.6569C3.84344 15.1571 5.87827 16 8 16ZM11.707 6.707C11.8892 6.5184 11.99 6.2658 11.9877 6.0036C11.9854 5.7414 11.8802 5.49059 11.6948 5.30518C11.5094 5.11977 11.2586 5.0146 10.9964 5.01233C10.7342 5.01005 10.4816 5.11084 10.293 5.293L7 8.586L5.707 7.293C5.5184 7.11084 5.2658 7.01005 5.0036 7.01233C4.7414 7.0146 4.49059 7.11977 4.30518 7.30518C4.11977 7.49059 4.0146 7.7414 4.01233 8.0036C4.01005 8.2658 4.11084 8.5184 4.293 8.707L6.293 10.707C6.48053 10.8945 6.73484 10.9998 7 10.9998C7.26516 10.9998 7.51947 10.8945 7.707 10.707L11.707 6.707Z" fill="#00ADA0"/>
                            </svg>
                            {{results.routines_initialized}} routines assigned
                        </p>
                        <div v-if="results.companies_with_ongoing_routines.length > 0" class="border rounded-md p-5 h-48 overflow-y-auto  my-3" :class="[darkMode ? 'bg-dark-background border-rose-800 text-white' : 'border-rose-300 bg-rose-100']">
                            <ul class="list-disc">
                                <b class="capitalize">Companies with ongoing routines</b>
                                <li class="pl-2 ml-4" v-for="company in results.companies_with_ongoing_routines">{{ company.name + '(' + company.id + ')' }}</li>
                            </ul>
                        </div>
                        <div v-if="results.errors.length > 0" class="border rounded-md p-5 h-48 overflow-y-auto  my-3" :class="[darkMode ? 'bg-dark-background border-rose-800 text-white' : 'border-rose-300 bg-rose-100']">
                            <ul class="list-disc">
                                <li class="pl-2 ml-4" v-for="message in results.errors">{{message}}</li>
                            </ul>
                        </div>
                    </div>

                    <div v-if="terminateSuccessful">
                        <p class="font-medium inline-flex items-center">
                            <svg class="w-4 mr-2" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 16C10.1217 16 12.1566 15.1571 13.6569 13.6569C15.1571 12.1566 16 10.1217 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 0 5.87827 0 8C0 10.1217 0.842855 12.1566 2.34315 13.6569C3.84344 15.1571 5.87827 16 8 16ZM11.707 6.707C11.8892 6.5184 11.99 6.2658 11.9877 6.0036C11.9854 5.7414 11.8802 5.49059 11.6948 5.30518C11.5094 5.11977 11.2586 5.0146 10.9964 5.01233C10.7342 5.01005 10.4816 5.11084 10.293 5.293L7 8.586L5.707 7.293C5.5184 7.11084 5.2658 7.01005 5.0036 7.01233C4.7414 7.0146 4.49059 7.11977 4.30518 7.30518C4.11977 7.49059 4.0146 7.7414 4.01233 8.0036C4.01005 8.2658 4.11084 8.5184 4.293 8.707L6.293 10.707C6.48053 10.8945 6.73484 10.9998 7 10.9998C7.26516 10.9998 7.51947 10.8945 7.707 10.707L11.707 6.707Z" fill="#00ADA0"/>
                            </svg>
                            {{results.terminated_count}} routines terminated
                        </p>
                        <div v-if="results.errors.length > 0" class="border rounded-md p-5 h-48 overflow-y-auto  my-3" :class="[darkMode ? 'bg-dark-background border-rose-800 text-white' : 'border-rose-300 bg-rose-100']">
                            <ul class="list-disc">
                                <li class="pl-2 ml-4" v-for="message in results.errors">{{message}}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>

</template>

<script>
import CustomInput from '../../Shared/components/CustomInput.vue'
import ToggleSwitch from '../../Shared/components/ToggleSwitch.vue'
import LoadingSpinner from '../../Shared/components/LoadingSpinner.vue'
import AlertsContainer from '../../Shared/components/AlertsContainer.vue'
import Dropdown from '../../Shared/components/Dropdown.vue'
import Modal from '../../Shared/components/Modal.vue'
import ApiService from "../../OutreachCadence/services/api";
import CustomCheckbox from "../../Shared/SlideWizard/components/CustomCheckbox.vue";
import Tooltip from "../../Shared/components/Tooltip.vue";

export default {
    name: "AssignCadenceModal",
    components: {Tooltip, CustomCheckbox, CustomInput, ToggleSwitch, LoadingSpinner, AlertsContainer, Dropdown, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        selectedCompanyIds: {
            type: Array,
            default: []
        },
        companySearchCriteria: {
            type: Object,
            default: []
        },
        totalCompanyCount: {
            type: Number,
            default: 0,
        },
        currentUserId: {
            type: [Number, String],
            required: false
        },
    },
    mounted() {
        this.updateAffectedCompanyOptions();
        this.getRoutines();
    },
    updated() {
        this.updateAffectedCompanyOptions();
        this.getRoutines();
    },
    data() {
        return {
            apiService: ApiService.make(),
            affectedCompanyOptions: [{'id': 'selected', 'name': 'Only Selected Companies'},{'id': 'all', 'name': 'All Companies'}],
            affectedCompanies: 'all',
            actionOptions: [{'id': 'assign', 'name': 'Assign Cadence Routine'},{'id': 'terminate', 'name': 'Terminate Existing Routines'}],
            action: 'assign',
            selectedCompaniesHaveBeenConfirmed: false,
            cadenceRoutines: [],
            selectedCadenceRoutineId: null,
            loadingConfirm: false,
            assignedSuccessful: false,
            terminateSuccessful: false,
            results: {},
            shared: true,
            filter: '',
            useAccountManager: false,
        }
    },
    computed: {
        confirmable() {
            return this.action === 'terminate' || this.selectedCadenceRoutineId !== null;
        },
        filteredRoutines() {
            let routines = this.cadenceRoutines
            if(!this.shared)
                routines = routines.filter(routine => routine.user_id == this.currentUserId)
            if(this.filter.length > 0)
                routines = routines.filter(routine => {
                    return routine.name.toLowerCase().indexOf(this.filter.toLowerCase()) >= 0 ||
                        routine.user.name.toLowerCase().indexOf(this.filter.toLowerCase()) >= 0
                })
            if(this.selectedCadenceRoutineId && !routines.find(routine => routine.id === this.selectedCadenceRoutineId))
                routines.unshift(this.cadenceRoutines.find(routine => routine.id === this.selectedCadenceRoutineId))
            return routines
        }
    },
    methods: {
        confirm() {
            switch (this.action){
                case 'assign':
                    this.loadingConfirm = true
                    this.apiService.assignCompanyRoutines(
                        this.selectedCompanyIds,
                        this.companySearchCriteria,
                        this.affectedCompanies === 'all',
                        this.selectedCadenceRoutineId,
                        true,
                        this.useAccountManager,
                    ).then((resp) => {
                        this.results = resp.data.data
                        this.assignedSuccessful = true
                    }).finally(() => {
                        this.loadingConfirm = false
                });
                    break;
                case 'terminate':
                    this.loadingConfirm = true
                    this.apiService.terminateCompanyRoutines(
                        this.selectedCompanyIds,
                        this.companySearchCriteria,
                        this.affectedCompanies === 'all',
                        true
                    ).then((resp) => {
                        this.results = resp.data.data
                        this.terminateSuccessful = true
                    }).finally(() => {
                        this.loadingConfirm = false
                    });
                    break;
            }
        },
        updateAffectedCompanyOptions() {
            const selectedCount = this.selectedCompanyIds.length;
            this.affectedCompanyOptions = [{'id': 'all', 'name': 'All (' + this.totalCompanyCount + ') Companies'}];
            if(selectedCount > 0){
                this.affectedCompanyOptions.push({'id': 'selected', 'name': 'Only Selected ('+selectedCount+') Companies'});
                this.affectedCompanies = 'selected';
            }
        },
        getRoutines() {
            this.apiService.getAllAvailableRoutineConfigs().then((data) => {
                this.cadenceRoutines = data.data.data;
            })
        },
        toggleModal() {
            this.assignedSuccessful = false
            this.terminateSuccessful = false
            this.assignedIds = []
            this.$emit('toggleAssignCadenceModal')
        },
        closeModal() {
            this.$emit('toggleAssignCadenceModal')
            this.assignedSuccessful = false
            this.terminateSuccessful = false
        }
    },
}
</script>

<style scoped>

</style>
