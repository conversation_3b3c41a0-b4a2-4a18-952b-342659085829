import axios from 'axios';
import {BaseApiService} from "./base";

export class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getFilterOptions() {
        return this.axios().get('/filter-options');
    }

    getFilterOptionUpdates(filterInputs) {
        return this.axios().post('/filter-option-updates', filterInputs);
    }

    search(searchInputs) {
        return this.axios().post('/search', searchInputs, {
            timeout: 60000
        });
    }

    saveUserPreset(filterPayload) {
        return this.axios().put('/save-preset', filterPayload);
    }

    deleteUserPreset(filterName) {
        return this.axios().delete('/delete-preset', {
            params: {
                name: filterName
            }
        });
    }

}
