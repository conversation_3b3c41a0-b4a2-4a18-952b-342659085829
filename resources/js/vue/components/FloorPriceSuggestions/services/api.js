import axios from 'axios';

class ApiService {
    constructor(baseUrl, baseEndpoint, version) {

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    static make() {
        return new ApiService('internal-api', 'floor-price-suggestions', 1);
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

   getInitialData () {
       return this.axios().get(`/initial-data`);
   }

    getPriceSuggestions(stateKey, industryId, industryServiceId) {
        return this.axios().get(`/industries/${industryId}/services/${industryServiceId}/states/${stateKey}`);
    }

    applySuggestedPrices(product, industryId, serviceId, propertyType, qualityTier, pricePolicy, prices) {
        return this.axios().post(`/industries/${industryId}/services/${serviceId}/apply-suggested-prices`, {
            product: product,
            property_type: propertyType,
            quality_tier: qualityTier,
            price_policy: pricePolicy,
            prices: prices
        });
    }

    getPriceMargin(industryId, industryServiceId) {
        return this.axios().get(`/industries/${industryId}/services/${industryServiceId}/price-margin`);
    }

    updatePriceMargin(priceMarginId, data) {
        return this.axios().patch(`/price-margin/${priceMarginId}`, data);
    }

    getStatistics(industryId, industryServiceId, stateKey) {
        return this.axios().get(`/industries/${industryId}/services/${industryServiceId}/states/${stateKey}/statistics`);
    }
}

export { ApiService };
