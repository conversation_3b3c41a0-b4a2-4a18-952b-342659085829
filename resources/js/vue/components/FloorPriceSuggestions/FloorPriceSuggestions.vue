<template>
    <div>
        <div class="main-layout font-body">
            <div class="w-full">
                <div class="w-full flex-auto pt-2 relative">
                    <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                        <div class="flex items-center justify-between py-4">
                            <div class="flex justify-between items-center py-2">
                                <h3 class="text-xl font-medium pb-0 leading-none mr-5">Floor Price Suggestions</h3>
                            </div>
                        </div>
                        <loading-spinner v-if="loading"></loading-spinner>
                        <price-suggestions :api="api" :configuration="configuration" :dark-mode="darkMode" v-else></price-suggestions>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {ApiService} from "./services/api.js";
import PriceSuggestions from "./components/PriceSuggestions.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";

export default {
    name: "FloorPriceSuggestions",
    components: {LoadingSpinner, PriceSuggestions},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            api: ApiService.make(),
            configuration: {},
            loading: false
        }
    },
    async created() {
        this.loading = true;

        await this.api.getInitialData().then(resp => this.configuration = resp.data.data.configuration);

        this.loading = false;
    }
}
</script>
