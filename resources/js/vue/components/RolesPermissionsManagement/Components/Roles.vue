<template>
    <div class="grid lg:grid-cols-4 gap-4">
        <div class="lg:col-span-4 rounded-lg bg-red-100 p-4 mb-3" v-if="error">
            <p class="text-sm font-medium text-red-800">{{ error }}</p>
        </div>
        <div class="lg:col-span-4 border rounded-lg"
             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
            <div class="p-5 flex items-center justify-between">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Roles</h5>
                <div class="" v-if="canEdit">
                    <button @click="addRole"
                        class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5">
                        Add New Role
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-8 gap-3 mb-2 px-5">
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs">ID</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Name</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-4">Permissions</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs" v-if="canEdit">Edit</p>
            </div>
            <div class="flex items-center justify-center h-80 md:h-120" v-if="saving">
                <loading-spinner></loading-spinner>
            </div>
            <div class="border-t border-b h-80 md:h-120 overflow-y-auto" v-else
                 :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div>
                    <div class="grid grid-cols-8 gap-x-3 border-b px-5 py-3"
                         v-for="role in roles" :key="role.id"
                         :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                        <p class="text-sm">
                            {{ role.id }}
                        </p>
                        <p class="text-sm col-span-2 truncate">
                            {{ role.name }}
                        </p>
                        <p class="text-sm col-span-4">
                            {{ role.permissions.sort((p1, p2) => p1.name.localeCompare(p2.name)).map(permission => permission.name).join(', ') }}
                        </p>
                        <p class="ml-2">
                            <button @click="editRole(role)" v-if="canEdit">
                                <svg width="12" height="12" viewBox="0 0 11 11" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M3.37773 10.2347L8.87785 4.7346L6.2654 2.12215L0.765271 7.62227C0.689551 7.69809 0.635763 7.79298 0.609613 7.89689L0 11L3.10251 10.3904C3.20668 10.3643 3.30197 10.3105 3.37773 10.2347V10.2347ZM10.6534 2.95903C10.8753 2.73705 11 2.43602 11 2.12215C11 1.80827 10.8753 1.50724 10.6534 1.28526L9.71474 0.346575C9.49276 0.124663 9.19173 0 8.87785 0C8.56397 0 8.26295 0.124663 8.04097 0.346575L7.10228 1.28526L9.71474 3.89772L10.6534 2.95903Z"
                                        fill="#0081FF"/>
                                </svg>
                            </button>
                        </p>
                    </div>
                </div>
            </div>
            <div class="p-3"></div>
            <Modal v-if="showModal" @confirm="saveRole" @close="closeModal" :close-text="'Cancel'" :confirm-text="editingRole.id ? 'Update' : 'Create'" :dark-mode="darkMode" :small="true" :no-buttons="true">
                <template v-slot:header>
                    <h4 class="text-xl">{{ editingRole.id ? 'Edit Role' : 'Add Role' }}</h4>
                </template>
                <template v-slot:content>
                    <div class="mb-4">
                        <div class="lg:col-span-4 rounded-lg bg-red-100 p-4 mb-3" v-if="permissionError">
                            <p class="text-sm font-medium text-red-800">{{ permissionError }}</p>
                        </div>
                        <label class="block pb-2 font-medium">Name</label>
                        <div class="flex items-center gap-3">
                            <div class="w-full">
                                <CustomInput
                                    v-model="editingRole.name"
                                    :dark-mode="darkMode"
                                />
                            </div>
                            <button
                                class="transition duration-200 text-white font-medium focus:outline-none py-2 rounded-md px-5 bg-primary-500 hover:bg-blue-500"
                                @click="saveRole"
                            >
                                Save
                            </button>
                        </div>
                    </div>
                    <div>
                        <div class="flex items-center gap-3 pb-4">
                            <div class="w-full">
                                <CustomInput
                                    v-model="searchPermissions"
                                    :dark-mode="darkMode"
                                    :search-icon="true"
                                    placeholder="Search Permissions..."
                                    @keyup.enter.stop="filterPermissions"
                                    @update:model-value="attemptSearch"
                                />
                            </div>
                        </div>
                        <div class="rounded-md h-100 overflow-y-scroll p-5"
                             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}"
                         >
                            <div class="grid gap-5 grid-cols-1 sm:grid-cols-2">
                                <div v-for="permission in filteredPermissions" class="flex items-center gap-3 h-max" :key="permission.id">
                                    <toggle-switch
                                        :dark-mode="darkMode"
                                        :model-value="permission.status"
                                        @click="syncPermission(permission)"
                                        :disabled="syncingPermission"
                                    ></toggle-switch>
                                    <p>{{ permission.name }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </Modal>
        </div>
    </div>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import SharedApiService from "../../Shared/services/api";
import CustomInput from "../../Shared/components/CustomInput.vue";
import {nextTick} from "vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store";

export default {
    name: "Roles",
    components: {CustomInput, ToggleSwitch, Dropdown, Modal, LoadingSpinner},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        roles: {
            type: Array,
            default: []
        },
        permissions: {
            type: Array,
            default: []
        },
        error: {
            type: String,
            default: null
        },
        saving: {
            type: Boolean,
            default: false
        },
        api: {
            type: SharedApiService,
            required: true
        }
    },
    data() {
        return {
            editingRole: {},
            showModal: false,
            permission: null,
            syncingPermission: false,
            permissionError: null,

            searchPermissions: '',
            filteredPermissions: [],
            filterActive: false,
            filterDebounceInterval: null,
            filterDebounceTimer: 400,
            permissionStore: useRolesPermissions()
        }
    },
    mounted() {
        this.permissions.sort((p1, p2) => p1.name.localeCompare(p2.name));
        this.filteredPermissions = this.permissions;
    },
    computed: {
        canEdit: function () {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_MANAGEMENT_EDIT);
        }
    },
    methods: {
        addRole() {
            this.editingRole = {permissions: []};
            this.showModal = true;
            this.permissions.map(permission => permission.status = false);
        },
        editRole(role) {
            this.editingRole = {...role};
            this.showModal = true;
            this.permissions.map(permission => permission.status = !! this.editingRole.permissions.find(per => per.id === permission.id));
        },
        saveRole() {
            this.editingRole.permissions = this.permissions.filter(permission => permission.status).map(permission => permission.id);

            if (this.editingRole.id) this.$emit('update-role', this.editingRole);
            else this.$emit('create-role', this.editingRole);

            this.closeModal();
        },
        syncPermission(permission) {
            permission.status = !permission.status;

            if (this.editingRole.id) {
                this.editingRole.permissions = this.permissions.filter(permission => permission.status).map(permission => permission.id);
                this.syncingPermission = true;
                this.permissionError = null;
                this.api.syncPermissions(this.editingRole).then(resp => {
                    this.roles.find(role => role.id === this.editingRole.id).permissions = resp.data.data.role.permissions;
                }).catch(e => {
                    this.permissionError =  e.response.data.message;
                    permission.status = !permission.status;
                }).finally(() => this.syncingPermission = false);
            }
        },
        closeModal() {
            this.editingRole = {};
            this.showModal = false;
        },
        filterPermissions() {
            clearInterval(this.filterDebounceInterval);
            this.filterDebounceInterval = null;

            const rx = new RegExp(this.searchPermissions.replace(/[^0-z]/g, ''), 'i');
            this.filteredPermissions = this.permissions.filter(permission => {
                this.filterActive = true;
                return rx.test(permission.name.replace(/[^0-z]/g, ''));
            });
        },
        clearSearch() {
            this.filteredPermissions = this.permission;
            this.filterActive = false;
        },
        async attemptSearch() {
            await nextTick();
            if (!this.searchPermissions && this.filterActive) {
                this.clearSearch();
            }
            else if (this.searchPermissions.length < 3) {
                return;
            }

            if (!this.filterDebounceInterval) {
                const interval = 50;
                this.filterDebounceInterval = setInterval(() => {
                    this.filterDebounceTimer -= interval;
                    if (this.filterDebounceTimer <= 0) {
                        this.filterPermissions();
                    }
                }, interval);
            }
        }
    }
}
</script>

<style scoped>

</style>
