<template>
    <div
        class="flex items-center"
    >
        <div class="mr-2 text-xs text-slate-500 font-semibold">
            Page {{ page }} of {{ lastPage }}
        </div>
        <div class="flex border rounded-lg"
             :class="{'bg-light-background': !darkMode, 'bg-dark-background border-dark-border': darkMode}"
        >
            <button :disabled="page === 1" @click="$emit('page-change', page-1)" class="border-r p-2" :class="[darkMode ? 'border-dark-border' : '']">
                <slot name="prev">
                    Prev
                </slot>
            </button>
            <button :disabled="page === lastPage" @click="$emit('page-change', page+1)" class="p-2">
                <slot name="next">
                    Next
                </slot>
            </button>
        </div>
    </div>
</template>
<script>
export default {
    name: "SimplePagination",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        page: {
            type: Number,
            default: 1,
        },
        perPage: {
            type: Number,
            default: 10,
        },
        total: {
            type: Number,
            required: true,
        }
    },
    emits: ['page-change'],
    computed: {
        lastPage() {
            return Math.ceil(this.total/this.perPage);
        }
    }
}
</script>