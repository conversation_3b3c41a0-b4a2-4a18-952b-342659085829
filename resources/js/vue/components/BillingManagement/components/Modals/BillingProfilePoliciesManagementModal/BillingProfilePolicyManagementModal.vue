<template>
    <Modal
        :loading-confirmation="billingPoliciesManagementStore.loading"
        :disable-confirm="billingPoliciesManagementStore.loading"
        :dark-mode="darkMode"
        no-buttons
        @close="handleClose"
    >
        <template v-slot:header>
            <span>Billing Profile Policy Management</span>
        </template>
        <template v-slot:content>
            <div class="flex flex-col gap-2">
                <AddPolicy :dark-mode="darkMode"/>
                <ListPolicies
                    class="mt-2"
                    v-model="billingPoliciesManagementStore.billingPolicies"
                    :dark-mode="darkMode"
                    @remove-policy-event="handleRemovePolicyEvent"
                    @edit-policy="handleEditPolicy"
                    @save="handleCreditManagementSave"
                    :loading="billingPoliciesManagementStore.loading"
                />
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../../../Shared/components/Modal.vue";
import {useBillingPoliciesManagementStore} from "../../../../../../stores/billing/billing-policies-management.store";
import AddPolicy from "./AddPolicy.vue";
import ListPolicies from "./ListPolicies.vue";
import SimpleAlert from "../../../../Shared/components/SimpleAlert.vue";

export default {
    name: "BillingProfilePoliciesManagementModal",
    components: {
        SimpleAlert,
        ListPolicies,
        AddPolicy,
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    emits: ['close', 'confirm'],
    data() {
        return {
            billingPoliciesManagementStore: useBillingPoliciesManagementStore(),
        }
    },

    methods: {
        async handleCreditManagementSave() {
            const parsedData = this.billingPoliciesManagementStore.billingPolicies.reduce((prev, current) => {

                return [
                    ...prev,
                    ...current.actions.map(a => ({
                        ...a,
                        id: a.id,
                        event_slug: a.event_slug,
                        action_slug: a.action_slug,
                    }))
                ]
            }, []);

            await this.billingPoliciesManagementStore.batchUpdate(parsedData)
        },

        handleRemovePolicyEvent(action) {
            this.billingPoliciesManagementStore.deleteBillingProfilePolicy(action.id)
        },

        handleEditPolicy({action, policy}) {
            this.billingPoliciesManagementStore.setSelectedActionSlug(action.action_slug)
            this.billingPoliciesManagementStore.setSelectedEventSlug(policy.event_slug)
        },

        handleClose(){
            this.$emit('close')
        },
    },
}
</script>
