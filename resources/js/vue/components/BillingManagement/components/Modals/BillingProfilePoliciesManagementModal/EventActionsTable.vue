<template>
    <div class="border">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center gap-1">
                <SimpleIcon
                    v-if="policy.event_description"
                    :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                    :tooltip="policy.event_description"
                    :color="simpleIcon.colors.BLUE"
                    class="cursor-help"
                />
                <p class="font-semibold">{{ policy.event_name }} ({{ policy.actions.length }})</p>
            </div>
            <simple-icon
                :icon="show ? simpleIcon.icons.CHEVRON_UP : simpleIcon.icons.CHEVRON_DOWN"
                clickable
                @click="handleShowList"
                :color="simpleIcon.colors.BLUE"
            />
        </div>
        <Draggable
            v-show="show"
            v-model="policy.actions"
            class="cursor-move flex flex-col transition-all ease-in-out"
            @change="handleSave"
        >
            <div v-for="(action, idx) in policy.actions" :key="idx"
                 class="p-4 flex flex-col bg-gray-50 border-t hover:bg-gray-100 text-sm">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-1">
                        <strong>{{ idx + 1 }}.</strong>
                        <p>{{ action.action_name }}</p>
                        <SimpleIcon
                            v-if="action.action_description"
                            :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                            :tooltip="action.action_description"
                            :color="simpleIcon.colors.BLUE"
                            class="cursor-help"
                        />
                    </div>
                    <div class="flex items-center gap-1">
                        <SimpleIcon
                            v-if="doesActionHaveProps(action)"
                            :icon="simpleIcon.icons.PENCIL"
                            :color="simpleIcon.colors.BLUE"
                            @click="handleEditAction(action)"
                            clickable
                        />
                        <SimpleIcon
                            :icon="simpleIcon.icons.BIN"
                            :color="simpleIcon.colors.RED"
                            clickable
                            @click=handleRemoveAction(action)
                        />
                    </div>
                </div>
                <div class="p-2" v-if="doesActionHaveProps(action) && selectedEventAction?.id === action.id">
                    <component v-model="action.action_data" :is="getActionConfigComponent(action)"/>
                    <div class="flex items-center justify-end">
                        <custom-button @click="handleSave">
                            Save
                        </custom-button>
                    </div>
                </div>
            </div>
        </Draggable>
    </div>
</template>
<script>
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../../composables/useSimpleIcon.js";
import {VueDraggableNext as Draggable} from "vue-draggable-next";
import {markRaw} from "vue";
import SendEmailNotificationConfigForm from "./ActionConfigs/SendEmailNotificationConfigForm.vue";
import CustomButton from "../../../../Shared/components/CustomButton.vue";

export default {
    name: 'EventActionsTable',
    components: {CustomButton, SimpleIcon, Draggable},
    props: {
        policy: {
            type: Object,
            default: {}
        }
    },
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        actionConfigComponents() {
            return {
                'send_email_notification': markRaw(SendEmailNotificationConfigForm)
            }
        },
    },
    data() {
        return {
            show: true,
            selectedEventAction: null
        }
    },
    methods: {
        getActionConfigComponent(action) {
            return this.actionConfigComponents[action.action_slug];
        },
        doesActionHaveProps(action) {
            return this.getActionConfigComponent(action)
        },

        handleShowList() {
            this.show = !this.show
        },

        handleEditAction(action) {
            if (!action.action_data) {
                action.action_data = {}
            }
            this.selectedEventAction = action
        },

        handleRemoveAction(action) {
            this.$emit('remove-policy-event', action)
        },

        handleSave() {
            this.$emit("save")
        },
    }
}
</script>
