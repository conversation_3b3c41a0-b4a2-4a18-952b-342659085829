<template>
    <div class="relative flex flex-col gap-2 rounded-lg"
    >
        <SimpleAlert
            v-if="billingPoliciesManagementStore.errorHandler.message"
            class="z-100 border shadow-md fixed left-1/2 transform -translate-x-1/2 mt-4"
            variant="light-red"
            :dark-mode="darkMode"
            :content="billingPoliciesManagementStore.errorHandler.message"
            dismissible
            @dismiss="billingPoliciesManagementStore.errorHandler.resetError()"
        >
            <template v-slot:icon>
                <SimpleIcon :dark-mode="darkMode" :icon="simpleIcon.icons.EXCLAMATION_CIRCLE"></SimpleIcon>
            </template>
        </SimpleAlert>
        <div class="flex gap-2">
            <Dropdown
                class="flex-1"
                v-model="billingPoliciesManagementStore.selectedEventSlug"
                placeholder="Event"
                :options="availableEventOptions"
                :dark-mode="darkMode"
            />
            <Dropdown
                class="flex-1"
                v-model="billingPoliciesManagementStore.selectedActionSlug"
                placeholder="Action"
                :options="availableActionOptions"
                :dark-mode="darkMode"
            />
        </div>
        <div class="flex items-center gap-2 justify-end">
            <CustomButton
                @click="handleClear"
                :dark-mode="darkMode"
                color="primary-outline"
            >
                Clear
            </CustomButton>

            <CustomButton
                @click="handleAdd"
                :dark-mode="darkMode"
            >
                Add
            </CustomButton>
        </div>
    </div>
</template>

<script>
import Dropdown from "../../../../Shared/components/Dropdown.vue";

import {useBillingPoliciesManagementStore} from "../../../../../../stores/billing/billing-policies-management.store";
import CustomButton from "../../../../Shared/components/CustomButton.vue";
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../../composables/useSimpleIcon";
import SimpleAlert from "../../../../Shared/components/SimpleAlert.vue";
import LabeledValue from "../../../../Shared/components/LabeledValue.vue";

export default {
    name: "AddPolicy",
    components: {
        LabeledValue,
        SimpleAlert,
        SimpleIcon,
        CustomButton,
        Dropdown,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            billingPoliciesManagementStore: useBillingPoliciesManagementStore(),
            loading: false,
            availableEventOptions: [],
            eventActionList: {},
            simpleIcon: useSimpleIcon(),
        }
    },

    async mounted() {
        const res = await this.billingPoliciesManagementStore.api.getEvents()

        this.availableEventOptions = res.data.data.events.map(e => ({
            id: e.event_slug,
            name: e.event_name,
            actions: e.actions ?? []
        }))
    },

    methods: {
        handleClear() {
            this.billingPoliciesManagementStore.setSelectedEventSlug();
            this.billingPoliciesManagementStore.setSelectedActionSlug();
        },

        checkActionIsInUse(event, action) {
            const found = this.billingPoliciesManagementStore.billingPolicies.find(e => e.event_slug === event)

            return found?.actions.findIndex(a => a.action_slug === action) > -1
        },

        async handleAdd() {
            await this.billingPoliciesManagementStore.createBillingProfilePolicy({
                event: this.billingPoliciesManagementStore.selectedEventSlug,
                action: this.billingPoliciesManagementStore.selectedActionSlug
            })

            this.billingPoliciesManagementStore.setSelectedActionSlug()
        }
    },

    computed: {
        availableActionOptions() {
            if (!this.billingPoliciesManagementStore.selectedEventSlug) return [];

            return this.availableEventOptions.find(e => e.id === this.billingPoliciesManagementStore.selectedEventSlug)
                ?.actions
                ?.map(action => ({id: action.action_slug, name: action.action_name})) ?? []
        },
    },

    watch: {
        'billingPoliciesManagementStore.selectedEventSlug'(newVal) {
            if (!newVal) this.billingPoliciesManagementStore.setSelectedActionSlug()
        }
    },
}
</script>
