<template>
    <div class="relative">
        <div v-if="loading" class="absolute w-full h-full bg-gray-100 opacity-90 z-100 flex items-center justify-center">
            <LoadingSpinner />
        </div>
        <div v-if="modelValue?.length > 0" class="flex flex-col gap-4">
            <EventActionsTable
                v-for="policy in modelValue"
                :policy="policy"
                @removePolicyEvent="(action) => $emit('remove-policy-event', action)"
                @save="$emit('save')"
            />
        </div>
        <div v-else class="text-center">
            No policies are currently configured. To create a new policy, select an event and the corresponding action
            from the options above
        </div>
    </div>
</template>

<script>
import Dropdown from "../../../../Shared/components/Dropdown.vue";
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../../composables/useSimpleIcon";
import {VueDraggableNext as Draggable} from "vue-draggable-next";
import LoadingSpinner from "../../../../Shared/components/LoadingSpinner.vue";
import EventActionsTable from "./EventActionsTable.vue";

export default {
    name: "ListPolicies",
    components: {
        EventActionsTable,
        LoadingSpinner,
        Draggable,
        SimpleIcon,
        Dropdown,
    },
    props: {
        modelValue: {
            type: Array,
            required: true,
            default: []
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        loading: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            simpleIcon: useSimpleIcon(),
        }
    },
}
</script>
