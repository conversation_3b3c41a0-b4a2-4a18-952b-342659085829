<template>
    <pop-upify classes="absolute shadow rounded-lg w-3/4" :dark-mode="darkMode">
        <invoice-management
            closeable @close="$emit('close')"
            :dark-mode="darkMode"
            :custom-title="tableTitle"
        />
    </pop-upify>
</template>
<script>
import Modal from "../../../Shared/components/Modal.vue";
import {useCreditManagementStore} from "../../../../../stores/credit/credit-management.store";
import CreateCreditType from "../CreditManagement/CreateCreditType.vue";
import CreditTypeManagementTable from "../CreditManagement/CreditTypeManagementTable.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store";
import InvoiceManagement from "../../../Shared/modules/Invoices/InvoiceManagement.vue";
import PopUpify from "../../../Shared/components/PopUpify.vue";

export default {
    name: "InvoiceManagementModal",
    components: {PopUpify, InvoiceManagement, CreditTypeManagementTable, CreateCreditType, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['close'],
    inject: ['tableTitle'],
}
</script>