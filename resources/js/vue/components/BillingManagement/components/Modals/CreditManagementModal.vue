<template>
    <Modal
        :no-buttons="!canUpdateCreditTypes"
        :loading-confirmation="loading"
        :dark-mode="darkMode"
        @confirm="handleCreditManagementSaved"
        @close="$emit('close')"
    >
        <template v-slot:header>
            <div>
                Credit Management Modal
            </div>
        </template>
        <template v-slot:content>
            <div class="flex flex-col gap-2">
                <create-credit-type :dark-mode="darkMode"/>
                <credit-type-management-table :loading="loading" :dark-mode="darkMode"/>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import {useCreditManagementStore} from "../../../../../stores/credit/credit-management.store";
import CreateCreditType from "../CreditManagement/CreateCreditType.vue";
import CreditTypeManagementTable from "../CreditManagement/CreditTypeManagementTable.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store";

export default {
    name: "CreditManagementModal",
    components: {CreditTypeManagementTable, CreateCreditType, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    emits: ['close', 'confirm'],
    data() {
        return {
            creditStore: useCreditManagementStore(),
            permissionStore: useRolesPermissions(),
            loading: false,
        }
    },
    computed: {
        canUpdateCreditTypes() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_BILLING_CREDITS_TYPES_UPDATE)
        }
    },
    methods: {
        async handleCreditManagementSaved() {
            this.loading = true;
            try {
                const resp = await this.creditStore.api.updateCreditTypes(this.creditStore.creditTypes)
                this.creditStore.creditTypes = resp.data.data;
            } catch (error) {
                console.error(error)
            }
            this.loading = false;
        }
    }
}
</script>