<template>
    <Modal
        :loading-confirmation="invoiceTemplatesManagementStore.loadingSave"
        :disable-confirm="invoiceTemplatesManagementStore.loadingSave"
        :dark-mode="darkMode"
        @confirm="handleCreditManagementSaved"
        @close="handleClose"
        full-width
        :restrict-width="!templateHtmlPreview"
    >
        <template v-slot:header>
            <span>{{ isEditing ? 'Edit' : 'Create' }} Invoice Template</span>
        </template>
        <template v-slot:content>
            <simple-alert
                v-if="invoiceTemplatesManagementStore.notificationMessage.message"
                class="z-100 border shadow-md fixed left-1/2 transform -translate-x-1/2 mt-4"
                :variant="invoiceTemplatesManagementStore.notificationMessage.variant"
                :dark-mode="darkMode"
                :content="invoiceTemplatesManagementStore.notificationMessage.message"
                dismissible
                :dismiss-in-seconds="2.5"
                @dismiss="invoiceTemplatesManagementStore.clearMessage()"
            >
                <template v-slot:icon>
                    <simple-icon :dark-mode="darkMode" :icon="simpleIcon.icons.EXCLAMATION_CIRCLE"></simple-icon>
                </template>
            </simple-alert>
            <div v-if="!loading" class="flex gap-2">
                <div class="flex-1">
                    <div class="grid grid-cols-2 gap-3 text-xs">
                        <div>
                            <div class="uppercase text-slate-500">Name</div>
                            <custom-input
                                :dark-mode="darkMode"
                                type="text"
                                v-model="selectedInvoiceTemplate.name"
                            />
                            <span v-if="invoiceTemplatesManagementStore.errorHandler.errors.name"
                                  class="text-red-400 text-sm">{{
                                    invoiceTemplatesManagementStore.errorHandler.errors.name
                                }}</span>
                        </div>
                        <div>
                            <div class="uppercase text-slate-500">Global</div>
                            <toggle-switch
                                v-model="selectedInvoiceTemplate.is_global"
                                :dark-mode="darkMode"
                                @update:modelValue="handleGlobalChange"
                            />
                            <span v-if="invoiceTemplatesManagementStore.errorHandler.errors.is_global"
                                  class="text-red-400 text-sm">{{
                                    invoiceTemplatesManagementStore.errorHandler.errors.is_global
                                }}</span>
                        </div>
                        <div v-if="!selectedInvoiceTemplate.is_global">
                            <div class="uppercase text-slate-500">Related Model</div>
                            <Dropdown
                                v-model="selectedInvoiceTemplate.model_type"
                                :dark-mode="darkMode"
                                :options="modelTypeOptions"
                                :selected="modelTypeOptions"
                            />
                            <span
                                v-if="invoiceTemplatesManagementStore.errorHandler.errors.model_type"
                                class="text-red-400 text-sm"
                            >
                                {{
                                    invoiceTemplatesManagementStore.errorHandler.errors.model_type
                                }}
                            </span>

                        </div>
                        <div v-if="!selectedInvoiceTemplate.is_global">
                            <div class="uppercase text-slate-500">Entity</div>
                            <autocomplete
                                :disable-input="!selectedInvoiceTemplate.model_type"
                                :dark-mode="darkMode"
                                v-model="selectedInvoiceTemplate.model_id"
                                :value="selectedInvoiceTemplate.model_id"
                                :options="invoiceTemplatesManagementStore.modelOptions"
                                :placeholder="selectedInvoiceTemplate?.model_data?.name ?? selectedInvoiceTemplate?.model_id ?? 'Type to search entity'"
                                @search="handleModelSearch($event)">
                            </autocomplete>
                            <span
                                v-if="invoiceTemplatesManagementStore.errorHandler.errors.model_id"
                                class="text-red-400 text-sm"
                            >
                                {{ invoiceTemplatesManagementStore.errorHandler.errors.model_id }}
                            </span>
                        </div>
                    </div>
                    <div class="mt-3 flex flex-col gap-2">
                        <div class="text-slate-500">Configuration</div>
                        <invoice-component-props-form
                            v-model="selectedInvoiceTemplate.props"
                            :errors="invoiceTemplatesManagementStore.errorHandler.errors"
                        />
                    </div>
                </div>
                <div v-if="templateHtmlPreview" class="flex-1">
                    <div class="flex items-center justify-center min-h-screen bg-gray-200 p-6 w-full h-full">
                        <div
                            v-if="!loadingTemplate"
                            class="bg-white border border-gray-300 rounded-lg shadow-lg p-6 w-full h-full overflow-auto"
                        >
                            <iframe class="min-h-fit" width="100%" height="100%" :srcdoc="templateHtmlPreview"></iframe>
                        </div>
                        <loading-spinner v-else/>
                    </div>
                </div>
            </div>
            <div v-else class="flex justify-center items-center">
                <loading-spinner/>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../../../Shared/components/Modal.vue";
import SimpleAlert from "../../../../Shared/components/SimpleAlert.vue";
import {useInvoiceTemplatesManagementStore} from "../../../../../../stores/billing/invoice-templates-management-store";
import Dropdown from "../../../../Shared/components/Dropdown.vue";
import CustomInputPassword from "../../../../Shared/components/CustomInputPassword.vue";
import CustomInput from "../../../../Shared/components/CustomInput.vue";
import Autocomplete from "../../../../Shared/components/Autocomplete.vue";
import InvoiceComponentPropsForm from "./InvoiceComponentPropsForm.vue";
import LoadingSpinner from "../../../../Shared/components/LoadingSpinner.vue";
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../../composables/useSimpleIcon";
import ToggleSwitch from "../../../../Shared/components/ToggleSwitch.vue";
import {useCooldown} from "../../../../../../composables/useCooldown";

export default {
    name: "InvoiceTemplateManagementModal",
    components: {
        ToggleSwitch,
        SimpleIcon,
        LoadingSpinner,
        InvoiceComponentPropsForm,
        Autocomplete,
        CustomInput,
        CustomInputPassword,
        Dropdown,
        SimpleAlert,
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    emits: ['close', 'confirm'],
    data() {
        return {
            invoiceTemplatesManagementStore: useInvoiceTemplatesManagementStore(),
            modelTypeOptions: [],
            modelOptions: [],
            templateHtmlPreview: '',
            loadingTemplate: false,
            loading: false,
            cooldown: useCooldown(),
        }
    },

    async created() {
        this.loading = true
        await this.getSearchableModels();

        const { selectedInvoiceTemplate } = this.invoiceTemplatesManagementStore

        if (selectedInvoiceTemplate?.id) {
            this.previewTemplate(selectedInvoiceTemplate.id)
        }

        if (selectedInvoiceTemplate.model_id) {
            const id = selectedInvoiceTemplate?.model_id;

            this.modelOptions = [{
                name: selectedInvoiceTemplate?.model_data?.name ?? id,
                id
            }]
        }

        this.loading = false
    },

    watch: {
        'invoiceTemplatesManagementStore.selectedInvoiceTemplate': {
            deep: true,
            handler(){
                if (this.invoiceTemplatesManagementStore.selectedInvoiceTemplate.id) {
                    this.cooldown.execute(
                        5000,
                        this.previewTemplate,
                        [this.invoiceTemplatesManagementStore.selectedInvoiceTemplate.id]
                    )
                }
            },
        }
    },

    methods: {
        async getSearchableModels() {
            const res = await this.invoiceTemplatesManagementStore.api.getSearchableModels()
            this.modelTypeOptions = res.data.data.searchable_models
        },
        handleModelSearch(query) {
            if (query) {
                this.invoiceTemplatesManagementStore.searchModels(query)
            }
        },
        async previewTemplate(templateId) {
            if (this.loadingTemplate) return;

            this.loadingTemplate = true
            try {
                const response = await this.invoiceTemplatesManagementStore.api.previewInvoiceTemplate(templateId)

                this.templateHtmlPreview = response.data.data?.html_preview ?? 'Error trying to preview html'
            } catch (err) {
                console.error(err)
                this.templateHtmlPreview = 'Error trying to preview invoice'
            }
            this.loadingTemplate = false
        },

        async handleCreditManagementSaved() {
            await this.invoiceTemplatesManagementStore.saveInvoiceTemplate()

            if (this.selectedInvoiceTemplate.id) {
                await this.previewTemplate(this.selectedInvoiceTemplate.id)
            }
        },

        handleClose() {
            this.$emit('close')
        },

        handleGlobalChange(value) {
            if (value) {
                this.selectedInvoiceTemplate.model_type = null
                this.selectedInvoiceTemplate.model_id = null
            }
        }
    },

    computed: {
        isEditing() {
            return this.selectedInvoiceTemplate?.id
        },

        simpleIcon() {
            return useSimpleIcon()
        },

        selectedInvoiceTemplate() {
            return this.invoiceTemplatesManagementStore.selectedInvoiceTemplate
        }
    },

}
</script>
