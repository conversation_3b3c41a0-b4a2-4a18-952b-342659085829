<template>
    <div class="grid grid-cols-2 gap-3">
        <div>
            <div class="text-xs uppercase text-slate-500">Currency</div>
            <Dropdown
                :options="currencyOptions"
                :dark-mode="darkMode"
                :selected="modelValue.currency"
                @update:modelValue="e => modelValue.currency = e"
            />
        </div>
        <div>
            <div class="text-xs uppercase text-slate-500">Invoice Logo</div>
            <file-upload
                :dark-mode="darkMode"
                @file-uploaded="handleFileUploaded"
            />
        </div>
        <div>
            <div class="text-xs uppercase text-slate-500">Support Email</div>
            <custom-input
                :dark-mode="darkMode"
                type="text"
                v-model="modelValue.support_email"
            />
        </div>
        <div>
            <div class="text-xs uppercase text-slate-500">Contact Address</div>
            <custom-input
                :dark-mode="darkMode"
                type="text"
                v-model="modelValue.invoice_contact_address"
            />
        </div>
    </div>
</template>
<script>
import CustomInput from "../../../../Shared/components/CustomInput.vue";
import Dropdown from "../../../../Shared/components/Dropdown.vue";
import FileUpload from "../../../../Inputs/FileUpload.vue";

export default {
    name: "InvoiceComponentPropsForm",
    components: {FileUpload, Dropdown, CustomInput},
    props: {
        component: {
            type: String
        },
        modelValue: {
            type: Object,
            default: {}
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            currencyOptions: []
        }
    },

    methods: {
        handleFileUploaded(file){
            console.log(file)
        }
    }
}
</script>
