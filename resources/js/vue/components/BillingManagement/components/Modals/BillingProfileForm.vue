<template>
    <div
         class="grid gap-2 items-start"
         :class="profileData.default ? 'grid-cols-2' : 'grid-cols-3'"
    >
        <div class="flex-1 grid grid-cols-3 col-span-2 gap-x-4 gap-y-2">
            <labeled-value label="Name" class="col-span-full">
                <text-field
                    v-model="profileData.name"
                    :dark-mode="darkMode"
                    :disabled="readonly"
                    class="h-7"
                >
                </text-field>
            </labeled-value>
            <labeled-value v-if="!companyId" label="Company">
                <entity-hyperlink
                    v-if="editing"
                    type="company"
                    :entity-id="profileData.company?.id"
                    :prefix="profileData.company?.name"
                >
                </entity-hyperlink>
                <company-search-autocomplete
                    v-model="profileData.company_id"
                    v-else
                >
                </company-search-autocomplete>
            </labeled-value>
            <labeled-value label="Threshold in Dollars">
                <custom-inline-input
                    v-model="profileData.threshold"
                    :disabled="readonly"
                    :dark-mode="darkMode"
                />
            </labeled-value>
            <labeled-value label="Allowed Charge Attempts">
                <custom-inline-input
                    v-model="profileData.charge_attempts"
                    :dark-mode="darkMode"
                    :disabled="readonly"
                />
            </labeled-value>
            <labeled-value v-if="profileData.payment_method !== 'manual'" label="Process Automatically">
                <toggle-switch
                    v-model="profileData.process_auto"
                    class="flex justify-end"
                    :dark-mode="darkMode"
                    :disabled="readonly"
                />
            </labeled-value>
            <labeled-value label="Preferred" tooltip="When enabled, this will be used as the fallback billing profile for all campaigns (both new and existing). If you want to split payments between two campaigns, the company should have three billing profiles: one preferred fallback, and two assigned individually to each campaign">
                <toggle-switch
                    v-model="profileData.default"
                    class="flex justify-end"
                    :dark-mode="darkMode"
                    :disabled="readonly"
                />
            </labeled-value>
            <labeled-value label="Payment method">
                <div class="flex items-center gap-2">
                    <div
                        v-for="paymentMethodOption in paymentMethodOptions"
                        class="flex gap-1 items-center"
                        @click="handleSelectPaymentMethod(paymentMethodOption)"
                        :class="[readonly ? '': 'cursor-pointer hover:opacity-75', profileData.payment_method !== paymentMethodOption ? 'opacity-50' : 'opacity-100']"
                    >
                        <custom-checkbox
                            v-if="!readonly"
                            :model-value="profileData.payment_method === paymentMethodOption"
                            disabled
                        />
                        <payment-method-badge
                            :type="paymentMethodOption"
                        />
                    </div>
                </div>
            </labeled-value>
            <labeled-value v-if="profileData.payment_method === 'stripe'" label="Preferred payment method">
                <loading-spinner v-if="loadingCompanyPaymentMethods"></loading-spinner>
                <div
                    v-else
                    class="flex items-center gap-1"
                >
                    <dropdown
                        v-model="profileData.payment_method_id"
                        :model-value="profileData.payment_method_id"
                        :options="companyPaymentMethodOptions"
                        placeholder="Set preferred payment method"
                        class="flex-1"
                        :disabled="readonly"
                    />
                    <simple-icon
                        v-if="profileData.payment_method_id && !readonly"
                        :icon="simpleIcon.icons.X_MARK"
                        tooltip="Remove preferred payment method"
                        clickable
                        @click="() => profileData.payment_method_id = null"
                    />
                </div>
            </labeled-value>
            <labeled-value label="Due In Days">
                <template v-slot:action>
                    <simple-icon
                        :dark-mode="darkMode"
                        :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                        :color="simpleIcon.colors.BLUE"
                        tooltip="How many days after issue date should the invoice be due by. When 0, the due date is the same as the issue date."
                    />
                </template>
                <number-with-spinners
                    base-style="flex items-center"
                    v-model="profileData.due_in_days"
                    :dark-mode="darkMode"
                    :number-step="1"
                    :min="0"
                    prefix=""
                    :disabled="readonly"
                />
            </labeled-value>
            <labeled-value label="Invoice Template">
                <loading-spinner v-if="loadingInvoiceTemplateOptions"></loading-spinner>
                <div
                    v-else
                    class="flex items-center gap-1"
                >
                    <dropdown
                        v-model="profileData.invoice_template_id"
                        :model-value="profileData.invoice_template_id"
                        :options="invoiceTemplateOptions"
                        placeholder="Set invoice template"
                        class="flex-1"
                        :disabled="readonly"
                    />
                </div>
            </labeled-value>
            <labeled-value label="Frequency" class="col-span-full">
                <simple-frequency-selector
                    v-if="profileData.frequency_type"
                    :type="profileData.frequency_type"
                    :dark-mode="darkMode"
                    :data="profileData.frequency_data"
                    @cron-updated="handleCronUpdated"
                    :disabled="readonly"
                />
            </labeled-value>
        </div>
        <div v-if="!profileData.default" class="flex flex-col gap-1">
            <campaign-selector
                v-if="!profileData.default"
                :dark-mode="darkMode"
                :model-value="campaigns"
                :disabled="readonly"
            />
            <custom-button v-if="!readonly" @click="handleToggleCampaigns">Toggle all</custom-button>
        </div>
    </div>
</template>
<script>
import SimpleFrequencySelector from "../../../Shared/components/Simple/SimpleFrequencySelector.vue";
import PaymentMethodBadge from "../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import CustomInlineInput from "../../../Shared/components/CustomInlineInput.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import CampaignSelector from "../BillingProfile/CampaignSelector.vue";
import EntityHyperlink from "../EntityHyperlink.vue";
import NumberWithSpinners from "../../../Campaigns/Wizard/components/NumberWithSpinners.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import {ROLES, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import useErrorHandler from "../../../../../composables/useErrorHandler.js";
import Api from "../../services/invoice-templates-api.js";
import ApiService from "../../services/billing-profiles-api.js";
import {useToastNotificationStore} from "../../../../../stores/billing/tost-notification.store.js";
import CompanyPaymentMethodsApiService from "../../services/company-payment-methods.js";
import TextField from "../../../IndustryManagement/WebsiteManagement/ApiKeys/components/TextField.vue";

export default {
    name: "BillingProfileForm",
    components: {
        TextField,
        CustomCheckbox,
        NumberWithSpinners,
        EntityHyperlink,
        CampaignSelector,
        CustomButton,
        CompanySearchAutocomplete,
        Dropdown,
        SimpleIcon,
        LabeledValue,
        LoadingSpinner,
        ToggleSwitch,
        CustomInlineInput,
        PaymentMethodBadge,
        SimpleFrequencySelector
    },
    props: {
        modelValue: {
            type: Object,
            default: {}
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            required: false
        },
        billingProfileId: {
            type: Number,
            required: false
        },
        readonly: {
            type: Boolean,
            default: false
        },
        data: {
            type: Object,
            default: {}
        },
    },
    data() {
        return {
            loading: false,
            loadingCompanyPaymentMethods: false,
            loadingInvoiceTemplateOptions: false,
            invoiceTemplateOptions: [],
            loadingConfirmation: false,
            errorHandler: useErrorHandler(),
            profileData: {
                due_in_days: 0,
                ...this.modelValue
            },
            campaigns: [],
            billingProfileTypes: [
                {id: 'stripe', name: 'Stripe'},
                {id: 'manual', name: 'Manual'},
            ],
            invoiceTemplateApi :Api.make(),
            apiService: ApiService.make(),
            toastNotificationStore: useToastNotificationStore(),
            paymentMethodOptions: ['manual', 'stripe'],
            companyPaymentMethodsApi: CompanyPaymentMethodsApiService.make(),
            companyPaymentMethods: [],
            rolesPermissions: useRolesPermissions(),
        }
    },
    watch: {
        campaigns: {
            deep: true,
            handler(){
                this.profileData.campaigns = this.campaigns.filter(e => e.associated).map(e => e.id)
            }
        },
        profileData: {
            deep: true,
            handler(){
                this.$emit('update:modelValue', this.profileData)
            }
        },
        'profileData.company_id': {
            handler() {
                this.getCompanyCampaigns(this.profileData.company_id)
                this.getCompanyPaymentMethods(this.profileData.company_id)
            },
        },
        'profileData.payment_method': {
            handler() {
                if (this.profileData.payment_method === 'manual') {
                    this.profileData.payment_method_id = null
                }
            },
        },
        'profileData.company.id': {
            handler() {
                if (this.profileData.company?.id) {
                    this.getCompanyCampaigns(this.profileData.company?.id)
                    this.getCompanyPaymentMethods(this.profileData.company?.id)
                }
            },
            deep: true
        },
        'profileData.default': {
            handler() {
                this.getCompanyCampaigns(this.profileData?.company?.id ?? this.companyId)
            },
            deep: true
        },
    },
    async created() {
        this.getInvoiceTemplateOptions()
        this.loading = true;
        this.profileData = {...this.profileData, ...this.data}
        if (!this.editing) {
            this.profileData = {
                threshold: 400,
                charge_attempts: 3,
                process_auto: true,
                default: true,
                payment_method: 'stripe',
                frequency_type: 'daily',
                company_id: this.companyId,
                frequency_data: {
                    day: 14
                },
            }
        } else if (this.billingProfileId) {
            const res = await this.apiService.getBillingProfile(this.billingProfileId)
            this.profileData = res.data.data
        }
        this.loading = false;
    },
    mounted() {
        this.getCompanyCampaigns(this.companyId)
    },
    methods: {
        async getInvoiceTemplateOptions() {
            this.loadingInvoiceTemplateOptions = true
            const response = await this.invoiceTemplateApi.getInvoiceTemplateOptions();
            this.invoiceTemplateOptions = response.data.data
            this.loadingInvoiceTemplateOptions = false
        },
        handleToggleCampaigns() {
            const isOneAssociated = this.campaigns.some(c => c.associated)
            this.campaigns = this.campaigns.map(c => ({...c, associated: !isOneAssociated}))
        },
        handleCronUpdated(type, data) {
            this.profileData.frequency_type = type;
            this.profileData.frequency_data = data
        },
        async getCompanyPaymentMethods(companyId) {
            this.loadingCompanyPaymentMethods = true

            try {
                const res = await this.companyPaymentMethodsApi.getCompanyPaymentMethods(companyId)
                this.companyPaymentMethods = res.data.data
            } catch (err) {
                console.error(err)
            }

            this.loadingCompanyPaymentMethods = false
        },
        async getCompanyCampaigns(companyId) {
            if (!companyId) {
                return
            }

            this.loadingConfirmation = true;

            const response = await this.apiService.getCompanyCampaigns(
                companyId,
                this.data?.id,
            );

            this.campaigns = response.data.data.campaigns.map(el => ({
                ...el,
                associated: this.profileData?.campaigns?.find(campaign => campaign?.id === el.id || campaign === el.id)
            }))

            this.loadingConfirmation = false;
        },
        handleSelectPaymentMethod(paymentMethodOption) {
            if (this.readonly) return
            this.profileData.payment_method = paymentMethodOption
        },
    },
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        },
        editing() {
            return this.profileData?.id || this.billingProfileId
        },
        companyPaymentMethodOptions() {
            return this.companyPaymentMethods
                .filter(e => e.type === this.profileData.payment_method)
        }
    },
}
</script>


<style scoped>

</style>
