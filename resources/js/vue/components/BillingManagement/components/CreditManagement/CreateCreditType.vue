<template>
    <div
        v-if="canCreateCreditType"
        class="flex flex-col border rounded-lg p-4 relative"
        :class="{'bg-dark-background': darkMode, 'bg-light-background': !darkMode}"
    >
        <simple-alert
            v-if="showAlert"
            class="z-100 border shadow-md fixed left-1/2 transform -translate-x-1/2 mt-4"
            variant="light-red"
            :dark-mode="darkMode"
            :content="errorHandler?.message"
            dismissible
            @dismiss="showAlert = false"
        >
            <template v-slot:icon>
                <simple-icon :dark-mode="darkMode" :icon="simpleIcon.icons.EXCLAMATION_CIRCLE"></simple-icon>
            </template>
        </simple-alert>
        <div class="flex">
            Create Credit Type
        </div>
        <loading-spinner v-if="loading" :dark-mode="darkMode"></loading-spinner>
        <div v-else class="grid grid-cols-3 gap-x-4 gap-y-2">
            <div class="col-span-1 flex flex-col gap-2">
                <div class="text-slate-500 text-sm font-semibold">Name</div>
                <input type="text" v-model="creditStore.newCreditType.name" class="border flex-1 rounded-md h-8"/>
                <div class="text-slate-500 text-sm font-semibold">Line Item Text</div>
                <input type="text" v-model="creditStore.newCreditType.line_item_text" class="border flex-1 rounded-md h-8"/>
            </div>
            <div class="col-span-2 flex justify-between flex-col gap-2">
                <div class="flex justify-between flex-col gap-2">
                    <div class="text-slate-500 text-sm font-semibold">Description</div>
                    <textarea v-model="creditStore.newCreditType.description" class="border flex-1 rounded-md"></textarea>
                </div>
                <div class="flex gap-2">
                    <div class="flex  gap-2 mr-2">
                        <div class="text-slate-500 text-sm font-semibold">Cash</div>
                        <toggle-switch v-model="creditStore.newCreditType.cash" :dark-mode="darkMode"/>
                    </div>
                    <div class="flex gap-2 mr-2">
                        <div class="text-slate-500 text-sm font-semibold">Active</div>
                        <toggle-switch v-model="creditStore.newCreditType.active" :dark-mode="darkMode"/>
                    </div>
                    <div class="flex gap-2 mr-2">
                        <div class="text-slate-500 text-sm font-semibold">Expiry</div>
                        <toggle-switch v-model="creditStore.newCreditType.expiry" :dark-mode="darkMode"/>
                    </div>
                    <div v-if="creditStore.newCreditType.expiry === true" class="flex flex-col">
                        <div class="flex items-center">
                            <div class="text-slate-500 text-sm mr-1 font-semibold">Expires in</div>
                            <custom-inline-input class="flex flex-col" type="number" v-model="creditStore.newCreditType.expires_in_days"/>
                            <div class="text-slate-500 ml-1 text-sm font-semibold">
                                days.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex justify-end mt-4">
            <custom-button :disabled="loading" :dark-mode="darkMode" @click="handleCreditTypeCreated">Create Credit</custom-button>
        </div>
    </div>
</template>
<script>
import {useCreditManagementStore} from "../../../../../stores/credit/credit-management.store";
import CustomInlineInput from "../../../Shared/components/CustomInlineInput.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import useErrorHandler from "../../../../../composables/useErrorHandler";
import SimpleAlert from "../../../Shared/components/SimpleAlert.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store";
const simpleIcon = useSimpleIcon();

export default {
    name: "CreateCreditType",
    components: {SimpleIcon, SimpleAlert, LoadingSpinner, CustomButton, ToggleSwitch, CustomInlineInput},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            creditStore: useCreditManagementStore(),
            permissionsStore: useRolesPermissions(),
            errorHandler: useErrorHandler(),
            simpleIcon,
            loading: false,
            showAlert: false,
        }
    },
    computed: {
        canCreateCreditType() {
            return this.permissionsStore.hasPermission(PERMISSIONS.PERMISSION_BILLING_CREDITS_TYPES_CREATE);
        }
    },
    methods: {
        async handleCreditTypeCreated() {
            this.loading = true;

            try {
                const resp = await this.creditStore.addCreditType();
                if (resp.data.data.status === true) {
                    this.creditStore.resetNewCreditType();
                }
            } catch (error) {
                this.errorHandler.handleError(error, 'Validation error')
                console.error(error)
                this.showAlert = true;

                setTimeout(() => {
                    this.showAlert = false;
                }, 4000)
            }

            this.loading = false;
        }
    }
}
</script>
