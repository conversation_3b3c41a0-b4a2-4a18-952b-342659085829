<template>
    <simple-table
        title="Credit Management"
        :dark-mode="darkMode"
        :data="creditStore.creditTypes"
        :headers="tableHeaders"
        :loading="loading"
        no-pagination
    >
        <template v-slot:row.col.consumption_order="{ value, item, rowIndex }">
            <div class="flex justify-start items-center gap-4 text-primary-500">
                <div v-if="canUpdateCreditTypes" class="flex flex-col">
                    <simple-icon v-show="rowIndex !== 0" clickable @click="creditStore.increasePriority(rowIndex)" :icon="simpleIcon.icons.CHEVRON_UP" :dark-mode="darkMode"/>
                    <simple-icon v-show="rowIndex !== creditStore.creditTypes.length - 1" clickable @click="creditStore.decreasePriority(rowIndex)" :icon="simpleIcon.icons.CHEVRON_DOWN" :dark-mode="darkMode"/>
                </div>
                <div>{{ rowIndex + 1 }}</div>
            </div>
        </template>
        <template v-slot:row.col.name="{ value, item, rowIndex }">
            <div class="truncate text-sm">
                {{value}}
            </div>
        </template>
        <template v-slot:row.col.description="{ value, item, rowIndex }">
            <div class="truncate text-sm">
                {{value}}
            </div>
        </template>
        <template v-slot:row.col.cash="{ value, item, rowIndex }">
            <toggle-switch :disabled="!canUpdateCreditTypes" v-model="creditStore.creditTypes[rowIndex].cash" :dark-mode="darkMode"></toggle-switch>
        </template>
        <template v-slot:row.col.active="{ value, item, rowIndex }">
            <toggle-switch :disabled="!canUpdateCreditTypes" v-model="creditStore.creditTypes[rowIndex].active" :dark-mode="darkMode"></toggle-switch>
        </template>
    </simple-table>
</template>
<script>
import {useCreditManagementStore} from "../../../../../stores/credit/credit-management.store";
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store";
const simpleIcon = useSimpleIcon();
export default {
    name: "CreditTypeManagementTable",
    components: {ToggleSwitch, SimpleIcon, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        loading: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            creditStore: useCreditManagementStore(),
            permissionStore: useRolesPermissions(),
            simpleIcon,
            tableHeaders: [
                {title: "Consumption Order", field: "consumption_order", cols: 1},
                {title: "Name", field: "name", cols: 1},
                {title: "Description", field: "description", cols: 2},
                {title: "Line item text", field: "line_item_text", cols: 1},
                {title: "Expires in days", field: "expires_in_days", cols: 1},
                {title: "Cash", field: "cash", cols: 1},
                {title: "Active", field: "active", cols: 1}
            ],
        }
    },
    computed: {
        canUpdateCreditTypes() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_BILLING_CREDITS_TYPES_UPDATE);
        }
    }
}
</script>
