<template>
    <div class="p-3 flex flex-col">
        <div class="font-bold text-lg mb-2" :class="[darkMode ? 'text-primary-500' : '']">
            Billing Summary
        </div>
        <div class="grid sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-2">
            <gross-volume-summary-card class="col-span-2" :dark-mode="darkMode"/>
            <div>
                <total-billed-invoices-summary-card :dark-mode="darkMode" class="pb-3 border-b" :class="[darkMode ? 'border-dark-border' : '']"/>
                <pending-invoices-summary-card :dark-mode="darkMode"/>
            </div>
        </div>
    </div>
</template>
<script>
import GrossVolumeSummaryCard from "./BillingSummaryCards/GrossVolumeSummaryCard.vue";
import PendingInvoicesSummaryCard from "./BillingSummaryCards/PendingInvoicesSummaryCard.vue";
import TotalBilledInvoicesSummaryCard from "./BillingSummaryCards/TotalBilledInvoicesSummaryCard.vue";

export default {
    name: "BillingSummary",
    components: {TotalBilledInvoicesSummaryCard, PendingInvoicesSummaryCard, GrossVolumeSummaryCard},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
}
</script>
