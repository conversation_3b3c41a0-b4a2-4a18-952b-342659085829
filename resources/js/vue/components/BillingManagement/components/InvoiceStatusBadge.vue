<template>
    <badge :color="statusColor" :dark-mode="darkMode">
        {{ statusTitle }}
    </badge>
</template>
<script>
import useInvoiceHelper from "../../../../composables/useInvoiceHelper";
import Badge from "../../Shared/components/Badge.vue";

const invoiceHelper = useInvoiceHelper()

export default {
    name: "InvoiceStatusBadge",
    components: {Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        status: {
            type: String,
            default: null,
        }
    },
    data() {
        return {
            invoiceHelper,
        }
    },
    computed: {
        statusResource() {
            return this.invoiceHelper.getStatusStyle(this.status)
        },
        statusTitle() {
            return this.statusResource?.title ?? this.status
        },
        statusColor() {
            return this.statusResource?.color ?? 'blue'
        }
    }
}
</script>
