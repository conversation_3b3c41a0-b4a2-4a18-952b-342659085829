<template>
    <div>
        <billing-card-wrapper
            variant="graph"
            :dark-mode="darkMode"
            :loading="loading"
        >
            <billing-card-line-chart-content
                v-if="!loading && chartData.datasets.length > 0"
                :dark-mode="darkMode"
                :chart-data="chartData"
                :summary-data="summaryData"
            />
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import BillingCardLineChartContent from "../BillingCardBuilders/BillingCardLineChartContent.vue";
import {useBillingManagementStore} from "../../../../../stores/billing/billing-management.store";
import BillingCardBarChartContent from "../BillingCardBuilders/BillingCardBarChartContent.vue";
import {useColorUtils} from "../../../../../composables/useColorUtils.js";


const colorUtils = useColorUtils();

export default {
    name: "GrossVolumeSummaryCard",
    components: {BillingCardBarChartContent, BillingCardLineChartContent, BillingCardWrapper},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            chartData: {
                labels: [],
                datasets: []
            },
            summaryData: [],
            billingStore: useBillingManagementStore()
        }
    },
    created() {
        this.getGrossVolumeSummary();
        this.billingStore.addPeriodUpdateEventListener('gross-volume-summary', this.getGrossVolumeSummary)
    },
    methods: {
        async getGrossVolumeSummary() {
            this.loading = true;
            const response = await this.billingStore.api.getGrossVolumeSummary()
            if (response.data.data.status) {
                this.chartData.labels = response.data.data.data.labels
                this.chartData.datasets = response.data.data.data.datasets.map((e, idx) => {
                    const color = colorUtils.getColorInSequence(idx);

                    return {
                        ...e,
                        borderColor: colorUtils.getLightColor(color),
                        backgroundColor: colorUtils.getDarkColor(color),
                    };
                })
                this.summaryData = response.data.data.data.summary;
            }
            this.loading = false;
        },
    }
}
</script>
