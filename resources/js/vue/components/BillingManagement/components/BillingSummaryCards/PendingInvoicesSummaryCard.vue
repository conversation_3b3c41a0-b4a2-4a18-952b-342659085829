<template>
    <div>
        <billing-card-wrapper
            heading="Pending Invoices"
            :dark-mode="darkMode"
            :loading="loading"
            variant="naked"
        >
            <template v-slot:headerAction>
                <a class="text-primary-500 text-xs font-semibold cursor-pointer" @click="handleViewMoreClicked">View</a>
            </template>
            <div class="flex flex-col pl-3">
                <div class="font-bold text-lg">
                    {{balance}}
                </div>
                <div class="font-semibold text-xs text-slate-500">
                    Deposited {{depositedAt}}
                </div>
            </div>
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import {useBillingManagementStore} from "../../../../../stores/billing/billing-management.store";
import useInvoiceHelper from "../../../../../composables/useInvoiceHelper";
const invoiceHelper = useInvoiceHelper()

export default {
    name: "PendingInvoicesSummaryCard",
    components: {BillingCardWrapper},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            balance: 123551.00,
            depositedAt: 'Feb 25',
            billingStore: useBillingManagementStore(),
            invoiceHelper,
        }
    },
    created() {
        this.getPayouts();
        this.billingStore.addPeriodUpdateEventListener('payout-summary', this.getPayouts)
    },
    methods: {
        async getPayouts() {
            this.loading = true;
            const response = await this.billingStore.api.getTotalInvoiceValueByStatus({'status': this.invoiceHelper.STATUSES.ISSUED})
            this.balance = response.data.data.data.total;
            this.loading = false;
        },
        handleViewMoreClicked() {

        }
    }
}
</script>
