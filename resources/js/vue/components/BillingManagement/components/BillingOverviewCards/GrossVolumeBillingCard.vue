<template>
    <div>
        <billing-card-wrapper
            heading="Gross Volume"
            variant="graph"
            :dark-mode="darkMode"
            :title-badge="badgeVal"
            :loading="loading"
        >
            <template v-slot:headerAction>
                <simple-icon clickable @click="handleViewMoreClicked" :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.INFORMATION_CIRCLE"></simple-icon>
            </template>
            <billing-card-bar-chart-content
                v-if="!loading && statistics.length > 0"
                :dark-mode="darkMode"
                :column-labels="labels"
                :statistics="statistics"
                secondary-insight="$0.0 period"
            />
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import BillingCardBarChartContent from "../BillingCardBuilders/BillingCardBarChartContent.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
import {useBillingManagementStore} from "../../../../../stores/billing/billing-management.store";
import BillingCardPaginatedContent from "../BillingCardBuilders/BillingCardPaginatedContent.vue";

const simpleIcon = useSimpleIcon()


export default {
    name: "GrossVolumeBillingCard",
    components: {BillingCardPaginatedContent, SimpleIcon, BillingCardBarChartContent, BillingCardWrapper},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            simpleIcon,
            labels: [
                'Testing 1',
                'Another'
            ],
            statistics: [
                152,
                80
            ],
            badgeVal: '0.0%',
            loading: false,
            billingStore: useBillingManagementStore(),
        }
    },
    created() {
        this.getGrossVolumeBilling();
        this.billingStore.addPeriodUpdateEventListener('gross-volume', this.getGrossVolumeBilling)
    },
    methods: {
        async getGrossVolumeBilling() {
            this.loading = true;
            const response = await this.billingStore.api.getGrossVolumeBilling();
            this.loading = false;
        },
        handleViewMoreClicked() {

        }
    }
}
</script>
