<template>
    <div>
        <billing-card-wrapper
            heading="Failed Payments"
            :dark-mode="darkMode"
            :loading="loading"
        >
            <billing-card-paginated-content
                v-if="!loading && failedPayments.length > 0"
                :dark-mode="darkMode"
                :data="failedPayments"
                :headers="headers"
                :pagination-data="paginationData"
                @page-change="handlePageChange"
            >
                <template v-slot:cost="{value}">
                    <div class="text-red-450">
                        ${{value}}
                    </div>
                </template>
            </billing-card-paginated-content>
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import BillingCardPaginatedContent from "../BillingCardBuilders/BillingCardPaginatedContent.vue";
import {useBillingManagementStore} from "../../../../../stores/billing/billing-management.store";

export default {
    name: "FailedPaymentsBillingCard",
    components: {BillingCardPaginatedContent, BillingCardWrapper},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            paymentPayload: {},
            paginationData: null,
            page: 1,
            perPage: 3,
            failedPayments: null,
            loading: false,
            headers: [
                {name: "ID", field: "id"},
                {name: "Invoice Id", field: "invoice_id"},
                {name: "Cost", field: "cost"},
            ],
            billingFilters: {},
            billingStore: useBillingManagementStore()
        }
    },
    created() {
        this.getBilling();
        this.billingStore.addPeriodUpdateEventListener( 'failed-payments', this.getBilling)
    },
    methods: {
        async getBilling() {
            this.loading = true
            const response = await this.billingStore.api.getFailedPayments(this.parseParams())
            const { data, links, meta} = response.data
            this.failedPayments = data ?? []
            this.paginationData = { links, ...meta}
            this.loading = false
        },
        handlePageChange({ newPage }){
            if (this.page === newPage) return

            this.page = newPage
            this.getBilling()
        },
        parseParams(){
            const parsedFilters = {...this.billingFilters}

            return {
                perPage: this.perPage,
                page: this.page,
                ...parsedFilters,
            }
        },
    },

}
</script>
