<template>
    <div>
        <billing-card-wrapper
            heading="Payments"
            :dark-mode="darkMode"
            :loading="loading"
        >
            <billing-card-totals-content
                v-if="!loading && paymentPayload.items.length > 0"
                :dark-mode="darkMode"
                :data="paymentPayload"
                total-title="Total Payments"
            >
            </billing-card-totals-content>
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardTotalsContent from "../BillingCardBuilders/BillingCardTotalsContent.vue";
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import {useBillingManagementStore} from "../../../../../stores/billing/billing-management.store";
import BillingCardBarChartContent from "../BillingCardBuilders/BillingCardBarChartContent.vue";

export default {
    name: "PaymentsBillingCard",
    components: {BillingCardBarChartContent, Billing<PERSON><PERSON><PERSON>rapper, Billing<PERSON><PERSON><PERSON><PERSON><PERSON>Content},
    props: {
        darkMode: {
            type: <PERSON><PERSON><PERSON>,
            default: false
        }
    },
    data() {
        return {
            paymentPayload: {
                total: 0,
                items: []
            },
            billingStore: useBillingManagementStore(),
            loading: false,
        }
    },
    created() {
        this.getPayments();
        this.billingStore.addPeriodUpdateEventListener('payments-billing', this.getPayments)

    },
    methods: {
        async getPayments() {
            this.loading = true;
            const response = await this.billingStore.api.getPayments();

            if (response.data.data.status) {
                this.paymentPayload.items = response.data.data.data.data;
                this.paymentPayload.total = response.data.data.data.total;
            }
            this.loading = false;
        }
    }
}
</script>
