<template>
    <div>
        <billing-card-wrapper
            heading="Chargebacks and Disputes"
            :dark-mode="darkMode"
            :loading="loading"
        >
            <billing-card-paginated-content
                v-if="!loading && failedPayments.length > 0"
                :dark-mode="darkMode"
                :data="failedPayments"
                :visible-fields="failedPaymentFields"
                :pagination-data="paginationData"
                @page-change="handlePageChange"
            >
                <template v-slot:cost="{value}">
                    <div class="text-red-450">
                        ${{value}}
                    </div>
                </template>
            </billing-card-paginated-content>
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import BillingCardPaginatedContent from "../BillingCardBuilders/BillingCardPaginatedContent.vue";
import {useBillingManagementStore} from "../../../../../stores/billing/billing-management.store";

export default {
    name: "ChargebacksAndDisputesBillingCard",
    components: {BillingCardPaginatedContent, BillingCardWrapper},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            paymentPayload: {},
            paginationData: null,
            page: 1,
            perPage: 3,
            failedPayments: null,
            loading: false,
            failedPaymentFields: [
                'id',
                'bundle_name',
                'status',
                'cost'
            ],
            billingFilters: {},
            billingStore: useBillingManagementStore()
        }
    },
    created() {
        this.getChargebacksAndDisputes();
        this.billingStore.addPeriodUpdateEventListener( 'chargebacks-disputes', this.getChargebacksAndDisputes)
    },
    methods: {
        async getChargebacksAndDisputes() {
            this.loading = true
            const response = await this.billingStore.api.getChargebacksAndDisputes(this.parseParams())
            const { data, links, meta} = response.data
            this.failedPayments = data ?? []
            this.paginationData = { links, ...meta}
            this.loading = false
        },
        handlePageChange({ newPage }){
            if (this.page === newPage) return

            this.page = newPage
            this.getChargebacksAndDisputes()
        },
        parseParams(){
            const parsedFilters = {...this.billingFilters}

            return {
                perPage: this.perPage,
                page: this.page,
                ...parsedFilters,
            }
        },
    },
}
</script>
