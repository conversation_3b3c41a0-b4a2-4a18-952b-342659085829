<template>
    <div>
        <billing-card-wrapper
            heading="Bundles Sold"
            :loading="loading"
            :dark-mode="darkMode"
            variant="graph"
        >
            <template v-slot:headerAction>
                <a class="text-primary-500 text-xs font-semibold cursor-pointer" @click="handleViewMoreClicked">View more</a>
            </template>
            <billing-card-bar-chart-content
                v-if="!loading && datasets.length > 0"
                :dark-mode="darkMode"
                :column-labels="labels"
                :primary-insight="primaryInsight"
                :secondary-insight="secondaryInsight"
                :datasets="datasets"
                x-title="Number of Bundles"
                y-title="Bundle Type"
            />
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import BillingCardBarChartContent from "../BillingCardBuilders/BillingCardBarChartContent.vue";
import {useBillingManagementStore} from "../../../../../stores/billing/billing-management.store";
import BillingCardTotalsContent from "../BillingCardBuilders/BillingCardTotalsContent.vue";
export default {
    name: "BundlesSoldBillingCard",
    components: {BillingCardTotalsContent, BillingCardBarChartContent, BillingCardWrapper},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            labels: [],
            datasets: [
                {
                    label: "Bundles Sold",
                    data: [],
                    minBarLength: 10,
                    backgroundColor: [
                        'rgba(245,157,11)',
                        'rgba(0,129,255)',
                        'rgba(0,48,96)',
                    ]
                },
            ],
            primaryInsight: "",
            secondaryInsight: "Total value",
            loading: false,
            billingStore: useBillingManagementStore(),
        }
    },
    created() {
        this.getBundlesSold();
        this.billingStore.addPeriodUpdateEventListener('bundles-sold', this.getBundlesSold)

    },
    methods: {
        async getBundlesSold() {
            this.loading = true;
            const response = await this.billingStore.api.getBundlesSold()
            if (response.data.data.status) {
                this.labels = response.data.data.data.labels
                this.datasets[0].data = response.data.data.data.dataset
                this.primaryInsight = response.data.data.data.total
            }
            this.loading = false;
        },
        handleViewMoreClicked() {

        }
    }
}
</script>
