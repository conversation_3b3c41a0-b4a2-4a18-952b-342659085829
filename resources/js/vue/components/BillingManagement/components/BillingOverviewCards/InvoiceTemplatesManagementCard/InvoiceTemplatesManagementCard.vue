<template>
    <div>
        <invoice-template-management-modal
            v-if="showEditOrCreateInvoiceTemplateModal"
            :dark-mode="darkMode"
            @close="handleModalClose"
        />
        <field-deletion-confirmation-modal
            v-if="showDeleteModal"
            :selected-field-name="invoiceTemplatesManagementStore.selectedInvoiceTemplate.name"
            @choice="handleDelete"
        />
        <billing-card-wrapper
            heading="Invoice Templates"
            :dark-mode="darkMode"
            :loading="invoiceTemplatesManagementStore.loadingList"
        >
            <template v-slot:headerAction>
                <simple-icon
                    class="mr-1"
                    clickable
                    :size="simpleIcon.sizes.SM"
                    @click="openEditInvoiceTemplateModal()"
                    :icon="simpleIcon.icons.PLUS"
                    :color="simpleIcon.colors.BLUE"
                />
            </template>
            <billing-card-paginated-content
                v-if="!invoiceTemplatesManagementStore.loadingList"
                :dark-mode="darkMode"
                :data="invoiceTemplatesManagementStore.templates"
                :headers="headers"
                client-paginate
                :pagination-data="invoiceTemplatesManagementStore.paginationData"
                @page-change="handlePageChange"
                no-data-message="No templates created yet"
            >
                <template v-slot:model="{item}">
                    <p v-if="!item.is_global">{{ item.model_type_name }} {{ item?.model_data?.name ?? item.model_id }}</p>
                    <p v-else>Global</p>
                </template>
                <template v-slot:actions="{item}">
                    <div class="flex items-center gap-1">
                        <simple-icon
                            clickable
                            :size="simpleIcon.sizes.SM"
                            @click="openEditInvoiceTemplateModal(item)"
                            :icon="simpleIcon.icons.PENCIL_SQUARE"
                            :color="simpleIcon.colors.BLUE"
                        />
                        <simple-icon
                            clickable
                            :size="simpleIcon.sizes.SM"
                            @click="openDeleteTemplateModal(item)"
                            :icon="simpleIcon.icons.BIN"
                            :color="simpleIcon.colors.RED"
                        />
                    </div>
                </template>
            </billing-card-paginated-content>
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../../BillingCardBuilders/BillingCardWrapper.vue";
import BillingCardPaginatedContent from "../../BillingCardBuilders/BillingCardPaginatedContent.vue";
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../../composables/useSimpleIcon";
import useArrayHelper from "../../../../../../composables/useArrayHelper";
import {useInvoiceTemplatesManagementStore} from "../../../../../../stores/billing/invoice-templates-management-store";
import InvoiceTemplateManagementModal
    from "../../Modals/InvoiceTemplateManagementModal/InvoiceTemplateManagementModal.vue";
import FieldDeletionConfirmationModal
    from "../../../../IndustryManagement/ConfigurableFields/components/FieldDeletionConfirmationModal.vue";

export default {
    name: "InvoiceTemplatesManagementCard",
    components: {
        FieldDeletionConfirmationModal,
        InvoiceTemplateManagementModal,
        SimpleIcon,
        BillingCardPaginatedContent,
        BillingCardWrapper
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            simpleIcon: useSimpleIcon(),
            paginationData: null,
            headers: [
                {name: 'Name', field: 'name'},
                {name: 'Model', field: 'model'},
                {name: 'Actions', field: 'actions'},
            ],
            invoiceTemplatesManagementStore: useInvoiceTemplatesManagementStore(),
            showEditOrCreateInvoiceTemplateModal: false,
            showDeleteModal: false,
        }
    },
    created() {
        this.invoiceTemplatesManagementStore.getTemplates(this.page)
    },
    methods: {
        ...useArrayHelper(),

        async handleDelete(choice) {
            if (choice) {
                await this.invoiceTemplatesManagementStore.deleteInvoice()
            } else {
                this.invoiceTemplatesManagementStore.setSelectedInvoiceTemplate(null)
            }

            this.showDeleteModal = false
        },

        openDeleteTemplateModal(item) {
            this.showDeleteModal = true
            this.invoiceTemplatesManagementStore.setSelectedInvoiceTemplate(item)
        },

        openEditInvoiceTemplateModal(invoiceTemplate = null) {
            this.showEditOrCreateInvoiceTemplateModal = true
            this.invoiceTemplatesManagementStore.setSelectedInvoiceTemplate(invoiceTemplate)
        },

        handleModalClose() {
            this.showEditOrCreateInvoiceTemplateModal = false
            this.invoiceTemplatesManagementStore.setSelectedInvoiceTemplate(false)
            this.invoiceTemplatesManagementStore.getTemplates()
        },

        handlePageChange(newPage) {
            this.invoiceTemplatesManagementStore.getTemplates(newPage)
        },
    },
}
</script>
