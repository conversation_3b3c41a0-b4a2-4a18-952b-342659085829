<template>
    <div :class="[darkMode ? darkWrapperStyle : lightWrapperStyle]">
        <div v-if="heading || titleBadge" class="flex pl-3 pt-3 justify-between items-center text-center">
            <div class="flex gap-2 items-center">
                <h5 class="text-xs uppercase text-primary-500 font-bold leading-tight">{{ heading }}</h5>
                <Badge v-if="titleBadge" :dark-mode="darkMode">
                    {{titleBadge}}
                </Badge>
            </div>
            <div class="flex gap-2 pr-2">
                <slot name="headerAction">
                </slot>
            </div>
        </div>
        <loading-spinner v-if="loading" :dark-mode="darkMode"/>
        <slot v-else>
            <div class="flex justify-center items-center text-slate-500 h-20">
                No Data
            </div>
        </slot>
    </div>
</template>
<script>
import Badge from "../../../Shared/components/Badge.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";

export default {
    name: "BillingCardWrapper",
    components: {LoadingSpinner, SimpleIcon, Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        heading: {
            type: String,
            default: null
        },
        variant: {
            type: String,
            default: "default"
        },
        titleBadge: {
            type: String,
            default: null,
        },
        loading: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        lightWrapperStyle() {
            const styles = {
                default: "border rounded-md overflow-hidden ",
                graph: "rounded-md border bg-light-background",
                wide: "border rounded-md overflow-hidden col-span-2",
                naked: "",
                light: "border rounded-md overflow-hidden bg-light-module"
            }

            return styles[this.variant] ?? styles.default
        },
        darkWrapperStyle() {
            const styles = {
                default: "border rounded-md overflow-hidden bg-dark-background border-dark-border",
                graph: "rounded-md border bg-dark-background border-dark-border",
                wide: "border rounded-md overflow-hidden col-span-2",
                naked: "",
                light: "border rounded-md overflow-hidden border-dark-border bg-dark-module",
            }

            return styles[this.variant] ?? styles.default
        }
    }
}
</script>
