<template>
    <div class="flex p-6">
        <div v-if="summaryData" class="flex flex-col mr-4 mb-4">
            <div class="text-primary-500 uppercase text-sm font-bold mb-2">
                {{summaryData.title}}
            </div>
            <div class="grid grid-cols-2 gap-4 pb-4 border-b">
                <div v-for="item in summaryData.data" class="flex flex-col">
                    <div class="font-bold">
                        {{Intl.NumberFormat('en-US', {style: 'currency', currency: 'USD'}).format(item.value)}}
                    </div>
                    <div class="font-semibold text-slate-500 text-xs" v-if="item?.additional">
                        {{ item.additional }}
                    </div>
                </div>
            </div>
        </div>
        <line-chart
            class="flex-1"
            chart-options="noGrid"
            :chart-data="chartData"
            :height="200"
        />
    </div>
</template>
<script>
import LineChart from "../../../Shared/components/LineChart.vue";

export default {
    name: "BillingCardLineChartContent",
    components: {LineChart},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        chartData: {
            type: Object,
            required: true,
        },
        summaryData: {
            type: Array,
            default: null
        },
    },
}
</script>
