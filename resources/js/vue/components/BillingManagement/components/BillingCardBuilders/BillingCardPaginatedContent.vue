<template>
    <div class="flex flex-col">
        <div
            v-if="headers.length > 0"
            class="border-b px-3 py-2 text-xs uppercase font-semibold leading-tight text-slate-500"
            :class="[colCount, darkMode ? 'border-dark-border' : '']"
        >
            <div class="last:text-right" v-for="header in headers" :class="`col-span-${header.cols ?? 1}`"> {{ header.name }}</div>
        </div>
        <div v-if="data.length > 0">
            <div v-for="item in data"
                 class="border-b p-3 text-xs uppercase font-bold leading-tight"
                 :class="[colCount, darkMode ? 'border-dark-border' : '']"
            >
                <div v-for="visibleField in visibleFields" class="flex last:justify-end" :class="`col-span-${visibleField.cols ?? 1}`">
                    <slot :name="visibleField.field" v-bind="{value: item[visibleField.field], item}">
                        {{ item[visibleField.field] }}
                    </slot>
                </div>
            </div>
        </div>
        <div v-else class="flex justify-center p-2">
            {{ noDataMessage }}
        </div>
        <div class="flex justify-end p-2">
            <simple-pagination
                v-if="paginationData?.total > 0"
                @page-change="(newPage) => $emit('page-change', newPage)"
                :dark-mode="darkMode"
                :page="paginationData.page"
                :per-page="paginationData.perPage"
                :total="paginationData.total"
            >
                <template v-slot:prev>
                    <simple-icon :icon="simpleIcon.icons.CHEVRON_LEFT"></simple-icon>
                </template>
                <template v-slot:next>
                    <simple-icon :icon="simpleIcon.icons.CHEVRON_RIGHT"></simple-icon>
                </template>
            </simple-pagination>
        </div>
    </div>
</template>
<script>
import Pagination from "../../../Shared/components/Pagination.vue";
import SimplePagination from "../SimplePagination.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon";

export default {
    name: "BillingCardPaginatedContent",
    components: {SimpleIcon, SimplePagination, Pagination},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        data: {
            type: Array,
            default: []
        },
        headers: {
            type: Array,
            default: []
        },
        paginationData: {
            type: Object,
            required: false,
        },
        clientPaginate: {
            type: Boolean,
            default: false,
        },
        noDataMessage: {
            type: String,
            default: 'No data'
        }
    },
    data() {
        return {
            simpleIcon: useSimpleIcon(),
        }
    },
    emits: ['page-change'],
    computed: {
        colCount() {
            const colCount = this.visibleFields.reduce((sum, item) => {
                return sum + (item.cols ?? 1);
            }, 0)
            return `grid grid-cols-${colCount}`;
        },

        visibleFields() {
            return this.headers.filter(obj =>
                obj.is_visible === true || obj.is_visible === undefined
            );
        }
    },
}
</script>
