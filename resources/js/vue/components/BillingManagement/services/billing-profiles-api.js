import axios from "axios";

export default class ApiService {
    defaultQuery = {}

    constructor(baseUrl, baseEndpoint, version, defaultQuery = {}) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
        this.defaultQuery = defaultQuery
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`,
            params: this.defaultQuery ?? {}
        }

        return axios.create(axiosConfig);
    }

    static make(defaultQuery) {
        return new ApiService('internal-api', 'billing/profiles', 1, defaultQuery);
    }

    getBillingProfiles(params = {}) {
        return this.axios().get('/', {
            params
        })
    }

    getBillingProfile(id) {
        return this.axios().get(`/${id}`)
    }

    updateBillingProfile(id, profileData) {
        return this.axios().put(`/${id}`, profileData)
    }

    createBillingProfile(data) {
        return this.axios().post(`/`, data)
    }

    getCompanyCampaigns(companyId, billingProfileId) {
        return this.axios().get('/company-campaigns', {
            params: {
                company_id: companyId,
                profile_id: billingProfileId,
            }
        })
    }

    archive(id) {
        return this.axios().patch(`/${id}/archive`)
    }

    restore(id) {
        return this.axios().patch(`/${id}/restore`)
    }
}
