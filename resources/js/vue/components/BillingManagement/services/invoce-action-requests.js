import axios from "axios";

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }


    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`,
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'billing/invoices/action-requests', 1);
    }

    getActionRequests(params = {}) {
        return this.axios().get('/', {
            params
        })
    }

    getActionRequest(id) {
        return this.axios().get(`/${id}`)
    }

    submitReview(requestId, data) {
        return this.axios().post(`/${requestId}/review`, data)
    }

    cancel(requestId) {
        return this.axios().patch(`/${requestId}/cancel`)
    }
}
