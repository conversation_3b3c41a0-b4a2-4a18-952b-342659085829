import axios from "axios";

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }


    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`,
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'billing', 1);
    }

    cancelInvoicePayment(invoicePaymentId) {
        return this.axios().post(`/payments/${invoicePaymentId}/cancel`)
    }

    makeInvoicePayment(invoiceId, data) {
        return this.axios().post(`invoices/${invoiceId}/payments`, data)
    }
}
