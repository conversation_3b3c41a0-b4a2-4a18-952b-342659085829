import axios from "axios";

export default class ApiService {
    defaultQuery = {}

    constructor(baseUrl, baseEndpoint, version, defaultQuery = {}) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
        this.defaultQuery = defaultQuery
    }


    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`,
            params: this.defaultQuery ?? {}
        }

        return axios.create(axiosConfig);
    }

    static make(defaultQuery) {
        return new ApiService('internal-api', 'billing/invoice-templates', 1, defaultQuery);
    }

    getInvoiceTemplates(params) {
        return this.axios().get('/', {
            params
        })
    }

    getInvoiceTemplateOptions(){
        return this.axios().get('/options')
    }

    getSearchableModels() {
        return this.axios().get('/searchable-models')
    }

    searchModels(modelType, query) {
        return this.axios().get('/model-search', {
            params: {
                model_type: modelType,
                query
            }
        })
    }

    create(data) {
        return this.axios().post('/', data)
    }

    update(id, data) {
        return this.axios().put(`/${id}`, data)
    }

    delete(id) {
        return this.axios().delete(`/${id}`)
    }

    previewInvoiceTemplate(templateId) {
        return this.axios().get(`/preview-template/${templateId}`)
    }
}
