<template>
    <div class="flex flex-col gap-2">
        <div class="flex gap-2 items-center text-sm font-semibold">
            <ul class="font-medium list-none">
                <li class="inline-flex items-center gap-1">
                    <div class="relative flex h-6 w-6 flex-none items-center justify-center bg-white">
                        <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300" />
                    </div>
                    Status change from
                    <span class="font-bold"> {{ invoiceHelper.getStatusStyle([invoiceActionRequest.payload?.arguments?.oldStatus]).title }} </span>
                    to
                    <span class="font-bold"> {{ invoiceHelper.getStatusStyle([invoiceActionRequest.payload?.arguments?.newStatus]).title }}</span>
                </li>
            </ul>
        </div>
        <div class="p-3 shadow-lg rounded-sm border-gray-200 border">
            <view-create-invoice-modal-content
                class="border rounded-sm border-gray-300"
                :invoice-id="invoiceActionRequest.related_id"
                :dark-mode="darkMode"
                :future-invoice-data="futureInvoiceData"
                readonly
            />
        </div>
    </div>
</template>
<script>

import InvoiceStatusBadge from "../../components/InvoiceStatusBadge.vue";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import ViewCreateInvoiceModalContent from "../../../Billing/ViewCreateInvoiceModalContent.vue";
import useInvoiceHelper from "../../../../../composables/useInvoiceHelper.js";
const invoiceHelper = useInvoiceHelper()

// TODO - Migrate to ActionSummary
export default {
    name: "UpdateInvoiceStatusActionDetail",
    components: {ViewCreateInvoiceModalContent, LabeledValue, InvoiceStatusBadge},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        invoiceActionRequest: {
            type: Object,
            default: {},
            required: true,
        }
    },

    data() {
        return {
            invoiceHelper,
            futureInvoiceData: {}
        }
    },

    created() {
        this.parseFutureInvoiceData()
    },

    methods: {
        parseFutureInvoiceData() {
            this.futureInvoiceData = {
                status: this.invoiceActionRequest.payload?.arguments?.newStatus
            }
        }
    }
}
</script>
