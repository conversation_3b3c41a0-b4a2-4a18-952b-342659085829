<template>
    <div class="flex flex-col gap-2">
        <div class="flex gap-2 items-center text-sm font-semibold">
            <ul class="font-medium list-none">
                <li class="inline-flex items-center gap-1">
                    <div class="relative flex h-6 w-6 flex-none items-center justify-center bg-white">
                        <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300" />
                    </div>
                    Issue a refund of
                    <span class="font-medium">${{ invoiceActionRequest.payload?.arguments?.refundObject?.total }}</span>
                    due to
                    <span class="font-medium">{{ invoiceActionRequest.payload?.arguments?.refundObject?.reason }}</span>
                </li>
            </ul>
        </div>
        <div class="p-3 shadow-lg rounded-sm border-gray-200 border">
            <view-create-invoice-modal-content
                class="border rounded-sm border-gray-300"
                :invoice-id="invoiceActionRequest.related_id"
                :dark-mode="darkMode"
                readonly
            />
        </div>
    </div>
</template>
<script>
import ViewCreateInvoiceModalContent from "../../../Billing/ViewCreateInvoiceModalContent.vue";

// TODO - Migrate to ActionSummary
export default {
    name: "IssueInvoiceRefund",
    components: {ViewCreateInvoiceModalContent},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        invoiceActionRequest: {
            type: Object,
            default: {},
            required: true,
        }
    },
}
</script>
