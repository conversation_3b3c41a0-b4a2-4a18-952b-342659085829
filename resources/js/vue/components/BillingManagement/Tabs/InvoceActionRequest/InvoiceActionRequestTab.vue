<template>
    <div>
        <simple-table
            v-model="invoiceRequestActionStore.filters"
            :dark-mode="darkMode"
            :data="invoiceRequestActionStore.data"
            :headers="headers"
            :loading="invoiceRequestActionStore.loading"
            :pagination-data="invoiceRequestActionStore.paginationData"
            :current-per-page="invoiceRequestActionStore.filters.per_page"
            title="Action Requests"
            @search="invoiceRequestActionStore.getRequestActions"
            @reset="handleReset"
            @page-change="handlePageChange"
            has-row-click
            @click-row="item => toggleInvoiceActionRequestModal(true, item)"
            :table-filters="tableFilters"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
        >
            <template v-slot:visible-filters>
                <company-search-autocomplete
                    v-model="invoiceRequestActionStore.filters.company_id"
                />
                <autocomplete
                    :dark-mode="darkMode"
                    v-model="invoiceRequestActionStore.filters.requested_by_id"
                    :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}"
                    :options="requesterOptions"
                    placeholder="Requester"
                    @search="searchRequester($event)"
                />
                <autocomplete
                    :dark-mode="darkMode"
                    v-model="invoiceRequestActionStore.filters.reviewed_by_id"
                    :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}"
                    :options="reviewerOptions"
                    placeholder="Reviewer"
                    @search="searchReviewer($event)"
                />
            </template>
            <template v-slot:row.col.requested_by="{value}">
                {{ value?.name ?? 'System' }}
            </template>
            <template v-slot:row.col.company="{item}">
                <entity-hyperlink
                    :dark-mode="darkMode"
                    type="company"
                    :entity-id="item.company.id"
                    :suffix="item.company.name"
                />
            </template>
            <template v-slot:row.col.reviewed_by="{value}">
                {{ value?.name }}
            </template>
            <template v-slot:row.col.status="{item}">
                <invoice-action-request-status-badge :status="item.status"/>
            </template>
        </simple-table>
        <view-create-invoice-modal
            v-if="showViewInvoiceModal"
            @close="toggleViewInvoiceModal(false)"
            :dark-mode="darkMode"
            :invoice-id="selectedInvoice.related_id"
        />
        <InvoiceActionRequestModal
            v-if="showViewInvoiceAction"
            @close="toggleInvoiceActionRequestModal(false)"
            :invoice-action-request="selectedInvoiceAction"
        >
        </InvoiceActionRequestModal>
    </div>
</template>
<script>
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import {useInvoiceRequestActionStore} from "../../../../../stores/billing/invoice-request-actions-store";
import ViewCreateInvoiceModal from "../../../Billing/ViewCreateInvoiceModal.vue";
import Badge from "../../../Shared/components/Badge.vue";
import InvoiceStatusBadge from "../../components/InvoiceStatusBadge.vue";
import InvoiceActionRequestModal from "./InvoiceActionRequestModal.vue";
import InvoiceActionRequestStatusBadge from "./InvoiceActionRequestStatusBadge.vue";
import Autocomplete from "../../../Shared/components/Autocomplete.vue";
import SharedApiService from "../../../Shared/services/api";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes";
import {SimpleTableFilterTypesEnum} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum";
import useInvoiceActionRequestHelper from "../../../../../composables/useInvoiceActionRequestHelper";
import useQueryParams from "../../../../../composables/useQueryParams";
import EntityHyperlink from "../../components/EntityHyperlink.vue";
import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";

const invoiceActionRequestHelper = useInvoiceActionRequestHelper();
const queryParamsHelper = useQueryParams();

export default {
    name: "InvoiceActionRequestTab",
    components: {
        CompanySearchAutocomplete,
        EntityHyperlink,
        Autocomplete,
        InvoiceActionRequestStatusBadge,
        InvoiceActionRequestModal,
        InvoiceStatusBadge,
        Badge,
        ViewCreateInvoiceModal,
        SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    created() {
        const {id, ...rest} = queryParamsHelper.getCurrentParams();

        if (id) {
            this.getRequestAndOpenModal(id)
            queryParamsHelper.setQueryParamsOnCurrentUrl(rest)
        }

        this.invoiceRequestActionStore.getRequestActions()
    },
    data() {
        return {
            invoiceRequestActionStore: useInvoiceRequestActionStore(),
            invoiceActionRequestHelper,
            headers: [
                {title: 'Company', field: 'company'},
                {title: 'Action', field: 'requested_action'},
                {title: 'Requested by', field: 'requested_by'},
                {title: 'Requested at', field: 'requested_at'},
                {title: 'Status', field: 'status'},
                {title: 'Reviewed by', field: 'reviewed_by'},
                {title: 'Reviewed at', field: 'reviewed_at'},
            ],
            reviewerOptions: [],
            requesterOptions: [],
            selectedInvoice: null,
            showViewInvoiceModal: false,
            selectedInvoiceAction: null,
            showViewInvoiceAction: false,
            sharedApi: SharedApiService.make(),
            tableFilters: [{
                type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                location: SimpleTableFilterTypesEnum.HIDDEN,
                field: 'status',
                title: 'Request Status',
                options: invoiceActionRequestHelper.getStatusesAsOptions()
            }]
        }
    },

    methods: {
        async getRequestAndOpenModal(id){
            const request = await this.invoiceRequestActionStore.getRequestAction(id);

            if (request) {
                this.toggleInvoiceActionRequestModal(true, request)
            }
        },
        handleReset() {
            this.invoiceRequestActionStore.resetFilters()
            this.invoiceRequestActionStore.getRequestActions()
        },

        toggleViewInvoiceModal(flag, data) {
            this.selectedInvoice = flag ? data : null
            this.showViewInvoiceModal = flag
        },

        toggleInvoiceActionRequestModal(flag, data) {
            this.selectedInvoiceAction = flag ? data : null
            this.showViewInvoiceAction = flag
        },
        searchAndAssignUsers(query, variable) {
            this.sharedApi.searchUserByNamesAndId(query).then(res => {
                this[variable] = res.data.data.users;
            })
        },
        searchRequester(query) {
            this.searchAndAssignUsers(query, 'requesterOptions')
        },

        searchReviewer(query) {
            this.searchAndAssignUsers(query, 'reviewerOptions')
        },
        handlePageChange({newPage}){
            this.invoiceRequestActionStore.setFilters({
                page: newPage
            })
            this.invoiceRequestActionStore.getRequestActions()
        },
    }
}
</script>
