<template>
    <badge :color="color">
        {{ title }}
    </badge>
</template>
<script>
import Badge from "../../../Shared/components/Badge.vue";
import useInvoiceActionRequestHelper from "../../../../../composables/useInvoiceActionRequestHelper";

const invoiceActionRequestHelper = useInvoiceActionRequestHelper();

export default {
    name: "InvoiceActionRequestStatusBadge",
    components: {Badge},
    props: {
        status: {
            type: String,
            required: true,
        }
    },
    computed: {
        title() {
            return invoiceActionRequestHelper.getStatusTitle(this.status)
        },
        color() {
            return {
                [invoiceActionRequestHelper.statuses.pending]: 'amber',
                [invoiceActionRequestHelper.statuses.approved]: 'green',
                [invoiceActionRequestHelper.statuses.rejected]: 'red',
                [invoiceActionRequestHelper.statuses.cancelled]: 'gray',
            }[this.status] ?? 'gray'
        }
    }
}
</script>
