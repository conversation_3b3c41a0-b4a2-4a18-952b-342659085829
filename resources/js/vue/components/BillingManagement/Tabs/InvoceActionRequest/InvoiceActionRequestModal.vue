<template>
    <modal
        :dark-mode="darkMode"
        @close="handleClose"
        @confirm="handleConfirm"
        container-classes=''
        confirm-text="Submit"
        :loading-confirmation="invoiceRequestActionStore.loadingReviewSubmission"
        no-buttons
    >
        <template v-slot:header>
            <div class="font-bold">
                Request #{{ invoiceActionRequest.id }}
            </div>
        </template>
        <template v-slot:content>
            <div class="container flex flex-col gap-2 relative">
                <div class="overflow-auto flex-col flex gap-4 p-10">
                    <div
                        class="grid grid-cols-4 gap-2 -lg border shadow p-6 rounded-lg shadow-primary-500/10 bg-grey-50"
                        :class="[
                        computedBorderStyle
                    ]">
                        <labeled-value
                            class="font-medium"
                            label="Company"
                        >
                            <entity-hyperlink
                                type="company"
                                :entity-id="invoiceActionRequest?.company?.id"
                                :dark-mode="darkMode"
                                :prefix="invoiceActionRequest?.company?.name"
                            />
                        </labeled-value>
                        <labeled-value
                            class="font-medium"
                            label="Action"
                            :value="invoiceActionRequest.requested_action"
                        />
                        <labeled-value label="Request">
                            <div class="flex flex-col gap-1">
                                <div class="flex gap-1 items-center">
                                    <simple-icon :icon="simpleIcon.icons.REQUESTER" tooltip="Requester"/>
                                    <p class="font-semibold truncate">{{ invoiceActionRequest.requested_by?.name }}</p>
                                </div>
                                <div class="flex gap-1 items-center">
                                    <simple-icon :icon="simpleIcon.icons.CALENDAR" tooltip="Requested at"/>
                                    <p>{{ invoiceActionRequest.requested_at }}</p>
                                </div>
                            </div>
                        </labeled-value>
                        <labeled-value v-if="invoiceActionRequest.reviewed_at" label="Review">
                            <div class="flex flex-col gap-1">
                                <div class="flex gap-1 items-center">
                                    <simple-icon :icon="simpleIcon.icons.REVIEWER" tooltip="Reviewer"/>
                                    <p class="font-semibold truncate">{{ invoiceActionRequest.reviewed_by?.name }}</p>
                                </div>
                                <div class="flex gap-1 items-center">
                                    <simple-icon :icon="simpleIcon.icons.CALENDAR" tooltip="Reviewed at"/>
                                    <p>{{ invoiceActionRequest.reviewed_at }}</p>
                                </div>
                            </div>
                        </labeled-value>
                        <labeled-value class="font-medium" label="Status">
                            <invoice-action-request-status-badge :status="invoiceActionRequest.status"/>
                        </labeled-value>
                        <labeled-value v-if="invoiceActionRequest.note" label="Note" class="font-medium col-span-full">
                            <CustomTextarea v-model="invoiceActionRequest.note" disabled/>
                        </labeled-value>
                    </div>
                    <div class="flex flex-col gap-2">
                        <p class="font-semibold text-lg">Requested Changes</p>
                        <component
                            v-if="invoiceActionRequestDetailComponent"
                            :is="invoiceActionRequestDetailComponent"
                            :invoice-action-request="invoiceActionRequest"
                            :approval="invoiceActionRequest"
                        >
                        </component>
                    </div>
                </div>
                <div
                    class="sticky bottom-0 left-0 w-full h-75 flex flex-col gap-3 px-8 pt-5 pb-4 bg-grey-50 border rounded-b-lg shadow-lg z-10">
                    <div v-if="showActions">
                        <div class="flex gap-6 w-full">
                            <div
                                v-for="action in actions"
                                @click="handleActionSelect(action.slug)"
                                class="p-2 gap-6 flex justify-center text-center items-center font-bold text-white flex-1 cursor-pointer rounded-sm mb-2"
                                :class="[reviewStatus === action.slug ? action.color : `hover:${action.color} hover:opacity-25 bg-gray-400`]"
                            >
                                <simple-icon :dark-mode="darkMode" :icon="action.icon"
                                             :color="simpleIcon.colors.WHITE"/>
                                <div>
                                    {{ action.title }}
                                </div>
                            </div>
                        </div>
                        <CustomTextarea v-model="reviewReason" placeholder="Reason..."/>
                        <div class="flex justify-end">
                            <custom-button :dark-mode="darkMode" @click="handleConfirm">Submit
                            </custom-button>
                        </div>
                    </div>
                    <div v-else-if="invoiceActionRequest.reason">
                        <labeled-value label="Reason">
                            <p>{{invoiceActionRequest.reason}}</p>
                        </labeled-value>
                    </div>
                </div>
            </div>
        </template>
    </modal>
</template>
<script>
import Modal from "../../../Shared/components/Modal.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import UpdateInvoiceStatusActionDetail from "./UpdateInvoiceStatusActionDetail.vue";
import Badge from "../../../Shared/components/Badge.vue";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import CustomTextarea from "../../../Shared/components/CustomTextarea.vue";
import {useInvoiceRequestActionStore} from "../../../../../stores/billing/invoice-request-actions-store";
import InvoiceActionRequestStatusBadge from "./InvoiceActionRequestStatusBadge.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";

const simpleIcon = useSimpleIcon()
import {useInvoiceModalStore} from "../../../../../stores/invoice/invoice-modal.store.js";
import IssueInvoiceToCollections from "./IssueInvoiceToCollections.vue";
import WriteOffInvoice from "./WriteOffInvoice.vue";
import IssueInvoiceRefund from "./IssueInvoiceRefund.vue";
import {markRaw} from "vue";
import AllocateCreditToCompanySummary
    from "../../../Shared/components/DisplayPendingActions/ActionSummary/AllocateCreditToCompanySummary.vue";
import ExpireCompanyCreditSummary
    from "../../../Shared/components/DisplayPendingActions/ActionSummary/ExpireCompanyCreditSummary.vue";
import ExtendCompanyCreditSummary
    from "../../../Shared/components/DisplayPendingActions/ActionSummary/ExtendCompanyCreditSummary.vue";
import SaveBillingProfileSummary
    from "../../../Shared/components/DisplayPendingActions/ActionSummary/SaveBillingProfileSummary.vue";
import MakeInvoicePayment from "../../../Shared/components/DisplayPendingActions/ActionSummary/MakeInvoicePayment.vue";
import EntityHyperlink from "../../components/EntityHyperlink.vue";
import ArchiveBillingProfileSummary
    from "../../../Shared/components/DisplayPendingActions/ActionSummary/ArchiveBillingProfileSummary.vue";
import RestoreBillingProfileSummary
    from "../../../Shared/components/DisplayPendingActions/ActionSummary/RestoreBillingProfileSummary.vue";

export default {
    name: "InvoiceActionRequestModal",
    components: {
        EntityHyperlink,
        SimpleIcon, InvoiceActionRequestStatusBadge, CustomTextarea, LabeledValue, Badge, CustomButton, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        invoiceActionRequest: {
            type: Object,
            default: {},
            required: true,
        }
    },
    data() {
        return {
            simpleIcon,
            invoiceRequestActionStore: useInvoiceRequestActionStore(),
            invoiceStore: useInvoiceModalStore(),
            reviewReason: null,
            reviewStatus: null,
            actions: [
                {title: 'Approve', color: 'bg-green-500', slug: 'approved', icon: simpleIcon.icons.HAND_THUMB_UP_SOLID},
                {title: 'Reject', color: 'bg-red-500', slug: 'rejected', icon: simpleIcon.icons.HAND_THUMB_DOWN_SOLID},
            ]
        }
    },

    computed: {
        computedBorderStyle() {
            const styles = {
                pending: 'border-yellow-500',
                approved: 'border-primary-500',
                rejected: 'border-red-500',
            }

            return styles[this.invoiceActionRequest.status] ?? styles.pending
        },
        showActions() {
            return this.invoiceActionRequest.status === 'pending' && this.invoiceRequestActionStore.canReview
        },
        invoiceActionRequestDetailComponent: function () {
            return {
                update_invoice_status: markRaw(UpdateInvoiceStatusActionDetail),
                issue_invoice_to_collections: markRaw(IssueInvoiceToCollections),
                write_off_invoice: markRaw(WriteOffInvoice),
                issue_invoice_refund: markRaw(IssueInvoiceRefund),
                apply_credit_to_company: markRaw(AllocateCreditToCompanySummary),
                expire_company_credit: markRaw(ExpireCompanyCreditSummary),
                extend_company_credit: markRaw(ExtendCompanyCreditSummary),
                create_billing_profile: markRaw(SaveBillingProfileSummary),
                update_billing_profile: markRaw(SaveBillingProfileSummary),
                make_invoice_payment: markRaw(MakeInvoicePayment),
                archive_billing_profile: markRaw(ArchiveBillingProfileSummary),
                restore_billing_profile: markRaw(RestoreBillingProfileSummary),
            }[this.invoiceActionRequest.requested_action_slug]
        }
    },

    methods: {
        handleConfirm() {
            if (!this.reviewStatus) return;

            this.invoiceRequestActionStore.submitReview({
                requestId: this.invoiceActionRequest.id,
                status: this.reviewStatus,
                reason: this.reviewReason,
            })

            this.handleClose()
        },
        handleClose() {
            this.invoiceStore.$reset()
            this.$emit('close')
        },
        handleActionSelect(action) {
            if (this.reviewStatus === action) {
                this.reviewStatus = null
            } else {
                this.reviewStatus = action
            }
        }
    }
}
</script>
