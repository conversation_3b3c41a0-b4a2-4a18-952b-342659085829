<template>
    <modal
        :dark-mode="darkMode"
        @close="$emit('close')"
        no-buttons
        small
    >
        <template v-slot:header>
            View Refund Items
        </template>
        <template v-slot:content>
            <div class="grid grid-cols-5">
                <div class="col-span-5 grid grid-cols-5 uppercase font-semibold px-2">
                    <div>ID</div>
                    <div>Item Type</div>
                    <div>Value</div>
                    <div class="col-span-2">Description</div>
                </div>
                <div
                    v-for="item in refundItems"
                    class="col-span-5 grid grid-cols-5 gap-2 bg-primary-100 rounded-lg p-2 my-2 border border-rounded"
                >
                    <div>{{ item.id }}</div>
                    <div>{{ item.type }}</div>
                    <div>{{ item.value }}</div>
                    <div class="col-span-2">{{ item.data }}</div>
                </div>
            </div>
        </template>
    </modal>
</template>
<script>
import Modal from "../../../Shared/components/Modal.vue";

export default {
    name: "RefundItemsModal",
    components: {Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        refundItems: {
            type: Object,
            default: null,
        }
    },
    emits: ['close']
}
</script>
