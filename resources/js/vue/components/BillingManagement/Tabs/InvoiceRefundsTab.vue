<template>
    <div>
        <simple-table
            title="Invoice Refunds"
            :dark-mode="darkMode"
            :loading="loading"
            v-model="tableFilter"
            @update:modelValue="handleFilterUpdate"
            :table-filters="tableFilters"
            :data="data"
            :current-per-page="tableFilter.per_page"
            :headers="headers"
            :pagination-data="paginationData"
            @search="handleSearch"
            @reset="handleReset"
            header-classes="uppercase text-xs font-medium rounded-lg flex"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
            body-classes="flex"
        >
            <template v-slot:row.col.invoice_id="{item,value}">
                <entity-hyperlink :entity-id="item.invoice_id" type="invoice" :prefix="item.invoice_id"/>
            </template>
            <template v-slot:row.col.total="{value}">
                {{$filters.centsToFormattedDollars(value)}}
            </template>
            <template v-slot:row.col.refund_charges="{item,value}">
                <div @click="refundCharges = value" class="flex gap-1 cursor-pointer text-primary-500 text-sm">
                    View {{ value.length }} Refund Transactions
                    <simple-icon :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.ARROW_TOP_RIGHT_ON_SQUARE"/>
                </div>
            </template>
            <template v-slot:row.col.status="{item,value}">
                <div class="flex">
                    <badge :dark-mode="darkMode">{{value}}</badge>
                </div>
            </template>
            <template v-slot:row.col.refund_items="{item,value}">
                <div @click="refundItems = value" class="flex gap-1 cursor-pointer text-primary-500 text-sm">
                    View {{ value.length }} Items
                    <simple-icon :color="simpleIcon.colors.BLUE" :icon="simpleIcon.icons.ARROW_TOP_RIGHT_ON_SQUARE"/>
                </div>
            </template>
            <template v-slot:row.col.created_at="{value}">
                {{$filters.dateFromTimestamp(value)}}
            </template>
        </simple-table>
        <view-create-invoice-modal
            v-if="invoiceId !== null"
            :dark-mode="darkMode"
            :invoice-id="invoiceId"
            readonly
            @close="invoiceId = null"
        >
        </view-create-invoice-modal>
        <refund-items-modal
            v-if="refundItems !== null"
            @close="refundItems = null"
            :refund-items="refundItems"
        />
        <refund-charges-modal
            v-if="refundCharges !== null"
            @close="refundCharges = null"
            :refund-charges="refundCharges"
        />
    </div>
</template>
<script>
import ViewCreateInvoiceModal from "../../Billing/ViewCreateInvoiceModal.vue";
import ApiService from "../../Billing/services/invoice-refund-api.js";
import useInvoiceTransactionHelper from "../../../../composables/useInvoiceTransactionHelper.js";
import useSimpleTableHelper from "../../../../composables/useSimpleTableHelper.js";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import {SimpleTableFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import Badge from "../../Shared/components/Badge.vue";
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import Modal from "../../Shared/components/Modal.vue";
import RefundItemsModal from "./Refunds/RefundItemsModal.vue";
import RefundChargesModal from "./Refunds/RefundChargesModal.vue";
import EntityHyperlink from "../components/EntityHyperlink.vue";

const defaultFilter = {page: 1, per_page: 25}
const invoiceTransactionHelper = useInvoiceTransactionHelper()
const tableHelper = useSimpleTableHelper()
const simpleIcon = useSimpleIcon()
export default {
    name: "InvoiceRefundsTab",
    components: {
        EntityHyperlink,
        RefundChargesModal, RefundItemsModal, Modal, SimpleIcon, ViewCreateInvoiceModal, Badge, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            invoiceId: null,
            refundItems: null,
            refundCharges: null,
            invoiceTransactionHelper,
            tableHelper,
            simpleIcon,
            apiService: ApiService.make(),
            headers: [
                {title: "Refund ID", field: "id"},
                {title: "Invoice", field: "invoice_id"},
                {title: "Refund Amount", field: 'total'},
                {title: "Refunded Transactions", field: "refund_charges"},
                {title: "Status", field: 'status'},
                {title: "Reason", field: 'reason'},
                {title: "Refund Items", field: 'refund_items'},
                {title: "Date", field: 'created_at'},
            ],
            data: [],
            tableFilter: {...defaultFilter},
            paginationData: {},
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'invoice_id',
                    title: 'Invoice ID'
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.NUMBER_RANGE,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'amount',
                    title: 'Transaction Value',
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'refund_status',
                    title: "Refund Status",
                    options: [
                        {name: "Refunded", id:'refunded'},
                        {name: "Requested", id:'requested'},
                        {name: "Pending", id:'pending'},
                    ]
                }
            ],
        }
    },
    created() {
        this.getInvoiceRefunds();
    },
    methods: {
        async getInvoiceRefunds() {
            this.loading = true
            const cleanedFilters = tableHelper.cleanFilters(this.tableFilter)
            const response = await this.apiService.getRefunds(cleanedFilters)
            const {data, links, meta} = response.data
            this.data = data
            this.paginationData = {links, ...meta}
            this.loading = false
        },
        async handleSearch() {
            await this.getInvoiceRefunds()
        },
        async handleReset() {
            this.tableFilter = {...defaultFilter}
            await this.getInvoiceRefunds()
        },
        handleFilterUpdate(args) {
            this.tableFilter = {
                ...args,
                page: args.page,
                per_page: args.perPage || args.per_page,
            }

            this.getInvoiceRefunds();
        }
    }
}
</script>
