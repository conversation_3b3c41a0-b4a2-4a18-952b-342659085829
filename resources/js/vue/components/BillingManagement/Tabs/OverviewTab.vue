<template>
    <div class="flex flex-col flex-grow">
        <div class="flex justify-between border-b font-bold text-lg" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <div class="flex items-center" :class="[darkMode ? 'text-primary-500' : '']">
                {{ this.periodSpan }}
            </div>
            <div class="flex gap-3 items-center relative" :class="[darkMode ? 'text-primary-500' : '']">
                <simple-icon clickable @click="showCalender = !showCalender" :icon="simpleIcon.icons.CALENDAR_DAYS" :dark-mode="darkMode"></simple-icon>
                <div v-if="showCalender" class="mt-3 absolute top-7 right-0">
                    <Datepicker
                        @update:modelValue="setDateTime"
                        inline
                        range
                        :dark="darkMode"
                        placeholder="mm-dd-yy"
                        format="PP"
                        timezone="America/Denver"
                    ></Datepicker>
                </div>
                <dropdown-menu disabled-dynamic-menu-placement :options="periodOptions" :dark-mode="darkMode">
                    <div class="items-center flex rounded-md p-2 gap-2" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                        <simple-icon :dark-mode="darkMode" :icon="simpleIcon.icons.CLOCK"/>
                        <div class="text-sm" :class="[darkMode ? 'text-primary-500' : '']">{{billingStore.targetTimeSpanString}}</div>
                    </div>
                </dropdown-menu>
                <simple-icon clickable @click="handleBack" :icon="simpleIcon.icons.CHEVRON_LEFT" :dark-mode="darkMode" />
                <simple-icon clickable @click="handleForward" :icon="simpleIcon.icons.CHEVRON_RIGHT" :dark-mode="darkMode" />
            </div>
        </div>
        <billing-summary :dark-mode="darkMode"/>
        <billing-overview :dark-mode="darkMode"/>
    </div>
</template>
<script>
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import DropdownMenu from "../../Mailbox/components/DropdownMenu.vue";
import BillingOverview from "../components/BillingOverview.vue";
import BillingSummary from "../components/BillingSummary.vue";
import {useBillingManagementStore} from "../../../../stores/billing/billing-management.store";
import useSimpleIcon from "../../../../composables/useSimpleIcon";
import DatePicker from "@vuepic/vue-datepicker";
import {DateTime} from "luxon";
const simpleIcon = useSimpleIcon()

export default {
    name: "OverviewTab",
    components: {
        DatePicker,
        DropdownMenu,
        SimpleIcon,
        BillingOverview,
        BillingSummary
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            simpleIcon,
            billingStore: useBillingManagementStore(),
            showCalender: false,
        }
    },
    computed: {
        periodOptions() {
            return this.billingStore.getBillingPeriods();
        },
        periodSpan() {
            return this.billingStore.targetTimeSpanString + ": " + DateTime.fromISO(this.billingStore.currentPeriod.startDate).setZone('America/Denver').toFormat('MMMM d, yyyy') + " - " + DateTime.fromISO(this.billingStore.currentPeriod.endDate).setZone('America/Denver').toFormat('MMMM d, yyyy')
        },
    },
    methods: {
        setDateTime(date) {
            const [startDate, endDate] = date;
            this.billingStore.setDateTime(startDate, endDate);
        },
        handleBack() {
            this.billingStore.move('backwards');
        },
        handleForward() {
            this.billingStore.move('forwards');
        },
    }
}
</script>
