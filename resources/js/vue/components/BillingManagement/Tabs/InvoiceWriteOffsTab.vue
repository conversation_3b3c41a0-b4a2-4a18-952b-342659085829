<template>
    <div>
        <simple-table
            title="Invoice Write Offs"
            :dark-mode="darkMode"
            :loading="loading"
            v-model="filter"
            :table-filters="tableFilters"
            @update:modelValue="handleFilterUpdate"
            :current-per-page="filter.per_page"
            :data="data"
            :headers="headers"
            :pagination-data="paginationData"
            @search="handleSearch"
            @reset="handleReset"
            header-classes="uppercase text-xs font-medium rounded-lg flex"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
            body-classes="flex"
        >
            <template v-slot:title-actions>
                <CustomButton v-if="canExport" :disabled="exporting" :dark-mode="darkMode"
                              @click="handleExportReport('csv')">
                    <svg v-if="exporting" aria-hidden="true" class="w-4 h-4 text-gray-200 animate-spin fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor"/>
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill"/>
                    </svg>
                    <p v-else>Export</p>
                </CustomButton>
            </template>
            <template v-slot:visible-filters>
                <company-search-autocomplete v-model="filter.company_id" :dark-mode="darkMode"/>
            </template>
            <template v-slot:row.col.company="{value}">
                <entity-hyperlink :entity-id="value.id" type="company" :prefix="value.name"/>
            </template>
            <template v-slot:row.col.invoice_id="{value}">
                <entity-hyperlink :entity-id="value" type="invoice" :prefix="value"/>
            </template>
            <template v-slot:row.col.amount="{value}">
                {{ $filters.centsToFormattedDollars(value) }}
            </template>
            <template v-slot:row.col.invoice_total="{value}">
                {{ $filters.centsToFormattedDollars(value) }}
            </template>
        </simple-table>
        <view-create-invoice-modal
            v-if="invoiceId !== null"
            :dark-mode="darkMode"
            :invoice-id="invoiceId"
            readonly
            @close="invoiceId = null"
        >
        </view-create-invoice-modal>
    </div>
</template>
<script>
import ViewCreateInvoiceModal from "../../Billing/ViewCreateInvoiceModal.vue";
import useInvoiceTransactionHelper from "../../../../composables/useInvoiceTransactionHelper.js";
import useSimpleTableHelper from "../../../../composables/useSimpleTableHelper.js";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import Badge from "../../Shared/components/Badge.vue";
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import Modal from "../../Shared/components/Modal.vue";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import {DateTime} from "luxon";
import {SimpleTableFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import CompanySearchAutocomplete from "../../Shared/components/Company/CompanySearchAutocomplete.vue";
import EntityHyperlink from "../components/EntityHyperlink.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import {downloadCsvString} from "../../../../composables/exportToCsv.js";

const defaultFilter = {page: 1, per_page: 25}
const invoiceTransactionHelper = useInvoiceTransactionHelper()
const tableHelper = useSimpleTableHelper()
const simpleIcon = useSimpleIcon()
export default {
    name: "InvoiceWriteOffsTab",
    components: {
        CustomButton,
        EntityHyperlink,
        CompanySearchAutocomplete, Modal, SimpleIcon, ViewCreateInvoiceModal, Badge, SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            exporting: false,
            rolesPermissions: useRolesPermissions(),
            loading: false,
            invoiceId: null,
            refundItems: null,
            refundCharges: null,
            invoiceStore: useInvoiceModalStore(),
            invoiceTransactionHelper,
            tableHelper,
            simpleIcon,
            headers: [
                {title: 'Invoice', field: 'invoice_id'},
                {title: 'Company Id', field: 'company.id'},
                {title: 'Company Name', field: 'company.name'},
                {title: 'Date', field: 'date'},
                {title: 'Total Issued', field: 'invoice_total'},
                {title: 'Total Written Off', field: 'amount'},
                {title: 'User', field: 'user_name'},
            ],
            data: [],
            filter: {
                ...defaultFilter
            },
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'invoice_id',
                    title: 'Invoice ID'
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'date_range',
                    title: 'Date',
                    type: SimpleTableHiddenFilterTypesEnum.DATE_RANGE,
                    timezone: 'America/Denver'
                },
            ],
            paginationData: {},
        }
    },
    created() {
        this.getInvoiceWriteOffs();
    },
    methods: {
        async handleExportReport() {
            this.exporting = true

            const response = await this.retrieveData(true);

            const formattedItems = response.data.data.map(item => [
                item.invoice_id,
                item.company.id,
                item.company.name,
                item.date,
                this.$filters.centsToFormattedDollars(item.invoice_total),
                this.$filters.centsToFormattedDollars(item.amount)
            ])

            const headers = this.headers.map(h => h.title);

            const formattedFilters = Object.entries(this.filter)
                .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
                .map((item, idx) => [idx === 0 ? 'Filters' : '', item])

            const exportInfoData = [
                ['Exported date', this.$filters.dateFromTimestamp((new Date()).toISOString(), 'usWithTime')],
                ...formattedFilters,
                ['Total Items', formattedItems.length],
            ]

            downloadCsvString(
                headers,
                [
                    ...formattedItems,
                    Array.from({length: headers.length}, () => ''),
                    ...exportInfoData,
                ],
                `invoice_write_offs_report_${Math.floor(Date.now() / 1000)}`
            )

            this.exporting = false
        },
        async retrieveData(all) {
            return await this.invoiceStore.api.listWrittenOffInvoices({
                ...this.filter,
                all,
                date_range: {
                    from: this.filter?.date_range?.from ? DateTime.fromISO(this.filter?.date_range?.from).setZone('America/Denver').startOf('day').toISO() : null,
                    to: this.filter?.date_range?.to ? DateTime.fromISO(this.filter?.date_range?.to).setZone('America/Denver').endOf('day').toISO() : null,
                }
            })
        },
        async getInvoiceWriteOffs() {
            this.loading = true
            const response = await this.retrieveData()
            const {data, links, meta} = response.data
            this.data = data
            this.paginationData = {links, ...meta}
            this.loading = false
        },
        async handleSearch() {
            await this.getInvoiceWriteOffs()
        },
        async handleReset() {
            this.filter = {...defaulitFilter}
            await this.getInvoiceWriteOffs()
        },
        handleFilterUpdate(args) {
            this.filter = {
                ...args,
                page: args.page,
                per_page: args.perPage || args.per_page,
            }

            this.getInvoiceWriteOffs();
        }
    },
    computed: {
        canExport() {
            return this.rolesPermissions.hasPermission(PERMISSIONS.BILLING_REPORTS_EXPORT)
        }
    },
}
</script>
