<template>
    <div>
        <simple-table
            title="Invoice Transactions"
            :dark-mode="darkMode"
            :loading="loading"
            v-model="tableFilter"
            @update:modelValue="handleFilterUpdate"
            :current-per-page="tableFilter.per_page"
            :table-filters="tableFilters"
            :data="data"
            :headers="headers"
            :pagination-data="paginationData"
            @search="handleSearch"
            @reset="handleReset"
            header-classes="uppercase text-xs font-medium rounded-lg flex"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
            body-classes="flex"
        >
            <template v-slot:row.col.invoice_uuid="{item,value}">
                <entity-hyperlink :entity-id="item.invoice_id" type="invoice" :prefix="item.invoice_id"/>
            </template>
            <template v-slot:row.col.transaction_type="{item, value}">
                <badge :dark-mode="darkMode" :color="invoiceTransactionHelper.PRESENTATION[value.id]?.badge_color">
                    {{value.name}}
                </badge>
            </template>
            <template v-slot:row.col.amount="{item, value}">
                {{$filters.centsToFormattedDollars(value)}}
            </template>
            <template v-slot:row.col.payload="{item, value}">
                <div  class="text-sm">
                    <div v-if="item.transaction_type.id === invoiceTransactionHelper.TYPES.INVOICE_CHARGE_DISPUTE_OPENED">
                        <div>Reason: {{value.reason}}</div>
                        <div>Status: {{value.status}}</div>
                    </div>
                    <div v-else-if="item.transaction_type.id === invoiceTransactionHelper.TYPES.INVOICE_CHARGE_FAILED">
                        <div>Message: {{value.message}}</div>
                    </div>
                    <div v-else-if="item.transaction_type.id === invoiceTransactionHelper.TYPES.INVOICE_CHARGE_DISPUTE_CLOSED">
                        <div>Status: {{value.status}}</div>
                    </div>
                    <div v-else-if="item.transaction_type.id === invoiceTransactionHelper.TYPES.INVOICE_CHARGE_REFUND_SUCCESS">
                        <div>Fully Refunded: {{value.fully_refunded}}</div>
                        <div>Amount Refunded: {{value.amount_refunded}}</div>
                    </div>
                    <div v-else>
                         {{value}}
                    </div>
                </div>
            </template>
        </simple-table>
        <view-create-invoice-modal
            v-if="invoiceId !== null"
            :dark-mode="darkMode"
            :invoice-id="invoiceId"
            readonly
            @close="invoiceId = null"
        >
        </view-create-invoice-modal>
    </div>
</template>
<script>
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import {SimpleTableFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import ApiService from "../services/invoices-transactions-api.js";
import useInvoiceTransactionHelper from "../../../../composables/useInvoiceTransactionHelper.js";
import Badge from "../../Shared/components/Badge.vue";
import ViewCreateInvoiceModal from "../../Billing/ViewCreateInvoiceModal.vue";
import useSimpleTableHelper from "../../../../composables/useSimpleTableHelper.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import EntityHyperlink from "../components/EntityHyperlink.vue";
const defaultFilter = {page: 1, per_page: 25}
const invoiceTransactionHelper = useInvoiceTransactionHelper()
const tableHelper = useSimpleTableHelper()
const simpleIcon = useSimpleIcon()
export default {
    name: "InvoiceTransactionsTab",
    components: {EntityHyperlink, SimpleIcon, ViewCreateInvoiceModal, Badge, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            invoiceId: null,
            invoiceTransactionHelper,
            tableHelper,
            simpleIcon,
            apiService: ApiService.make(),
            headers: [
                {title: "Transaction ID", field: "id"},
                {title: "Invoice", field: "invoice_uuid"},
                {title: "External Reference", field: "external_reference", cols: 2},
                {title: "Amount", field: 'amount'},
                {title: "Transaction Type", field: 'transaction_type'},
                {title: "Transaction Date", field: 'transaction_date'},
            ],
            data: [],
            tableFilter: {...defaultFilter},
            paginationData: {},
            tableFilters: [],
        }
    },
    created() {
        this.getTransactions();
        this.getTableFilters();
    },
    methods: {
        async getTransactions() {
            this.loading = true
            const cleanedFilters = tableHelper.cleanFilters(this.tableFilter)
            const response = await this.apiService.getTransactions(cleanedFilters)
            const {data, links, meta} = response.data
            this.data = data
            this.paginationData = {links, ...meta}
            this.loading = false
        },
        async getTableFilters() {
            const response = await this.apiService.getInvoiceTransactionFilterOptions();

            const filters = [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'invoice_id',
                    title: 'Invoice ID'
                },
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'external_reference',
                    title: 'External Reference'
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.NUMBER_RANGE,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'amount',
                    title: 'Transaction Value',
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    field: 'transaction_types',
                    title: 'Transaction Type',
                    options: response.data.data.filters.transaction_types,
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    field: 'transaction_scenarios',
                    title: 'Transaction Scenario',
                    options: response.data.data.filters.transaction_scenarios,
                }
            ];

            this.tableFilters = filters
        },
        async handleSearch() {
            await this.getTransactions()
        },
        async handleReset() {
            this.tableFilter = {...defaultFilter}
            await this.getTransactions()
        },
        handleFilterUpdate(args) {
            this.tableFilter = {
                ...args,
                page: args.page,
                per_page: args.perPage || args.per_page,
            }

            this.getTransactions();
        }
    }

}
</script>
