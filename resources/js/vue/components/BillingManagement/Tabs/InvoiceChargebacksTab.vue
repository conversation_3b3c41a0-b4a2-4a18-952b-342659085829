<template>
    <div>
        <simple-table
            title="Invoice Chargebacks"
            :dark-mode="darkMode"
            :loading="loading"
            v-model="tableFilter"
            :table-filters="tableFilters"
            :data="data"
            :current-per-page="tableFilter.per_page"
            :headers="headers"
            :pagination-data="paginationData"
            header-classes="uppercase text-xs font-medium rounded-lg flex"
            body-classes="flex"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
            @update:modelValue="handleFilterUpdate"
            @search="handleSearch"
            @reset="handleReset"
        >
            <template v-slot:visible-filters>
                <company-search-autocomplete v-model="tableFilter.company_id" :dark-mode="darkMode"/>
            </template>
            <template v-slot:row.col.invoice_id="{value}">
                <entity-hyperlink :entity-id="value" type="invoice" :prefix="value"/>
            </template>
            <template v-slot:row.col.company="{value}">
                <entity-hyperlink :entity-id="value.id" type="company" :prefix="value.name"/>
            </template>
        </simple-table>
        <view-create-invoice-modal
            v-if="invoiceId !== null"
            :dark-mode="darkMode"
            :invoice-id="invoiceId"
            readonly
            @close="invoiceId = null"
        >
        </view-create-invoice-modal>
    </div>
</template>
<script>
import ViewCreateInvoiceModal from "../../Billing/ViewCreateInvoiceModal.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import Badge from "../../Shared/components/Badge.vue";
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import Modal from "../../Shared/components/Modal.vue";
import RefundItemsModal from "./Refunds/RefundItemsModal.vue";
import RefundChargesModal from "./Refunds/RefundChargesModal.vue";
import {useInvoiceChargebackStore} from "../../../../stores/invoice/invoice-chargeback.js";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import EntityHyperlink from "../components/EntityHyperlink.vue";
import {SimpleTableFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import CompanySearchAutocomplete from "../../Shared/components/Company/CompanySearchAutocomplete.vue";
import UserSearchAutocomplete from "../../Shared/components/User/UserSearchAutocomplete.vue";
import {DateTime} from "luxon";
export default {
    name: "InvoiceRefundsTab",
    components: {
        UserSearchAutocomplete, CompanySearchAutocomplete,
        EntityHyperlink,
        RefundChargesModal, RefundItemsModal, Modal, SimpleIcon, ViewCreateInvoiceModal, Badge, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            simpleIcon: useSimpleIcon(),
            invoiceId: null,
            loading: false,
            invoiceChargebackStore: useInvoiceChargebackStore(),
            headers: [
                {title: "Invoice Id", field: "invoice_id"},
                {title: "Company", field: "company"},
                {title: "Reason", field: "reason"},
                {title: "Status", field: 'status'},
                {title: "Amount", field: "amount"},
                {title: "Created at", field: 'created_at'},
                {title: "Updated at", field: 'updated_at'},
            ],
            data: [],
            tableFilter: {page: 1, per_page: 25},
            paginationData: {},
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'invoice_id',
                    title: 'Invoice ID'
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'date_range',
                    title: 'Issued date',
                    type: SimpleTableHiddenFilterTypesEnum.DATE_RANGE,
                    timezone: 'America/Denver'
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    field: 'status',
                    title: 'Status',
                    options: [
                        {
                            id: 'needs_response',
                            name: 'Needs Response'
                        },
                        {
                            id: 'won',
                            name: 'Won'
                        },
                        {
                            id: 'lost',
                            name: 'Lost'
                        },
                    ],
                },
            ],
        }
    },
    created() {
        this.getChargebacks();
    },
    methods: {
        async getChargebacks() {
            this.loading = true
            const {data, links, meta} = await this.invoiceChargebackStore.getChargebacks({
                ...this.tableFilter,
                date_range: {
                    from: this.tableFilter?.date_range?.from ? DateTime.fromISO(this.tableFilter?.date_range?.from).setZone('America/Denver').startOf('day').toISO() : null,
                    to: this.tableFilter?.date_range?.to ? DateTime.fromISO(this.tableFilter?.date_range?.to).setZone('America/Denver').endOf('day').toISO() : null,
                }
            })
            this.data = data
            this.paginationData = {links, ...meta}
            this.loading = false
        },
        handleFilterUpdate(args) {
            this.tableFilter = {
                ...args,
                page: args.page,
                per_page: args.perPage || args.per_page,
            }

            this.getChargebacks();
        },
        async handleSearch() {
            await this.getChargebacks()
        },
        async handleReset() {
            this.tableFilter = {page: 1, per_page: 10}
            await this.getChargebacks()
        },
    }
}
</script>
