<template>
    <div>
        <simple-table
            v-model="filters"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            :pagination-data="paginationData"
            :current-per-page="filters.per_page"
            title="Accounts receivable report"
            @search="handleSearch"
            @reset="handleReset"
            @page-change="handlePageChange"
            :table-filters="tableFilters"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
        >
            <template v-slot:disclaimer>
                <div class="p-2 mb-10 grid grid-cols-6 items-center">
                    <div v-for="(val, key) in aggregates">
                        <labeled-value :label="key">
                            {{ val }}
                        </labeled-value>
                    </div>
                </div>
            </template>
            <template v-slot:title-actions>
                <CustomButton v-if="canExport" :disabled="exporting" :dark-mode="darkMode"
                              @click="handleExportReport('csv')">
                    <svg v-if="exporting" aria-hidden="true" class="w-4 h-4 text-gray-200 animate-spin fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor"/>
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill"/>
                    </svg>
                    <p v-else>Export</p>
                </CustomButton>
            </template>
            <template v-slot:visible-filters>
                <date-picker
                    v-model="filters.date"
                    :range="false"
                    :enable-time-picker="false"
                    :dark="darkMode"
                    auto-apply
                    placeholder="mm-dd-yy"
                    format="PP"
                    timezone="America/Denver"
                >
                    <template #input-icon>
                        <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                fill="#0081FF"/>
                            <path
                                d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                fill="#0081FF"/>
                        </svg>
                    </template>
                </date-picker>
                <company-search-autocomplete
                    v-model="filters.company_id"
                    :dark-mode="darkMode"
                />
                <user-search-autocomplete
                    v-if="isFinanceOwner"
                    v-model="filters.account_manager_user_id"
                    :dark-mode="darkMode"
                    placeholder="Account Manager"
                >
                </user-search-autocomplete>
                <user-search-autocomplete
                    v-if="isFinanceOwner"
                    v-model="filters.onboarding_manager_user_id"
                    :dark-mode="darkMode"
                    placeholder="Onboarding Manager"
                >
                </user-search-autocomplete>
                <user-search-autocomplete
                    v-if="isFinanceOwner"
                    v-model="filters.business_development_manager_user_id"
                    :dark-mode="darkMode"
                    placeholder="Business Development Manager"
                >
                </user-search-autocomplete>
            </template>
            <template v-slot:row.col.company_id="{item}">
                <entity-hyperlink :entity-id="item.company?.id" type="company" :prefix="item.company?.id"/>
            </template>
            <template v-slot:row.col.company_name="{item}">
                <entity-hyperlink :prefix="item.company?.name" :entity-id="item.company?.id" type="company"/>
            </template>
            <template v-slot:row.col.status="{value}">
                <invoice-status-badge :status="value"></invoice-status-badge>
            </template>
            <template v-slot:row.col.invoice_id="{value}">

                <entity-hyperlink
                    v-if="value !== 'N/A'"
                    :prefix="value"
                    :entity-id="value"
                    type="invoice"
                >
                </entity-hyperlink>
                <p v-else>{{ value }}</p>
            </template>
        </simple-table>
    </div>
</template>
<script>
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import DatePicker from "@vuepic/vue-datepicker";
import Autocomplete from "../../../Shared/components/Autocomplete.vue";
import SimpleAlert from "../../../Shared/components/SimpleAlert.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes";
import {SimpleTableFilterTypesEnum} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum";
import InvoiceStatusBadge from "../../components/InvoiceStatusBadge.vue";
import ApiService from "../../services/invoices-report-api.js";
import EntityHyperlink from "../../components/EntityHyperlink.vue";
import UserSearchAutocomplete from "../../../Shared/components/User/UserSearchAutocomplete.vue";
import {downloadCsvString} from "../../../../../composables/exportToCsv.js";
import {PERMISSIONS, ROLES, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import {DateTime} from "luxon";
import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";

export default {
    name: "ReceivableInvoicesReport",
    components: {
        CompanySearchAutocomplete,
        LabeledValue,
        UserSearchAutocomplete,
        EntityHyperlink,
        InvoiceStatusBadge,
        Dropdown, SimpleAlert, Autocomplete, DatePicker, CustomButton, SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    created() {
        this.calculateHeaders()
        this.handleReset()
        this.getInvoiceStatusOptions();
    },
    data() {
        return {
            selectedReceivableInvoice: null,
            invoiceStatusOptions: [],
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.INDUSTRY,
                    field: 'industry_ids',
                    title: 'Industries'
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'grouped_by',
                    title: 'Reference',
                    removable: false,
                    options: [
                        {id: 'invoice', name: 'Invoice'},
                        {id: 'company', name: 'Company'},
                        {id: 'onboarding_manager', name: 'Onboarding Manager'},
                        {id: 'account_manager', name: 'Account Manager'},
                        {id: 'business_development_manager', name: 'Business Development Manager'},
                    ]
                },
            ],
            filters: {
                grouped_by: 'invoice',
                per_page: 25,
            },
            api: ApiService.make(),
            data: [],
            loading: false,
            headers: [],
            paginationData: {},
            exporting: false,
            selectedJobs: [],
            rolesPermissions: useRolesPermissions(),
            aggregates: {}
        }
    },

    watch: {
        'filters.grouped_by'() {
            this.handleSearch()
        }
    },

    computed: {
        isFinanceOwner() {
            return this.rolesPermissions.hasAnyRole([
                ROLES.FINANCE_OWNER,
            ])
        },
        canExport() {
            return this.rolesPermissions.hasPermission(PERMISSIONS.BILLING_REPORTS_EXPORT)
        },
    },

    methods: {
        calculateHeaders() {
            if (this.filters.grouped_by === 'company') {
                return this.headers = [
                    {title: 'Company Id', field: 'company_id', sortable: true},
                    {title: 'Company Name', field: 'company_name', sortable: false},
                    {title: 'Account Manager', field: 'account_manager.name', sortable: false},
                    {title: 'Onboarding Manager', field: 'onboarding_manager.name', sortable: false},
                    {
                        title: 'Business Development Manager',
                        field: 'business_development_manager.name',
                        sortable: false
                    },
                    {title: 'Invoices Count', field: 'invoices_count', sortable: false},
                    {title: 'Outstanding', field: 'total_receivable', sortable: true},
                ]
            }

            if ([
                'onboarding_manager',
                'account_manager',
                'business_development_manager'
            ].includes(this.filters.grouped_by)) {
                return this.headers = [
                    {title: 'Name', field: this.filters.grouped_by + '.name', sortable: false},
                    {title: 'Type', field: 'reference', sortable: false},
                    {title: 'Invoices Count', field: 'invoices_count', sortable: false},
                    {title: 'Companies Count', field: 'companies_count', sortable: false},
                    {title: 'Outstanding', field: 'total_receivable', sortable: true},
                ]
            }

            return this.headers = [
                {title: 'Company Id', field: 'company_id', sortable: true},
                {title: 'Company Name', field: 'company_name', sortable: false},
                {title: 'Account Manager', field: 'account_manager.name', sortable: false},
                {title: 'Onboarding Manager', field: 'onboarding_manager.name', sortable: false},
                {
                    title: 'Business Development Manager',
                    field: 'business_development_manager.name',
                    sortable: false
                },
                {title: 'Invoice id', field: 'invoice_id', sortable: true},
                {title: 'Issue At', field: 'issue_at', sortable: true},
                {title: 'Due At', field: 'due_at', sortable: true},
                {title: 'Last Update', field: 'created_at', sortable: true},
                {title: 'Status', field: 'status', sortable: true},
                {title: 'Outstanding', field: 'total_receivable', sortable: true},
            ]
        },
        getExportRowData(item) {
            if (this.filters.grouped_by === 'company') {
                return [
                    item.company.id,
                    item.company.name,
                    item.account_manager?.name,
                    item.onboarding_manager?.name,
                    item.business_development_manager?.name,
                    item.invoices_count,
                    item.total_receivable,
                ]
            }

            if ([
                'onboarding_manager',
                'account_manager',
                'business_development_manager'
            ].includes(this.filters.grouped_by)) {
                return [
                    item[this.filters.grouped_by]?.name,
                    item.reference,
                    item.invoices_count,
                    item.companies_count,
                    item.total_receivable,
                ]
            }

            return [
                item?.company?.id,
                item?.company?.name,
                item?.account_manager?.name,
                item?.onboarding_manager?.name,
                item?.business_development_manager?.name,
                item?.invoice_id,
                item.issue_at,
                item.due_at,
                item?.created_at,
                item?.status,
                item?.total_receivable,
            ]
        },
        async getReceivableInvoicesReport(params = {}) {
            const date = this.filters.date
                ? DateTime.fromJSDate(this.filters.date).setZone('America/Denver').endOf('day').toISO()
                : undefined;

            return await this.api.getReceivableInvoicesReport({
                ...this.filters,
                ...params,
                date
            });
        },
        async getReport() {
            if (this.loading) return
            this.loading = true

            const res = await this.getReceivableInvoicesReport();

            this.data = res.data.data.data
            this.aggregates = res.data.aggregates
            this.paginationData = res.data.data.meta

            this.loading = false
        },
        async handleExportReport() {
            this.exporting = true

            const response = await this.getReceivableInvoicesReport({
                all: 1
            });

            const formattedItems = response.data.data.data.map(item => this.getExportRowData(item))

            const headers = this.calculateHeaders().map(h => h.title);

            // TODO - Share code with other reports
            const formattedFilters = Object.entries(this.filters)
                .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
                .map((filter, idx) => [idx === 0 ? 'Filters' : '', filter])

            const formattedAggregates = Object.entries(response.data.aggregates)
                .map(([key, value]) => `${key}: ${value}`)
                .map((item, idx) => [idx === 0 ? 'Aggregates' : '', item])

            const exportInfoData = [
                ['Exported date', this.$filters.dateFromTimestamp((new Date()).toISOString(), 'usWithTime')],
                ...formattedFilters,
                ...formattedAggregates,
                ['Total Items', formattedItems.length],
            ]

            downloadCsvString(
                headers,
                [
                    ...formattedItems,
                    Array.from({length: headers.length}, () => ''),
                    ...exportInfoData,
                ],
                `accounts_receivable_report_${Math.floor(Date.now() / 1000)}`
            )

            this.exporting = false
        },
        async getInvoiceStatusOptions() {
            try {
                const invoiceStatuses = await this.api.getInvoiceStatuses();

                this.tableFilters.push({
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'invoice_status',
                    title: 'Invoice Status',
                    options: invoiceStatuses?.data?.data ?? []
                })
            } catch (err) {
                console.error(err)
            }
        },
        handleSearch() {
            this.calculateHeaders()
            this.filters.page = 1
            this.getReport()
        },
        handleReset() {
            this.filters = {
                date: new Date(),
                grouped_by: 'invoice',
                per_page: 25,
            }

            this.getReport()
        },
        handlePageChange({newPage}) {
            this.filters.page = newPage
            this.getReport()
        }
    }
}
</script>
