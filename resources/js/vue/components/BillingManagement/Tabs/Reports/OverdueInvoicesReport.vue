<template>
    <div>
        <simple-table
            v-model="filters"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            :table-filters="tableFilters"
            :pagination-data="paginationData"
            :current-per-page="filters.per_page"
            title="Overdue Invoices Report"
            @search="getReport"
            @reset="handleReset"
            @page-change="handlePageChange"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
        >
            <template v-slot:title-actions>
                <CustomButton v-if="isFinanceOwner" :disabled="exporting" :dark-mode="darkMode"
                              @click="handleExportReport('csv')">
                    <svg v-if="exporting" aria-hidden="true" class="w-4 h-4 text-gray-200 animate-spin fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor"/>
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill"/>
                    </svg>
                    <p v-else>Export</p>
                </CustomButton>
            </template>
            <template v-slot:visible-filters>
                <date-picker
                    v-model="filters.date"
                    :range="false"
                    :enable-time-picker="false"
                    :dark="darkMode"
                    auto-apply
                    placeholder="mm-dd-yy"
                    format="PP"
                    timezone="America/Denver"
                >
                    <template #input-icon>
                        <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                fill="#0081FF"/>
                            <path
                                d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                fill="#0081FF"/>
                        </svg>
                    </template>
                </date-picker>
                <company-search-autocomplete v-model="filters.company_id" :dark-mode="darkMode">
                </company-search-autocomplete>
            </template>
            <template #row.col.payment_method="{value}">
                <payment-method-badge :type="value"/>
            </template>
            <template #row.col.invoice_status="{value}">
                <invoice-status-badge :dark-mode="darkMode" :status="value"/>
            </template>
            <template v-slot:row.col.invoice_id="{value}">
                <entity-hyperlink
                    :entity-id="value"
                    :prefix="value"
                    type="invoice"
                >
                </entity-hyperlink>
            </template>
            <template v-slot:row.col.company_id="{item}">
                <entity-hyperlink
                    :prefix="item.company_id"
                    :entity-id="item.company_id"
                    type="company"
                >
                </entity-hyperlink>
            </template>
            <template v-slot:row.col.company_name="{item}">
                <entity-hyperlink
                    :prefix="item.company_name"
                    :entity-id="item.company_id"
                    type="company"
                >
                </entity-hyperlink>
            </template>
        </simple-table>
    </div>
</template>
<script>
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import Autocomplete from "../../../Shared/components/Autocomplete.vue";
import SharedApiService from "../../../Shared/services/api";
import SimpleAlert from "../../../Shared/components/SimpleAlert.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import InvoiceStatusBadge from "../../components/InvoiceStatusBadge.vue";
import ApiService from "../../services/invoices-report-api.js";
import DisplayInvoiceTransactionStatuses
    from "../../../Billing/CreateInvoiceModalContentCompanyData/components/DisplayInvoiceTransactionStatuses.vue";
import Badge from "../../../Shared/components/Badge.vue";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import EntityHyperlink from "../../components/EntityHyperlink.vue";
import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";
import UserSearchAutocomplete from "../../../Shared/components/User/UserSearchAutocomplete.vue";
import {downloadCsvString} from "../../../../../composables/exportToCsv.js";
import {ROLES, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import HoverTooltip from "../../../Shared/components/HoverTooltip.vue";
import PaymentMethodBadge from "../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import {
    SimpleTableFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import DatePicker from "@vuepic/vue-datepicker";
import {DateTime} from "luxon";
import {useToastNotificationStore} from "../../../../../stores/billing/tost-notification.store.js";
import useBillingReportHelper from "../../../../../composables/useBillingReportHelper.js";
const billingReportHelper = useBillingReportHelper()

export default {
    name: "OverdueInvoicesReport",
    components: {
        DatePicker,
        PaymentMethodBadge, HoverTooltip,
        UserSearchAutocomplete,
        CompanySearchAutocomplete,
        EntityHyperlink,
        LabeledValue,
        Badge,
        DisplayInvoiceTransactionStatuses,
        InvoiceStatusBadge,
        Dropdown,
        SimpleAlert,
        Autocomplete,
        CustomButton,
        SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    created() {
        this.handleReset()
    },
    computed: {
        isFinanceOwner() {
            return this.rolesPermissions.hasAnyRole([
                ROLES.FINANCE_OWNER,
            ])
        },
    },
    data() {
        return {
            rolesPermissions: useRolesPermissions(),
            industryOptions: [],
            sharedApi: SharedApiService.make(),
            showViewInvoiceModal: false,
            selectedReceivableInvoice: null,
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'invoice_id',
                    title: 'Invoice Id',
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'reference',
                    title: 'Reference',
                    options: billingReportHelper.groupByOptions,
                    removable: false
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'payment_method',
                    title: 'Payment Method',
                    options: [
                        {id: 'manual', name: 'ACH'},
                        {id: 'stripe', name: 'Credit Card'},
                    ],
                    removable: false
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'payment_status',
                    title: 'Payment Status',
                    options: [{id: 'paid', name: 'Paid'}, {id: 'unpaid', name: 'Unpaid'}, {id: 'all', name: 'All'}],
                },
            ],
            headers: [],
            filters: {},
            api: ApiService.make(),
            data: [],
            loading: false,
            paginationData: {},
            toastNotificationStore: useToastNotificationStore(),
            exporting: false,
        }
    },

    watch: {
        'filters.reference': {
            handler(newVal, oldVal) {
                if (newVal === oldVal) {
                    return
                }

                if (newVal === billingReportHelper.groupBy.company.id) {
                    this.filters.sort_by = [];
                    this.headers = [
                        {title: 'Company Id', field: 'company_id', sortable: true},
                        {title: 'Company Name', field: 'company_name', sortable: true},
                        {title: 'Payment Method', field: 'payment_method', sortable: true},
                        {title: 'Company Payment Terms (Days)', field: 'billing_profile_due_in_days', sortable: true},
                        {title: 'Invoice Count', field: 'issued_count', sortable: true},
                        {title: 'Outstanding', field: 'outstanding_total', sortable: true},
                        {title: 'Total Issued', field: 'items_total', sortable: true},
                        {title: 'Invoices Unpaid', field: 'unpaid_count', sortable: true},
                        {title: 'Invoices Overdue', field: 'due_count', sortable: true},
                        {title: 'Amount Overdue', field: 'total_due', sortable: true},
                        {title: 'Avg. Payment History', field: 'avg_days_to_pay', sortable: true},
                    ]
                } else if (newVal === billingReportHelper.groupBy.invoice.id){
                    this.filters.sort_by = [];
                    this.headers = [
                        {title: 'Invoice', field: 'invoice_id', sortable: true},
                        {title: 'Invoice Status', field: 'invoice_status', sortable: true},
                        {title: 'Company Id', field: 'company_id', sortable: true},
                        {title: 'Company Name', field: 'company_name', sortable: true},
                        {title: 'Payment Method', field: 'payment_method', sortable: true},
                        {title: 'Outstanding', field: 'outstanding_total', sortable: true},
                        {title: 'Total Issued', field: 'items_total', sortable: true},
                        {title: 'Issue Date', field: 'issue_date', sortable: true},
                        {title: 'Due Date', field: 'due_date', sortable: true},
                        {title: 'Pay Date', field: 'pay_date', sortable: true},
                        {title: 'Days To Pay', field: 'days_to_pay', sortable: true},
                    ]
                }
            }, deep: true
        }
    },

    methods: {
        async getReport() {
            this.loading = true

            const date = this.filters.date
                ? DateTime.fromJSDate(this.filters.date).setZone('America/Denver').toISO()
                : undefined;

            try {
                const res = await this.api.getOverdueInvoicesReport({
                    ...this.filters,
                    date
                });

                this.data = res.data.data
                this.paginationData = {links: res.data.links, ...res.data.meta}
            } catch (err) {
                console.error(err)
                this.toastNotificationStore.notifyError({
                    message: 'Error loading report'
                })
            }

            this.loading = false
        },
        handleReset() {
            this.filters = {
                per_page: 25,
                page: 1,
                sort_by: ['days_to_pay:asc'],
                reference: billingReportHelper.groupBy.invoice.id,
                payment_status: 'all',
                date: new Date().toISOString(),
            }

            this.getReport()
        },

        async handleExportReport() {
            this.exporting = true;

            try {
                const response = await this.api.getOverdueInvoicesReport({
                    ...this.filters,
                    all: 1
                });

                const {headers, formattedItems} = this.formatReportData(response.data.data);
                const exportData = this.buildExportData(headers, formattedItems);

                downloadCsvString(
                    headers,
                    exportData,
                    `invoices_balance_report_${Math.floor(Date.now() / 1000)}`
                );
            } catch (error) {
                this.toastNotificationStore.notifyError({
                    message: 'Error exporting report'
                })
                console.error(error);
            } finally {
                this.exporting = false;
            }
        },

        formatReportData(data) {
            return {
                headers: this.getHeaders(this.filters.reference),
                formattedItems: this.mapDataToRows(data, this.filters.reference)
            };
        },

        getHeaders(reference) {
            return {
                [billingReportHelper.groupBy.company.id]: [
                    'Company Id',
                    'Company Name',
                    'Payment Method',
                    'Company Payment Terms (Days)',
                    'Invoice Count',
                    'Outstanding',
                    'Total',
                    'Invoices Unpaid',
                    'Invoices Overdue',
                    'Amount Overdue',
                    'Avg. Payment History'
                ],
                [billingReportHelper.groupBy.invoice.id]: [
                    'Invoice',
                    'Invoice Status',
                    'Company Id',
                    'Company Name',
                    'Payment Method',
                    'Outstanding',
                    'Total',
                    'Issue Date',
                    'Due Date',
                    'Pay Date',
                    'Days To Pay'
                ]
            }[reference] ?? []
        },

        mapDataToRows(data, reference) {
            return {
                [billingReportHelper.groupBy.company.id]: data.map(item => [
                    item.company_id,
                    item.company_name,
                    item.payment_method === 'stripe' ? 'Credit Card' : 'ACH',
                    item.billing_profile_due_in_days,
                    item.issued_count,
                    item.outstanding_total,
                    item.items_total,
                    item.unpaid_count,
                    item.due_count,
                    item.total_due,
                    item.avg_days_to_pay
                ]),
                [billingReportHelper.groupBy.invoice.id]: data.map(item => [
                    item.invoice_id,
                    item.invoice_status,
                    item.company_id,
                    item.company_name,
                    item.payment_method === 'stripe' ? 'Credit Card' : 'ACH',
                    item.outstanding_total,
                    item.items_total,
                    item.issue_date,
                    item.due_date,
                    item.pay_date,
                    item.days_to_pay
                ])
            }[reference]
        },

        buildExportData(headers, formattedItems) {
            const exportInfo = this.buildExportInfo(formattedItems.length);
            const separator = Array.from({length: headers.length}, () => '');

            return [
                ...formattedItems,
                separator,
                ...exportInfo
            ];
        },

        buildExportInfo(itemCount) {
            const exportDate = this.$filters.dateFromTimestamp(
                new Date().toISOString(),
                'usWithTime'
            );

            const filterEntries = Object.entries(this.filters)
                .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
                .map((item, idx) => [idx === 0 ? 'Filters' : '', item]);

            return [
                ['Exported date', exportDate],
                ...filterEntries,
                ['Total Items', itemCount]
            ];
        },

        handlePageChange({newPage}) {
            this.filters.page = newPage
            this.getReport()
        }
    }
}
</script>
