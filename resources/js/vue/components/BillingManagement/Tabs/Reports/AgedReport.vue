<template>
    <div>
        <simple-table
            v-model="filters"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            :pagination-data="paginationData"
            title="Outstanding aged report"
            @search="getReport"
            @page-change="handlePageChange"
            @reset="handleReset"
            :current-per-page="filters.per_page"
            :table-filters="tableFilters"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
        >
            <template v-if="canExport" v-slot:title-actions>
                <CustomButton :disabled="exporting" :dark-mode="darkMode" @click="handleExportReport('csv')">
                    <svg v-if="exporting" aria-hidden="true" class="w-4 h-4 text-gray-200 animate-spin fill-blue-600"
                         viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor"/>
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill"/>
                    </svg>
                    <p v-else>Export</p>
                </CustomButton>
            </template>
            <template v-slot:visible-filters>
                <company-search-autocomplete v-model="filters.company_id" :dark-mode="darkMode"/>
                <user-search-autocomplete
                    v-if="isFinanceOwner"
                    v-model="filters.account_manager_user_id"
                    :dark-mode="darkMode"
                    placeholder="Account Manager"
                >
                </user-search-autocomplete>
                <user-search-autocomplete
                    v-if="isFinanceOwner"
                    v-model="filters.onboarding_manager_user_id"
                    :dark-mode="darkMode"
                    placeholder="Onboarding Manager"
                >
                </user-search-autocomplete>
                <user-search-autocomplete
                    v-if="isFinanceOwner"
                    v-model="filters.business_development_manager_user_id"
                    :dark-mode="darkMode"
                    placeholder="Business Development Manager"
                >
                </user-search-autocomplete>
            </template>
            <template v-slot:row.col.invoice_id="{value}">
                <entity-hyperlink
                    :entity-id="value"
                    :prefix="value"
                    type="invoice"
                >
                </entity-hyperlink>
            </template>
            <template v-slot:row.col.company_id="{item}">
                <entity-hyperlink
                    :prefix="item.company_id"
                    :entity-id="item.company_id"
                    type="company"
                >
                </entity-hyperlink>
            </template>
            <template v-slot:row.col.company_name="{item}">
                <entity-hyperlink
                    :prefix="item.company_name"
                    :entity-id="item.company_id"
                    type="company"
                >
                </entity-hyperlink>
            </template>
        </simple-table>
    </div>
</template>
<script>
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import Autocomplete from "../../../Shared/components/Autocomplete.vue";
import SharedApiService from "../../../Shared/services/api";
import SimpleAlert from "../../../Shared/components/SimpleAlert.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes";
import {SimpleTableFilterTypesEnum} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum";
import InvoiceStatusBadge from "../../components/InvoiceStatusBadge.vue";
import ApiService from "../../services/invoices-report-api.js";
import DisplayInvoiceTransactionStatuses
    from "../../../Billing/CreateInvoiceModalContentCompanyData/components/DisplayInvoiceTransactionStatuses.vue";
import Badge from "../../../Shared/components/Badge.vue";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import useQueryParams from "../../../../../composables/useQueryParams.js";
import EntityHyperlink from "../../components/EntityHyperlink.vue";
import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";
import UserSearchAutocomplete from "../../../Shared/components/User/UserSearchAutocomplete.vue";
import {downloadCsvString} from "../../../../../composables/exportToCsv.js";
import {PERMISSIONS, ROLES, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import {DateTime} from "luxon";

const AGED_REPORT_GROUPING = {
    INVOICE: 'invoice',
    COMPANY: 'company',
}

export default {
    name: "AgedReport",
    components: {
        UserSearchAutocomplete,
        CompanySearchAutocomplete,
        EntityHyperlink,
        LabeledValue,
        Badge,
        DisplayInvoiceTransactionStatuses,
        InvoiceStatusBadge,
        Dropdown,
        SimpleAlert,
        Autocomplete,
        CustomButton,
        SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    created() {
        this.handleReset()
        this.getOptions()
    },
    computed: {
        isFinanceOwner() {
            return this.rolesPermissions.hasAnyRole([
                ROLES.FINANCE_OWNER,
            ])
        },
        canExport() {
            return this.rolesPermissions.hasPermission(PERMISSIONS.BILLING_REPORTS_EXPORT)
        }
    },
    watch: {
        'filters.grouped_by': function (val) {
            const invoiceColIndex = this.headers.findIndex(e => e.field === 'invoice_id')

            this.headers[invoiceColIndex].show = val === 'invoice'
        }
    },
    data() {
        return {
            headers: [
                {title: 'Company Id', field: 'company_id', sortable: true},
                {title: 'Company Name', field: 'company_name', sortable: false},
                {title: 'Invoice', field: 'invoice_id', show: true, sortable: true},
                {title: '0-15 days', field: '0_15', sortable: true},
                {title: '16-30 days', field: '16_30', sortable: true},
                {title: '31-60 days', field: '31_60', sortable: true},
                {title: '61-90 days', field: '61_90', sortable: true},
                {title: '90+ days', field: '90_plus', sortable: true},
                {title: 'Total issued', field: 'total_issued', sortable: true},
            ],
            rolesPermissions: useRolesPermissions(),
            industryOptions: [],
            sharedApi: SharedApiService.make(),
            showViewInvoiceModal: false,
            selectedReceivableInvoice: null,
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.DATE_RANGE,
                    field: 'date_range',
                    title: 'Date Range',
                    timezone: 'America/Denver'
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'grouped_by',
                    title: 'Reference',
                    options: [
                        {id: AGED_REPORT_GROUPING.INVOICE, name: 'Invoice'},
                        {id: AGED_REPORT_GROUPING.COMPANY, name: 'Company'},
                    ]
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.INDUSTRY,
                    field: 'industry_id',
                    title: 'Industry',
                }
            ],
            filters: {
                grouped_by: 'invoice',
                per_page: 25,
            },
            aggregates: {},
            api: ApiService.make(),
            data: [],
            loading: false,
            paginationData: {},
            exporting: false,
        }
    },

    methods: {
        async getOptions() {
            this.getIndustryOptions()
        },
        getIndustryOptions() {
            this.sharedApi.getOdinIndustries()
                .then(resp => {
                    if (resp.data?.data?.status) {
                        this.industryOptions = resp.data.data.industries ?? [];
                    }
                })
        },
        async retrieveReport(params = {}){
            const { from: rawFrom, to: rawTo } = this.filters.date_range ?? {};

            const from = rawFrom
                ? DateTime.fromISO(rawFrom).setZone('America/Denver').startOf('day').toISO()
                : undefined;

            const to = rawTo
                ? DateTime.fromISO(rawTo).setZone('America/Denver').endOf('day').toISO()
                : undefined;

            return await this.api.getAgedReport({
                ...this.filters,
                ...params,
                ...(from || to ? { date_range: { from, to } } : {})
            });
        },
        async getReport() {
            this.loading = true

            const res = await this.retrieveReport();

            this.data = res.data.data
            this.paginationData = {links: res.data.links, ...res.data.meta}

            this.loading = false
        },
        handlePageChange({newPage}) {
            this.filters.page = newPage
            this.getReport()
        },
        async handleExportReport() {
            this.exporting = true

            const response = await this.retrieveReport({
                all: 1
            });

            const formattedItems = response.data.data.map(item => [
                item.company_id,
                item.company_name,
                item.invoice_id,
                item['0_15'],
                item['16_30'],
                item['31_60'],
                item['61_90'],
                item['90_plus'],
                item.total_issued,
            ])

            const headers = [
                'Company Id',
                'Company Name',
                'Invoice Id',
                '0-15 days',
                '16-30 days',
                '31-60 days',
                '61-90 days',
                '90+ days',
                'Total issued',
            ];

            const formattedFilters = Object.entries(this.filters)
                .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
                .map((item, idx) => [idx === 0 ? 'Filters' : '', item])

            const exportInfoData = [
                ['Exported date', this.$filters.dateFromTimestamp((new Date()).toISOString(), 'usWithTime')],
                ...formattedFilters,
                ['Total Items', formattedItems.length],
            ]

            downloadCsvString(
                headers,
                [
                    ...formattedItems,
                    Array.from({length: headers.length}, () => ''),
                    ...exportInfoData,
                ],
                `aged_report_${Math.floor(Date.now() / 1000)}`
            )

            this.exporting = false
        },
        handleReset() {
            this.filters = {
                grouped_by: AGED_REPORT_GROUPING.INVOICE,
                per_page: 25
            }

            this.getReport()
        },
    },
}
</script>
