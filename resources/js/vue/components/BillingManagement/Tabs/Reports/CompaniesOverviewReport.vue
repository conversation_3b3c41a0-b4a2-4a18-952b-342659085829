<template>
    <div>
        <simple-table
            v-model="filters"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            :current-per-page="filters.per_page"
            :pagination-data="paginationData"
            title="Company Overview Reports"
            @search="getReport"
            @reset="handleReset"
            @page-change="handlePageChange"
            :table-filters="tableFilters"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
        >
            <template v-slot:visible-filters>
                <company-search-autocomplete v-model="filters.company_id" :dark-mode="darkMode"/>
            </template>
            <template v-slot:row.col.company_name="{value, item}">
                <entity-hyperlink :prefix="value" :entity-id="item?.company_id" type="company"/>
            </template>
            <template v-slot:row.col.company_id="{value}">
                <entity-hyperlink :prefix="value" :entity-id="value" type="company"/>
            </template>
            <template v-slot:row.col.billing_profiles="{value}">
                <limited-list v-if="value" class="flex flex-col" :list-items="value" show-count="1">
                    <template #list-item="{item}">
                        <div class="flex flex-col gap-1">
                            <payment-method-badge :type="item.payment_method"/>
                            <p>Threshold: {{ item.threshold_in_dollars }}</p>
                            <p>
                                {{ simpleFrequencyHelper.getHumanReadableText(item.frequency_type, item.frequency_data) }}</p>
                        </div>
                    </template>
                    <template #list-remaining>
                        <hover-tooltip tool-tip-style="light" right large :dark-mode="darkMode">
                            <template #title>
                                <badge :dark-mode="darkMode">{{value.length}}</badge>
                            </template>
                            <template #default>
                                <div class="flex flex-wrap justify-center items-center gap-1">
                                    <div v-for="item in value" class="flex flex-col gap-1">
                                        <payment-method-badge :type="item.payment_method"/>
                                        <p>Threshold: {{ item.threshold_in_dollars }}</p>
                                        <p>
                                            {{ simpleFrequencyHelper.getHumanReadableText(item.frequency_type, item.frequency_data) }}</p>
                                    </div>
                                </div>
                            </template>
                        </hover-tooltip>
                    </template>
                </limited-list>
            </template>
        </simple-table>
    </div>
</template>
<script>
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import DatePicker from "@vuepic/vue-datepicker";
import Autocomplete from "../../../Shared/components/Autocomplete.vue";
import SharedApiService from "../../../Shared/services/api";
import SimpleAlert from "../../../Shared/components/SimpleAlert.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import InvoiceStatusBadge from "../../components/InvoiceStatusBadge.vue";
import ApiService from "../../services/invoices-report-api.js";
import EntityHyperlink from "../../components/EntityHyperlink.vue";
import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import {
    SimpleTableFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {useCreditManagementStore} from "../../../../../stores/credit/credit-management.store.js";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import Badge from "../../../Shared/components/Badge.vue";
import PaymentMethodBadge from "../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import {useSimpleFrequencyHelper} from "../../../../../composables/useSimpleFrequencyHelper.js";
import LimitedList from "../../../Shared/components/Simple/LimitedList.vue";
import HoverTooltip from "../../../Shared/components/HoverTooltip.vue";

export default {
    name: "CompaniesOverviewReport",
    components: {
        HoverTooltip,
        LimitedList,
        PaymentMethodBadge,
        Badge,
        CustomInput,
        CompanySearchAutocomplete,
        EntityHyperlink,
        InvoiceStatusBadge,
        Dropdown, SimpleAlert, Autocomplete, DatePicker, CustomButton, SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    created() {
        this.handleReset()
        this.getFilterOptions()
    },
    data() {
        return {
            simpleFrequencyHelper: useSimpleFrequencyHelper(),
            sharedApi: SharedApiService.make(),
            selectedReceivableInvoice: null,
            headers: [
                {title: 'Company Id', field: 'company_id', sortable: true},
                {title: 'Company Name', field: 'company_name', sortable: true},
                {title: 'Last Lead Delivered At', field: 'max_delivered_at', sortable: true},
                {title: 'First Lead Delivered At', field: 'min_delivered_at', sortable: true},
                {title: 'Total To Be Invoiced', field: 'total_to_be_invoiced', sortable: true},
                {title: 'Total Unrejectable', field: 'total_unrejectable', sortable: true},
                {title: 'Billing Profiles', field: 'billing_profiles'},
            ],
            companyOptions: [],
            accountManagerOptions: [],
            successManagerOptions: [],
            invoiceStatusOptions: [],
            tableFilters: [],
            filters: {
                per_page: 25
            },
            api: ApiService.make(),
            data: [],
            loading: false,
            paginationData: {
                perPage: 25
            },
        }
    },

    methods: {
        useSimpleFrequencyHelper,
        async getFilterOptions() {
            this.tableFilters = []
        },
        async getReport() {
            this.loading = true

            const res = await this.api.getCompaniesOverviewReport(this.filters);

            this.data = res.data.data
            this.paginationData = {links: res.data.links, ...res.data.meta}

            this.loading = false
        },
        handlePageChange({newPage}) {
            this.filters.page = newPage
            this.getReport()
        },
        async searchCompanies(query) {
            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companyOptions = res.data.data.companies;
                }
            })
        },
        handleReset() {
            this.filters = {
                perPage: 25,
                per_page: 25,
            }

            this.getReport()
        },
    },
}
</script>
