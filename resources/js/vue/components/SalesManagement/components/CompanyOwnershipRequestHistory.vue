<script setup>
import BaseTable from '../../Shared/components/BaseTable.vue';
import {onMounted, ref} from 'vue';
import ApiService from '../services/api/ownership-api.js';

const props = defineProps({
    darkMode: {
        default: false,
        type:    <PERSON>olean,
    },
});

const loading                  = ref(false);
const apiService               = ApiService.make();
const ownershipRequests        = ref([]);

function getRole(ownershipRequest) {
    return ownershipRequest?.role ?? 'N/A';
}

onMounted(() => {
    getOwnershipRequestHistory();
});

const getOwnershipRequestHistory = () => {
    loading.value = true;
    apiService.getOwnershipRequestHistory().then(res => {
        ownershipRequests.value = res.data.data;
    }).catch(error => {
        console.error('Error fetching posts:', error);
    }).finally(() => {
        loading.value = false;
    });
};

// This exposes the getOwnershipRequestHistory function so that it can be used by the parent component "Dashboard"
defineExpose({
  getOwnershipRequestHistory,
});

</script>

<template>
    <div class="border rounded-lg"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <p class="p-5 font-bold text-sm text-primary-500 uppercase">Company Ownership Request History</p>
        <BaseTable :dark-mode="darkMode" :loading="loading">
            <template #head>
                <tr>
                    <th>Requested By</th>
                    <th>Decided By</th>
                    <th>Company</th>
                    <th>Role</th>
                    <th>Date Requested</th>
                    <th>Date Decided</th>
                    <th>Status</th>
                </tr>
            </template>
            <template #body>
                <tr v-for="ownershipRequest in ownershipRequests.requests" :key="ownershipRequest.id">
                    <td>{{ ownershipRequest?.user?.name ?? 'N/A' }}</td>
                    <td>{{ ownershipRequest?.deciding_user?.name ?? 'N/A'}}</td>
                    <td><a target="_blank"
                           class="text-primary-500" :href="ownershipRequest?.company?.profile_link">{{
                            ownershipRequest?.company?.name ?? 'N/A'
                        }}</a></td>
                    <td class="capitalize">{{ getRole(ownershipRequest) }}</td>
                    <td>{{ ownershipRequest?.date_requested ?? 'N/A' }}</td>
                    <td>{{ ownershipRequest?.date_decided ?? 'N/A' }}</td>
                    <td class="capitalize">{{ ownershipRequest?.status ?? 'N/A' }}</td>
                </tr>
            </template>
        </BaseTable>
    </div>
</template>

<style scoped>

</style>
