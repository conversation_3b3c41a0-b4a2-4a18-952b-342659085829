<template>
    <div>
        <AlertsContainer v-if="alerts.alertActive.value" :dark-mode="darkMode" :alert-type="alerts.alertType.value" :text="alerts.alertText.value" />
        <div class="border rounded-lg" :class="[
            darkMode ? 'bg-dark-module border-dark-border text-white' : 'bg-light-module border-light-border text-black',
            saving ? 'pointer-events-none opacity-50' : '',
        ]">
            <p class="p-5 font-bold text-sm text-primary-500 uppercase">Company Account Assignments</p>
            <SimpleTable
                :dark-mode="darkMode"
                :data="filteredParticipants"
                :headers="headers"
                :loading="loading || saving"
                :no-pagination="true"
                :has-row-click="false"
                title="Round Robin Participants"
                row-classes="gap-5 grid items-center py-2 rounded px-5"
                class="border-none"
                body-wrapper-classes="max-h-[25vh]"
            >
                <template #disclaimer>
                    <div class="my-5">
                        <div class="flex items-center justify-between">
                            <div class="w-128">
                                <CustomInput
                                    v-model="searchParticipants"
                                    :dark-mode="darkMode"
                                    :search-icon="true"
                                    placeholder="Search Participants..."
                                    @keyup.enter.stop="filterParticipants"
                                    @update:model-value="attemptFilter"
                                />
                            </div>
                            <div class="flex items-center gap-x-3">
                                <div class="w-128">
                                    <Autocomplete
                                        search-icon
                                        :dark-mode="darkMode"
                                        class="w-full max-w-sm"
                                        v-model="selectedUser"
                                        :options="users"
                                        :placeholder="'Search by User'"
                                        :loading="loadingAutocomplete"
                                        :create-user-input-option="false"
                                        :min-search-input-len="userSearchMinCharacters"
                                        @search="searchUsers"
                                        @input="handleUserSearchInput"
                                        @keyup.stop.esc="clearSearch"
                                    />
                                </div>
                                <CustomButton
                                    :dark-mode="darkMode"
                                    :disabled="!selectedUser"
                                    @click="addUser"
                                >
                                    Add User
                                </CustomButton>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-slot:row.col.active="{item}">
                    <CustomCheckbox
                        :dark-mode="darkMode"
                        :model-value="item.active"
                        @update:model-value="(newValue) => handleStatusChange(item, newValue)"
                    />
                </template>
                <template v-slot:footer>
                    <div class="flex w-full justify-end">
                        <CustomButton
                            :dark-mode="darkMode"
                            :disabled="!addParticipants.size && !removeParticipants.size"
                            @click="saveChanges"
                        >
                            Save
                        </CustomButton>
                    </div>
                </template>
            </SimpleTable>
        </div>
    </div>
</template>

<script setup>
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import UseAlerts from "../../Shared/composables/UseAlerts.js";
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import { nextTick, onMounted, ref } from "vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import { ApiFactory } from "../services/api/factory.js";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import CustomCheckbox from "../../Shared/SlideWizard/components/CustomCheckbox.vue";

const props = defineProps({
    darkMode: false,
});

const api = ApiFactory.makeApiService('api');

const alerts = UseAlerts();

const loading = ref(false);
const loadingAutocomplete = ref(false);
const saving = ref(false);
const participants = ref([]);
const filteredParticipants = ref([]);
const headers = [
    { title: 'Name', field: 'user.name', sortable: false },
    { title: 'Active', field: 'active', sortable: false },
];

const addParticipants = ref(new Map());
const removeParticipants = ref(new Map());

const users = ref([]);
const selectedUser = ref(null);
const userSearchMinCharacters = 3;

const searchParticipants = ref('');
const filterActive = ref(false);
const filterDebounceTimer = 400;
const filterMinCharacters = 3;
const filterDebounceInterval = ref(null);

onMounted(() => void initialize());

const initialize = async () => {
    await getParticipants();
    loading.value = false;
}

const getParticipants = async () => {
    if (loading.value)
        return;
    loading.value = true;

    const response = await api.getAccountAssignmentParticipants().catch(e => e);
    if (response.data.data?.participants) {
        participants.value = response.data.data.participants;
        filteredParticipants.value = participants.value;
    }
    else {
        alerts.showAlert('error', response.data?.message ?? 'There was an error retrieving the participants.');
    }
    loading.value = false;
}

const handleStatusChange = (participant, newValue) => {
    if (newValue)
        addParticipant(participant.user.id);
    else
        removeParticipant(participant.user.id);
}

const addParticipant = (userId) => {
    if (!removeParticipants.value.delete(userId))
        addParticipants.value.set(userId, true);
}

const removeParticipant = (userId) => {
    if (!addParticipants.value.delete(userId))
        removeParticipants.value.set(userId, true);
}

const saveChanges = async () => {
    if (saving.value)
        return;

    const addIds = Array.from(addParticipants.value.keys());
    const removeIds = Array.from(removeParticipants.value.keys());

    saving.value = true;
    const response = await api.updateAccountAssignmentParticipants(addIds, removeIds).catch(e => e);
    if (response.data?.data?.participants) {
        participants.value = response.data.data.participants;
        filteredParticipants.value = participants.value;
        clearSelections();
        clearFilter();
    }
    else {
        alerts.showAlert('error', response.data?.data?.message ?? "An error occurred updating participants.");
    }

    saving.value = false;
}

const addUser = async () => {
    if (saving.value || !selectedUser.value)
        return;

    addParticipants.value.set(selectedUser.value, true);
    await saveChanges();
    selectedUser.value = null;
}

const clearSelections = () => {
    addParticipants.value.clear();
    removeParticipants.value.clear();
}

const attemptFilter = async () => {
    await nextTick();
    if (!searchParticipants.value && filterActive.value)
        clearFilter();
    else if (searchParticipants.value?.length < filterMinCharacters)
        return;

    if (!filterDebounceInterval.value) {
        const interval = 50;
        let timer = filterDebounceTimer;
        filterDebounceInterval.value = setInterval(() => {
            timer -= interval;
            if (timer <= 0) {
                filterParticipants();
            }
        }, interval);
    }
}

const filterParticipants = () => {
    clearInterval(filterDebounceInterval.value);
    filterDebounceInterval.value = null;

    const rx = new RegExp(searchParticipants.value.replace(/[^0-z]/g, ''), 'i');
    filteredParticipants.value = participants.value.filter(participant => rx.test(participant.user.name.replace(/[^0-z]/g, '')));
    filterActive.value = true;
}

const clearFilter = () => {
    filteredParticipants.value = participants.value;
    filterActive.value = false;
    searchParticipants.value = '';
}

const handleUserSearchInput = ({ target }) => {
    if (!target.value?.trim())
        selectedUser.value = null;
}

const searchUsers = async (searchText) => {
    if (!searchText)
        selectedUser.value = null;
    if (searchText?.length < userSearchMinCharacters)
        return;

    loadingAutocomplete.value = true;
    const excludeIds = participants.value.map(participant => participant.user.id);
    selectedUser.value = null;
    const response = await api.searchUsers(searchText, excludeIds).catch(e => e);
    if (response.data?.data?.users) {
        users.value = response.data.data.users;
    }
    else {
        alerts.showAlert('error', response.data?.data?.message ?? 'There was an error searching Users.');
    }

    loadingAutocomplete.value = false;
}

const clearSearch = () => {
    selectedUser.value = null;
}
</script>