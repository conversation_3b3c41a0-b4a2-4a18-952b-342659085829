<script setup>
import ActionsHandle from '../../Shared/components/ActionsHandle.vue';
import BaseTable from '../../Shared/components/BaseTable.vue';
import {onMounted, ref} from 'vue';
import Modal from '../../Shared/components/Modal.vue';
import CustomButton from '../../Shared/components/CustomButton.vue';
import ApiService from '../services/api/ownership-api.js';
import Alert from '../../Shared/components/Alert.vue';

const alert      = ref({});
const timeout    = ref(null);
const flashAlert = (type, message) => {
    if (timeout) {
        clearTimeout(timeout.value);
        timeout.value = null;
    }
    alert.value   = {type, message};
    timeout.value = setTimeout(() => alert.value = {}, 4000);
};

const props = defineProps({
    darkMode: {
        default: false,
        type:    Boolean,
    },
});

const emit = defineEmits(['ownershipProcessed']);

const loading                  = ref(false);
const apiService               = ApiService.make();
const ownershipRequests        = ref([]);
const ownershipRequestActions  = ref([
    {event: 'approve', name: 'Approve'},
    {event: 'deny', name: 'Deny'},
]);
const selectedOwnershipRequest = ref(null);
const selectedAction           = ref(null);

function processOwnership(ownershipRequest, action) {
    if (action === 'Approve') {
        apiService.approveOwnershipRequest(ownershipRequest.id).then(
            flashAlert('success', 'Ownership approved'),
        ).catch(error => {
            flashAlert('error', error);
        }).finally(() => {
            getOwnershipRequests();

            emit('ownershipProcessed');
        });
    }
    else {
        apiService.denyOwnershipRequest(ownershipRequest.id).then(
            flashAlert('success', 'Ownership denied'),
        ).catch(error => {
            flashAlert('error', error);
        }).finally(() => {
            getOwnershipRequests();

            emit('ownershipProcessed');
        });
    }
    cancelSelection();
}

function toggleProcessingModal(ownershipRequest, action) {
    selectedOwnershipRequest.value = ownershipRequest;
    selectedAction.value           = action;
}

function cancelSelection() {
    selectedOwnershipRequest.value = null;
    selectedAction.value           = null;
}

function getRole(ownershipRequest) {
    return ownershipRequest?.role ?? 'N/A';
}

function getCurrentOwner(ownershipRequest) {
    const requestedByCurrentOwner = ownershipRequest.hasOwnProperty('requested_by_current_owner') &&
                                    ownershipRequest.requested_by_current_owner;

    const currentOwnerExists = ownershipRequest.hasOwnProperty('current_owner');

    if (currentOwnerExists && !requestedByCurrentOwner) {
        return ownershipRequest.current_owner ?? 'None';
    }
    else if (requestedByCurrentOwner) {
        return `Requester Already Owns This Company As ${getRole(ownershipRequest)}`;
    }

    return 'N/A';
}

onMounted(() => {
    getOwnershipRequests();
});

const getOwnershipRequests = () => {
    loading.value = true;
    apiService.getPendingOwnershipRequests().then(res => {
        ownershipRequests.value = res.data.data;
    }).catch(error => {
        console.error('Error fetching posts:', error);
    }).finally(() => {
        loading.value = false;
    });
};
</script>

<template>
    <Alert v-if="alert.message" :dark-mode="darkMode" class="fixed flex justify-center top-12 z-50 inset-x-0"
           :alert-type="alert.type" :text="alert.message"></Alert>
    <div class="border rounded-lg"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <p class="p-5 font-bold text-sm text-primary-500 uppercase">Company Ownership Requests</p>
        <BaseTable :dark-mode="darkMode" :loading="loading">
            <template #head>
                <tr>
                    <th>Requested By</th>
                    <th>Company</th>
                    <th>Role</th>
                    <th>Current Owner</th>
                    <th class="w-20">Actions</th>
                </tr>
            </template>
            <template #body>
                <tr v-for="ownershipRequest in ownershipRequests.requests" :key="ownershipRequest.id">
                    <td>{{ ownershipRequest?.user?.name ?? 'N/A' }}</td>
                    <td><a target="_blank"
                           class="text-primary-500" :href="ownershipRequest?.company?.profile_link">{{
                            ownershipRequest?.company?.name ?? 'N/A'
                        }}</a></td>
                    <td class="capitalize">{{ getRole(ownershipRequest) }}</td>
                    <td>{{ getCurrentOwner(ownershipRequest) }}</td>
                    <td class="w-20">
                        <ActionsHandle
                            :dark-mode="darkMode"
                            width="w-52"
                            :custom-actions="ownershipRequestActions"
                            :no-custom-action="false"
                            :no-delete-button="true"
                            :no-edit-button="true"
                            @approve="toggleProcessingModal(ownershipRequest, 'Approve')"
                            @deny="toggleProcessingModal(ownershipRequest, 'Deny')"
                        >

                        </ActionsHandle>
                    </td>
                </tr>
            </template>
        </BaseTable>
        <Modal
            :dark-mode="darkMode"
            small
            v-if="selectedOwnershipRequest"
            no-min-height
            no-close-button
            no-buttons
            container-classes="p-0"
        >
            <template #header>
                <p class="font-semibold">
                    {{ selectedAction }} Ownership
                </p>
            </template>
            <template #content>
                <div class="p-6">
                    Are you sure you want to {{ selectedAction }} <b>{{ selectedOwnershipRequest.user.name }}</b> to
                    have ownership of <b>{{ selectedOwnershipRequest.company.name }}</b>?
                </div>
                <div class="p-6 rounded-b-lg flex gap-3"
                     :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
                    <CustomButton :dark-mode="darkMode"
                                  @click="processOwnership(selectedOwnershipRequest, selectedAction)"
                                  :color="selectedAction === 'Approve' ? 'green' : 'red'">{{ selectedAction }}
                    </CustomButton>
                    <CustomButton :dark-mode="darkMode" @click="cancelSelection" color="slate-outline">Cancel
                    </CustomButton>
                </div>
            </template>
        </Modal>
    </div>
</template>

<style scoped>

</style>
