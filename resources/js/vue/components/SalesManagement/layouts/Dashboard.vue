<template>
    <div>
        <div class="main-layout font-body">
            <div class="w-full flex flex-col">
                <div class="w-full flex-auto pt-3 relative">
                    <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                        <div class="flex items-center justify-between flex-wrap py-4">
                            <div class="flex justify-between items-center w-full py-2">
                                <h3 class="text-xl font-medium pb-0 leading-none mr-5">
                                    Sales Management
                                </h3>
                            </div>
                        </div>
                    </div>
                    <div class="mx-4 pb-16 md:mx-10 grid grid-cols-2 gap-6">
                        <CompanyOwnershipRequests @ownership-processed="triggerHistoryUpdate" :dark-mode="darkMode"></CompanyOwnershipRequests>
                        <CompanyOwnershipRequestHistory ref="history" :dark-mode="darkMode"></CompanyOwnershipRequestHistory>
                        <AccountsRoundRobin :dark-mode="darkMode" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import CompanyOwnershipRequests from "../components/CompanyOwnershipRequests.vue";
import CompanyOwnershipRequestHistory from '../components/CompanyOwnershipRequestHistory.vue';
import {ref} from 'vue';
import AccountsRoundRobin from "../components/AccountsRoundRobin.vue";

const props = defineProps({
    darkMode: false,
})

// Instantiate the history as a ref and then call the getOwnershipRequestHistory method on the CompanyOwnershipRequestHistory
// This ensures that the history is updated each time an ownership request is processed in the CompanyOwnershipRequests component
const history = ref(null);

const triggerHistoryUpdate = () => {
    history.value.getOwnershipRequestHistory()
}
</script>
