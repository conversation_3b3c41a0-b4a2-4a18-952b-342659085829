<template>
    <div class="m-10" :class="{'text-white': darkMode}">
        <simple-table
            :loading="loading"
            :dark-mode="darkMode"
            title="Company Users"
            :headers="headers"
            :data="data"
            v-model="tableFilter"
            :table-filters="tableFilters"
            :pagination-data="paginationData"
            :current-per-page="perPage"
            @search="handleSearch"
            @reset="handleReset"
            @per-page-change="handlePerPageChange"
            @page-change="handlePageChange"
            column-toggle
            header-alignment="left"
            header-classes="uppercase text-xs font-medium text-center rounded-lg flex"
            body-classes="flex items-center"
        >
            <template v-slot:visible-filters>
                <autocomplete
                    v-model="tableFilter.company_id"
                    :value="tableFilter.company_id"
                    :dark-mode="darkMode"
                    placeholder="Enter Company Name or ID"
                    @search="searchCompany"
                    :options="tableFilterCompanyOptions"
                ></autocomplete>
            </template>
            <template v-slot:row.col.id="{ value, item }">
                <div class="flex flex-col gap-1 truncate">
                    <p class="font-bold text-sm ">{{ value }}</p>
                </div>
            </template>
            <template v-slot:row.col.title_name="{ value, item }">
                <div class="flex flex-col gap-1 truncate">
                    <a :href="`/company-users/${item.id}`" target="_blank" class="font-semibold text-primary-500 text-sm">{{ value }}</a>
                    <div v-if="item.decision_maker" class="inline-flex flex-wrap">
                        <Badge color="cyan" :dark-mode="darkMode">
                            {{ item.decision_maker }}
                        </Badge>
                    </div>
                </div>
            </template>
            <template v-slot:row.col.phone_cell="{ value, item }">
                <div class="flex flex-col gap-1 truncate">
                    <div class="font-bold text-sm " v-if="value && item.phone_office">
                        {{ value }} / {{ item.phone_office}}
                    </div>
                    <div class="font-bold text-sm " v-else-if="value && !item.phone_office">
                        {{value}}
                    </div>
                    <div class="font-bold text-sm " v-else-if="!value && item.phone_office">
                        {{item.phone_office}}
                    </div>
                    <div v-if="item.phone_verified" class="inline-flex flex-wrap">
                        <Badge color="cyan" :dark-mode="darkMode">
                            Verified
                        </Badge>
                    </div>
                    <div v-else class="inline-flex flex-wrap">
                        <Badge color="red" :dark-mode="darkMode">
                            Unverified
                        </Badge>
                    </div>
                </div>
            </template>
            <template v-slot:row.col.email="{ value, item }">
                <div class="flex flex-col gap-1 truncate">
                    <email-link :email="value" />
                    <div v-if="item.email_verified" class="inline-flex flex-wrap ">
                        <Badge color="cyan" :dark-mode="darkMode">
                            Verified
                        </Badge>
                    </div>
                    <div v-else class="inline-flex flex-wrap ">
                        <Badge color="red" :dark-mode="darkMode">
                            Unverified
                        </Badge>
                    </div>
                </div>
            </template>
            <template v-slot:row.col.company_name="{ value, item }">
                <div class="flex flex-col items-center gap-1 truncate">
                    <div class="flex items-center cursor-pointer">
                        <svg class="mr-1 fill-current text-primary-500" width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.8156 6.42855C7.19448 6.42855 7.55784 6.27804 7.82575 6.01013C8.09366 5.74223 8.24417 5.37886 8.24417 4.99998C8.24417 4.6211 8.09366 4.25774 7.82575 3.98983C7.55784 3.72192 7.19448 3.57141 6.8156 3.57141C6.43671 3.57141 6.07335 3.72192 5.80544 3.98983C5.53753 4.25774 5.38702 4.6211 5.38702 4.99998C5.38702 5.37886 5.53753 5.74223 5.80544 6.01013C6.07335 6.27804 6.43671 6.42855 6.8156 6.42855Z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M0 5C0.91 2.10214 3.61714 0 6.81571 0C10.0143 0 12.7214 2.10214 13.6314 5C12.7214 7.89786 10.0143 10 6.81571 10C3.61714 10 0.91 7.89786 0 5ZM9.67286 5C9.67286 5.75776 9.37184 6.48449 8.83602 7.02031C8.3002 7.55612 7.57348 7.85714 6.81571 7.85714C6.05795 7.85714 5.33123 7.55612 4.79541 7.02031C4.25959 6.48449 3.95857 5.75776 3.95857 5C3.95857 4.24224 4.25959 3.51551 4.79541 2.97969C5.33123 2.44388 6.05795 2.14286 6.81571 2.14286C7.57348 2.14286 8.3002 2.44388 8.83602 2.97969C9.37184 3.51551 9.67286 4.24224 9.67286 5Z"/></svg>
                        <a :href="`/companies/${item.company_id}`" target="_blank" class="font-semibold text-primary-500 text-sm">{{ value }}</a>
                    </div>
                </div>
            </template>
            <template v-slot:row.col.last_contact="{ value, item }">
                <div class="flex flex-col gap-1">
                    <div v-if="value" class="flex flex-col">
                        <p class="font-semibold text-sm">{{ value.date }}</p>
                        <div class="flex items-center">
                            <Badge color="cyan" :dark-mode="darkMode">
                                {{ value.direction }}
                            </Badge>
                        </div>
                    </div>
                    <div v-else>
                        <p class="text-sm">Never Contacted</p>
                    </div>
                </div>
            </template>
            <template v-slot:row.col.last_contact_outbound="{ value, item }">
                <div class="flex flex-col gap-1">
                    <div v-if="item.last_contact?.outbound" class="flex flex-col">
                        <p class="text-sm">{{ item.last_contact.outbound.date }}</p>
                        <div class="flex items-center">
                            <Badge color="cyan" :dark-mode="darkMode">
                                {{ item.last_contact.outbound.type }}
                            </Badge>
                        </div>
                    </div>
                    <div v-else>
                        <p class="text-sm">No Outbound Contact</p>
                    </div>
                </div>
            </template>
            <template v-slot:row.col.last_contact_inbound="{ value, item }">
                <div class="flex flex-col gap-1">
                    <div v-if="item.last_contact?.inbound" class="flex flex-col">
                        <p class="text-sm">{{ item.last_contact.inbound.date }}</p>
                        <div class="flex items-center">
                            <Badge color="cyan" :dark-mode="darkMode">
                                {{ item.last_contact.inbound.type }}
                            </Badge>
                        </div>
                    </div>
                    <div v-else>
                        <p class="font-semibold text-sm">No Inbound Contact</p>
                    </div>
                </div>
            </template>
            <template v-slot:row.col.status="{ value, item }">
                <div class="flex flex-col gap-1">
                    <div v-if="value === 'Active'" class="inline-flex flex-wrap">
                        <Badge color="cyan" :dark-mode="darkMode">
                            {{ value }}
                        </Badge>
                    </div>
                    <div v-else class="inline-flex flex-wrap ">
                        <Badge color="red" :dark-mode="darkMode">
                            {{ value }}
                        </Badge>
                    </div>
                </div>
            </template>
            <template v-slot:row.col.notes="{ value, item }">
                <div class="flex flex-col gap-1">
                    <div v-if="value === null">
                        <p class="text-sm">No Notes</p>
                    </div>
                    <div v-else>
                        <p class="font-semibold text-sm  text-primary-500" @click="toggleNotesModal( value )">View</p>
                    </div>
                </div>
            </template>
        </simple-table>
    </div>
    <modal :dark-mode="darkMode" @close="toggleNotesModal" small no-buttons v-if="notesModel">
        <template v-slot:header>
            <h4 class="text-xl font-medium">User Notes</h4>
        </template>
        <template v-slot:content>
            <h4 class="text-xl"> {{ this.userNotes }}</h4>
        </template>
    </modal>
</template>

<script>

import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import {SimpleTableFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum";
import {SimpleTableHiddenFilterTypesEnum} from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes";
import ApiService from "./services/api"
import Modal from "../Shared/components/Modal.vue";
import Autocomplete from "../Shared/components/Autocomplete.vue";
import SharedApiService from "../Shared/services/api";
import MinMaxFilter from "./MinMaxFilter.vue";
import {markRaw} from "vue";
import Badge from "../Shared/components/Badge.vue";
import EmailLink from "../Mailbox/EmailLink.vue";

export default {
    name: "CompanyUsers",
    components: {EmailLink, Badge, Autocomplete, Modal, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            loading: false,
            apiService: null,
            headers: [
                {title: 'ID', field: 'id'},
                {title: 'Title / Name / Decision Maker', field: 'title_name', cols: 2},
                {title: 'Cell / Office Number', field: 'phone_cell', cols: 2},
                {title: 'Email', field: 'email', cols: 2},
                {title: 'Company', field: 'company_name', cols: 2},
                {title: 'Last Contact', field: 'last_contact'},
                {title: 'Last Outbound', field: 'last_contact_outbound', show: false},
                {title: 'Last Inbound', field: 'last_contact_inbound', show: false},
                {title: 'Active/Inactive', field: 'status'},
                {title: 'Notes', field: 'notes'},
            ],
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'user_details',
                    title: "User's Name, Phone or Email",
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'decision_maker',
                    title: 'Authority Level',
                    options: [
                        {name: "Decision Maker", id: "1"},
                        {name: "Standard", id: "0"}
                    ]
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'user_status',
                    title: 'User Status',
                    options: [
                        {name: "Active", id: "1"},
                        {name: "Inactive", id: "0"}
                    ]
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'user_title',
                    title: 'User Title',
                    options: [
                        { name: "No Title", id: "no_title"},
                    ]
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.DATE_RANGE,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'last_contacted',
                    title: 'Last Contacted',
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.CUSTOM,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'total_calls_range',
                    title: 'Total Calls',
                    component: markRaw(MinMaxFilter),
                    getChipsHtml: function (value, filter) {
                        let chip = "";
                        if (value.min && value.max) {
                            chip = "Between " + value.min + " and " + value.max + " Calls"
                        } else if (value.min && !value.max) {
                            chip = "Minimum of " + value.min + " Calls";
                        } else if (value.max && !value.min)  {
                            chip = "Maximum of " + value.max + " Calls"
                        }

                        return chip;
                    }
                },

            ],
            tableFilter: {},
            tableFilterCompanyOptions: [],
            sharedApi: SharedApiService.make(),
            data: [],
            page: 1,
            perPage: 10,
            paginationData: {},
            notesModel: false,
            userNotes: null,
        }
    },
    created() {
        this.apiService = ApiService.make();
        this.getCompanyUsers();
        this.getFilterOptions();
    },
    methods: {
        searchCompany(query) {
            this.sharedApi.getAdminCompanies(query, {}).then(resp => this.tableFilterCompanyOptions = resp.data.data.companies);
            },
        async getCompanyUsers() {
            this.loading = true;
            const response = await this.apiService.getCompanyUsers(this.parseParams());
            const { data, links, meta } = response.data;
            this.data = data;
            this.paginationData = { links, ...meta};
            this.loading = false;
        },
        parseParams(){
            const parsedFilters = {...this.tableFilter};

            return {
                perPage: this.perPage,
                page: this.page,
                ...parsedFilters,
            }
        },
        handleSearch() {
            this.getCompanyUsers();
        },
        handleReset() {
            this.page = 1;
            this.perPage = 100;
            this.tableFilter = {};
            this.getCompanyUsers();
        },
        handlePerPageChange(perPage){
            if (this.perPage === perPage) return
            this.page = 1;
            this.perPage = perPage;
            this.getCompanyUsers();
        },
        handlePageChange({ newPage }){
            if (this.page === newPage) return;
            this.page = newPage;
            this.getCompanyUsers();
        },
        toggleNotesModal(notes = null) {
            this.notesModel = !this.notesModel;
            this.userNotes = notes;
        },
        async getFilterOptions() {
            const $filterTitleIndex = this.tableFilters.findIndex(filter => filter.field === 'user_title');

            const response = await this.apiService.getFilterOptions();

            this.tableFilters[$filterTitleIndex].options.push(...response.data.data.filter_options);
        }
    }
}
</script>
