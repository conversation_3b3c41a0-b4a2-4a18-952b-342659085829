<template>
    <base-filter @apply="$emit('apply')" @cancel="$emit('cancel')" :dark-mode="darkMode" class="relative">
        <div class="p-4 flex flex-col justify-between align-content-between gap-4">
            <div>
                <custom-input :dark-mode="darkMode" placeholder="Minimum Calls" @update:modelValue="updateMinCalls"/>
            </div>
            <div>
                <custom-input :dark-mode="darkMode" placeholder="Max Calls" @update:modelValue="updateMaxCalls"/>
            </div>
        </div>
    </base-filter>
</template>
<script>
import BaseFilter from "../Shared/components/SimpleTable/components/SimpleTableHiddenFilters/BaseFilters/BaseFilter.vue";
import CustomInput from "../Shared/components/CustomInput.vue";

export default {
    name: "MinMaxFilter",
    components: {
        CustomInput,
        BaseFilter,
    },
    props: {
        darkMode: {
            type: <PERSON>olean,
            default: false
        },
        filter: {
            type: Object,
            required: true,
        },
        modelValue: {
            type: String,
            default: ''
        },
    },
    emits: ['update:modelValue', 'apply', 'cancel'],
    data () {
        return {
            range: {
                min: null,
                max: null
            }
        }
    },
    methods: {
        updateMinCalls(event) {
            this.range.min = event
            this.$emit("update:modelValue", this.range);
        },
        updateMaxCalls(event) {
            this.range.max = event
            this.$emit("update:modelValue", this.range);
        }
    },
}
</script>
