<template>
    <div class="main-layout">
        <div>
            <alerts-container v-if="alertActive" :alert-type="alertType" :dark-mode="darkMode" :text="alertText"/>
            <div>
                <display
                    :api="api"
                    :consumer-api="consumerApi"
                    :dark-mode="darkMode"
                    :displayed-queue="displayedQueue"
                    :initial-lead-id="specificLeadId"
                    :minimum-review-time="minimumReviewTimeMillisecs"
                    @activate-alert="showAlert($event.type, $event.text, $event.timeout)"
                    :future-campaign="futureCampaign"
                    :lead="lead"
                />
            </div>
        </div>
    </div>
</template>

<script>
import Display from "./layouts/Display.vue";
import {ApiFactory} from "../LeadProcessing/services/api/factory";
import AlertsContainer from "../LeadProcessing/components/AlertsContainer.vue";

import CommunicatesMixin from "../../mixins/communcation/communicates-mixin";
import ConsumerApiService from "../Shared/services/consumer_api";
import AlertsMixin from "../../mixins/alerts-mixin";

export default {
    components: {AlertsContainer, Display},
    mixins: [CommunicatesMixin, AlertsMixin],
    data: function () {
        return {
            api: null,
            consumerApi: ConsumerApiService.make(),
            userQueue: null,
            displayedQueue: "display",
            alertActive: false,
            alertType: '',
            alertText: '',
            displaySpecificLead: false,
            specificLeadId: this.initialLeadId,
            error: '',
            minimumReviewTimeMillisecs: 10000,
            checkNextLeadIntervalMillisecs: 60000,
            lead: {}
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: 'dummy'
        },
        initialLeadId: {
            type: String,
            default: null
        },
        futureCampaign: {
            type: Boolean,
            default: false
        },
        leadBasic: {
            type: String,
            default: '{}'
        }
    },
    created() {
        this.api = ApiFactory.makeApiService(this.apiDriver);
        this.displaySpecificLead = !!this.specificLeadId;

        this.lead = JSON.parse(this.leadBasic);

        this.getTimeConfigurations();
    },
    methods: {
        getTimeConfigurations() {
            this.api.getTimeConfigurations().then(
                (resp) => {
                    if (resp.data.data.status === true) {
                        this.minimumReviewTimeMillisecs = resp.data.data.configs.minimum_review_time * 1000;
                        this.checkNextLeadIntervalMillisecs = resp.data.data.configs.check_next_lead_interval_seconds * 1000;
                    }
                }
            ).catch(
                (err) => {

                }
            );
        },
    }
}
</script>
