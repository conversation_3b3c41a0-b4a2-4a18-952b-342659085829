<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto pt-3 pb-8 relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">

                <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div v-if="!loading && lead?.basic">
                        <LeadHeader
                            :dark-mode="darkMode"
                            :lead="lead"
                            @toggle-history="toggleHistory"
                        >
                            <template v-slot:buttons>
                                <div v-if="!['Cancelled', 'Allocated'].includes(lead.basic?.status)" class="flex flex-row flex-wrap items-center">
                                    <button @click="showModal('cancel')" class="transition duration-200 text-sm font-semibold focus:outline-none py-2 rounded-md px-5 mr-3"
                                            :class="{'bg-grey-250 hover:bg-grey-400 text-white': !darkMode, 'bg-grey-500 hover:bg-grey-600 text-white': darkMode}">
                                        Cancel Lead
                                    </button>
                                </div>
                            </template>
                        </LeadHeader>
                        <div class="flex py-2 text-red-500" v-if="data.consumerProductData.test_product">
                            This is a test product
                        </div>
                        <div>
                            <div class="grid md:grid-cols-1 lg:grid-cols-3 gap-4">
                                <div class="lg:col-span-2">
                                    <div class="mb-4">
                                        <div class="grid grid-cols-2 gap-4 h-full" v-if="data.consumerProductData">
                                            <consumer-product-date-info
                                                :consumer-product-data="data.consumerProductData"
                                                :dark-mode="darkMode" />
                                            <consumer-product-utility
                                                :consumer-product-data="data.consumerProductData"
                                                :dark-mode="darkMode" />
                                            <consumer-contact
                                                :consumer-product-data="data.consumerProductData"
                                                :consumer-product-id="lead.basic.id"
                                                :dark-mode="darkMode" />
                                            <consumer-product-basic-info
                                                :consumer-product-data="data.consumerProductData"
                                                :dark-mode="darkMode"/>
                                        </div>
                                    </div>
                                    <consumer-product-assignments :consumer-product-id="initialLeadId" :dark-mode="darkMode" v-if="futureCampaign"/>
                                    <interactive-map v-if="data.consumerProductData.address" :dark-mode="darkMode" :address="data.consumerProductData.address"></interactive-map>
                                </div>
                                <div class="lg:col-span-1 lg:col-start-3 lg:row-span-1">
                                    <affiliate-lead-info v-if="lead.basic.affiliate" :dark-mode="darkMode" :consumer-product-id="initialLeadId" class="mb-4"/>
                                    <affiliate-lead-internal-tracking v-if="lead.basic.affiliate" :dark-mode="darkMode" :consumer-product-id="initialLeadId" class="mb-4"/>
                                    <consumer-product-verification :dark-mode="darkMode" :consumer-product-id="initialLeadId" class="mb-4"/>
                                    <comments
                                        class="mb-4"
                                        :dark-mode="darkMode"
                                        :consumer-product-id="lead.basic.id"
                                        :initial-comments="data.consumerProductData?.comments ?? null"
                                    />
                                    <consumer-product-previous-leads class="mb-4" :consumer-product-id="initialLeadId" :dark-mode="darkMode" />
                                    <consumer-reviews class="mb-4" :dark-mode="darkMode" :consumer-product-id="initialLeadId" />
                                    <consumer-product-companies class="mb-4" :dark-mode="darkMode" :consumer-product-id="initialLeadId"/>
                                    <ping-post class="mb-4" :dark-mode="darkMode" :consumer-product-id="lead.basic.id"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else-if="loading">
                        <loading-spinner :dark-mode="darkMode" />
                    </div>
                    <div v-else>
                        <div>
                            <button class="px-5 py-1 rounded text-blue-550 outline-none"
                                    :class="{
                                        'bg-cyan-100 hover:bg-cyan-300': !darkMode,
                                        'bg-dark-background hover:bg-blue-600': darkMode
                                    }"
                                    @click="toggleHistory(true)">
                                History
                            </button>
                        </div>
                        <div class="flex flex-row w-full justify-center items-center text-2xl">
                            No leads in queue
                        </div>
                    </div>
                </div>

                <processing-history v-if="showHistory" :dark-mode="darkMode" :api="api" @close="toggleHistory(false)"></processing-history>
            </div>
        </div>

        <ConsumerProcessingReasonsModal
            v-if="displayModal"
            :dark-mode="darkMode"
            :modal-configuration="this.modalConfiguration"
            :comments="data.consumerProductData?.comments ?? null"
            @close-modal="hideModal"
            @confirm-selection="confirmModalSelection"
        />

    </div>
</template>
<script>
import QueueInteractionMixin from "../mixins/queue-interaction-mixin";
import ConsumerProductDateInfo from "../../Shared/modules/Consumer/ConsumerProductDateInfo.vue";
import ConsumerProductUtility from "../../Shared/modules/Consumer/ConsumerProductUtility.vue";
import ConsumerContact from "../../Shared/modules/Consumer/ConsumerContact.vue";
import InteractiveMap from "../../LeadProcessing/components/InteractiveMap.vue";
import ConsumerProductBasicInfo from "../../Shared/modules/Consumer/ConsumerProductBasicInfo.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Comments from "../../LeadProcessing/components/Comments.vue";
import PingPost from "../../LeadProcessing/components/PingPostLogs.vue";
import ConsumerProductVerification from "../../Shared/modules/Consumer/ConsumerProductVerification.vue";
import ProcessingHistory from "../../LeadProcessing/components/ProcessingHistory.vue";
import ConsumerProductPreviousLeads from "../../Shared/modules/Consumer/ConsumerProductPreviousLeads.vue";
import StatusReason from "../../LeadProcessing/components/StatusReason.vue";
import ConsumerReviews from "../../Shared/modules/Consumer/ConsumerReviews.vue";
import Modal from "../../LeadProcessing/components/Modal.vue";
import CompaniesAlreadySoldTo from "../../Shared/modules/CompaniesAlreadySoldTo.vue";
import ConsumerProductCompanies from "../../Shared/modules/ConsumerProductCompanies.vue";
import ConsumerProductAssignments from "../../Shared/modules/Consumer/ConsumerProductAssignments.vue";
import LegacyAdminMixin from "../../Shared/mixins/legacy-admin-mixin.js";
import {mapState} from 'pinia';
import {useRolesPermissions} from '../../../../stores/roles-permissions.store.js'
import AffiliateLeadInfo from "../../LeadProcessing/components/AffiliateLeadInfo.vue";
import AffiliateLeadInternalTracking from "../../LeadProcessing/components/AffiliateLeadInternalTracking.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import ConsumerProcessingReasonsModal from "../../Shared/modules/Consumer/ConsumerProcessingReasonsModal.vue";
import LeadHeader from "../../LeadProcessing/components/LeadHeader.vue";
import ModalsMixin from "../../LeadProcessing/mixins/modals-mixin.js";

export default {
    components: {
        ConsumerProcessingReasonsModal,
        CustomInput,
        AffiliateLeadInternalTracking,
        AffiliateLeadInfo,
        ConsumerProductAssignments,
        ConsumerProductCompanies,
        CompaniesAlreadySoldTo,
        Modal,
        ConsumerReviews,
        StatusReason,
        ConsumerProductPreviousLeads,
        ProcessingHistory,
        ConsumerProductVerification,
        LeadHeader,
        Comments,
        PingPost,
        LoadingSpinner,
        ConsumerProductBasicInfo,
        InteractiveMap, ConsumerContact, ConsumerProductUtility, ConsumerProductDateInfo},
    mixins: [QueueInteractionMixin, LegacyAdminMixin, ModalsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        futureCampaign: {
            type: Boolean,
            default: false
        },
        lead: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            showHistory: false,
            industryColor: 'rgb(0 129 255)',
        }
    },
    computed: {
        ...mapState(useRolesPermissions, ['hasRole']),
        publicComments() {
            this.data.consumerProductData?.comments?.length
                ? this.data.consumerProductData.comments.filter(comment => comment.public)
                : null;
        },
    },
    methods: {
        toggleHistory(show) {
            this.showHistory = show;
        },
        confirmModalSelection(additionalComments, publicComment, removePublicCommentIds) {
            this.confirmSelection('', additionalComments, publicComment, removePublicCommentIds);
        },
    },
}
</script>
<style scoped>

</style>
