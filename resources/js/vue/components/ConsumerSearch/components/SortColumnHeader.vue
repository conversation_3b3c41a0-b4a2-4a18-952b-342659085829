<template>
	<th v-if="!disableSorting" class="uppercase table-header-text cursor-pointer"
	     :class="[getSorting ? 'text-blue-550 font-semibold' : 'hover:text-cyan-400']" @click="sort">
		<slot></slot>
		<svg v-if="getSorting"
		     :class="{
                                'text-blue-550 rotate-180': getAscending,
                                'text-blue-550': getDescending,
                                'hover:text-cyan-400': getSorting
                            }"
		     class="transform transition-all duration-200 w-6 fill-current"
		     width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd"
			      d="M9.70711 0.292893C10.0976 0.683418 10.0976 1.31658 9.70711 1.70711L5.70711 5.70711C5.51957 5.89464 5.26522 6 5 6C4.73478 6 4.48043 5.89464 4.29289 5.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976315 1.31658 -0.0976314 1.70711 0.292893L5 3.58579L8.29289 0.292893C8.68342 -0.0976311 9.31658 -0.0976311 9.70711 0.292893Z"/>
		</svg>
        <span v-if="precedence">
            {{ precedence }}
        </span>
	</th>
	<th v-else class="uppercase table-header-text p-5">
		<slot></slot>
	</th>
</template>
<script>
export default {
	emits: ['sort'],
	props: {
		disableSorting: {
			type: Boolean,
			default: false
		},
        precedence: {
            type: Number,
            required: false
        },
	},
	data () {
		return {
			sortedDirection: null,
		}
	},
	methods: {
		sort () {
			if (!this.getSorting) {
				this.sortedDirection = 'asc'
				this.$emit('sort', this.sortedDirection)
			} else if (this.getAscending) {
				this.sortedDirection = 'desc'
				this.$emit('sort', this.sortedDirection)
			} else if (this.getDescending) {
				this.sortedDirection = null
				this.$emit('sort', this.sortedDirection)
			}
		}
	},
	computed: {
		getSorting () {
			return !!this.sortedDirection
		},
		getAscending () {
			return this.sortedDirection === 'asc'
		},
		getDescending () {
			return this.sortedDirection === 'desc'
		}
	}
}
</script>

<style scoped>
.table-header-text {
	@apply uppercase text-xs font-bold rounded;
}
</style>
