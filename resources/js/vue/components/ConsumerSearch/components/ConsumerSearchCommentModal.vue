<template>
    <pop-upify classes="absolute shadow rounded-lg min-w-[30vw] max-w-screen-sm">
        <comments :dark-mode="darkMode" :consumer-product-id="consumerProductId">
            <template v-slot:header-actions>
                <button class="modal-default-button" @click="$emit('close')"
                        :class="{'text-black': !darkMode, 'text-grey-120': darkMode}">
                    <svg class="w-4 fill-current" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.9035 4.78018L9.32136 1.36237C9.47273 1.21896 9.56036 1.01977 9.56036 0.796696C9.56036 0.358513 9.20184 0 8.76366 0C8.54058 0 8.34139 0.0876191 8.19799 0.231024L4.78018 3.65686L1.36237 0.231024C1.21896 0.0876191 1.01977 0 0.796696 0C0.358513 0 0 0.358513 0 0.796696C0 1.01977 0.0876191 1.21896 0.231024 1.36237L3.65686 4.78018L0.238999 8.19799C0.0876269 8.34139 0 8.54058 0 8.76366C0 9.20184 0.358513 9.56036 0.796696 9.56036C1.01977 9.56036 1.21896 9.47274 1.36237 9.32933L4.78018 5.9035L8.19799 9.32136C8.34139 9.47273 8.54058 9.56036 8.76366 9.56036C9.20184 9.56036 9.56036 9.20184 9.56036 8.76366C9.56036 8.54058 9.47274 8.34139 9.32933 8.19799L5.9035 4.78018Z"/>
                    </svg>
                </button>
            </template>
        </comments>
    </pop-upify>
</template>
<script>
import PopUpify from "../../Shared/components/PopUpify.vue";
import Comments from "../../LeadProcessing/components/Comments.vue";

export default {
    name: "ConsumerSearchCommentModal",
    components: {Comments, PopUpify},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        consumerProductId: {
            type: Number,
            required: true
        }
    },
    emits: ['close'],
}
</script>
