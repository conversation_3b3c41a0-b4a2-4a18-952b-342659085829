<template>
    <div>
        <alerts-container :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText"/>
        <div class="border-y overflow-y-auto divide-y" :class="[darkMode ? 'border-dark-border divide-dark-border bg-dark-background' : 'border-light-border divide-slate-200 bg-light-background']">
            <div v-if="errorMessage || consumers?.length === 0" class="w-full min-h-96 flex items-center justify-center">
                <p v-if="errorMessage" class="text-red-400 text-sm font-semibold">{{ errorMessage }}</p>
                <p v-else class="text-sm font-semibold">No data found</p>
            </div>
            <div v-else v-for="consumer in consumers" :key="consumer.id" class="px-5 min-h-[140px] py-6 grid gap-5 text-sm items-center" :class="[darkMode ? 'text-slate-100 bg-dark-background hover:bg-dark-module' : 'text-slate-900 bg-light-background hover:bg-light-module', gridCols]">
                <div class="flex flex-col justify-start gap-1">
                    <template v-if="isLeadProcessor">
                        <a target="_blank" :href="consumer.consumer_url" class="text-primary-500 font-bold flex items-center">
                            <p>{{ consumer.id }}</p>
                            <p class="text-slate-500 ml-2">(Lead #{{ consumer.lead_id }})</p>
                        </a>
                    </template>
                    <template v-else>
                        <a target="_blank" :href="`/consumer-product/?consumer_product_id=${ consumer.lead_id }`"  class="text-primary-500 font-bold flex items-center">
                            <p>{{ consumer.id }}</p>
                            <p class="text-slate-500 ml-2">(Lead #{{ consumer.lead_id }})</p>
                        </a>
                    </template>

                    <p class="font-semibold">{{ getProducts(consumer) }}</p>
                    <p class="text-slate-500 font-medium">{{ $filters.dateFromTimestamp(consumer.created_at) }}</p>
                    <div>
                        <Badge v-if="isConsumerTestLead(consumer)" color="amber" :dark-mode="darkMode">
                            <svg class="mr-1 fill-current" width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M12 17.75C12.4142 17.75 12.75 17.4142 12.75 17V11C12.75 10.5858 12.4142 10.25 12 10.25C11.5858 10.25 11.25 10.5858 11.25 11V17C11.25 17.4142 11.5858 17.75 12 17.75Z" fill="#1C274C"></path> <path d="M12 7C12.5523 7 13 7.44772 13 8C13 8.55228 12.5523 9 12 9C11.4477 9 11 8.55228 11 8C11 7.44772 11.4477 7 12 7Z" fill="#1C274C"></path> <path fill-rule="evenodd" clip-rule="evenodd" d="M1.25 12C1.25 6.06294 6.06294 1.25 12 1.25C17.9371 1.25 22.75 6.06294 22.75 12C22.75 17.9371 17.9371 22.75 12 22.75C6.06294 22.75 1.25 17.9371 1.25 12ZM12 2.75C6.89137 2.75 2.75 6.89137 2.75 12C2.75 17.1086 6.89137 21.25 12 21.25C17.1086 21.25 21.25 17.1086 21.25 12C21.25 6.89137 17.1086 2.75 12 2.75Z" fill="#1C274C"></path> </g></svg>
                            Test Lead
                        </Badge>
                    </div>
                </div>
                <div class="flex flex-col justify-start gap-2 w-full max-w-screen-lg mx-auto">
                    <div class="inline-flex flex-wrap items-center gap-2">
                        <Badge v-if="consumer.direct_leads_status !== null" color="cyan" :dark-mode="darkMode">Direct Leads {{ consumer.direct_leads_status }}</Badge>
                        <Badge v-if="consumer.good_to_sell" color="green" :dark-mode="darkMode">
                            <svg class="mr-1 fill-current" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.25 1.75C5.72501 1.75 4.26247 2.3558 3.18414 3.43414C2.1058 4.51247 1.5 5.97501 1.5 7.5C1.5 8.2551 1.64873 9.00281 1.93769 9.70043C2.22666 10.3981 2.6502 11.0319 3.18414 11.5659C3.71807 12.0998 4.35195 12.5233 5.04957 12.8123C5.74719 13.1013 6.4949 13.25 7.25 13.25C8.0051 13.25 8.75281 13.1013 9.45043 12.8123C10.1481 12.5233 10.7819 12.0998 11.3159 11.5659C11.8498 11.0319 12.2733 10.3981 12.5623 9.70043C12.8513 9.00281 13 8.2551 13 7.5C13 5.97501 12.3942 4.51247 11.3159 3.43414C10.2375 2.3558 8.77499 1.75 7.25 1.75ZM2.12348 2.37348C3.48311 1.01384 5.32718 0.25 7.25 0.25C9.17282 0.25 11.0169 1.01384 12.3765 2.37348C13.7362 3.73311 14.5 5.57718 14.5 7.5C14.5 8.45208 14.3125 9.39484 13.9481 10.2745C13.5838 11.1541 13.0497 11.9533 12.3765 12.6265C11.7033 13.2997 10.9041 13.8338 10.0245 14.1981C9.14484 14.5625 8.20208 14.75 7.25 14.75C6.29792 14.75 5.35516 14.5625 4.47554 14.1981C3.59593 13.8338 2.7967 13.2997 2.12348 12.6265C1.45025 11.9533 0.91622 11.1541 0.551873 10.2745C0.187527 9.39484 0 8.45208 0 7.5C0 5.57718 0.763837 3.73311 2.12348 2.37348ZM9.947 5.52523C10.2399 5.81812 10.2399 6.29299 9.947 6.58589L7.05811 9.47477C6.76521 9.76767 6.29034 9.76767 5.99745 9.47477L4.553 8.03033C4.26011 7.73744 4.26011 7.26256 4.553 6.96967C4.8459 6.67678 5.32077 6.67678 5.61366 6.96967L6.52778 7.88378L8.88634 5.52523C9.17923 5.23233 9.6541 5.23233 9.947 5.52523Z"/></svg>
                            Good to Sell
                        </Badge>
                        <Badge v-else color="red" :dark-mode="darkMode">
                            <svg class="mr-1 fill-current" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.75C5.60761 1.75 4.27226 2.30312 3.28769 3.28769C2.30312 4.27226 1.75 5.60761 1.75 7C1.75 7.68944 1.8858 8.37213 2.14963 9.00909C2.41347 9.64605 2.80018 10.2248 3.28769 10.7123C3.7752 11.1998 4.35395 11.5865 4.99091 11.8504C5.62787 12.1142 6.31056 12.25 7 12.25C7.68944 12.25 8.37213 12.1142 9.00909 11.8504C9.64605 11.5865 10.2248 11.1998 10.7123 10.7123C11.1998 10.2248 11.5865 9.64605 11.8504 9.00909C12.1142 8.37213 12.25 7.68944 12.25 7C12.25 5.60761 11.6969 4.27226 10.7123 3.28769C9.72774 2.30312 8.39239 1.75 7 1.75ZM2.22703 2.22703C3.4929 0.961159 5.20979 0.25 7 0.25C8.79021 0.25 10.5071 0.961159 11.773 2.22703C13.0388 3.4929 13.75 5.20979 13.75 7C13.75 7.88642 13.5754 8.76417 13.2362 9.58311C12.897 10.4021 12.3998 11.1462 11.773 11.773C11.1462 12.3998 10.4021 12.897 9.58311 13.2362C8.76417 13.5754 7.88642 13.75 7 13.75C6.11358 13.75 5.23584 13.5754 4.41689 13.2362C3.59794 12.897 2.85382 12.3998 2.22703 11.773C1.60023 11.1462 1.10303 10.4021 0.763813 9.58311C0.424594 8.76417 0.25 7.88642 0.25 7C0.25 5.20979 0.961159 3.4929 2.22703 2.22703ZM5.13634 5.13634C5.42923 4.84344 5.9041 4.84344 6.197 5.13634L7 5.93934L7.803 5.13634C8.0959 4.84344 8.57077 4.84344 8.86366 5.13634C9.15656 5.42923 9.15656 5.9041 8.86366 6.197L8.06066 7L8.86366 7.803C9.15656 8.0959 9.15656 8.57077 8.86366 8.86366C8.57077 9.15656 8.0959 9.15656 7.803 8.86366L7 8.06066L6.197 8.86366C5.9041 9.15656 5.42923 9.15656 5.13634 8.86366C4.84344 8.57077 4.84344 8.0959 5.13634 7.803L5.93934 7L5.13634 6.197C4.84344 5.9041 4.84344 5.42923 5.13634 5.13634Z"/></svg>
                            Awaiting Classification
                        </Badge>
                        <MarketingStrategyBadge v-if="consumer.marketing_strategy" :type="consumer.marketing_strategy"></MarketingStrategyBadge>
                        <Badge v-if="consumer.locked" color="cyan" :dark-mode="darkMode" class="flex gap-2 items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-lock" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <rect x="5" y="11" width="14" height="10" rx="2" />
                                <circle cx="12" cy="16" r="1" />
                                <path d="M8 11v-4a4 4 0 0 1 8 0v4" />
                            </svg>
                            {{consumer.locked === 'system' ? 'Locked By System' : 'Locked By User'}}
                        </Badge>
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <Badge color="purple" :dark-mode="darkMode" v-if="consumer.origin">{{ consumer.origin }}</Badge>
                        <Badge color="orange" :dark-mode="darkMode" v-if="consumer.ad_track">{{ consumer.ad_track }}</Badge>
                        <Badge color="gray" :dark-mode="darkMode" v-if="consumer.status">{{ consumer.status }}</Badge>
                        <simple-icon
                            v-if="consumer.cloned"
                            :dark-mode="darkMode"
                            :color="simpleIcon.colors.BLUE"
                            :icon="simpleIcon.icons.USERS_SOLID"
                            tooltip="Cloned"
                        />
                    </div>
                </div>
                <div class="flex flex-col justify-start gap-1">
                    <p class="font-bold">{{ consumer.name }}</p>
                    <p class="font-semibold text-slate-500">{{ consumer.email }}</p>
                    <div class="flex flex-col items-start justify-center">
                        <div v-if="consumer.can_call || permissionStore.hasRole(ROLES.LEADS_AUDITOR)"
                           @click="call(consumer)"
                           class="font-semibold text-primary-500 mr-2 cursor-pointer">
                            <div v-if="!consumer.can_call" class="text-red-500">
                                Recently Contacted
                            </div>
                            {{ canViewPPI ? $filters.formatPhoneNumber(consumer.phone) || '-' : '***-***-****' }}
                        </div>
                        <div v-else class="font-semibold mr-2">
                            REDACTED
                        </div>
                        <div v-if="!consumer.phone" class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-red-500' : 'text-red-700']">
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24" fill="none" class="mr-1 fill-current">
                                <path fill-rule="evenodd"
                                      d="M17.834 6.166a8.25 8.25 0 1 0 0 11.668.75.75 0 0 1 1.06 1.06c-3.807 3.808-9.98 3.808-13.788 0-3.808-3.807-3.808-9.98 0-13.788 3.807-3.808 9.98-3.808 13.788 0A9.722 9.722 0 0 1 21.75 12c0 .975-.296 1.887-.809 2.571-.514.685-1.28 1.179-2.191 1.179-.904 0-1.666-.487-2.18-1.164a5.25 5.25 0 1 1-.82-6.26V8.25a.75.75 0 0 1 1.5 0V12c0 .682.208 1.27.509 1.671.3.401.659.579.991.579.332 0 .69-.178.991-.579.3-.4.509-.99.509-1.671a8.222 8.222 0 0 0-2.416-5.834ZM15.75 12a3.75 3.75 0 1 0-7.5 0 3.75 3.75 0 0 0 7.5 0Z"
                                      clip-rule="evenodd"/>
                            </svg>
                            Email Only
                        </div>
                        <div v-else-if="consumer.verified" class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-emerald-500' : 'text-emerald-700']">
                            <svg class="mr-1 fill-current" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.25 1.75C5.72501 1.75 4.26247 2.3558 3.18414 3.43414C2.1058 4.51247 1.5 5.97501 1.5 7.5C1.5 8.2551 1.64873 9.00281 1.93769 9.70043C2.22666 10.3981 2.6502 11.0319 3.18414 11.5659C3.71807 12.0998 4.35195 12.5233 5.04957 12.8123C5.74719 13.1013 6.4949 13.25 7.25 13.25C8.0051 13.25 8.75281 13.1013 9.45043 12.8123C10.1481 12.5233 10.7819 12.0998 11.3159 11.5659C11.8498 11.0319 12.2733 10.3981 12.5623 9.70043C12.8513 9.00281 13 8.2551 13 7.5C13 5.97501 12.3942 4.51247 11.3159 3.43414C10.2375 2.3558 8.77499 1.75 7.25 1.75ZM2.12348 2.37348C3.48311 1.01384 5.32718 0.25 7.25 0.25C9.17282 0.25 11.0169 1.01384 12.3765 2.37348C13.7362 3.73311 14.5 5.57718 14.5 7.5C14.5 8.45208 14.3125 9.39484 13.9481 10.2745C13.5838 11.1541 13.0497 11.9533 12.3765 12.6265C11.7033 13.2997 10.9041 13.8338 10.0245 14.1981C9.14484 14.5625 8.20208 14.75 7.25 14.75C6.29792 14.75 5.35516 14.5625 4.47554 14.1981C3.59593 13.8338 2.7967 13.2997 2.12348 12.6265C1.45025 11.9533 0.91622 11.1541 0.551873 10.2745C0.187527 9.39484 0 8.45208 0 7.5C0 5.57718 0.763837 3.73311 2.12348 2.37348ZM9.947 5.52523C10.2399 5.81812 10.2399 6.29299 9.947 6.58589L7.05811 9.47477C6.76521 9.76767 6.29034 9.76767 5.99745 9.47477L4.553 8.03033C4.26011 7.73744 4.26011 7.26256 4.553 6.96967C4.8459 6.67678 5.32077 6.67678 5.61366 6.96967L6.52778 7.88378L8.88634 5.52523C9.17923 5.23233 9.6541 5.23233 9.947 5.52523Z"/></svg>
                            SMS Verified
                        </div>
                        <div v-else class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-red-500' : 'text-red-700']">
                            <svg class="mr-1 fill-current" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.75C5.60761 1.75 4.27226 2.30312 3.28769 3.28769C2.30312 4.27226 1.75 5.60761 1.75 7C1.75 7.68944 1.8858 8.37213 2.14963 9.00909C2.41347 9.64605 2.80018 10.2248 3.28769 10.7123C3.7752 11.1998 4.35395 11.5865 4.99091 11.8504C5.62787 12.1142 6.31056 12.25 7 12.25C7.68944 12.25 8.37213 12.1142 9.00909 11.8504C9.64605 11.5865 10.2248 11.1998 10.7123 10.7123C11.1998 10.2248 11.5865 9.64605 11.8504 9.00909C12.1142 8.37213 12.25 7.68944 12.25 7C12.25 5.60761 11.6969 4.27226 10.7123 3.28769C9.72774 2.30312 8.39239 1.75 7 1.75ZM2.22703 2.22703C3.4929 0.961159 5.20979 0.25 7 0.25C8.79021 0.25 10.5071 0.961159 11.773 2.22703C13.0388 3.4929 13.75 5.20979 13.75 7C13.75 7.88642 13.5754 8.76417 13.2362 9.58311C12.897 10.4021 12.3998 11.1462 11.773 11.773C11.1462 12.3998 10.4021 12.897 9.58311 13.2362C8.76417 13.5754 7.88642 13.75 7 13.75C6.11358 13.75 5.23584 13.5754 4.41689 13.2362C3.59794 12.897 2.85382 12.3998 2.22703 11.773C1.60023 11.1462 1.10303 10.4021 0.763813 9.58311C0.424594 8.76417 0.25 7.88642 0.25 7C0.25 5.20979 0.961159 3.4929 2.22703 2.22703ZM5.13634 5.13634C5.42923 4.84344 5.9041 4.84344 6.197 5.13634L7 5.93934L7.803 5.13634C8.0959 4.84344 8.57077 4.84344 8.86366 5.13634C9.15656 5.42923 9.15656 5.9041 8.86366 6.197L8.06066 7L8.86366 7.803C9.15656 8.0959 9.15656 8.57077 8.86366 8.86366C8.57077 9.15656 8.0959 9.15656 7.803 8.86366L7 8.06066L6.197 8.86366C5.9041 9.15656 5.42923 9.15656 5.13634 8.86366C4.84344 8.57077 4.84344 8.0959 5.13634 7.803L5.93934 7L5.13634 6.197C4.84344 5.9041 4.84344 5.42923 5.13634 5.13634Z"/></svg>
                            SMS Unverified
                        </div>
                    </div>
                    <div v-if="consumer.quotesPreviouslyRaised?.length > 0" class="cursor-context-menu inline-flex items-center font-medium relative group" :class="[darkMode ? 'text-rose-500' : 'text-rose-700']">
                        <svg class="mr-1 fill-current" width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.8156 6.42855C7.19448 6.42855 7.55784 6.27804 7.82575 6.01013C8.09366 5.74223 8.24417 5.37886 8.24417 4.99998C8.24417 4.6211 8.09366 4.25774 7.82575 3.98983C7.55784 3.72192 7.19448 3.57141 6.8156 3.57141C6.43671 3.57141 6.07335 3.72192 5.80544 3.98983C5.53753 4.25774 5.38702 4.6211 5.38702 4.99998C5.38702 5.37886 5.53753 5.74223 5.80544 6.01013C6.07335 6.27804 6.43671 6.42855 6.8156 6.42855Z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M0 5C0.91 2.10214 3.61714 0 6.81571 0C10.0143 0 12.7214 2.10214 13.6314 5C12.7214 7.89786 10.0143 10 6.81571 10C3.61714 10 0.91 7.89786 0 5ZM9.67286 5C9.67286 5.75776 9.37184 6.48449 8.83602 7.02031C8.3002 7.55612 7.57348 7.85714 6.81571 7.85714C6.05795 7.85714 5.33123 7.55612 4.79541 7.02031C4.25959 6.48449 3.95857 5.75776 3.95857 5C3.95857 4.24224 4.25959 3.51551 4.79541 2.97969C5.33123 2.44388 6.05795 2.14286 6.81571 2.14286C7.57348 2.14286 8.3002 2.44388 8.83602 2.97969C9.37184 3.51551 9.67286 4.24224 9.67286 5Z"/></svg>
                        Quotes previously raised
                        <div v-for="raisedBy in consumer.quotesPreviouslyRaised" :key="raisedBy" class="hidden z-40 group-hover:block absolute top-0 left-0 w-[50rem] rounded-md shadow-module border" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                            <p class="p-4 text-red font-bold">Email Address: {{ raisedBy.email }} or IP: {{ raisedBy.ip }} has also raised the following quotes:</p>
                            <div class="grid grid-cols-5 gap-4 px-4 border-b pb-2" :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                                <p class="table-header-text">ID</p>
                                <p class="table-header-text">DATE RAISED</p>
                                <p class="table-header-text">STATUS</p>
                                <p class="table-header-text">NAME</p>
                                <p class="table-header-text">SOURCE</p>
                            </div>
                            <div class="overflow-auto h-40">
                                <div v-for="quote in raisedBy.quotes" class="grid grid-cols-5 gap-4 px-4 py-2 text-sm" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">
                                    <p>{{quote.id}}</p>
                                    <p>{{quote.dateRaised}}</p>
                                    <p>{{quote.status}}</p>
                                    <p>{{quote.name}}</p>
                                    <p>{{quote.source}}</p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div>
                    <p class="font-bold">{{ consumer.address }}</p>
                    <a :href="consumer.map_url" target="_blank" class="text-primary-500">View map</a>
                </div>
                <div class="flex flex-col justify-start gap-1">
                    <p class="font-bold">{{ consumer.industry }}</p>
                    <div class="inline-flex items-center font-medium relative group">
                        <p>{{ consumer.service }}</p>
                    </div>
                    <p class="font-bold">{{ consumer.contact_requests }} Quotes Requested</p>
                </div>
                <div v-if="showComments" class="flex flex-col justify-start gap-1">
                    <div v-if="consumer.last_contacted_at" class="mb-1">
                        Last Contact: {{ $filters.dateFromTimestamp(consumer.last_contacted_at, 'usWithTime') }}
                    </div>
                    <div @click="showCommentsForLead = consumer.lead_id" class="cursor-pointer inline-flex items-center font-medium relative group gap-1 text-primary-500">
                        <simple-icon :icon="simpleIcon.icons.CHAT_BUBBLE_BOTTOM_CENTER_TEXT" :color="simpleIcon.colors.BLUE" :dark-mode="darkMode"/>
                        View Comments
                    </div>
                </div>
                <div class="flex flex-col justify-start gap-1 col-span-2">
                    <table v-if="consumerHasProductAssigned(consumer)" class="mb-2">
                        <tr class="mb-2">
                            <th class="font-bold text-left">Direct Leads</th>
                            <th class="font-bold text-left">Lead</th>
                            <th></th>
                        </tr>
                        <tr>
                            <td>
                                <Badge :dark-mode="darkMode" color="indigo" v-if="getDirectLeads(consumer).length">
                                    {{ getDirectLeads(consumer).reduce((acc, lead) => acc + lead.companies_assigned?.length ?? 0, 0) }} / {{ consumer.contact_requests }}
                                </Badge>
                                <span v-else class="font-bold text-sm">N/A</span>
                            </td>
                            <td>
                                <Badge :dark-mode="darkMode" color="purple" v-if="getLeads(consumer).length">
                                    {{ getLeads(consumer).reduce((acc, lead) => acc + lead.companies_assigned?.length ?? 0, 0) }} / {{ consumer.contact_requests }}
                                </Badge>
                                <span v-else class="font-bold text-sm">N/A</span>
                            </td>
                            <td>
                                <div v-if="consumerHasProductAssigned(consumer)" class=" inline-flex items-center font-medium relative group">
                                    <div class="grid gap-1">
                                        <div class="whitespace-pre" v-for="consumerProduct in consumer.consumer_products" :key="consumerProduct.id">
                                            <p v-if="consumerProduct.secondary_service" class="mt-2 text-slate-500 whitespace-normal">
                                                Secondary service - {{ consumerProduct.secondary_service }}
                                            </p>
                                            <div v-if="consumerProduct.companies_assigned.length > 0" v-for="assignedCompany in consumerProduct.companies_assigned">
                                                <a class="hover:underline" :href="assignedCompany.link" :title="assignedCompany.name" target="_blank">
                                                    {{ assignedCompany.name + ' - ' + assignedCompany.cost }}
                                                </a>
                                                <div v-if="assignedCompany.delivered" class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-emerald-500' : 'text-emerald-700']">
                                                    <svg class="mr-1 fill-current" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.25 1.75C5.72501 1.75 4.26247 2.3558 3.18414 3.43414C2.1058 4.51247 1.5 5.97501 1.5 7.5C1.5 8.2551 1.64873 9.00281 1.93769 9.70043C2.22666 10.3981 2.6502 11.0319 3.18414 11.5659C3.71807 12.0998 4.35195 12.5233 5.04957 12.8123C5.74719 13.1013 6.4949 13.25 7.25 13.25C8.0051 13.25 8.75281 13.1013 9.45043 12.8123C10.1481 12.5233 10.7819 12.0998 11.3159 11.5659C11.8498 11.0319 12.2733 10.3981 12.5623 9.70043C12.8513 9.00281 13 8.2551 13 7.5C13 5.97501 12.3942 4.51247 11.3159 3.43414C10.2375 2.3558 8.77499 1.75 7.25 1.75ZM2.12348 2.37348C3.48311 1.01384 5.32718 0.25 7.25 0.25C9.17282 0.25 11.0169 1.01384 12.3765 2.37348C13.7362 3.73311 14.5 5.57718 14.5 7.5C14.5 8.45208 14.3125 9.39484 13.9481 10.2745C13.5838 11.1541 13.0497 11.9533 12.3765 12.6265C11.7033 13.2997 10.9041 13.8338 10.0245 14.1981C9.14484 14.5625 8.20208 14.75 7.25 14.75C6.29792 14.75 5.35516 14.5625 4.47554 14.1981C3.59593 13.8338 2.7967 13.2997 2.12348 12.6265C1.45025 11.9533 0.91622 11.1541 0.551873 10.2745C0.187527 9.39484 0 8.45208 0 7.5C0 5.57718 0.763837 3.73311 2.12348 2.37348ZM9.947 5.52523C10.2399 5.81812 10.2399 6.29299 9.947 6.58589L7.05811 9.47477C6.76521 9.76767 6.29034 9.76767 5.99745 9.47477L4.553 8.03033C4.26011 7.73744 4.26011 7.26256 4.553 6.96967C4.8459 6.67678 5.32077 6.67678 5.61366 6.96967L6.52778 7.88378L8.88634 5.52523C9.17923 5.23233 9.6541 5.23233 9.947 5.52523Z"/></svg>
                                                    Delivered
                                                </div>
                                                <div v-else class="inline-flex flex-wrap items-center font-medium" :class="[darkMode ? 'text-red-500' : 'text-red-700']">
                                                    <svg class="mr-1 fill-current" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.75C5.60761 1.75 4.27226 2.30312 3.28769 3.28769C2.30312 4.27226 1.75 5.60761 1.75 7C1.75 7.68944 1.8858 8.37213 2.14963 9.00909C2.41347 9.64605 2.80018 10.2248 3.28769 10.7123C3.7752 11.1998 4.35395 11.5865 4.99091 11.8504C5.62787 12.1142 6.31056 12.25 7 12.25C7.68944 12.25 8.37213 12.1142 9.00909 11.8504C9.64605 11.5865 10.2248 11.1998 10.7123 10.7123C11.1998 10.2248 11.5865 9.64605 11.8504 9.00909C12.1142 8.37213 12.25 7.68944 12.25 7C12.25 5.60761 11.6969 4.27226 10.7123 3.28769C9.72774 2.30312 8.39239 1.75 7 1.75ZM2.22703 2.22703C3.4929 0.961159 5.20979 0.25 7 0.25C8.79021 0.25 10.5071 0.961159 11.773 2.22703C13.0388 3.4929 13.75 5.20979 13.75 7C13.75 7.88642 13.5754 8.76417 13.2362 9.58311C12.897 10.4021 12.3998 11.1462 11.773 11.773C11.1462 12.3998 10.4021 12.897 9.58311 13.2362C8.76417 13.5754 7.88642 13.75 7 13.75C6.11358 13.75 5.23584 13.5754 4.41689 13.2362C3.59794 12.897 2.85382 12.3998 2.22703 11.773C1.60023 11.1462 1.10303 10.4021 0.763813 9.58311C0.424594 8.76417 0.25 7.88642 0.25 7C0.25 5.20979 0.961159 3.4929 2.22703 2.22703ZM5.13634 5.13634C5.42923 4.84344 5.9041 4.84344 6.197 5.13634L7 5.93934L7.803 5.13634C8.0959 4.84344 8.57077 4.84344 8.86366 5.13634C9.15656 5.42923 9.15656 5.9041 8.86366 6.197L8.06066 7L8.86366 7.803C9.15656 8.0959 9.15656 8.57077 8.86366 8.86366C8.57077 9.15656 8.0959 9.15656 7.803 8.86366L7 8.06066L6.197 8.86366C5.9041 9.15656 5.42923 9.15656 5.13634 8.86366C4.84344 8.57077 4.84344 8.0959 5.13634 7.803L5.93934 7L5.13634 6.197C4.84344 5.9041 4.84344 5.42923 5.13634 5.13634Z"/></svg>
                                                    Not delivered
                                                </div>
                                            </div>
                                            <div v-else>unassigned</div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                    <div v-else>
                        <p>Unassigned</p>
                        <div v-if="consumer.available_campaigns != null">
                            Location availability: {{ consumer.available_campaigns }} campaigns, {{ consumer.available_budget === -1 ? 'unlimited budget' : `$${consumer.available_budget}` }}
                        </div>
                    </div>
                </div>
                <div v-if="hasAvailableBudget" class="font-medium">
                    <div>
                        Budget: {{ consumer.available_budget == -1 ? 'Unlimited' : '$' + consumer.available_budget }}
                    </div>
                    <div>
                        Campaigns: {{ consumer.available_campaigns }}
                    </div>
                </div>
            </div>
        </div>
        <ConsumerSearchCommentModal
            v-if="showCommentsForLead !== null"
            @close="showCommentsForLead = null"
            :consumer-product-id="showCommentsForLead"
            :dark-mode="darkMode"
        />
    </div>
</template>

<script>
import {ApiFactory} from "../services/api/factory";
import Badge from "../../Shared/components/Badge.vue";
import MarketingStrategyBadge from "../../Shared/components/MarketingStrategyBadge.vue";
import {mapState} from "pinia";
import {useCallStore} from "../../../../stores/call.store.js";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin.js";
import AlertsMixin from "../../../mixins/alerts-mixin.js";
import {getPhoneNumber} from "../../../../modules/contacts/helpers.js";
import {CommunicationRelationTypes} from "../../Communications/enums/communication.js";
import {PERMISSIONS, ROLES, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import ConsumerSearchCommentModal from "./ConsumerSearchCommentModal.vue";
const simpleIcon = useSimpleIcon()
export default {
    name: "ConsumerSearchTableBody",
    components: {ConsumerSearchCommentModal, SimpleIcon, AlertsContainer, MarketingStrategyBadge, Badge},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        consumers: {
            type: Array,
            default: []
        },
        showComments: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: 'api',
        },
        errorMessage: {
            type: String,
            default: null
        }
    },
    mixins: [DispatchesGlobalEventsMixin, AlertsMixin],
    data() {
      return {
          consumerSearchApi: ApiFactory.makeApiService(this.apiDriver),
          isLeadProcessor: null,
          permissionStore: useRolesPermissions(),
          simpleIcon,
          showCommentsForLead: null,
      }
    },
    mounted() {
      this.checkLeadProcessor();
    },
    computed: {
        ROLES() {
            return ROLES
        },
        ...mapState(useCallStore, [
            'callActive'
        ]),
        canViewPPI() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_VIEW_LEAD_PII)
        },
        hasAvailableBudget() {
            return this.consumers[0]?.available_budget != null;
        },
        gridCols() {
            let cols = 7;
            if (this.hasAvailableBudget) {
                cols++
            }
            if (this.showComments) {
                cols++
            }
            return `grid-cols-${cols}`
        }
    },
    methods: {
        isConsumerTestLead(consumer){
          return consumer.consumer_products.some(cp => cp.is_test_lead)
        },
        getProducts(consumer) {
            return consumer.consumer_products?.reduce((output, consumerProduct) => {
                return output.includes(consumerProduct.product)
                    ? output
                    : [...output, consumerProduct.product]
            }, []).join(' / ');
        },
        consumerHasProductAssigned(consumer) {
            return !!(consumer.consumer_products?.reduce((output, consumerProduct) => {
                return output + consumerProduct.companies_assigned?.length ?? 0;
            }, 0));
        },
        async checkLeadProcessor() {
            this.consumerSearchApi.checkLeadProcessor().then(resp => {
                this.isLeadProcessor = resp.data.data.isLeadProcessor;
            }).catch(err => {
                console.error("Error checking lead processor status: ", err);
            })
        },
        getLeads(consumer) {
            return consumer.consumer_products?.filter(consumerProduct => consumerProduct.product?.toLowerCase() === 'lead') ?? [];
        },
        getDirectLeads(consumer) {
            return consumer.consumer_products?.filter(consumerProduct => consumerProduct.product?.toLowerCase() === 'direct leads') ?? [];
        },
        call(consumer) {
            if (!this.callActive) {
                const primaryConsumerProductId = consumer.consumer_products[0].id;
                this.dispatchGlobalEvent('call', {
                    phone: getPhoneNumber(consumer.phone),
                    name: consumer.name,
                    id: primaryConsumerProductId,
                    relId: primaryConsumerProductId,
                    relType: CommunicationRelationTypes.CONSUMER_PRODUCT,
                });
            } else {
                console.warn('Please end your current call before making another one.')
                this.showAlert('warning', 'Please end your current call before making another one.')
            }
        },
    }
}
</script>

<style scoped>

</style>
