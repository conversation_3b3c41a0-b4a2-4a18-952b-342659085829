<template>
    <div>
        <div class="border rounded-lg"
             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border',
                saving ? 'pointer-events-none opacity-50 greyscale-[50%]' : '']"
        >
            <AlertsContainer
                v-if="alertActive"
                :alert-type="alertType"
                :text="alertText"
                :dark-mode="darkMode"
            />
            <div class="p-5 flex items-center">
                <h5 class="text-blue-550 text-sm uppercase font-semibold leading-tight">Consumer Requests Search</h5>
            </div>
            <div class="flex items-center flex-wrap gap-3 px-5 pb-5">
                <CustomInput
                    :dark-mode="darkMode"
                    class="w-full max-w-sm"
                    search-icon placeholder="Search by zipcode, address, or person"
                    v-model="searchInputGeneral"
                    @keyup.enter="submitSearch"
                />
                <CustomInput
                    :dark-mode="darkMode"
                    class="w-full max-w-md"
                    search-icon placeholder="Search by Quote ID or Legacy ID"
                    v-model="searchInputId"
                    @keyup.enter="submitSearch"
                    type="number"
                />
                <autocomplete
                    search-icon
                    :dark-mode="darkMode"
                    class="w-full max-w-sm"
                    v-model="selectedCompany"
                    :options="companies"
                    :placeholder="'Search by Company'"
                    :create-user-input-option="false"
                    @search="searchCompanies($event)"
                    @input="handleAutocompleteInput"
                >
                </autocomplete>
                <div>
                    <div v-if="campaignLoading">
                        <LoadingSpinner size="w-8 h-8" />
                    </div>

                    <div v-else-if="showCampaignFilter" class="min-w-[20rem] max-w-sm">
                        <MultiSelect
                            :dark-mode="darkMode"
                            :options="companyCampaigns"
                            :show-search-box="false"
                            text-place-holder="Select Campaign"
                            @input="selectedCampaigns = $event"
                            :selected-ids="selectedCampaigns"
                        >
                            <template #option="{option}">
                                <div class="flex gap-1 items-center">
                                    <div>
                                        {{ option?.name }}
                                    </div>
                                    <company-campaign-status-badge
                                        :status="option.payload.status"
                                        :dark-mode="darkMode"
                                    />
                                </div>
                            </template>
                        </MultiSelect>
                    </div>
                </div>
                <div v-if="selectedCompany">
                    <dropdown
                        class="min-w-32"
                        v-model="soldTo"
                        :dark-mode="darkMode"
                        placeholder="Scope By"
                        :options="COMPANY_SCOPE_OPTIONS"
                    />
                </div>
                <CustomButton type="submit" :dark-mode="darkMode"
                              @click="submitSearch"
                >
                    Search
                </CustomButton>
                <CustomButton :dark-mode="darkMode" color="slate-inverse" type="reset"
                              @click="resetFilters"
                >
                    Reset
                </CustomButton>
                <Filterable
                    :dark-mode="darkMode"
                    :filters="filters"
                    :custom-categories="customCategories"
                    v-model="filterInputs"
                    @update:defaults="updateFilterDefaults"
                    @update:filterOptions="getFilterOptionUpdates"
                    @update:customValue="handleCustomUpdate"
                    @custom:delete-option="openDeletePresetModal"
                />
                <CustomButton
                    :dark-mode="darkMode"
                    color="slate-inverse"
                    @click="openSavePresetModal"
                >
                    Save Preset
                </CustomButton>
                <button
                    v-if="canExportConsumers"
                    class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                    @click.stop="handleExportToCsv"
                >
                    Export to CSV
                </button>
                <button
                    v-if="canCreateMarketingCampaign || canEditMarketingCampaigns"
                    class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                    @click.stop="showMarketingCampaignModal = true"
                >
                    Marketing Campaign
                </button>
            </div>
            <FilterableActivePills
                class="px-8 mb-6"
                v-if="filters.length"
                :filters="filters"
                :active-filters="filterInputs"
                :dark-mode="darkMode"
                @reset-filter="clearFilter"
            />
            <ConsumerSearchTableHeader
                :consumers="consumers"
                :dark-mode="darkMode"
                :showComments="showComments"
                @sort-date="applyDateSortToSearch"
            />
            <LoadingSpinner v-if="loading"/>
            <ConsumerSearchTableBody v-else
                :consumers="consumers"
                :dark-mode="darkMode"
                :showComments="showComments"
                :error-message="errorHandler.message"
            />
            <div class="flex items-center justify-between">
                <div class="flex">
                    <div class="p-3 flex items-center gap-1" v-for="aggregate in CONSUMER_SEARCH_AGGREGATES">
                        <span class="text-sm text-slate-500">{{ aggregate.name }}</span>
                        <loading-spinner v-if="aggregatesLoading" size="xs" :dark-mode="darkMode"/>
                        <span v-else class="text-sm"> {{ aggregates[aggregate.field] ?? 0}}</span>
                    </div>
                </div>
                <div class="p-3 flex items-center justify-end gap-2">
                    <div>
                        <span class="text-sm text-slate-500">Results Per Page</span>
                    </div>
                    <div>
                        <Dropdown
                            :dark-mode="darkMode"
                            placement="top"
                            :options="perPageOptions"
                            v-model="perPageSelection"
                            :selected="25"
                            @update:modelValue="() => handlePaginationEvent({newPage: 1})"
                        />
                    </div>
                    <Pagination
                        :dark-mode="darkMode"
                        :pagination-data="paginationData.meta ?? {}"
                        :show-pagination="true"
                        @change-page="handlePaginationEvent"
                    />
                </div>
            </div>
        </div>
        <Modal
            v-if="showPresetModal"
            @close="closePresetModal"
            @confirm="confirmPresetModal"
            :dark-mode="darkMode"
            :small="true"
            :confirm-text="deletingPreset ? 'Delete' : 'Save'"
        >
            <template v-slot:header>
                {{ deletingPreset ? 'Delete' : 'Save' }} Filter Preset
            </template>
            <template v-slot:content>
                <LoadingSpinner
                    v-if="loading || saving"
                />
                <div v-else>
                    <div v-if="!deletingPreset">
                        <div @keyup.enter="confirmPresetModal">
                            <CustomInput
                                label="Filter Preset Name"
                                :dark-mode="darkMode"
                                v-model="presetName"
                                placeholder="Enter a unique name..."
                            />
                        </div>
                    </div>
                    <div v-else>
                        <p>Are you sure you wish to delete the preset '{{ deletingPreset }}'?</p>
                    </div>
                    <div class="my-2 text-center text-red-500" v-if="modalError">
                        {{ modalError }}
                    </div>
                </div>
            </template>
        </Modal>
        <marketing-campaign-modal
            v-if="showMarketingCampaignModal"
            @close="closeMarketingCampaignModal"
            :filters="allInputs"
            :consumer-count="paginationData?.meta?.total"
            :dark-mode="darkMode"
        />
    </div>
</template>

<script>
import CustomButton from "../Shared/components/CustomButton.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import Filterable from "../Shared/components/Filterables/Filterable.vue";
import { ApiFactory } from "./services/api/factory.js";
import Dropdown from "../LeadProcessing/components/Dropdown.vue";
import Pagination from "../Shared/components/Pagination.vue";
import ConsumerSearchTableHeader from "./components/ConsumerSearchTableHeader.vue";
import ConsumerSearchTableBody from "./components/ConsumerSearchTableBody.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import FilterableActivePills from "../Shared/components/Filterables/FilterableActivePills.vue";
import {nextTick} from "vue";
import Modal from "../LeadProcessing/components/Modal.vue";
import AlertsContainer from "../LeadProcessing/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import Autocomplete from "../Shared/components/Autocomplete.vue";
import SharedApiService from "../Shared/services/api";
import MultiSelect from "../Shared/components/MultiSelect.vue";
import useErrorHandler from "../../../composables/useErrorHandler.js";
import CompanyCampaignStatusBadge from "../Shared/components/CompanyCampaign/CompanyCampaignStatusBadge.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store.js";
import {downloadCsvString} from "../../../composables/exportToCsv.js";
import MarketingCampaignModal from "../MarketingCampaign/MarketingCampaignModal.vue";

const CONSUMER_SEARCH_AGGREGATES = [
    {name: "Average Installers Per Sold Lead:", field: "average_installers_lead_sold"},
    {name: "Standard Leads:", field: "standard_lead_count"},
    {name: "Premium Leads:", field: "premium_lead_count"},
]

const COMPANY_SCOPE_OPTIONS = [
    {id: 'all', name: 'All'},
    {id: 'not_sold_to', name: 'Not Sold To Company'},
    {id: 'sold_to', name: 'Sold To Company'},
]

export default {
    name: "ConsumerSearch",
    components: {
        MarketingCampaignModal,
        CompanyCampaignStatusBadge,
        MultiSelect,
        AlertsContainer,
        Modal,
        FilterableActivePills,
        LoadingSpinner,
        ConsumerSearchTableBody,
        ConsumerSearchTableHeader,
        Pagination,
        Dropdown,
        Filterable,
        CustomInput,
        CustomButton,
        Autocomplete,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: 'api',
        }
    },
    mixins: [AlertsMixin],
    data() {
        return {
            consumerSearchApi: ApiFactory.makeApiService(this.apiDriver),
            filtersPopup: false,
            selectedFilter: null,
            selectedOption: null,
            filters: [],
            filterInputs: {},
            filterDefaults: {},
            searchInputGeneral: '',
            searchInputId: '',
            errorHandler: useErrorHandler(),

            loading: false,
            saving: false,
            consumers: [],
            paginationData: {},
            perPageOptions: [ { id: 10, name: "10" }, { id: 25, name: "25" }, { id: 50, name: "50" }, { id: 100, name: "100" } ],
            perPageSelection: 25,
            page: 1,
            presets: [],
            selectedPreset: null,
            presetName: "",
            showPresetModal: false,
            updatingPreset: false,
            deletingPreset: null,
            modalError: null,

            showComments: true,

            sharedApi: SharedApiService.make(),

            selectedCompany: '',
            companies: [],
            selectedCampaigns: [],
            companyCampaigns: [],
            soldTo: null,
            showCampaignFilter: false,
            permissionStore: useRolesPermissions(),
            campaignLoading: false,
            campaignError: false,
            aggregates: [],
            aggregatesLoading: false,
            CONSUMER_SEARCH_AGGREGATES,
            showMarketingCampaignModal: false,

            dateSort: null,
            COMPANY_SCOPE_OPTIONS,
        }
    },
    computed: {
        customCategories() {
            const options = {};
            this.presets.forEach(preset => Object.assign(options, { [preset.name]: preset.name }));

            return [{
                    type: 'custom-preset',
                    name: 'User Presets',
                    id: 'presets',
                    options
                }];
        },
        canExportConsumers() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_CONSUMER_SEARCH_EXPORT)
        },
        canCreateMarketingCampaign() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_MARKETING_CAMPAIGNS_CREATE)
        },
        canEditMarketingCampaigns() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_MARKETING_CAMPAIGNS_EDIT)
        },
        allInputs() {
            return {
                search_id: this.searchInputId,
                search_text: this.searchInputGeneral,
                filters: this.filterInputs,
                page: this.page,
                per_page: this.perPageSelection,
                company_id: this.selectedCompany,
                campaign_id: this.selectedCampaigns,
                sold: this.soldTo,
                sort: {
                    date: this.dateSort
                }
            }
        },
    },
    mounted() {
        this.getFilterOptions();
    },
    watch: {
        selectedCompany: function (newVal, oldVal) {
            if (newVal !== oldVal) {
                this.selectedCampaigns = [];
                this.showCampaignFilter = false;
                if (newVal) {
                    this.searchCompanyCampaigns(newVal);
                } else {
                    this.companyCampaigns = [];
                    this.showCampaignFilter = false;
                }
            }
        },
    },
    methods: {
        closeMarketingCampaignModal(args) {
            if (args !== undefined) {
                this.showAlert('success', args)
            }

            this.showMarketingCampaignModal = false
        },
        async handleExportToCsv(){
            const keys = {
                'first_name': 'First Name',
                'last_name': 'Last Name',
                'email': 'Email',
                'uuid': 'Lead Reference',
            }

            const transformed = this.consumers.map(consumer => {
                return Object.keys(keys).reduce((prev, current) => {
                    prev.push(consumer[current] ?? null)
                    return prev;
                }, [])
            })

            const consumersRefs = this.consumers.map(consumer => consumer.uuid)

            const fileName = `ConsumerSearchExport_${+new Date()}`

            await this.sharedApi.createActivityLog(
                'consumer',
                'consumer-search-exported',
                null,
                null,
                {'consumer_refs': consumersRefs}
            )

            downloadCsvString(Object.values(keys), transformed, `${fileName}.csv`)

        },
        getFilterOptions() {
            this.consumerSearchApi.getFilterOptions().then(resp => {
                if (resp.data?.data?.status) {
                    this.filters = resp.data.data.filter_options;
                    this.presets = this.sortPresets(resp.data.data.presets ?? []);
                    // this.resetFilters();
                }
            }).catch(err => {
                this.errorHandler.handleError(err)
                this.showAlert('error', err.message);
            });
        },
        // This method handles Filterables with child Filterables which need updating when query is submitted
        updateFilterOptions(updatedFilterOptions) {
            if (updatedFilterOptions) {
                for (const filterKey in updatedFilterOptions) {
                    const targetIndex = this.filters.findIndex(filter => {
                        return filter.id === filterKey;
                    });
                    if (targetIndex >= 0) this.filters[targetIndex] = updatedFilterOptions[filterKey];
                    this.validateInputsOnUpdatedFilter(updatedFilterOptions[filterKey]);
                }
            }
        },
        // Validate already-present filter inputs for dependent Child components - currently only MultiSelect, update to handle other types if required
        validateInputsOnUpdatedFilter(updatedFilter) {
            if (!this.filterInputs) return;

            const filterValues = (filterComponent, parentId) => {
                const id = filterComponent.id;
                if (id in this.filterInputs[parentId]) {
                    const validValues = Object.values(filterComponent.options);
                    this.filterInputs[parentId][id] = this.filterInputs[parentId][id].filter(input => validValues.includes(input));
                }
            }
            const parentId = updatedFilter.id;

            if (!parentId in this.filterInputs || !this.filterInputs[parentId]) return;

            filterValues(updatedFilter, parentId);
            for (const child in (updatedFilter.children ?? {})) {
                filterValues(updatedFilter.children[child], parentId);
            }
        },
        getFilterOptionUpdates() {
            this.consumerSearchApi.getFilterOptionUpdates({
                filters: this.filterInputs,
            }).then(resp => {
                if (resp.data?.data?.status) {
                    this.updateFilterOptions(resp.data.data.filter_updates ?? null);
                }
            }).catch(err => {
                this.showAlert('error', err.message);
            });
        },
        submitSearch() {
            this.errorHandler.resetError()
            if (this.loading) return;
            this.loading = true;

            this.consumerSearchApi.search(this.allInputs ).then(resp => {
                if (resp.data?.data?.status) {
                    const paginationData = resp.data.data.consumers;
                    this.consumers = paginationData.data;
                    this.paginationData = paginationData;
                    this.updateFilterOptions(resp.data.data.filter_updates ?? null);
                }
            }).catch(err => {
                this.errorHandler.handleError(err)
                this.showAlert('error', err.message);
            }).finally(() => {
                this.retrieveAggregateData(); //not great but need to prioritize table search first
                this.loading = false;
            });
        },
        async retrieveAggregateData() {
            this.aggregatesLoading = true;
            const response = await this.consumerSearchApi.getAggregate(
                {
                    search_id: this.searchInputId,
                    search_text: this.searchInputGeneral,
                    filters: this.filterInputs,
                    company_id: this.selectedCompany,
                    campaign_id: this.selectedCampaigns
                }
            );
            this.aggregates = response.data.data.data;
            this.aggregatesLoading = false;
        },
        handlePaginationEvent(newPageEvent) {
            this.page = newPageEvent.newPage ?? 1
            this.submitSearch();
        },
        async resetFilters() {
            await nextTick();
            this.errorHandler.resetError()
            this.searchInputGeneral = '';
            this.searchInputId = '';
            this.filters.forEach(filter => {
                this.filterInputs[filter.id] = this.filterDefaults?.[filter.id] ?? null;
            });

            this.selectedCompany = '';
            this.selectedCampaigns = [];
            this.showCampaignFilter = false;

            this.dateSort = null;

            // this.submitSearch();
        },
        toggleFiltersPopup() {
            this.filtersPopup = ! this.filtersPopup;
            if(this.filtersPopup === false) {
                this.selectedFilter = null;
            }
        },
        selectFilter(filter) {
            this.selectedFilter = filter;
            this.selectedOption = null;
        },
        selectOption(option) {
            this.selectedOption = option;
        },
        clearFilter(filterId) {
            delete this.filterInputs[filterId];
        },
        updateFilterDefaults(filterChange) {
            Object.assign(this.filterDefaults, { ...filterChange });
        },
        saveFilterPreset() {
            const payload = {
                name:  this.presetName.trim(),
                value: this.filterInputs,
            }
            this.updatePresets(payload);
        },
        deleteFilterPreset() {
            if (this.saving || !this.deletingPreset) return;
            this.saving = true;

            this.consumerSearchApi.deleteUserPreset(this.deletingPreset).then(resp => {
                if (resp.data?.data?.status) {
                    this.presets = resp.data.data.presets;
                }
            }).catch(err => {
                console.error(err);
            }).finally(() => {
                this.saving = false;
                this.deletingPreset = null;
                this.showPresetModal = false;
            });
        },
        updatePresets(payload) {
            if (this.saving) return;
            this.saving = true;

            this.consumerSearchApi.saveUserPreset(payload).then(resp => {
                if (resp.data?.data?.status) {
                    this.presets = this.sortPresets(resp.data.data.presets);
                }
            }).catch(err => {
                this.showAlert('error', err.message);
            }).finally(() => {
                this.saving = false;
                this.closePresetModal();
            });
        },
        sortPresets(presetArray) {
            return presetArray.sort((a,b) => a.name > b.name ? 1 : 0);
        },
        openSavePresetModal() {
            this.deletingPreset = false;
            this.showPresetModal = true;
        },
        openDeletePresetModal(deleteOption) {
            this.deletingPreset = deleteOption;
            this.showPresetModal = true;
        },
        closePresetModal() {
            this.deletingPreset = false;
            this.showPresetModal = false;
            this.presetName = "";
            this.modalError = null;
        },
        confirmPresetModal() {
            if (this.deletingPreset) {
                this.deleteFilterPreset();
            }
            else {
                if (!this.validatePresetName()) return;
                this.saveFilterPreset();
            }
        },
        validatePresetName() {
            this.modalError = null;
            const errors = [];
            const currentName = this.presetName.trim().toLowerCase();
            const invalidNames = this.presets.map(preset => preset.name.toLowerCase());
            if (invalidNames.includes(currentName)) {
                errors.push('That name is already in use. Please enter a unique name.');
            }

            if (errors.length) {
                this.modalError = errors.join("\n");
                return false;
            }
            else return true;
        },
        async handlePresetChange() {
            if (this.selectedPreset) {
                const targetPreset = this.presets.find(preset => preset.name === this.selectedPreset);
                if (targetPreset) {
                    const validIds = this.filters.map(filter => filter.id);
                    for (const key in targetPreset.value) {
                        if (validIds.includes(key)) {
                            const clone = { ...targetPreset.value }
                            this.filterInputs[key] = clone[key];
                        }
                    }

                    this.submitSearch();
                }
            }
        },
        handleCustomUpdate(newVal) {
            if ('presets' in newVal) {
                this.selectedPreset = newVal.presets;
                this.handlePresetChange();
            }
        },
        searchCompanies(query) {
            if (!query) {
                this.selectedCompany = '';
                this.companyCampaigns = [];
                this.showCampaignFilter = false;
                return;
            }

            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companies = res.data.data.companies;

                    if (this.companies.length === 0) {
                        this.selectedCompany = '';
                        this.companyCampaigns = [];
                        this.showCampaignFilter = false;
                    }else{
                        this.showCampaignFilter = false;
                    }
                }
            }).catch(err => {
                this.showAlert('error', 'Error retrieving consumers');
                console.error(err);
            });
        },
        searchCompanyCampaigns(companyId, query) {
            if (!companyId) {
                this.companyCampaigns = [];
                this.showCampaignFilter = false;
                return;
            }

            this.campaignLoading = true;
            this.campaignError = false;

            this.sharedApi.searchCompanyCampaigns(companyId, query)
                .then(res => {
                    this.companyCampaigns = res.data.data.results;
                    this.showCampaignFilter = this.companyCampaigns.length > 0;
                })
                .catch(err => {
                    this.campaignError = true;  // Handle error case
                    this.showAlert('error', 'Error retrieving campaigns');
                    console.error(err);
                })
                .finally(() => {
                    this.campaignLoading = false;  // End the loading state
                });
        },
        handleAutocompleteInput(value) {
            if (!value) {
                this.selectedCompany = '';
                this.companyCampaigns = [];
                this.showCampaignFilter = false;
            }
        },
        applyDateSortToSearch(direction) {
            this.dateSort = direction;
            this.submitSearch();
        },
    },
}
</script>
