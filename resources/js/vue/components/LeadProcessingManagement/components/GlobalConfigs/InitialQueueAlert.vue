<template>
    <div>
        <div class="flex items-center border-b">
            <p class="mr-2">Initial Queue Alert</p>
            <SimpleIcon icon="plus" class="cursor-pointer text-primary-500" @click="addNew"/>
        </div>
        <LoadingSpinner v-if="loadingAlerts"/>
        <div class="mt-3" v-else>
            <div class="grid grid-cols-8 gap-3 mt-5 mb-2 px-3 text-slate-400 font-medium tracking-wide uppercase text-xs">
                <p class="col-span-2">Type</p>
                <p class="text-center">Active</p>
                <p class="col-span-2">Recipients</p>
                <p class="col-span-2">Config</p>
                <p></p>
            </div>
            <div class="border-t overflow-y-auto"
                 :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div
                    class="grid grid-cols-8 text-sm p-3 gap-3 border-b"
                    :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                    v-for="alert in alerts"
                >
                    <p class="col-span-2">{{ alert.type_readable }}</p>
                    <div class="flex justify-center">
                        <SimpleIcon icon="check-circle" class="text-green-500" v-if="alert.active"/>
                        <SimpleIcon icon="x-circle" class="text-red-500" v-else/>
                    </div>
                    <div class="col-span-2">
                        <p v-for="recipient in alert.recipients">{{ getRecipientName(recipient) }}</p>
                    </div>
                    <div class="grid gap-2 col-span-2">
                        <p v-for="key in Object.keys(alert.payload)">
                            {{ key }}: {{ alert.payload[key] }}
                        </p>
                    </div>
                    <div class="flex gap-4">
                        <SimpleIcon icon="pencil-square" class="cursor-pointer text-primary-500" @click="editAlert(alert)"/>
                        <SimpleIcon icon="trash" class="cursor-pointer text-red-500" @click="deleteAlert(alert)"/>
                    </div>
                </div>
            </div>

        </div>
        <modal :dark-mode="darkMode" v-if="showModal" @close="cancel" small @confirm="save" :disable-confirm="saving">
            <template v-slot:header>
                Create Alert
            </template>
            <template v-slot:content>
                <div class="grid grid-cols-2 text-sm items-center gap-5 ml-[5rem] mr-[10rem]">
                    <p class="text-right font-semibold">Type:</p>
                    <p>Unprocessed Leads</p>
                    <p class="text-right font-semibold">Active:</p>
                    <ToggleSwitch :dark-mode="darkMode" v-model="editingAlert.active" :disabled="saving"/>
                    <p class="text-right font-semibold">Duration (minutes):</p>
                    <CustomInput :dark-mode="darkMode" v-model="editingAlert.payload.duration" type="number" :disabled="saving"/>
                    <p class="text-right font-semibold">Recipients:</p>
                    <MultiSelect :options="recipientOptions" :selected-ids="editingAlert.recipients" text-place-holder="Select" :dark-mode="darkMode" :disabled="saving"/>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";
import Modal from "../../../Shared/components/Modal.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import {ApiService} from "../../../LeadProcessing/services/api/api.js";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";

export default {
    name: "InitialQueueAlert",
    components: {LoadingSpinner, ToggleSwitch, Modal, MultiSelect, CustomInput, SimpleIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        api: {
            type: ApiService,
            required: true
        },
        queue: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            showModal: false,
            editingAlert: {},
            recipientOptions: [],
            loadingAlerts: false,
            saving: false,
            alerts: []
        }
    },
    created() {
        this.resetEditing();
        this.getAlerts();
    },
    methods: {
        getAlerts() {
            this.loadingAlerts = true;
            this.api.getAlerts(this.queue.id)
                .then(resp => {
                    this.alerts = resp.data.data.alerts;
                    this.recipientOptions = resp.data.data.recipients;
                })
                .catch(e => console.error(e))
                .finally(() => this.loadingAlerts  = false);
        },
        addNew() {
            this.showModal = true;
            this.resetEditing();
        },
        cancel() {
            this.showModal = false;
            this.resetEditing();
        },
        editAlert(alert) {
            this.editingAlert = {...alert};
            this.showModal = true;
        },
        save() {
            this.saving = true;
            this.api.saveAlert(
                this.queue.id,
                this.editingAlert.type,
                this.editingAlert.active,
                this.editingAlert.payload,
                this.editingAlert.recipients,
                this.editingAlert.id
            )
                .then(() => {
                    this.showModal = false;
                    this.resetEditing();
                    this.getAlerts();
                })
                .catch(e => console.error(e))
                .finally(() => this.saving = false);
        },
        deleteAlert(alert) {
            this.api.deleteAlert(this.queue.id, alert.id)
                .then(() => this.getAlerts())
                .catch(e => console.error(e));
        },
        resetEditing() {
            this.editingAlert = {
                id: null,
                type: 'unprocessed_leads',
                active: true,
                payload: {
                    duration: undefined
                },
                recipients: []
            };
        },
        getRecipientName(id) {
            return this.recipientOptions.find(recipient => recipient.id === id)?.name;
        }
    }
}
</script>
