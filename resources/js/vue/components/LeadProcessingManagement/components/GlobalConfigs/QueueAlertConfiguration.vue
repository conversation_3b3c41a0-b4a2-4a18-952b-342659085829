<template>
    <div class="border rounded-lg lg:col-span-2"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="p-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Queue Alerts</h5>
            <InitialQueueAlert :dark-mode="darkMode" :api="api" :queue="initialQueue"/>
        </div>
    </div>
</template>

<script>
import InitialQueueAlert from "./InitialQueueAlert.vue";
import {ApiService} from "../../../LeadProcessing/services/api/api.js";

export default {
    name: "QueueAlertConfiguration",
    components: {InitialQueueAlert},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        api: {
            type: ApiService,
            require: true
        },
        queues: {
            type: Array,
            default: []
        }
    },
    computed: {
        initialQueue () {
            return this.queues.find(queue => queue.status_value === 'initial')
        }
    }
}
</script>
