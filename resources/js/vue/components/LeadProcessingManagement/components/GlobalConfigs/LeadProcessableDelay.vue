<template>
    <div class="border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="p-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Lead Processable Buffer</h5>
            <p class="font-medium pb-3">
                Time until lead is processable
            </p>
            <div class="grid grid-cols-2 gap-4 mb-5">
                <div class="flex items-center">
                    <input :disabled="!processableDelayInput"
                           class="text-center w-full border border-grey-350 rounded px-4 py-1 mr-2 outline-none"
                           min="0"
                           type="number"
                           v-model="leadProcessableDelay"
                           :class="{
                                'border-grey-350 bg-light-module': !darkMode,
                                'bg-grey-100 cursor-not-allowed' : !darkMode && !processableDelayInput,
                                'border-blue-400 bg-dark-background text-blue-400': darkMode && processableDelayInput,
                                'bg-dark-module border-blue-400 cursor-not-allowed' : darkMode && !processableDelayInput
                           }"
                    />
                    <span class="text-sm">Second(s)</span>
                </div>
            </div>
            <button v-if="processableDelayInput" @click="toggleEditDelayInput" class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5 mr-3"
                    :class="{'bg-grey-250 hover:bg-light-background text-white': !darkMode, 'bg-grey-500 hover:bg-grey-600 text-white': darkMode}">
                Cancel
            </button>

            <button v-if="processableDelayInput" @click="update" class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                    :class="{'border border-primary-500 text-blue-550': !processableDelayInput, 'bg-primary-500 hover:bg-blue-500 text-white': processableDelayInput}">
                Update Processable Buffer
            </button>
            <button v-else
                    @click="toggleEditDelayInput"
                    class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                    :class="{'border border-primary-500 text-blue-550': !processableDelayInput, 'bg-primary-500 hover:bg-blue-500 text-white': processableDelayInput}">
                Edit Processable Buffer
            </button>
        </div>
    </div>
</template>

<script>
    export default {
        name: "LeadProcessableDelay",
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            modelValue: {
                type: Number,
                default: 0
            }
        },
        data() {
            return {
                processableDelayInput: false,
            }
        },
        emits: ['update:modelValue', 'update'],
        computed: {
            leadProcessableDelay: {
                get() {
                    return this.modelValue;
                },
                set(value) {
                    this.$emit('update:modelValue', parseInt(value));
                }
            }
        },
        methods: {
            toggleEditDelayInput() {
                this.processableDelayInput = !this.processableDelayInput;
            },
            update() {
                this.toggleEditDelayInput();
                this.$emit('update');
            }
        },
    }
</script>

<style scoped>
    /* Hide arrows for input type number */
    /* Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }
</style>
