<template>
    <div class="border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="p-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Lead Timezone Buffer</h5>
            <p class="font-medium pb-3">
                Timezone buffer for opening lead delivery
            </p>
            <div class="grid grid-cols-2 gap-4 mb-5">
                <div class="flex items-center">
                    <input v-model="hours"
                           :disabled="!timezoneBufferInputs"
                           class="text-center w-full border border-grey-350 rounded px-4 py-1 mr-2"
                           type="number"
                           min="0"
                           :class="{
                                'border-grey-350 bg-light-module': !darkMode,
                                'bg-grey-100 cursor-not-allowed'  : !darkMode && !timezoneBufferInputs,
                                'border-blue-400 bg-dark-background text-blue-400': darkMode && timezoneBufferInputs,
                                'bg-dark-module border-blue-400 cursor-not-allowed' : darkMode && !timezoneBufferInputs
                           }"/>
                    <span class="text-sm">Hour(s)</span>
                </div>
                <div class="flex items-center">
                    <input v-model="minutes"
                           :disabled="!timezoneBufferInputs"
                           class="text-center w-full border border-grey-350 rounded px-4 py-1 mr-2"
                           type="number"
                           min="0"
                           :class="{
                                'border-grey-350 bg-light-module': !darkMode,
                                'bg-grey-100 cursor-not-allowed' : !darkMode && !timezoneBufferInputs,
                                'border-blue-400 bg-dark-background text-blue-400': darkMode && timezoneBufferInputs,
                                'bg-dark-module border-blue-400 cursor-not-allowed' : darkMode && !timezoneBufferInputs
                           }"/>
                    <span class="text-sm">Minute(s)</span>
                </div>
            </div>
            <button v-if="timezoneBufferInputs" @click="toggleEditTimezoneBuffer" class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5 mr-3"
                    :class="{'bg-grey-250 hover:bg-light-background text-white': !darkMode, 'bg-grey-500 hover:bg-grey-600 text-white': darkMode}">
                Cancel
            </button>

            <button v-if="timezoneBufferInputs" @click="update" class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                    :class="{'border border-primary-500 text-blue-550': !timezoneBufferInputs, 'bg-primary-500 hover:bg-blue-500 text-white': timezoneBufferInputs}">
                Update Timezone Buffer
            </button>
            <button v-else @click="toggleEditTimezoneBuffer" class="transition duration-200 text-sm font-medium focus:outline-none py-2 rounded-md px-5"
                    :class="{'border border-primary-500 text-blue-550': !timezoneBufferInputs, 'bg-primary-500 hover:bg-blue-500 text-white': timezoneBufferInputs}">
                Edit Timezone Buffer
            </button>
        </div>
    </div>
</template>

<script>
    export default {
        name: "LeadTimezoneBuffer",
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            modelValue: {
                type: Number,
                default: 0
            }
        },
        data() {
            return {
                timezoneBufferInputs: false,
            }
        },
        emits: ['update:modelValue', 'update'],
        computed: {
            hours: {
                get() {
                    return Math.floor(this.modelValue / 60);
                },
                set(value) {
                    this.$emit('update:modelValue', (parseInt(value) * 60) + this.minutes);
                }
            },
            minutes: {
                get() {
                    return this.modelValue % 60;
                },
                set(value) {
                    this.$emit('update:modelValue', parseInt(value) + (this.hours * 60))
                }
            },
        },
        methods: {
            toggleEditTimezoneBuffer() {
                this.timezoneBufferInputs = !this.timezoneBufferInputs;
            },
            update() {
                this.toggleEditTimezoneBuffer();
                this.$emit('update');
            }
        },
    }
</script>

<style scoped>
    /* Hide arrows for input type number */
    /* Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }
</style>
