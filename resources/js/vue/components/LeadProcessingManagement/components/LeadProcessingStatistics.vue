<template>
    <div class="relative border rounded-lg h-full"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div v-if="industries?.length" class="p-5 grid md:grid-cols-1 lg:grid-cols-2 gap-5">
            <div class="lg:col-span-2">
                <div class="flex items-center gap-x-8 pb-3">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Lead Statistics</h5>
                    <Dropdown
                        class="w-[16rem]"
                        :dark-mode="darkMode"
                        :options="getFilterOptions(industries)"
                        v-model="selectedIndustry"
                        @change="handleIndustryChange"
                    />
                    <div class="text-sm rounded-full flex items-center"
                         :class="{'text-grey-400': !darkMode, 'text-blue-400': darkMode}">
                        Queued:
                        <div class="rounded-full text-sm pl-1 h-full font-medium">
                            {{ totalLeads }}</div>
                    </div>
                </div>
            </div>
            <div class="lg:border-r lg:pr-4" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
                <div class="flex gap-5 mb-5 items-center">
                    <h5 class="text-primary-400 text-sm uppercase font-semibold leading-tight text-nowrap">Queued Leads</h5>
                    <Dropdown
                        class="w-[16rem]"
                        :dark-mode="darkMode"
                        :options="getTimeZoneOptions()"
                        v-model="selectedTimezone"
                        @change="handleTimezoneChange"
                    />
                </div>
                <div class="flex flex-wrap mb-5">
                    <div class="flex items-center mr-8">
                        <div class="flex-shrink-0 w-3 h-3 mr-2 rounded-full bg-cyan-500"
                        ></div>
                        <p class="text-sm">Initial</p>
                    </div>
                    <div class="flex items-center mr-8">
                        <div class="flex-shrink-0 w-3 h-3 text-sm mr-1 rounded-full bg-yellow-500"
                        ></div>
                        <p class="text-sm">Pending</p>
                    </div>
                    <div class="flex items-center mr-8">
                        <div class="flex-shrink-0 w-3 h-3 text-sm mr-1 bg-orange-500 rounded-full"
                        ></div>
                        <p class="text-sm">Under Review</p>
                    </div>
                    <div class="flex items-center mr-8">
                        <div class="flex-shrink-0 w-3 h-3 text-sm mr-1 bg-amber-900 rounded-full"
                        ></div>
                        <p class="text-sm">Aged</p>
                    </div>
                    <div class="flex items-center mr-8">
                        <div class="flex-shrink-0 w-3 h-3 text-sm mr-1 bg-green-500 rounded-full"
                        ></div>
                        <p class="text-sm">Affiliate</p>
                    </div>
                </div>
                <div class="w-full">
                    <div class="flex items-center mb-4">
                        <div class="p-2 h-10 rounded-r "
                             :class="{'bg-cyan-500' : !darkMode, 'border-r-2 border-cyan-500 bg-dark-background': darkMode}"
                             :style="{width: calculateQueuePercentage(statistics.initial) + '%'}">
                            <div class="flex justify-end items-center h-full mr-2 text-grey-120 font-bold"
                                 v-if="calculateQueuePercentage(statistics.initial) > 10">{{ Math.round(calculateQueuePercentage(statistics.initial)) }}%</div>
                        </div>
                        <p class="ml-2 font-bold text-cyan-500">
                            {{ statistics.initial }}
                        </p>
                    </div>

                    <div class="flex items-center mb-4">
                        <div class="p-2 h-10 rounded-r"
                             :class="{'bg-yellow-500' : !darkMode, 'border-r-2 border-yellow-500 bg-dark-background': darkMode}"
                             :style="{width: calculateQueuePercentage(statistics.pending_review) + '%'}">
                            <div class="flex justify-end items-center h-full mr-2 text-white font-bold"
                                 v-if="calculateQueuePercentage(statistics.pending_review) > 10">{{ Math.round(calculateQueuePercentage(statistics.pending_review)) }}%</div>
                        </div>
                        <p class="ml-2 font-bold text-yellow-500">
                            {{ statistics.pending_review }}
                        </p>
                    </div>

                    <div class="flex items-center mb-4">
                        <div class="p-2 h-10 rounded-r "
                             :class="{'bg-orange-500' : !darkMode, 'border-r-2 border-orange-500 bg-dark-background': darkMode}"
                             :style="{width: calculateQueuePercentage(statistics.under_review) + '%'}">
                            <div class="flex justify-end items-center h-full mr-2 text-white font-bold" v-if="calculateQueuePercentage(statistics.under_review) > 10">{{ Math.round(calculateQueuePercentage(statistics.under_review)) }}%</div>
                        </div>
                        <p class="ml-2 font-bold text-orange-500">
                            {{ statistics.under_review }}
                        </p>
                    </div>
                    <div class="flex items-center mb-4">
                        <div class="p-2 h-10 rounded-r"
                             :class="{'bg-amber-900' : !darkMode, 'border-r-2 border-amber-900 bg-dark-background': darkMode}"
                             :style="{width: calculateQueuePercentage(statistics.aged) + '%'}">
                            <div class="flex justify-end items-center h-full mr-2 text-white font-bold" v-if="calculateQueuePercentage(statistics.aged) > 10">{{ Math.round(calculateQueuePercentage(statistics.aged)) }}%</div>
                        </div>
                        <p class="ml-2 font-bold text-amber-900">
                            {{ statistics.aged }}
                        </p>
                    </div>
                    <div class="flex items-center mb-4">
                        <div class="p-2 h-10 rounded-r"
                             :class="{'bg-green-500' : !darkMode, 'border-r-2 border-green-500 bg-dark-background': darkMode}"
                             :style="{width: calculateQueuePercentage(statistics.affiliate) + '%'}">
                            <div class="flex justify-end items-center h-full mr-2 text-white font-bold" v-if="calculateQueuePercentage(statistics.affiliate) > 10">{{ Math.round(calculateQueuePercentage(statistics.affiliate)) }}%</div>
                        </div>
                        <p class="ml-2 font-bold text-green-500">
                            {{ statistics.affiliate }}
                        </p>
                    </div>
                </div>
            </div>
            <div>
                <h5 class="text-primary-400 text-sm uppercase font-semibold pb-3 leading-tight">Other Leads</h5>
                <div class="flex flex-wrap mb-5">
                    <div class="flex items-center mr-8">
                        <div class="flex-shrink-0 w-3 h-3 text-sm mr-1 bg-primary-500 rounded-full"
                        ></div>
                        <p class="text-sm">Allocated</p>
                    </div>
                    <div class="flex items-center mr-8">
                        <div class="flex-shrink-0 w-3 h-3 text-sm mr-1 bg-red-300 rounded-full"></div>
                        <p class="text-sm">Cancelled</p>
                    </div>
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-3 h-3 text-sm mr-1 bg-blue-300 rounded-full"></div>
                        <p class="text-sm">Unsold</p>
                    </div>
                </div>

                <div class="w-full">
                    <div class="flex items-center mb-4">
                        <div class="p-2 h-10 rounded-r"
                             :class="{'bg-primary-500' : !darkMode, 'border-r-2 border-primary-500 bg-dark-background': darkMode}"
                             :style="{width: calculateOtherPercentage(statistics.allocated) + '%'}">
                            <div class="flex justify-end items-center h-full mr-2 text-white font-bold" v-if="calculateOtherPercentage(statistics.allocated) > 10">{{ Math.round(calculateOtherPercentage(statistics.allocated)) }}%</div>
                        </div>
                        <p class="ml-2 font-bold text-blue-550">
                            {{ statistics.allocated }}
                        </p>
                    </div>

                    <div class="flex items-center mb-4">
                        <div class="p-2 h-10 rounded-r"
                             :class="{'bg-red-300' : !darkMode, 'border-r-2 border-red-300 bg-dark-background': darkMode}"
                             :style="{width: calculateOtherPercentage(statistics.cancelled) + '%'}">
                            <div class="flex justify-end items-center h-full mr-2 text-white font-bold" v-if="calculateOtherPercentage(statistics.cancelled) > 10">{{ Math.round(calculateOtherPercentage(statistics.cancelled)) }}%</div>
                        </div>
                        <p class="ml-2 font-bold text-red-300">
                            {{ statistics.cancelled }}
                        </p>
                    </div>

                    <div class="flex items-center">
                        <div class="p-2 h-10 rounded-r"
                             :class="{'bg-blue-300' : !darkMode, 'border-r-2 border-blue-300 bg-dark-background': darkMode}"
                             :style="{width: calculateOtherPercentage(statistics.unsold) + '%'}">
                            <div class="flex justify-end items-center h-full mr-2 text-white font-bold" v-if="calculateOtherPercentage(statistics.unsold) > 10">{{ Math.round(calculateOtherPercentage(statistics.unsold)) }}%</div>
                        </div>
                        <p class="ml-2 font-bold text-blue-300">
                            {{ statistics.unsold }}
                        </p>
                    </div>

                </div>
            </div>
        </div>
        <div v-if="loading"
            class="absolute z-10 bg-opacity-30 inset-0 flex items-center backdrop-blur-sm justify-center"
             :class="{'bg-light-module': !darkMode, 'bg-dark-module': darkMode}">
            <loading-spinner />
        </div>
    </div>
</template>

<script>
import Dropdown from "../../Shared/components/Dropdown.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import { nextTick } from "vue";

export default {
    name: "LeadProcessingStatistics",
    components: { LoadingSpinner, Dropdown },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        industries: {
            type: Array,
            default: [],
        },
        timeZoneConfigurations: {
            type: Array,
            default: []
        },
        api: {
            type: Object,
            default: {},
        },
    },
    computed: {
      totalLeads() {
          return this.statistics.initial + this.statistics.pending_review + this.statistics.under_review + this.statistics.aged + this.statistics.affiliate;
      }
    },
    data() {
        return {
            selectedIndustry: null,
            statistics: {},
            loading: false,
            selectedTimezone: -5,
            timeZonesWithAll: [],
        }
    },
    mounted() {
        setInterval(() => {
            // Set an interval to refresh the page every 60 seconds (60000 ms)
            this.getStatistics();
        }, 60000);
    },
    methods: {
        calculateQueuePercentage(value) {
            const total = this.totalLeads;

            if(total === 0)
                return 0;

            return (value / total) * 100;
        },
        calculateOtherPercentage(value) {
            const total = this.statistics.cancelled + this.statistics.unsold + this.statistics.allocated;

            if(total === 0)
                return 0;

            return (value / total) * 100;
        },
        getStatistics() {
            this.loading = true;
            if (!(this.selectedIndustry > -1)) {
                this.loading = false;
                return;
            }
            this.api.getStatistics(this.selectedIndustry, this.selectedTimezone).then(resp => {
                if(resp.data.data.status && resp.data.data.statistics) {
                    this.statistics = resp.data.data.statistics;
                }
            }).catch((e) => console.error(e)
            ).finally(() => this.loading = false);
        },
        getFilterOptions(baseOptions) {
            return [
                { name: 'All', id: 0 },
                ...baseOptions.map(option => ({ name: option.name, id: option.id })),
            ];
        },
        getTimeZoneOptions() {
            return this.timeZoneConfigurations.map(timezone => {
                return {name: timezone.name, id: timezone.standard_utc_offset}
            });
        },
        async handleTimezoneChange({ id }) {
            if (id === this.selectedTimezone) return;

            await nextTick();
            this.getStatistics();
        },
        async handleIndustryChange({ id }) {
            if (id === this.selectedIndustry) return;
            await nextTick();
            this.getStatistics();
        }
    },
    watch: {
        industries(newVal, oldVal) {
            if (!oldVal?.length) {
                this.selectedIndustry = this.selectedIndustry ?? this.industries[0]?.id
                this.getStatistics();
            }
        },
    }
}
</script>

<style scoped>

</style>
