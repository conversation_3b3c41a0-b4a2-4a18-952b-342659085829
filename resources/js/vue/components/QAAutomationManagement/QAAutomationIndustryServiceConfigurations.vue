<template>
    <simple-table
        :data="data"
        :dark-mode="darkMode"
        :headers="headers"
        title="QA Automation"
        no-pagination
        :loading="loading"
        row-classes="gap-5 grid items-center py-4 rounded px-5"
    >
        <template #title-actions>
            <custom-button @click="openModal">
                Add new Industry Service
            </custom-button>
        </template>
        <template #row.col.type="{item}">
            <div class="">
                <p v-if="item.type === 1">Regex</p>
                <p v-else-if="item.type === 2">Phone Unverified</p>
                <p v-else>Other</p>
            </div>
        </template>
        <template #row.col.active="{item}">
            <div class="flex items-center gap-1">
                <toggle-switch :dark-mode="darkMode" v-model="item.enabled" @change="toggleActiveStatus(item)"/>
            </div>
        </template>
        <template #row.col.actions="{item}">
            <div class="flex items-center gap-1">
                <simple-icon
                    v-if="canDelete"
                    :icon="simpleIcon.icons.BIN"
                    @click="showConfirmDelete = true; deleteItemId=item.id"
                    clickable
                    :color="simpleIcon.colors.RED"
                />
            </div>
        </template>
    </simple-table>
<!--    todo add in edit-->
    <modal v-if="showCreateModal" :dark-mode="darkMode" confirm-text="Save"
           @confirm="createQaAutomationIndustryServiceConfiguration" :small=true @close="closeModal">
        <template v-slot:header>
            <p class="font-semibold">Add a new QA automation configuration</p>
        </template>
        <template v-slot:content class="flex items-center justify-center">
            <div class="">
                <p class="text-sm pb-1">Select Service</p>
                <Dropdown
                    :dark-mode="darkMode"
                    :options="industryServices"
                    v-model="selectedIndustryService"
                />
            </div>
            <div class="">
                <p class="text-sm pb-1">Select Type</p>
                <Dropdown
                    :model-value="selectedType"
                    :dark-mode="darkMode"
                    :options="typeOptions"
                    v-model="selectedType"
                />
            </div>
        </template>
    </modal>
    <Modal
        :small="true"
        :dark-mode="darkMode"
        v-if="showConfirmDelete"
        @close="closeModal"
        @confirm="handleDelete(this.deleteItemId)"
        :close-text="'Cancel'"
        :confirm-text="'Delete'"
    >
        <template v-slot:header>
            Confirm Delete
        </template>
        <template v-slot:content>
            Are you sure you wish to delete this industry service from QA Automation?
        </template>
    </Modal>
</template>

<script>
import {defineComponent} from "vue";
import ToggleSwitch from "../Shared/components/ToggleSwitch.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store.js";
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import qaAutomationApiService from "./Services/api.js";
import Modal from "../Shared/components/Modal.vue";
import SharedApiService from "../Shared/services/api.js";
import {useToastNotificationStore} from "../../../stores/billing/tost-notification.store.js";

export default defineComponent({
    name: "QAAutomationIndustryServiceConfigurations",
    components: {Modal, SimpleIcon, SimpleTable, ToggleSwitch, Dropdown, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            simpleIcon: useSimpleIcon(),
            permissionStore: useRolesPermissions(),
            loading: false,
            headers: [
                {title: "Industry Service Id", field: "industry_service_id"},
                {title: "Industry Service", field: "industry_service_name"},
                {title: "type", field: 'type'},
                {title: "Active", field: "active"},
                {title: "Updated At", field: 'updated_at'},
                {title: "Actions", field: "actions"},
            ],
            data: [],
            showCreateModal: false,
            sharedApi: SharedApiService.make(),
            industryServices: [],
            selectedType: 1,
            selectedIndustryService: null,
            toastNotificationStore: useToastNotificationStore(),
            typeOptions: [
                {id: 1, name: 'regex'},
                {id: 2, name: 'phone unverified'},
            ],
            showConfirmDelete: false,
            deleteItemId: null,
        }
    },
    async mounted() {
        await this.getQaAutomationIndustryServiceConfigurations()
        await this.getIndustryServices()
    },
    methods: {
        async getQaAutomationIndustryServiceConfigurations() {
            this.loading = true
            qaAutomationApiService.getIndustryServiceConfigurations().then(resp => {
                this.data = resp.data.data.industry_service_configurations;
            })
                .catch(error => this.toastNotificationStore.notifyError(error.message))
                .finally(() => {
                    this.loading = false
                })
        },
        async createQaAutomationIndustryServiceConfiguration() {
            this.loading = true
            qaAutomationApiService.createIndustryServiceConfigurations({
                industry_service_id: this.industryServices.filter(service => service.id === this.selectedIndustryService)[0].num_id,
                enabled: true,
                type: this.selectedType,
            })
                .catch(error => this.toastNotificationStore.notifyError(error.message))
                .finally(() => {
                    this.closeModal()
                    this.getQaAutomationIndustryServiceConfigurations()
                    this.loading = false
                })
        },
        async getIndustryServices() {
            this.sharedApi.getIndustryServices().then(res => {
                if (res.data.data.status === true) {
                    this.industryServices = res.data.data.industry_services;
                } else {
                    this.showAlert('error', 'Problem loading services');
                }
            }).catch(() => {
                this.showAlert('error', 'Error loading services');
            });
        },
        async toggleActiveStatus(item) {
            qaAutomationApiService.updateIndustryServiceConfigurations(item)
        },
        openModal() {
            this.showCreateModal = true;
        },
        closeModal() {
            this.showCreateModal = false
            this.selectedIndustryService = null
            this.selectedType = null
            this.showConfirmDelete = false
            this.deleteItemId = null
        },
        handleDelete(itemId) {
            qaAutomationApiService.deleteIndustryServiceConfigurations(itemId)
                .catch(error => this.toastNotificationStore.notifyError(error.message))
                .finally(() => {
                    this.loading = false
                    this.getQaAutomationIndustryServiceConfigurations()
                    this.closeModal()
                })
        }
    },
    computed: {
        canEdit() {
            return this.permissionStore.hasPermission(PERMISSIONS.QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_EDIT);
        },
        canDelete() {
            return this.permissionStore.hasPermission(PERMISSIONS.QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_DELETE);
        }
    }
})
</script>