<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div class="flex flex-col px-5 pt-5 border-b mb-5 gap-4"
                         :class="{'bg-light-module': !darkMode, 'bg-dark-module': darkMode}"
                    >
                        <div class="flex items-center justify-between flex-wrap pl-4">
                            <h3 class="text-xl font-medium pb-0 leading-none mr-5"
                                :class="[darkMode ? 'text-primary-500' : '']">QA Automation</h3>
                        </div>
                        <div class="flex justify-between">
                            <tab
                                :dark-mode="darkMode"
                                :tabs="tabTitles"
                                @selected="selectTab"
                                tab-style="fit"
                                background-color="light"
                                tab-type="Normal"
                            />
                        </div>
                    </div>
                    <div class="mx-10 rounded-md flex flex-col flex-grow"
                         :class="[darkMode ? 'bg-dark-module border-dark-border text-slate-200' : 'bg-light-module']">
                        <component
                            :is="currentTabComponent"
                            :dark-mode="darkMode"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Tab from "../Shared/components/Tab.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import {markRaw} from "vue";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store.js";
import QAAutomationRules from "./QAAutomationRules.vue";
import QAAutomationIndustryServiceConfigurations from "./QAAutomationIndustryServiceConfigurations.vue";
import useQueryParams from "../../../composables/useQueryParams.js";

export default {
    name: 'QAAutomationManagement',
    components: {
        Tab,
        QAAutomationIndustryServiceConfigurations,
        QAAutomationRules
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    mixins: [AlertsMixin],
    data () {
        return {
            queryParamsHelper: useQueryParams(),
            loading: false,
            rolesAndPermissions: useRolesPermissions(),
            selectedTab: null,
            tabs: [
                {
                    name: 'Industry Service Management', component: markRaw(QAAutomationIndustryServiceConfigurations), current: true,
                },
            ],
        }
    },
    mounted() {
        this.rolesAndPermissions.initialize().then(() => {
            const canViewRules = this.rolesAndPermissions.hasPermission(PERMISSIONS.QA_AUTOMATION_RULE_MANAGEMENT_VIEW);

            if (canViewRules) {
                this.tabs.push({
                    name: 'Rules',
                    component: markRaw(QAAutomationRules),
                    current: false,
                })
            }
        })
    },
    methods: {
        selectTab(tab) {
            this.queryParamsHelper.setQueryParamsOnCurrentUrl({tab})
            this.setSelectedTab(tab)
        },
        setSelectedTab(tab) {
            this.selectedTab = tab;
            this.tabTitles.forEach(e => {
                e.current = e.name === this.selectedTab
            })
        },
    },
    computed: {
        tabTitles() {
            return this.tabs;
        },
        currentTabComponent() {
            return this.tabTitles.find(e => e.current)?.component
        },
    },
}
</script>
