<template>
    <div>
        <tab
            :dark-mode="darkMode"
            :tabs="tabs"
            @selected="(tab) => selectedTab = tab"
            :tab-type="'Normal'"
            :tabs-classes="'w-full md:w-[22rem]'"
        />
        <ConsumerTemplateSelector :api="api" :dark-mode="darkMode" :industries="industries" :templates="templates"/>
    </div>
</template>

<script>
import Api from "../services/template-management-api.js";
import Tab from "../../Shared/components/Tab.vue";
import ConsumerTemplateSelector from "./ConsumerTemplateSelector.vue";

export default {
    name: "TemplateSelector",
    components: {ConsumerTemplateSelector, Tab},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            api: Api.make(),
            tabs: [
                {name: 'Consumer', current: true}
            ],
            selectedTab: 'Consumer',
            templates: [],
            industries: [],
        }
    },
    created() {
        this.getSelectorData()
    },
    methods: {
        getSelectorData() {
            this.api.getTemplateSelectorData().then(resp => {
                this.industries = resp.data.data.industries;
                this.templates = resp.data.data.templates
            }).catch(e => console.error(e));
        }
    }
}
</script>
