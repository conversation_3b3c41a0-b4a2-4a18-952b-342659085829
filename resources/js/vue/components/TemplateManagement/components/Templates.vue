<template>
    <div>
        <div :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}" class="w-full flex-auto pt-3 relative px-10">
            <div class="inline-flex items-center cursor-pointer text-primary-500 font-semibold" @click="$emit('back')">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" height="15" width="15" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="m18.75 4.5-7.5 7.5 7.5 7.5m-6-15L5.25 12l7.5 7.5" />
                </svg>
                <p class="text-sm">Back</p>
            </div>
            <div :class="[darkMode ? 'text-white' : 'text-slate-900']">
                <h3 class="text-xl font-medium mt-3 mb-5 leading-none">{{ title }}</h3>
            </div>
            <div :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}" class="border rounded-lg">
                <div class="flex justify-end my-7 px-5">
                    <custom-button @click="openEditor">Create New Template</custom-button>
                </div>
                <div class="grid grid-cols-9 gap-x-3 mb-2 px-5">
                    <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">
                        Name
                    </p>
                    <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">
                        Description
                    </p>
                    <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">
                        Owner
                    </p>
                    <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">
                        Last Updated
                    </p>
                </div>
                <div :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}"
                     class="border-t border-b h-120 overflow-y-auto">
                    <div class="flex items-center justify-center h-full" v-if="loading">
                        <loading-spinner></loading-spinner>
                    </div>
                    <div v-else>
                        <div v-for="template in templates"
                             :key="template.id"
                             :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}"
                             class="grid grid-cols-9 gap-x-3 border-b px-5 py-3 text-sm">
                            <p class="text-sm col-span-2">
                                {{ template.name }}
                            </p>
                            <p class="text-sm col-span-2">
                                {{ template.description }}
                            </p>
                            <p class="text-sm col-span-2">
                                {{ getOwnerDetails(template) }}
                            </p>
                            <p class="text-sm col-span-2 truncate">
                                {{ template.updated_at ? new Date(template.updated_at).toLocaleString() : '' }}
                            </p>
                            <p class="flex gap-3 items-center cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="h-5 text-cyan-500" @click="viewTemplate(template)">
                                    <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/>
                                    <path fill-rule="evenodd"
                                          d="M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 0 1 0-1.113ZM17.25 12a5.25 5.25 0 1 1-10.5 0 5.25 5.25 0 0 1 10.5 0Z"
                                          clip-rule="evenodd"/>
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="h-5 text-primary-500" @click="openEditor(template)"
                                     v-if="canEdit(template)">
                                    <path
                                        d="M21.731 2.269a2.625 2.625 0 0 0-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 0 0 0-3.712ZM19.513 8.199l-3.712-3.712-8.4 8.4a5.25 5.25 0 0 0-1.32 2.214l-.8 2.685a.75.75 0 0 0 .933.933l2.685-.8a5.25 5.25 0 0 0 2.214-1.32l8.4-8.4Z"/>
                                    <path
                                        d="M5.25 5.25a3 3 0 0 0-3 3v10.5a3 3 0 0 0 3 3h10.5a3 3 0 0 0 3-3V13.5a.75.75 0 0 0-1.5 0v5.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5V8.25a1.5 1.5 0 0 1 1.5-1.5h5.25a.75.75 0 0 0 0-1.5H5.25Z"/>
                                </svg>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <modal v-if="showEditModal" @close="closeEditor" :dark-mode="darkMode" @confirm="this.saveTemplate" :no-scroll="true" :disable-confirm="saving">
            <template v-slot:header>
                {{ !editingTemplate.id ? 'Add a new' : 'Update' }} template
            </template>
            <template v-slot:content>
                <div class="text-sm pr-[7rem]">
                    <div class="grid grid-cols-5 gap-5">
                        <p class="text-right">Name</p>
                        <custom-input v-model="editingTemplate.name" :type="'text'" class="col-span-4" :dark-mode="darkMode"></custom-input>
                        <p class="text-right" v-if="subjectEnabled">Subject</p>
                        <shortcode-input v-model="editingTemplate.subject" class="col-span-4" v-if="subjectEnabled" :dark-mode="darkMode" :placeholder="''"
                                         :input-height="'h-9'"></shortcode-input>
                        <p class="text-right">Description</p>
                        <custom-input v-model="editingTemplate.description" :type="'text'" class="col-span-4" :dark-mode="darkMode"></custom-input>
                        <p class="text-right">Content</p>
                        <shortcode-input v-model="editingTemplate.content" :type="'textarea'" class="col-span-4" :dark-mode="darkMode" :textarea-height="'h-32'"
                                         :placeholder="''"></shortcode-input>
                    </div>
                </div>
            </template>
        </modal>
        <modal v-if="showViewModal" @close="showViewModal = false" :hide-confirm="true" :dark-mode="darkMode">
            <template v-slot:header>
                {{ this.viewingTemplate.name }}
            </template>
            <template v-slot:content>
                <div class="text-md">
                    <div class="mb-5">
                        <p v-if="subjectEnabled" class="font-semibold">Subject</p>
                        <p class="border-t p-5 text-sm mt-2" v-if="subjectEnabled"
                           :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                            {{ viewingTemplate.subject }}
                        </p>
                    </div>
                    <div>
                        <p class="font-semibold">Content</p>
                        <textarea class="p-5 text-sm mt-2 w-full h-auto min-h-48" :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}" :disabled="true">{{ viewingTemplate.content }}</textarea>
                    </div>
                </div>
            </template>
        </modal>
        <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>
    </div>
</template>

<script>
import Api from "../services/template-management-api.js";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import Modal from "../../Shared/components/Modal.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import ShortcodeInput from "../../Shared/components/ShortcodeInput.vue";
import {useUserStore} from "../../../../stores/user-store.js";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import AlertsMixin from "../../../mixins/alerts-mixin.js";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";

export default {
    name: "Templates",
    components: {AlertsContainer, ShortcodeInput, CustomInput, Modal, CustomButton, LoadingSpinner, ActionsHandle},
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        templateType: {
            type: String,
            required: true
        }
    },
    emits: ['back'],
    data() {
        return {
            api: Api.make(),
            templates: [],
            loading: false,
            saving: false,
            editingTemplate: {},
            viewingTemplate: {},
            showEditModal: false,
            showViewModal: false,
            userStore: useUserStore(),
            permissionStore: useRolesPermissions()
        }
    },
    created() {
        this.initialize()
        this.initializeTemplateEdit();
    },
    computed: {
        title() {
            if (this.templateType === 'sms') {
                return 'SMS Templates';
            }

            return `${this.templateType} Templates`
        },
        subjectEnabled() {
            if (this.templateType === 'sms') {
                return false;
            }

            return true;
        }
    },
    methods: {
        initialize() {
            this.loadTemplates();
        },
        async loadTemplates() {
            this.loading = true;
            await this.api.getTemplates(this.templateType)
                .then(resp => this.templates = resp.data.data.templates)
                .catch(e => this.showAlert('error', e.message ?? 'Something went wrong'));
            this.loading = false;
        },
        getOwnerDetails(template) {
            if (!template.owner) {
                return 'Unknown';
            }

            if (template.owner.first_name || template.owner.last_name) {
                return `${template.owner.first_name} ${template.owner.last_name} (${template.owner.email})`;
            }

            if (template.owner.name) {
                return `${template.owner.name} (${template.owner.email})`;
            }

            return template.owner.email;
        },
        openEditor(template = null) {
            this.initializeTemplateEdit();

            if (template) {
                this.editingTemplate = {...template};
            }

            this.showEditModal = true;
        },
        closeEditor() {
            this.initializeTemplateEdit();
            this.showEditModal = false;
        },
        async saveTemplate() {
            this.saving = true;
            await this.api.saveTemplate(
                this.templateType,
                {
                    name: this.editingTemplate.name,
                    subject: this.editingTemplate.subject,
                    description: this.editingTemplate.description,
                    content: this.editingTemplate.content
                },
                this.editingTemplate.id
            ).then(resp => {
                this.loadTemplates();
                this.closeEditor();
            }).catch(e => this.showAlert('error', e.message ?? 'Something went wrong'));
            this.saving = false;
        },
        initializeTemplateEdit() {
            this.editingTemplate = {
                id: null,
                name: null,
                content: null,
                subject: null,
                description: null
            };
        },
        viewTemplate(template) {
            this.viewingTemplate = template;
            this.showViewModal = true;
        },
        canEdit(template) {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_SMS_TEMPLATE_EDIT) && template.owner?.email === this.userStore.user.email;
        }
    }
}
</script>
