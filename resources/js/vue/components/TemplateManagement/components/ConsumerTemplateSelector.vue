<template>
    <div class="p-7 min-h-[30rem]" :class="{'bg-light-module': !darkMode, 'bg-dark-module': darkMode}">
        <div class="grid grid-cols-5 items-center gap-5 mb-5">
            <div>
                <p class="text-sm mb-2 font-semibold">Type</p>
                <dropdown :dark-mode="darkMode" :options="types" v-model="selectedType" @change="resetTemplateSelection"/>
            </div>
            <div>
                <p class="text-sm mb-2 font-semibold">Purpose</p>
                <dropdown :dark-mode="darkMode" :options="purposeOptions" v-model="selectedPurpose"/>
            </div>
            <div>
                <p class="text-sm mb-2 font-semibold">Industry</p>
                <dropdown :dark-mode="darkMode" :options="industryOptions" v-model="selectedIndustry" :placeholder="'Select'"/>
            </div>
            <div>
                <p class="text-sm mb-2 font-semibold">Template</p>
                <dropdown :dark-mode="darkMode" :options="templateOptions" v-model="selectedTemplate" :placeholder="'Select'"/>
            </div>
            <div class="self-end">
                <custom-button :dark-mode="darkMode" :color="'green'" :disabled="!selectedTemplate || saving || loading"
                               @click="saveTemplateSelection">
                    Save
                </custom-button>
            </div>
        </div>
        <div class="pt-3 border-b font-semibold" v-if="templateSelections.length">
            Selections
        </div>
        <div class="flex items-center my-7 p-7" v-if="loading">
            <loading-spinner></loading-spinner>
        </div>
        <div v-else class="grid grid-cols-5 items-center gap-5 text-sm my-3" v-for="selection in templateSelections">
            <div>{{ selection.type }}</div>
            <div>{{ selection.purpose }}</div>
            <div>{{ selection.industry }}</div>
            <div>{{ selection.template }}</div>
            <div>
                <custom-button :dark-mode="darkMode" :color="'red'" :height="'h-6'" @click="confirmDelete(selection)">Delete</custom-button>
            </div>
        </div>
        <modal :dark-mode="darkMode" :small="true" @confirm="deleteSelection" @close="clearDelete" v-if="showDeleteModal" :confirm-text="'Delete'" :disable-confirm="saving">
            <template v-slot:header>Confirm delete?</template>
            <template v-slot:content>
                <div class="grid grid-cols-3 gap-3 text-sm">
                    <p class="font-semibold text-right">Type:</p>
                    <p class="col-span-2">{{ deletingSelection.type }}</p>
                    <p class="font-semibold text-right">Purpose:</p>
                    <p class="col-span-2">{{ deletingSelection.purpose }}</p>
                    <p class="font-semibold text-right">Industry:</p>
                    <p class="col-span-2">{{ deletingSelection.industry }}</p>
                    <p class="font-semibold text-right">Template:</p>
                    <p class="col-span-2">{{ deletingSelection.template }}</p>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import Api from "../services/template-management-api.js";
import Dropdown from "../../Shared/components/Dropdown.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Modal from "../../Shared/components/Modal.vue";

export default {
    name: "ConsumerTemplateSelector",
    components: {Modal, LoadingSpinner, CustomButton, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        api: {
            type: Api,
            required: true
        },
        industries: {
            type: Array,
            default: []
        },
        templates: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            templateRelation: 'consumer',
            templateSelections: [],
            types: [
                {id: 'email', name: 'Email'},
                {id: 'sms', name: 'SMS'},
            ],
            selectedType: 'email',
            purposeOptions: [{id: 'consumer_email_verification', name: 'Consumer Email Verification'},],
            selectedPurpose: 'consumer_email_verification',
            selectedIndustry: 0,
            selectedTemplate: null,
            saving: false,
            loading: false,
            showDeleteModal: false,
            deletingSelection: null
        }
    },
    created() {
        this.getTemplateSelections();
    },
    computed: {
        templateOptions() {
            return this.templates.filter(template => template.type === this.selectedType).map(template => {
                return {
                    id: template.id,
                    name: template.name
                };
            });
        },
        industryOptions() {
            return [{id: 0, name: 'Select'}, ...this.industries.map(industry => {
                return {
                    id: industry.id,
                    name: industry.name
                };
            })];
        }
    },
    methods: {
        resetTemplateSelection() {
            this.selectedTemplate = null;
        },
        getTemplateSelections() {
            this.loading = true;
            this.templateSelections = [];
            this.api.getTemplateSelectors(this.templateRelation)
                .then(resp => this.templateSelections = resp.data.data.selections)
                .catch(e => console.error(e))
                .finally(() => this.loading = false);
        },
        saveTemplateSelection() {
            this.saving = true;
            this.api.saveTemplateSelector(this.templateRelation, this.prepareDataForSaving())
                .then(() => this.getTemplateSelections())
                .catch(e => console.error(e))
                .finally(() => this.saving = false);

        },
        prepareDataForSaving() {
            return {
                purpose_key: this.selectedPurpose,
                template_type: this.selectedType,
                template_id: this.selectedTemplate,
                industry_id: this.selectedIndustry > 0 ? this.selectedIndustry : null
            }
        },
        confirmDelete(selection) {
            this.showDeleteModal = true;
            this.deletingSelection = selection;
        },
        clearDelete() {
            this.showDeleteModal = false;
            this.deletingSelection = null;
        },
        deleteSelection() {
            this.saving = true;
            this.api.deleteTemplateSelector(this.templateRelation, this.deletingSelection.id)
                .then(() => {
                    this.getTemplateSelections();
                    this.clearDelete();
                })
                .catch(e => console.error(e))
                .finally(() => this.saving = false);
        }
    }
}
</script>
