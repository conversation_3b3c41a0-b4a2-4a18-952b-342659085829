export default class Api {
    baseUrl;
    version;
    baseEndpoint;
    constructor(baseUrl = 'internal-api', version= 1, baseEndpoint = 'template-management') {
        this.baseEndpoint = baseEndpoint;
        this.version = version;
        this.baseUrl = baseUrl;
    }

    static make() {
        return new Api();
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getTemplates(type, params = {}) {
        return this.axios().get(`/${type}`, {
            params
        })
    }

    saveTemplate(type, data, templateId= null) {
        if (templateId) {
            //update
            return this.axios().patch(`${type}/${templateId}`, data);
        }

        //create
        return this.axios().post(`${type}`, data);
    }

    getTemplateSelectorData() {
        return this.axios().get('/template-selectors/get-initial-data');
    }

    getTemplateSelectors(relation) {
        return this.axios().get(`/template-selectors/${relation}`);
    }

    saveTemplateSelector(relation, data) {
        return this.axios().post(`/template-selectors/${relation}`, data);
    }

    deleteTemplateSelector(relation, id) {
        return this.axios().delete(`/template-selectors/${relation}/${id}`);
    }
}
