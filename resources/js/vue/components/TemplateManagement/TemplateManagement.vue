<template>
    <div>
        <email-templates :dark-mode="darkMode" v-if="selectedTemplate === 'email'" @back="selectedTemplate = null"></email-templates>
        <templates :dark-mode="darkMode" v-else-if="selectedTemplate === 'sms'" :template-type="'sms'" @back="selectedTemplate = null"></templates>
        <div :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}" v-else
             class="w-full flex-auto pt-3 relative">
            <div :class="[darkMode ? 'text-white' : 'text-slate-900']" class="px-10">
                <h3 class="text-xl font-medium mt-3 mb-2 leading-none">Template Editors</h3>
                <hr>
                <div class="flex gap-5 py-5">
                    <custom-button @click="selectedTemplate='email'" v-if="permissionStore.hasPermission(PERMISSIONS.PERMISSION_EMAIL_TEMPLATE)">Email Templates</custom-button>
                    <custom-button @click="selectedTemplate='sms'" v-if="permissionStore.hasPermission(PERMISSIONS.PERMISSION_SMS_TEMPLATE_VIEW)">SMS Templates</custom-button>
                </div>
                <h3 class="text-xl font-medium mt-9 mb-2 leading-none">Template Selectors</h3>
                <hr>
                <div class="mt-3">
                    <template-selector :dark-mode="darkMode"></template-selector>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import CustomButton from "../Shared/components/CustomButton.vue";
import EmailTemplates from "../EmailTemplates/EmailTemplates.vue";
import Templates from "./components/Templates.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../stores/roles-permissions.store.js";
import TemplateSelector from "./components/TemplateSelector.vue";

export default {
    name: "TemplateManagement",
    computed: {
        PERMISSIONS() {
            return PERMISSIONS
        }
    },
    components: {TemplateSelector, Templates, EmailTemplates, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            selectedTemplate: null,
            permissionStore: useRolesPermissions()
        }
    }
}
</script>
