<template>
    <div>
        <div v-if="loading" class="grid justify-center items-center">
            <LoadingSpinner :dark-mode="darkMode"/>
        </div>
        <div v-else class="grid grid-cols-2 gap-1 px-1">
            <div v-for="permission in permissionOptions" class="flex gap-3 items-center">
                <input
                    class="cursor-pointer w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded"
                    @click="handleClick(permission.id)"
                    :checked="modelValue.includes(permission.id)"
                    type="checkbox"
                    :name="permission.name"
                />
                <p class="cursor-pointer" @click="handleClick(permission.id)">{{permission.name}}</p>
            </div>
        </div>
    </div>
</template>

<script>
import SharedApiService from "../../../Shared/services/api";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";

export default {
    name: 'PermissionPicker',
    components: {LoadingSpinner},
    props: {
        darkMode: {
            type: <PERSON>olean,
            default: false,
        },
        modelValue: {
            type: Array,
            required: true,
        },
    },

    data() {
        return {
            api: SharedApiService.make(),
            permissionOptions: [],
            loading: false,
        }
    },

    async mounted() {
        this.loading = true;

        await this.getPermissionOptions();

        this.loading = false;
    },

    methods: {
        async getPermissionOptions(){
            const res = await this.api.getPermissionOptions();
            this.permissionOptions = res.data.data.permissions;
        },

        handleClick(id){
            if (!id) return;

            let newValue = [...this.modelValue];

            const idx = this.modelValue.indexOf(id);

            if (idx > -1) newValue.splice(idx, 1);
            else newValue.push(id);

            this.$emit('update:modelValue', newValue);
        }
    },
};
</script>
