<template>
    <modal :loading-confirmation="loading" @confirm="handleConfirm" @close="handleClose" :dark-mode="darkMode" confirm-text="Save" :restrict-width="false" :custom-width="'max-w-[80vw]'">
        <template v-slot:header>
            <p class="font-semibold">{{modalTitle}}</p>
        </template>
        <template v-slot:content>
            <div class="grid md:grid-cols-2 gap-4 relative z-10">
                <small class="z-20 absolute top-6 inset-x-0 text-red-500 text-center">{{errors.api_error}}</small>
                <div>
                    <p class="font-semibold text-sm mb-1" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                        Name *
                    </p>
                    <CustomInput :dark-mode="darkMode" v-model="localUser.name" placeholder="Enter name"/>
                    <small class="text-red-500">{{errors.name}}</small>
                </div>
                <div>
                    <p class="font-semibold text-sm mb-1" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                        Email *
                    </p>
                    <CustomInput :dark-mode="darkMode" v-model="localUser.email" placeholder="Enter email"/>
                    <small class="text-red-500">{{errors.email}}</small>
                </div>
                <div>
                    <p class="font-semibold text-sm mb-1" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                        Roles *
                    </p>
                    <div class="p-4 border rounded-md max-h-[280px] overflow-y-auto relative" :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']">
                        <RolePicker v-model="localUser.roles" :dark-mode="darkMode" />
                        <small class="absolute top-4 inset-x-0 text-red-500">{{errors.roles}}</small>
                    </div>
                </div>
                <div>
                    <p class="font-semibold text-sm mb-1" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                        Direct Permissions
                    </p>
                    <div class="p-4 border rounded-md max-h-[280px] overflow-y-auto relative" :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']">
                        <PermissionPicker v-model="localUser.permissions" :dark-mode="darkMode" />
                        <small class="absolute top-4 inset-x-0 text-red-500">{{errors.permissions}}</small>
                    </div>
                </div>

                <div >
                    <p class="font-semibold text-sm mb-1" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                        Phone
                    </p>
                    <Dropdown
                        v-model="localUser.phone"
                        :dark-mode="darkMode"
                        :options="phoneOptions"
                        placeholder="Select A Phone Number"
                        placement="top"
                    />
                    <small class="text-red-500">{{errors.phone}}</small>
                </div>
                <div class="flex flex-col">
                    <p class="font-semibold text-sm mb-1" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                        Legacy Id
                    </p>
                    <CustomInput
                        :dark-mode="darkMode"
                        v-model="localUser.legacy_user_id"
                        type="number"
                        placeholder="Enter legacy user id - or create one"
                    />
                </div>
                <div class="flex flex-col">
                    <p class="font-semibold text-sm mb-1" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                        Slack Username
                    </p>
                    <CustomInput
                        :dark-mode="darkMode"
                        v-model="localUser.slack_username"
                        type="text"
                        placeholder="Enter Slack Username"
                    />
                </div>
                <div class="grid grid-cols-2 mb-1">
                    <div class="col-span-1 flex justify-between">
                        <div v-if="!localUser.legacy_user_id" class="flex flex-col">
                            <p class="font-semibold text-sm mb-1" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                                Create Legacy User?
                            </p>
                            <toggle-switch
                                v-model="localUser.create_legacy_user"
                                :dark-mode="darkMode"
                            />
                            <small class="text-red-500">{{errors.create_legacy_user}}</small>
                        </div>
                        <div>
                            <p class="font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                                Force 2FA?
                            </p>
                            <toggle-switch
                                v-model="localUser.force_two_factor_auth"
                                :dark-mode="darkMode"
                            />
                            <small class="text-red-500">{{errors.force_two_factor_auth}}</small>
                        </div>
                    </div>
                </div>
                <div class="col-span-full flex gap-1">
                    <div v-if="localUser.created_by_name" class="self-start font-semibold ">
                        <p class="font-semibold text-sm" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                            Created By:
                        </p>
                        <p class="text-xs mb-1" :class="[darkMode ? 'text-slate-200' : 'text-slate-500']">
                            {{ localUser.created_by_name }} <br> {{ this.$filters.dateFromTimestamp(localUser.created_at) }}
                        </p>
                    </div>
                    <div v-if="localUser.updated_by_name" class="self-start font-semibold ">
                        <p class="font-semibold text-sm" :class="[darkMode ? 'text-slate-200' : 'text-slate-900']">
                            Updated By:
                        </p>
                        <p class="text-xs mb-1" :class="[darkMode ? 'text-slate-200' : 'text-slate-500']">
                            {{ localUser.updated_by_name }} <br> {{ this.$filters.dateFromTimestamp(localUser.updated_at) }}
                        </p>
                    </div>
                </div>
            </div>
        </template>
    </modal>
</template>

<script>
import Dropdown from "../../../Shared/components/Dropdown.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";
import Modal from "../../../Shared/components/Modal.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import { ApiFactory } from "../../services/api/factory";
import RolePicker from "./RolePicker.vue";
import SharedApiService from "../../../Shared/services/api";
import PermissionPicker from "./PermissionPicker.vue";

const INIT_USER_DATA = {
    id: null,
    name: '',
    email: '',
    roles: [],
    permissions: [],
    phone: null,
    legacy_user_id: null,
    force_two_factor_auth: true,
    create_legacy_user: true,
    slack_username: '',
}

export default {
    name: 'CreateOrEditUserModal',
    components: {PermissionPicker, RolePicker, CustomInput, ToggleSwitch, Modal, MultiSelect, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            required: true,
        },
        selectedUser: {
            type: Object,
            default: {}
        }
    },

    data() {
        return {
            loading: false,
            api: ApiFactory.makeApiService('api'),
            sharedApiService: SharedApiService.make(),
            localUser: {
                ...INIT_USER_DATA,
            },
            errors: {},
            phoneOptions: []
        }
    },

    async mounted() {
        if (this.isUpdate) {
            this.localUser = {
                ...this.selectedUser,
            }
        }

        const { data } = await this.sharedApiService.getAvailablePhones();

        if (this.localUser.phone) {
            this.phoneOptions.push({
                id: this.localUser.phone.id,
                name: this.localUser.phone.phone,
            })
            this.localUser.phone = this.localUser.phone.id
        }

        this.phoneOptions.push(...data.data.phones)
    },

    methods: {
        resetLocalUser(){
            this.localUser = {
                ...INIT_USER_DATA
            }
        },

        resetErrors(){
            this.errors = {}
        },

        handleClose(updated = false){
            this.resetErrors()
            this.resetLocalUser();
            this.$emit('close', updated);
        },

        handleError(error){
            console.error(error)
            if (error.response && error.response.data.errors && error.response.status === 422) {
                Object.entries(error.response.data.errors).forEach(([key, err]) => {
                    this.errors[key] = err[0]
                })
            } else {
                this.errors.api_error = error.message
            }
        },

        async saveUser(){
            let phoneNumber = this.localUser.phone?.id ?? this.localUser.phone ?? '';

            const payload = {
                name: this.localUser.name,
                email: this.localUser.email,
                legacy_user_id: this.localUser.legacy_user_id,
                roles: this.localUser.roles,
                permissions: this.localUser.permissions,
                phone: phoneNumber,
                force_two_factor_auth: this.localUser.force_two_factor_auth,
                create_legacy_user: this.localUser.create_legacy_user,
                slack_username: this.localUser.slack_username,
            }

            if (!this.isUpdate) {
                return await this.api.createUser(payload)
            }

            return await this.api.updateUser(this.selectedUser.id, payload)
        },

        async handleConfirm(){
            this.loading = true

            try {
                await this.saveUser()
                this.$emit('save');
                this.handleClose(true);
            } catch (err) {
                this.handleError(err)
            }

            this.loading = false
        }
    },

    computed: {
        modalTitle(){
            return this.isUpdate ? 'Edit User' : 'Add User'
        },

        isUpdate(){
            return !!this.selectedUser?.id
        }
    },

    watch: {
        localUser: {
            handler(){
                this.resetErrors();
            },
            deep: true
        }
    }
};
</script>
