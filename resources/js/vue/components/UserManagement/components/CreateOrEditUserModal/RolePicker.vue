<template>
    <div>
        <div v-if="loading" class="grid justify-center items-center">
            <LoadingSpinner :dark-mode="darkMode"/>
        </div>
        <div v-else class="grid grid-cols-2 gap-1 px-1">
            <div v-for="role in roles" class="flex gap-3 items-center">
                <input
                    class="cursor-pointer w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded"
                    @click="handleClick(role.id)"
                    :checked="modelValue.includes(role.id)"
                    type="checkbox"
                    :name="role.name"
                />
                <p class="cursor-pointer" @click="handleClick(role.id)">{{role.name}}</p>
            </div>
        </div>
    </div>
</template>

<script>
import CustomInput from "../../../Shared/components/CustomInput.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import Modal from "../../../Shared/components/Modal.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import SharedApiService from "../../../Shared/services/api";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";

export default {
    name: 'RolePicker',
    components: {LoadingSpinner, CustomInput, ToggleSwitch, Modal, MultiSelect, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            required: true,
        },
        modelValue: {
            type: Array,
            required: true,
        },
    },

    async mounted() {
        this.loading = true

        await this.getRoleOptions()

        this.loading = false
    },

    data() {
        return {
            api: SharedApiService.make(),
            rolesOptions: [],
            loading: true,
        }
    },

    methods: {
        async getRoleOptions(){
            const res = await this.api.getRoleOptions();
            this.rolesOptions = res.data.data.roles
        },

        handleClick(id){
            if (!id) return;

            let newValue = [...this.modelValue]

            const idx = this.modelValue.indexOf(id)

            if (idx > -1) newValue.splice(idx, 1)
            else newValue.push(id)

            this.$emit('update:modelValue', newValue)
        }
    },

    computed: {
        roles(){
            return this.rolesOptions
        }
    }
};
</script>
