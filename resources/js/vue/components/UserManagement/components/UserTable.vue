<template>
    <div class="m-10" :class="{'text-white': darkMode}">
        <simple-table
            :dark-mode="darkMode"
            title="User List"
            v-model="tableFilter"
            :table-filters="tableFilters"
            @update:modelValue="getUsers"
            :data="data"
            :headers="headers"
            :loading="loading"
            @search="handleSearch"
            @reset="handleReset"
            bodyWrapperClasses="h-200"
            :no-pagination=true
            :row-classes="'gap-5 grid items-center py-4 rounded px-5'"
        >
            <template v-slot:row.col.id="{item, value}">
                <p class="text-sm grid">
                   <span>ID: {{ item.id }}</span>
                   <span v-if="item.legacy_user_id">Legacy ID: {{ item.legacy_user_id }}</span>
                </p>
            </template>
            <template v-slot:row.col.name="{item, value}">
                <p class="text-sm truncate">{{ item.name }} <br> {{ item.email }}</p>
            </template>
            <template v-slot:row.col.email="{item, value}">
                <p class="text-sm truncate">{{ item.email }}</p>
            </template>
            <template v-slot:row.col.roles="{item, value}">
                <p class="text-sm truncate">{{ item.roles.join(', ')}}</p>
            </template>
            <template v-slot:row.col.permissions="{item, value}">
                <p class="text-sm">{{ item.permissions.join(', ')}}</p>
            </template>
            <template v-slot:row.col.phone="{item, value}">
                <p class="text-sm truncate">{{ $filters.formatPhoneNumber(item?.phone?.phone) }}</p>
            </template>
            <template v-slot:row.col.2fa_enabled="{item, value}">
                <p class="text-sm truncate">{{ item.verified_2fa ? 'Yes' : 'No' }}</p>
            </template>
            <template v-slot:row.col.actions="{item, value}">
                <ActionsHandle :dark-mode="darkMode"  @edit="() => handleEditUser(item)" @delete="showDeleteUser(item.id)" />
            </template>
            <template v-slot:row.col.impersonate="{item, value}">
                <a :href="`/impersonate/${item.id}`" class="inline-flex text-sm items-center text-primary-500" v-if="canImpersonate">
                    <svg class="mr-2" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 1.6C6.30261 1.6 4.67475 2.27428 3.47452 3.47452C2.27428 4.67475 1.6 6.30261 1.6 8C1.6 8.84046 1.76554 9.67269 2.08717 10.4492C2.25799 10.8616 2.47107 11.254 2.72237 11.6203C4.36235 10.8162 6.1679 10.3973 8.00061 10.4C9.89259 10.4001 11.6842 10.8384 13.2777 11.6202C13.529 11.2539 13.742 10.8615 13.9128 10.4492C14.2345 9.67269 14.4 8.84046 14.4 8C14.4 6.30261 13.7257 4.67475 12.5255 3.47452C11.3253 2.27428 9.69739 1.6 8 1.6ZM14.1321 13.1379C14.6541 12.5149 15.079 11.8149 15.391 11.0615C15.7931 10.0909 16 9.05057 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 4.76837e-08 5.87827 0 8C0 9.05058 0.206926 10.0909 0.608964 11.0615C0.92104 11.8149 1.34594 12.5149 1.86791 13.1379C1.8797 13.1529 1.89195 13.1674 1.90463 13.1814C2.04411 13.3454 2.19038 13.5041 2.34315 13.6569C3.08601 14.3997 3.96793 14.989 4.93853 15.391C5.90914 15.7931 6.94943 16 8 16C9.05057 16 10.0909 15.7931 11.0615 15.391C12.0321 14.989 12.914 14.3997 13.6569 13.6569C13.8096 13.5041 13.9559 13.3454 14.0954 13.1814C14.1081 13.1674 14.1203 13.1529 14.1321 13.1379ZM12.1588 12.8646C10.8857 12.3084 9.47969 12 8 12L7.99879 12C6.56432 11.9978 5.14905 12.2938 3.84145 12.8648C4.35185 13.3011 4.92855 13.6551 5.55083 13.9128C6.32731 14.2345 7.15954 14.4 8 14.4C8.84046 14.4 9.67269 14.2345 10.4492 13.9128C11.0715 13.655 11.6483 13.301 12.1588 12.8646ZM8.00061 10.4C8.00081 10.4 8.00101 10.4 8.00121 10.4L8 11.2V10.4C8.0002 10.4 8.0004 10.4 8.00061 10.4ZM8 4.8C7.57565 4.8 7.16869 4.96857 6.86863 5.26863C6.56857 5.56869 6.4 5.97565 6.4 6.4C6.4 6.82435 6.56857 7.23131 6.86863 7.53137C7.16869 7.83143 7.57565 8 8 8C8.42435 8 8.83131 7.83143 9.13137 7.53137C9.43143 7.23131 9.6 6.82435 9.6 6.4C9.6 5.97565 9.43143 5.56869 9.13137 5.26863C8.83131 4.96857 8.42435 4.8 8 4.8ZM5.73726 4.13726C6.33737 3.53714 7.15131 3.2 8 3.2C8.84869 3.2 9.66263 3.53714 10.2627 4.13726C10.8629 4.73737 11.2 5.55131 11.2 6.4C11.2 7.24869 10.8629 8.06263 10.2627 8.66274C9.66263 9.26286 8.84869 9.6 8 9.6C7.15131 9.6 6.33737 9.26286 5.73726 8.66274C5.13714 8.06263 4.8 7.24869 4.8 6.4C4.8 5.55131 5.13714 4.73737 5.73726 4.13726Z" fill="#0081FF"/>
                    </svg>
                    Impersonate
                </a>
            </template>
            <template v-slot:custom-buttons="slotProps">
                <slot name="custom-buttons" v-bind="slotProps">
                    <div class="inline-flex items-center gap-2">
                        <p class="text-sm font-semibold text-slate-500">Show Deactivated users</p>
                        <toggle-switch  v-model="showDeactivatedUsers" :dark-mode="darkMode"></toggle-switch>
                    </div>
                    <CustomButton @click="showEditOrCreateUserModal = true" class="justify-self-end">
                        <p class="text-center">Add User</p>
                    </CustomButton>
                </slot>
            </template>
        </simple-table>
    </div>
    <Modal
        v-if="deleteUserModal"
        :dark-mode="darkMode"
        confirm-text="Delete"
        close-text="Cancel"
        :loading-confirmation="deletingUser"
        @close="showDeleteUser(null)"
        @confirm="deleteUser"
        small
    >
        <template v-slot:header>
            <h4 class="text-xl font-medium">Are you sure you want to delete this User?</h4>
        </template>
    </Modal>
    <CreateOrEditUserModal
        v-if="showEditOrCreateUserModal"
        :dark-mode="darkMode"
        @save="handleUserSave"
        @close="handleEditOrCreateModalClose"
        :selected-user="selectedUser"
    />
</template>

<script>
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import {SimpleTableFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {ApiFactory} from "../services/api/factory.js";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import Modal from "../../Shared/components/Modal.vue";
import CreateOrEditUserModal from "./CreateOrEditUserModal/CreateOrEditUserModal.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";

const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 100,
}

export default {
    name: "UserTable",
    components: {CustomButton, ToggleSwitch, CreateOrEditUserModal, Modal, ActionsHandle, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        isImpersonating: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            api: ApiFactory.makeApiService('api'),
            loading: false,
            tableFilter: {},
            headers: [
                {title: "id & legacy id", field: "id"},
                {title: "name & email", field: "name", cols: 2},
                {title: "role(s)", field: "roles", cols: 3},
                {title: "direct permission(s)", field: "permissions", cols: 3},
                {title: "phone", field: "phone"},
                {title: "2fa enabled", field: "2fa_enabled"},
                {title: "Actions", field: "actions"},
                {title: " ", field: "impersonate"},
            ],
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: "user_detail",
                    title: "Enter User Name or Email",
                },
            ],
            data: [],
            paginationData: {},
            deleteUserId: null,
            deleteUserModal: false,
            showEditOrCreateUserModal: false,
            selectedUser: null,
            deletingUser: false,
            showDeactivatedUsers: false,
            permissionStore: useRolesPermissions(),
        }
    },
    created() {
        this.getUsers();
    },
    computed: {
        canImpersonate() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_IMPERSONATE_USERS) && !this.isImpersonating;
        }
    },
    methods: {
        async getUsers() {
            this.loading = true;
            const response =  await this.api.getAllUsers(this.tableFilter, this.showDeactivatedUsers);
            const { data } = response.data;
            this.data = data;
            this.loading = false;
        },
        async handleSearch() {
            this.tableFilter = {...this.tableFilter};
            await this.getUsers();
        },
        async handleReset() {
            this.tableFilter = {};
            this.showDeactivatedUsers = false;
            await this.getUsers();
        },
        showDeleteUser(userId) {
            this.deleteUserId = userId;
            this.deleteUserModal = !this.deleteUserModal;
        },
        async deleteUser() {
            this.deletingUser = true;
            try {
                await this.api.deleteUser(this.deleteUserId);
                this.showDeleteUser(null);
                await this.getUsers();
            } catch (err) {
                console.error(err)
            }
            this.deletingUser = false;
        },
        async handleUserSave(){
            this.tableFilter = {...this.tableFilter};
            await this.getUsers();
        },
        handleEditUser(user){
            this.selectedUser = user;
            this.showEditOrCreateUserModal = true;
        },
        handleEditOrCreateModalClose(updated){
            if (updated) this.$emit('refresh')
            this.showEditOrCreateUserModal = false;
            this.selectedUser = null;
        }
    },
    watch: {
        showDeactivatedUsers() {
           this.getUsers();
        }
    }
}
</script>

<style scoped>

</style>
