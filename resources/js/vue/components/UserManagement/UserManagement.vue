<template>
    <div class="m-8">
        <div class="ml-2 mb-4">
            <h3 class="text-xl font-medium pb-0 leading-none mr-5" :class="{'text-slate-900': !darkMode, 'text-slate-100': darkMode}">User Management</h3>
        </div>
        <user-table
            :dark-mode="darkMode"
            :is-impersonating="isImpersonating"
        />
    </div>
</template>

<script>
import UserTable from "./components/UserTable.vue";
import {ApiFactory} from "./services/api/factory.js";
import Dashboard from "../LeadProcessingManagement/layouts/Dashboard.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import SharedApiService from "../Shared/services/api.js";
import alertsMixin from "../../mixins/alerts-mixin.js";

export default {
    components: {LoadingSpinner, Dashboard, UserTable},
    mixins: [alertsMixin],
    data() {
        return {
            api: ApiFactory.makeApiService(this.apiDriver),
            sharedApiService: SharedApiService.make(),
            users: [],
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        isImpersonating: {
            type: Boolean,
            default: false
        },
        apiDriver: {
            type: String,
            default: 'api'
        },
    },
    created() {},
    methods: {},
};
</script>
