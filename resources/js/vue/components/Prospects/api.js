import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'prospecting', 2);
    }

    getNextAvailableProspect(timezones) {
        return this.axios().get('get-next-available-prospect', {params: {timezones}})
    }

    getNextAvailableRegistration() {
        return this.axios().get('get-next-available-registration')
    }

    getStateOptions() {
        return this.axios().get('state-options')
    }

    getCityOptions(stateAbbr) {
        return this.axios().get('city-options/' + stateAbbr)
    }

    saveProspect(prospect) {
        return this.axios().patch('update-prospect/' + prospect.reference, prospect)
    }

    convertToCompany(prospect) {
        return this.axios().post('convert-to-company/' + prospect.reference, prospect)
    }

    archiveProspect(prospect) {
        return this.axios().post('archive-prospect/' + prospect.reference)
    }

    releaseBackToQueue(prospect) {
        return this.axios().post('release-back-to-queue/' + prospect.reference)
    }

    getMyProspects() {
        return this.axios().get('get-my-prospects')
    }

    getMyConvertedCompanies() {
        return this.axios().get('get-my-converted-companies')
    }

    flagAsDuplicate(prospectReference, companyId) {
        return this.axios().post('flag-as-duplicate', {
            'prospect_external_reference': prospectReference,
            'duplicate_company_id': companyId
        })
    }

    getCloserForDemo(){
        return this.axios().get('get-closer-for-demo')
    }

    getBdmEmailContent(prospectReference) {
        return this.axios().get(`get-bdm-email-content/${prospectReference}`);
    }

    sendEmail(prospectReference, email, content, subject, cc = undefined, bcc = undefined) {
        return this.axios().post(`send-email/${prospectReference}`, {
            email,
            content,
            subject,
            cc,
            bcc
        });
    }

    getEmailPreview(prospectReference, subject, content) {
        return this.axios().post(`email-preview/${prospectReference}`, {
            content,
            subject
        });
    }

    saveEmailTemplate(content, subject, name, templateId = undefined) {
        return this.axios().post('save-email-template', {
            content,
            subject,
            name,
            templateId
        });
    }

    scanMissedProducts() {
        return this.axios().get('scan-missed-products');
    }

    sendMissedProductsNotification(companyId, companyUserIds) {
        return this.axios().post(`/${companyId}/send-missed-products-notification`, {
            company_user_ids: companyUserIds
        });
    }

    getAvailableMissedProductPreviews(companyId) {
        return this.axios().get(`/${companyId}/available-product-previews`);
    }

    deliverMissedProducts(companyId, consumerProductIds) {
        return this.axios().post(`/${companyId}/deliver-missed-products`, {
            consumer_product_ids: consumerProductIds
        });
    }

    getProspect(prospectId){
        return this.axios().get('get-prospect/' + prospectId)
    }

    getAvailableEmailRecipients(companyId) {
        return this.axios().get(`/${companyId}/available-email-recipients`);
    }

    getNextAvailableExistingCompany(timezones){
        return this.axios().get('get-next-available-existing-company', {params: {timezones}})
    }

    releaseCompanyBackToQueue(company) {
        return this.axios().post('release-company-back-to-queue/' + company.id)
    }

    getVerifiedCompanyCount(){
        return this.axios().get('get-verified-company-count')
    }

    getMostRecentRegistrations(){
        return this.axios().get('get-most-recent-registrations')
    }
}
