<script setup>
import {computed, onMounted, ref} from "vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import ProspectInfo from "./components/ProspectInfo.vue";
import ApiService from "./api.js"
import Alert from "../Shared/components/Alert.vue";
import MyProspects from "./components/MyProspects.vue";
import Modal from "../Shared/components/Modal.vue";
import ConvertedCompanies from "./components/ConvertedCompanies.vue";

const props = defineProps({
    darkMode: false,
})
const prospect = ref(null)
const loading = ref(false)
const message = ref(null)
const apiService = ApiService.make();

const getNextAvailableProspect = () => {
    loading.value = true
    apiService.getNextAvailableProspect().then(res => {
        if(res.data.prospect)
            prospect.value = res.data.prospect
        else
            message.value = 'No New Prospects Available. Try Again Later'
    }).finally(() => loading.value = false)
}

const save = (nextProspect, message) => {
    apiService.saveProspect(prospect.value).then(res => {
        if (message) {
            flashAlert('success', prospect.value.company_name + message)
        } else {
            flashAlert('success', prospect.value.company_name + ' Saved Successfully')
        }

        if(nextProspect){
            getNextAvailableProspect()
        }else {
            prospect.value = res.data.prospect
        }
    })
}

const alert = ref({})
const timeout = ref(null)
const flashAlert = (type, message) => {
    if(timeout){
        clearTimeout(timeout.value)
        timeout.value = null
    }
    alert.value = {type, message}
    timeout.value = setTimeout(() => alert.value = {}, 4000)
}

const tabs = ref([
    {name: 'My Prospects', current: true},
    {name: 'Prospect Companies', current: false},
    {name: 'Converted Companies', current: false},
])
const selectedTab = ref('My Prospects')
const selectTab = (selected) => {
    selectedTab.value = selected;
}

const themeClasses = computed(() => {
    if(props.darkMode === true) {
        return 'bg-dark-module border-dark-border text-slate-50'
    }
    else {
        return 'bg-light-module border-light-border text-slate-900'
    }
})

function editProspect(selectedProspect) {
    prospect.value = selectedProspect
    selectTab('Prospect Companies')
}

const showAlertModal = ref(false)
const alertMessage = ref('')

</script>

<template>

    <Modal v-if="showAlertModal" hide-confirm close-text="Dismiss" @close="showAlertModal = false">
        <template #content><div class="alert" v-html="alertMessage"></div></template>
    </Modal>

    <div class="flex gap-6 mt-8 px-8">
        <div class="fixed top-20 inset-x-0 flex m-4 z-[9999]" v-if="alert.message">
            <Alert :dark-mode="darkMode" class="mx-auto" :alert-type="alert.type" :text="alert.message"></Alert>
        </div>
        <!--   Tab Menu   -->
        <div class="w-72 flex-shrink-0"></div>
        <div class="w-72 flex-shrink-0 fixed top-[6rem] left-10 border rounded-lg z-40 overflow-hidden" :class="[themeClasses]">
            <h1 class="px-5 py-3 font-semibold text-lg border-b" :class="[themeClasses]">Prospects</h1>
            <div class="h-[calc(50vh-8rem)] overflow-y-auto">
                <ol>
                    <li v-for="tab in tabs" :key="tab"
                        @click="selectTab(tab.name)"
                        class="px-5 py-2 cursor-pointer relative border-l-2"
                        :class="[(darkMode ? (selectedTab === tab.name ? 'border-primary-500 text-primary-500 font-semibold bg-dark-background' : 'border-transparent hover:bg-dark-border hover:text-white font-medium text-slate-200')
                        : (selectedTab === tab.name ? 'border-primary-500 text-primary-500 font-semibold bg-primary-50' : 'border-transparent hover:bg-slate-100 hover:text-slate-900 font-medium text-slate-700'))]">
                        {{tab.name}}
                    </li>
                </ol>
            </div>
        </div>
        <!--   My Prospects   -->
        <div v-if="selectedTab === 'My Prospects'" class="border rounded-lg flex-grow" :class="[themeClasses]">
            <MyProspects :dark-mode="darkMode" @edit-prospect="editProspect"></MyProspects>
        </div>
        <!--   Find New Prospects   -->
        <div v-else-if="selectedTab === 'Prospect Companies'" class="flex-grow">
            <div v-if="loading" class="h-[412px] flex justify-center items-center">
                <LoadingSpinner></LoadingSpinner>
            </div>
            <div v-else class="pb-16">
                <div v-if="prospect">
                    <ProspectInfo
                        @save="save"
                        :dark-mode="darkMode"
                        :prospect="prospect"
                        :api-service="apiService"
                        @flash-alert="flashAlert">
                    </ProspectInfo>
                </div>
                <div v-else class="flex justify-center h-[412px] items-center border rounded-lg" :class="themeClasses">
                    <div class="text-center">
                        <CustomButton icon @click="getNextAvailableProspect">
                            <template #icon>
                                <svg class="w-4 mr-1" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.65894 16.4672C10.4339 16.4668 12.1577 15.8726 13.5559 14.7792L17.9519 19.1752L19.3659 17.7612L14.9699 13.3652C16.0639 11.9669 16.6585 10.2427 16.6589 8.46721C16.6589 4.05621 13.0699 0.467213 8.65894 0.467213C4.24794 0.467213 0.658936 4.05621 0.658936 8.46721C0.658936 12.8782 4.24794 16.4672 8.65894 16.4672ZM8.65894 2.46721C11.9679 2.46721 14.6589 5.15821 14.6589 8.46721C14.6589 11.7762 11.9679 14.4672 8.65894 14.4672C5.34994 14.4672 2.65894 11.7762 2.65894 8.46721C2.65894 5.15821 5.34994 2.46721 8.65894 2.46721Z" fill="white"/></svg>
                            </template>
                            Search for Prospects
                        </CustomButton>
                        <div class="pt-4" v-if="message">{{message}}</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Converted Companies -->
        <div v-else-if="selectedTab === 'Converted Companies'" class="border rounded-lg flex-grow" :class="[themeClasses]">
            <ConvertedCompanies :dark-mode="darkMode"></ConvertedCompanies>
        </div>
    </div>
</template>

<style>
    .alert a{
        @apply text-primary-500 underline;
    }
</style>
