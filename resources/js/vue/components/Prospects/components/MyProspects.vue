<script setup>
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import {computed, onMounted, ref, defineEmits} from "vue";
import ApiService from "./../api.js";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import Badge from "../../Shared/components/Badge.vue";

const props = defineProps({
    darkMode: false,
})
const themeBackgroundClasses = computed(() => {
    if(props.darkMode === true) {
        return 'bg-dark-background border-dark-border text-slate-50'
    }
    else {
        return 'bg-light-background border-light-border text-slate-900'
    }
})



onMounted(() => {
    getMyProspects()
})
const apiService = ApiService.make();
const loading = ref(false)
const myProspects = ref(null);
const getMyProspects = () => {
    loading.value = true
    apiService.getMyProspects().then(res => {
        if(res.data.prospects)
            myProspects.value = res.data.prospects.map((i) => {
                return i;
            });
    }).catch((e) => {
        flashAlert(e)
    }).finally(() => {
        loading.value = false
    })
}

const emit = defineEmits(['flash-alert', 'edit-prospect', 'save'])
function flashAlert(type, message) {
    emit('flash-alert', type, message);
}
function editProspect(prospect) {
    emit('edit-prospect', prospect);
}
function save(nextProspect, message) {
    emit('save', nextProspect, message);
}
function releaseBackToQueue(prospect) {
    if(prospect.status === 'active') {
        apiService.releaseBackToQueue(prospect)
            .then(res => save(false, ' has been released back to Queue.'))
            .finally(
                getMyProspects()
            )
    }
}
const myProspectActions = ref([
    {event: 'edit-prospect', name: 'Edit Prospect'},
    {event: 'release-back-to-queue', name: 'Release Back to Queue'},
]);
</script>

<template>
    <div>
        <h3 class="font-bold text-sm uppercase text-primary-500 p-5">
            My Prospects
        </h3>
        <div class="grid grid-cols-5 gap-3 mr-3 px-5 pb-2 text-slate-500">
            <p class="text-xs font-semibold uppercase">Company</p>
            <p class="text-xs font-semibold uppercase text-center">Progress</p>
            <p class="text-xs font-semibold uppercase col-span-2 ">Notes</p>
            <p class="text-xs font-semibold uppercase text-right">Actions</p>
        </div>
        <div class="h-100 overflow-y-auto border-y" :class="themeBackgroundClasses">
            <div v-if="loading" class="h-64 flex justify-center items-center" >
                <LoadingSpinner />
            </div>
            <div v-else
                 v-for="prospect in myProspects" :key="prospect.id"
                 class="grid grid-cols-5 gap-3 py-3 px-5 items-center border-b relative group h-16 transition-all duration-100"
                 :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-50 hover:bg-dark-module' : 'bg-light-background border-light-border text-slate-900 hover:bg-primary-50']" >
                <p @click="editProspect(prospect)" class="group-hover:text-primary-500 font-medium truncate cursor-pointer py-3">{{prospect.company_name}}</p>
                <p class="text-center">
                    <badge v-if="prospect.company_name && prospect.decision_maker_first_name && prospect.decision_maker_last_name && prospect.company_website && prospect.decision_maker_email && prospect.address_state_abbr && prospect.address_city_key && prospect.address_street && (prospect.industry_ids.length > 0)" color="green" :dark-mode="darkMode">Ready</badge>
                    <badge v-else color="blue" :dark-mode="darkMode">Working</badge>
                </p>
                <p v-html="prospect.notes ? prospect.notes : ''" class="col-span-2 line-clamp-2 text-sm" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']"></p>
                <div class="absolute right-5">
                    <ActionsHandle
                        :dark-mode="darkMode"
                        width="w-52"
                        :custom-actions="myProspectActions"
                        :no-custom-action="false"
                        :no-delete-button="true"
                        :no-edit-button="true"
                        @edit-prospect="editProspect(prospect)"
                        @release-back-to-queue="releaseBackToQueue(prospect)"
                    ></ActionsHandle>
                </div>
                <div class="h-full absolute left-0 w-0.5 bg-transparent group-hover:bg-primary-500 transition-all duration-100"></div>
            </div>
        </div>
        <div class="p-3"></div>
    </div>
</template>

<style scoped>

</style>
