<script>
import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin.js";
export default {
    name: "Dial<PERSON>rospect",
    props: {
        darkMode: {
            default: false,
            type: Boolean
        },
        prospect: {}
    },
    mixins: [
        DispatchesGlobalEventsMixin
    ],
    methods: {
        call() {
            this.dispatchGlobalEvent('call', {
                phone: this.prospect.decision_maker_phone,
                name: this.prospect.decision_maker_first_name + ' ' + this.prospect.decision_maker_last_name,
            });
        }
    }
}
</script>

<template>
    <div @click="call" class="inline-flex justify-center items-center gap-1 absolute right-1 rounded-md w-20 border border-primary-500 px-2 py-1 group cursor-pointer" :class="[darkMode ? 'bg-dark-module hover:bg-dark-border' : 'bg-light-module hover:bg-primary-100']">
        <svg class="inline w-3 fill-current text-primary-500" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16.6877 13.3165L12.8086 9.78955C12.6252 9.62289 12.3842 9.534 12.1366 9.54166C11.8889 9.54931 11.6539 9.65291 11.4812 9.83058L9.19758 12.179C8.64792 12.0741 7.54287 11.7296 6.40537 10.595C5.26787 9.45651 4.92338 8.3486 4.82127 7.80276L7.16784 5.51824C7.34573 5.34564 7.44948 5.1106 7.45714 4.86286C7.4648 4.61511 7.37576 4.37411 7.20887 4.19085L3.68282 0.312706C3.51586 0.128873 3.28382 0.0173642 3.03597 0.00186253C2.78812 -0.0136391 2.54399 0.0680874 2.35542 0.229685L0.284644 2.00558C0.11966 2.17116 0.0211864 2.39153 0.00790328 2.6249C-0.00641086 2.86347 -0.279334 8.51464 4.1027 12.8986C7.92553 16.7204 12.7141 17 14.0329 17C14.2257 17 14.344 16.9943 14.3755 16.9924C14.6088 16.9793 14.8291 16.8804 14.9939 16.7147L16.7688 14.643C16.9311 14.455 17.0134 14.2111 16.9982 13.9632C16.9831 13.7154 16.8716 13.4833 16.6877 13.3165Z"/>
        </svg>
        <p class="text-sm text-primary-500 font-semibold">Call</p>
    </div>
</template>

<style scoped>

</style>
