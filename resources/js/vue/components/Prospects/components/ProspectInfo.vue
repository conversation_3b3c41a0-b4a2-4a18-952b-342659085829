<script setup>
    import CustomInput from "../../Shared/components/CustomInput.vue";
    import {computed, onMounted, ref, defineEmits, onBeforeUnmount} from "vue";
    import DropdownWithSearchFilter from "../../Shared/components/DropdownWithSearchFilter.vue";
    import CustomButton from "../../Shared/components/CustomButton.vue";
    import FilledCheckIcon from "./FilledCheckIcon.vue";
    import FilledCheckProgressBar from "./FilledCheckProgressBar.vue";
    import MultiSelect from "../../Shared/components/MultiSelect.vue";
    import SharedApiServiceFactory from "../../Shared/services/api.js";
    import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
    import DialProspect from "./DialProspect.vue";
    import MarkdownEditor from "../../Shared/components/MarkdownEditor.vue";

    const props = defineProps({
        prospect: {},
        apiService: null,
        darkMode: false,
    })

    const themeClasses = computed(() => {
        if(props.darkMode === true) {
            return 'bg-dark-module border-dark-border text-slate-50'
        }
        else {
            return 'bg-light-module border-light-border text-slate-900'
        }
    })

    const stateOptions = ref([])
    onMounted(() => {
        props.apiService.getStateOptions().then(res => {
            stateOptions.value = res.data
            refreshCityOptions(false)
        })
    })

    const cityOptions = ref([])
    const refreshCityOptions = (clearCurrentCity) => {
        if (clearCurrentCity)
            props.prospect.address_city_key = null

        props.apiService.getCityOptions(props.prospect.address_state_abbr).then(res => cityOptions.value = res.data)
    }

    const emit = defineEmits(['save', 'flash-alert']);
    function save(nextProspect, message) {
        emit('save', nextProspect, message);
    }
    function flashAlert(type, message) {
        emit('flash-alert', type, message);
    }
    const sharedApi = SharedApiServiceFactory.make()
    const industries = ref([])
    onMounted(() => {
        const loadingIndustries = ref(true)
        sharedApi.getOdinIndustries().then(res => {
            if(res.data.data.status === true) {
                industries.value = res.data.data.industries.map((i) => {
                    return {
                        id: i.id,
                        name: i.name
                    };
                });
            }
        })
        .catch(() => {
            flashAlert('error', 'Could not load industries.');
        })
        .finally(() => {
            loadingIndustries.value = false
        });
    })

    const prospectFields = [
        'company_name',
        'company_website',
        'company_phone',
        'industry_ids',
        'decision_maker_first_name',
        'decision_maker_last_name',
        'decision_maker_email',
        'address_state_abbr',
        'address_city_key',
        'address_street',
    ];
    const getTotalInputs = computed(() => prospectFields.length);
    const getAmountOfFilledInputs = computed(() => {
        return prospectFields.reduce((count, field) => {
            const fieldValue = props.prospect[field]
            if(field === 'company_website')
            {
                return count + (websiteValidation(fieldValue) ? 1 : 0)
            }
            else if(field === 'decision_maker_email')
            {
                return count + (emailValidation(fieldValue) ? 1 : 0)
            }
            else {
                const isFilled = Array.isArray(fieldValue) ? fieldValue.length > 0 : Boolean(fieldValue);
                return count + (isFilled ? 1 : 0);
            }
        }, 0);
    });

    const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
    function websiteValidation(value) {
        return urlRegex.test(value);
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    function emailValidation(value) {
        return emailRegex.test(value);
    }

    const prospectReady = computed(() => {
        return getTotalInputs.value === getAmountOfFilledInputs.value
    })

    const phoneRegex = /^(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/;
    function phoneValidation(value) {
        return phoneRegex.test(value);
    }

    const convertToCompany = () => props.apiService.convertToCompany(props.prospect).then(res => save(false))
    const archiveProspect = () => props.apiService.archiveProspect(props.prospect).then(res => save(false))
    const releaseBackToQueue = () => props.apiService.releaseBackToQueue(props.prospect).then(res => save(false, ' has been released back to Queue.'))

    const customActions = ref([
        {event: 'archive', name: 'Archive'},
        {event: 'release', name: 'Release Back to Queue'},
    ]);

    const closer = ref(null)

    // TODO: Change Calendly Meeting URL
    const openCalendly = async () => {

        props.apiService.getCloserForDemo().then(res => {
            closer.value = res.data
            try {
                Calendly.initPopupWidget({
                    url: closer.value.meeting_url,
                    prefill: {
                        name: (props.prospect.decision_maker_first_name ?? '') + ' ' + (props.prospect.decision_maker_last_name ?? ''),
                        email: props.prospect.decision_maker_email,
                    },
                });
            } catch (error) {
                flashAlert('error', 'Error opening Calendly')
            }
        })


    };

    const isCalendlyScheduledEvent = (e) => {
        return e.data.event && e.data.event.indexOf('calendly') === 0 && e.data.event === 'calendly.event_scheduled'
    };
    const calendlyListener = (e) => {
        if (isCalendlyScheduledEvent(e)) {demoBooked(e)}
    }

    onMounted(() => {
        if(props.prospect.notes === null) {
            props.prospect.notes = ''
        }
        window.addEventListener('message', calendlyListener)
    })

    onBeforeUnmount(() => {
        window.removeEventListener('message', calendlyListener)
    })

    const demoBooked = (e) => {
        props.prospect.demo = {
            booked: true,
            user_id: closer.value.user_id,
            calendly_event_url: e.data.payload.event.uri
        }
        convertToCompany()
    }

    const customDuplicateActions = ref([
        {event: 'visit-page', name: 'Visit Page'},
        {event: 'flag-as-duplicate', name: 'Flag as Duplicate'},
    ]);

    function visitPage(url) {
        window.open(url, '_blank');
    }
    function flagAsDuplicate(companyId) {
        props.apiService.flagAsDuplicate(props.prospect.reference, companyId)
    }


</script>

<template>

    <div class="">
        <div class="border rounded-lg flex flex-wrap justify-between mb-4" :class="[themeClasses]">
            <div class="inline-flex items-center p-5">
                <h4 class="font-semibold text-lg">{{prospect.company_name}}</h4>
                <p v-if="prospect.source === 'registration'" class="ml-2 font-bold text-xl">
                    (<span class="text-primary-500">New Registration</span>)
                </p>
            </div>
            <div class="inline-flex items-center flex-wrap">
                <div class="inline-flex flex-wrap items-center gap-5 border-l p-5" :class="themeClasses">
                    <ActionsHandle v-if="prospect.company_id < 1" :dark-mode="darkMode"
                                   width="w-52"
                                   :custom-actions="customActions"
                                   :no-custom-action="false"
                                   :no-delete-button="true"
                                   :no-edit-button="true"
                                   @archive="archiveProspect"
                                   @release="releaseBackToQueue"
                    ></ActionsHandle>
                    <CustomButton :disabled="!prospectReady || prospect.company_id > 0" :dark-mode="darkMode" color="primary-outline" @click="convertToCompany">
                        Convert to Company
                    </CustomButton>
                    <CustomButton @click="openCalendly()" :disabled="!prospectReady || prospect.resolution === 'demo_booked'" :dark-mode="darkMode" color="primary-outline">
                        Schedule a Demo
                    </CustomButton>
                </div>
                <div class="inline-flex items-center gap-5 border-l p-5" :class="themeClasses">
                    <CustomButton @click="save(false)" :dark-mode="darkMode" color="primary-outline">
                        Save
                    </CustomButton>
                    <CustomButton @click="save(true)" :dark-mode="darkMode">
                        Next Company
                    </CustomButton>
                </div>
            </div>
            <FilledCheckProgressBar :total="getTotalInputs" :completed="getAmountOfFilledInputs" :dark-mode="darkMode"/>
        </div>
        <div class="grid lg:grid-cols-2 gap-4">
            <div>
                <div class="rounded-lg border mb-4 p-5" :class="themeClasses">
                    <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                        Company
                    </h3>
                    <div class="grid gap-5">
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Name
                                <FilledCheckIcon :filled="prospect.company_name"></FilledCheckIcon>
                            </label>
                            <custom-input placeholder="Name" :dark-mode="darkMode" v-model="prospect.company_name"></custom-input>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Website
                                <FilledCheckIcon :filled="websiteValidation(prospect.company_website)"></FilledCheckIcon>
                            </label>
                            <custom-input placeholder="Website" :dark-mode="darkMode" v-model="prospect.company_website"></custom-input>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Phone
                                <FilledCheckIcon :filled="phoneValidation(prospect.company_phone)"></FilledCheckIcon>
                            </label>
                            <custom-input placeholder="Company Phone" :dark-mode="darkMode" v-model="prospect.company_phone"></custom-input>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Industry
                                <FilledCheckIcon :filled="prospect.industry_ids.length > 0"></FilledCheckIcon>
                            </label>
                            <multi-select :options="industries"
                                          :dark-mode="darkMode"
                                          :show-search-box="true"
                                          :text-place-holder="'Select Industries'"
                                          :selected-ids="prospect.industry_ids"
                                          :classes="'w-full'"/>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg border mb-4 p-5" :class="themeClasses">
                    <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                        Decision Maker
                    </h3>
                    <div class="grid gap-5">
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                First Name
                                <FilledCheckIcon :filled="prospect.decision_maker_first_name"></FilledCheckIcon>
                            </label>
                            <custom-input :dark-mode="darkMode" placeholder="First name" v-model="prospect.decision_maker_first_name"></custom-input>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Last Name
                                <FilledCheckIcon :filled="prospect.decision_maker_last_name"></FilledCheckIcon>
                            </label>
                            <custom-input :dark-mode="darkMode" placeholder="Last name" v-model="prospect.decision_maker_last_name"></custom-input>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Email Address
                                <FilledCheckIcon :filled="emailValidation(prospect.decision_maker_email)"></FilledCheckIcon>
                            </label>
                            <custom-input :dark-mode="darkMode" placeholder="Email address" v-model="prospect.decision_maker_email"></custom-input>
                        </div>
                        <div>
                            <div class="flex justify-between items-center">
                                <label class="inline-flex mb-1 text-sm font-semibold">
                                    Phone
                                    <FilledCheckIcon :filled="phoneValidation(prospect.decision_maker_phone)"></FilledCheckIcon>
                                </label>
                            </div>
                            <div class="relative flex items-center">
                                <custom-input class="w-full" :dark-mode="darkMode" placeholder="Decision Maker Phone" v-model="prospect.decision_maker_phone"></custom-input>
                                <DialProspect
                                    :dark-mode="darkMode"
                                    :prospect="prospect"
                                    v-if="phoneValidation(prospect.decision_maker_phone)"></DialProspect>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="rounded-lg border mb-4 p-5" :class="themeClasses">
                    <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                        Address
                    </h3>
                    <div class="grid gap-5">
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                State
                                <FilledCheckIcon :filled="prospect.address_state_abbr"></FilledCheckIcon>
                            </label>
                            <DropdownWithSearchFilter placeholder="Select" :dark-mode="darkMode" @input="refreshCityOptions(true)" :options="stateOptions" v-model="prospect.address_state_abbr"></DropdownWithSearchFilter>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                City
                                <FilledCheckIcon :filled="prospect.address_city_key"></FilledCheckIcon>
                            </label>
                            <DropdownWithSearchFilter placeholder="Select" :dark-mode="darkMode" :options="cityOptions" v-model="prospect.address_city_key"></DropdownWithSearchFilter>
                        </div>
                        <div>
                            <label class="inline-flex mb-1 text-sm font-semibold">
                                Street
                                <FilledCheckIcon :filled="prospect.address_street"></FilledCheckIcon>
                            </label>
                            <custom-input :dark-mode="darkMode" placeholder="Street" v-model="prospect.address_street" ></custom-input>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <div class="rounded-lg border mb-4" :class="themeClasses">
                    <h3 class="font-bold text-sm uppercase text-primary-500 p-5">
                        Possible Duplicates
                    </h3>
                    <div class="h-48 overflow-y-auto border-y" :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border']">
                        <div v-for="match in prospect.duplicate_companies" :key="match.company_name"
                             class="grid grid-cols-1 py-4 h-12 px-5 items-center border-b relative group transition-all duration-100 cursor-pointer"
                             :class="[prospect.reference === match.company_id ? (darkMode ? 'bg-rose-900 bg-opacity-25 border-dark-border text-slate-50' : 'bg-rose-100 border-light-border text-slate-900') : (darkMode ? 'bg-dark-background border-dark-border text-slate-50 hover:bg-dark-module' : 'bg-light-background border-light-border text-slate-900 hover:bg-primary-50')]" >
                            <div class="absolute right-3">
                                <ActionsHandle
                                    width="w-52"
                                    :custom-actions="customDuplicateActions"
                                    :no-custom-action="false"
                                    :no-delete-button="true"
                                    :no-edit-button="true"
                                    @visit-page="visitPage(match.profile_link)"
                                    @flag-as-duplicate="flagAsDuplicate(match.company_id)"
                                    :dark-mode="darkMode"></ActionsHandle>
                            </div>
                            <div class="flex flex-col">
                                <p class="leading-none capitalize text-sm font-medium">
                                    {{match.company_name}}
                                    <svg v-if="prospect.company_id === match.company_id" class="inline ml-2 w-3.5" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12.6957 2.26952H2.64031V0.722534H1.09332V14.6454H0.319824V16.1924H3.4138V14.6454H2.64031V10.778H12.6957C12.9009 10.778 13.0976 10.6965 13.2427 10.5514C13.3877 10.4064 13.4692 10.2096 13.4692 10.0045V3.04302C13.4692 2.83788 13.3877 2.64113 13.2427 2.49608C13.0976 2.35102 12.9009 2.26952 12.6957 2.26952Z" fill="#F43F5E"/>
                                    </svg>
                                </p>
                                <small>{{match.website}}</small>
                            </div>
                        </div>
                    </div>
                    <div class="p-3">

                    </div>
                </div>
                <div class="rounded-lg border mb-4 p-5" :class="themeClasses">
                    <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                        Description
                    </h3>
                    <div class="grid gap-5">
                        <div v-if="prospect.description" v-html="prospect.company_description"></div>
                    </div>
                </div>
                <div class="rounded-lg border mb-4 p-5" :class="themeClasses">
                    <h3 class="font-bold text-sm uppercase text-primary-500 pb-5">
                        Notes
                    </h3>
                    <div>
                        <markdown-editor v-model:content="prospect.notes" :dark-mode="darkMode" rows="8"></markdown-editor>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style>
.toastui-editor-dark.toastui-editor-defaultUI {
    @apply border-dark-border text-white;
}
.toastui-editor-main .toastui-editor-md-container,
.toastui-editor-main .toastui-editor-ww-container {
    @apply bg-light-background;
}
.toastui-editor-defaultUI-toolbar {
    @apply bg-light-module border-transparent;
}
.toastui-editor-mode-switch {
    @apply bg-light-module border-transparent;
}
.toastui-editor-dark .toastui-editor-md-container,
.toastui-editor-dark .toastui-editor-ww-container {
    @apply bg-dark-background;
}

.toastui-editor-dark .toastui-editor-defaultUI-toolbar {
    @apply bg-dark-module border-transparent;
}

.toastui-editor-dark .toastui-editor-mode-switch {
    @apply bg-dark-module border-transparent;
}

.toastui-editor-dark .toastui-editor-mode-switch .tab-item {
    @apply border-transparent bg-dark-module text-slate-500;
}

.toastui-editor-dark .toastui-editor-mode-switch .tab-item.active {
    @apply border-dark-border bg-dark-background text-slate-300;
}

.toastui-editor-dark .toastui-editor-toolbar-icons {
    @apply bg-dark-module border-transparent;
}

.toastui-editor-dark .toastui-editor-toolbar-icons:not(:disabled):hover {
    @apply bg-dark-border border-transparent;
}

.toastui-editor-toolbar-icons {
    @apply bg-light-module border-transparent;
}

.toastui-editor-toolbar-icons:not(:disabled):hover {
    @apply bg-light-border border-transparent;
}
</style>
