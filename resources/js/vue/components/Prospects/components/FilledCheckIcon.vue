<script setup>
const props = defineProps({
    filled: {}
})
</script>

<template>
    <div class="inline">
        <svg class="ml-1 text-emerald-400" v-if="filled" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.97119 16.8385C11.0929 16.8385 13.1278 15.9956 14.628 14.4953C16.1283 12.995 16.9712 10.9602 16.9712 8.83847C16.9712 6.71674 16.1283 4.68191 14.628 3.18162C13.1278 1.68133 11.0929 0.83847 8.97119 0.83847C6.84946 0.83847 4.81463 1.68133 3.31434 3.18162C1.81405 4.68191 0.971191 6.71674 0.971191 8.83847C0.971191 10.9602 1.81405 12.995 3.31434 14.4953C4.81463 15.9956 6.84946 16.8385 8.97119 16.8385ZM12.6782 7.54547C12.8603 7.35687 12.9611 7.10427 12.9589 6.84207C12.9566 6.57987 12.8514 6.32906 12.666 6.14365C12.4806 5.95824 12.2298 5.85307 11.9676 5.8508C11.7054 5.84852 11.4528 5.94931 11.2642 6.13147L7.97119 9.42447L6.67819 8.13147C6.48959 7.94931 6.23699 7.84852 5.97479 7.8508C5.71259 7.85308 5.46178 7.95824 5.27637 8.14365C5.09096 8.32906 4.9858 8.57987 4.98352 8.84207C4.98124 9.10427 5.08203 9.35687 5.26419 9.54547L7.26419 11.5455C7.45172 11.7329 7.70603 11.8383 7.97119 11.8383C8.23636 11.8383 8.49066 11.7329 8.67819 11.5455L12.6782 7.54547Z" fill="currentColor"/>
        </svg>
        <svg class="ml-1 text-rose-400" v-else width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M16.0044 8.61331C16.0044 10.735 15.1615 12.7699 13.6612 14.2702C12.161 15.7705 10.1261 16.6133 8.00439 16.6133C5.88266 16.6133 3.84783 15.7705 2.34754 14.2702C0.847249 12.7699 0.00439453 10.735 0.00439453 8.61331C0.00439453 6.49158 0.847249 4.45675 2.34754 2.95646C3.84783 1.45617 5.88266 0.613312 8.00439 0.613312C10.1261 0.613312 12.161 1.45617 13.6612 2.95646C15.1615 4.45675 16.0044 6.49158 16.0044 8.61331ZM8.00439 5.61331C7.82869 5.61314 7.65605 5.65927 7.50384 5.74704C7.35164 5.83482 7.22525 5.96115 7.13739 6.11331C7.07396 6.23119 6.98751 6.33513 6.88316 6.41897C6.77881 6.50282 6.65868 6.56485 6.52991 6.60141C6.40114 6.63797 6.26634 6.6483 6.1335 6.6318C6.00066 6.61529 5.87249 6.57228 5.75658 6.50532C5.64067 6.43836 5.53938 6.34881 5.45873 6.24198C5.37807 6.13515 5.31968 6.01321 5.28702 5.88339C5.25437 5.75358 5.2481 5.61853 5.26861 5.48625C5.28911 5.35397 5.33597 5.22715 5.40639 5.11331C5.73661 4.54142 6.2463 4.09447 6.85642 3.84177C7.46654 3.58907 8.14299 3.54475 8.78086 3.71567C9.41874 3.88659 9.98239 4.26321 10.3844 4.78711C10.7864 5.31102 11.0044 5.95293 11.0044 6.61331C11.0046 7.23393 10.8123 7.83934 10.4542 8.34616C10.096 8.85299 9.58947 9.2363 9.00439 9.44331V9.61331C9.00439 9.87853 8.89904 10.1329 8.7115 10.3204C8.52396 10.508 8.26961 10.6133 8.00439 10.6133C7.73918 10.6133 7.48482 10.508 7.29729 10.3204C7.10975 10.1329 7.00439 9.87853 7.00439 9.61331V8.61331C7.00439 8.3481 7.10975 8.09374 7.29729 7.90621C7.48482 7.71867 7.73918 7.61331 8.00439 7.61331C8.26961 7.61331 8.52396 7.50795 8.7115 7.32042C8.89904 7.13288 9.00439 6.87853 9.00439 6.61331C9.00439 6.3481 8.89904 6.09374 8.7115 5.90621C8.52396 5.71867 8.26961 5.61331 8.00439 5.61331ZM8.00439 13.6133C8.26961 13.6133 8.52396 13.508 8.7115 13.3204C8.89904 13.1329 9.00439 12.8785 9.00439 12.6133C9.00439 12.3481 8.89904 12.0937 8.7115 11.9062C8.52396 11.7187 8.26961 11.6133 8.00439 11.6133C7.73918 11.6133 7.48482 11.7187 7.29729 11.9062C7.10975 12.0937 7.00439 12.3481 7.00439 12.6133C7.00439 12.8785 7.10975 13.1329 7.29729 13.3204C7.48482 13.508 7.73918 13.6133 8.00439 13.6133Z" fill="currentColor"/>
        </svg>
    </div>
</template>

<style scoped>

</style>
