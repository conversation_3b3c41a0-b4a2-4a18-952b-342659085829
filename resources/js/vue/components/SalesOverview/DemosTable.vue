
<template>
    <div :class="[initialLoad ? 'pointer-events-none opacity-50 grayscale' : '']">
        <div class="w-full flex-auto relative">
            <SimpleTable
                :dark-mode="darkMode"
                :data="demos"
                :pagination-data="paginationData"
                :headers="headers"
                :loading="loading"
                :has-row-click="true"
                title="Search Demos"
                @page-change="handlePageChange"
                @per-page-change="handlePerPageChange"
                :current-per-page="paginationData?.per_page ?? 25"
                @click-row="openDetailsModal"
                row-classes="cursor-pointer gap-5 grid items-center py-2 rounded px-5"
                class="border-none"
                @column-clicked="handleCompanyClicked"
            >
                <template #disclaimer>
                    <div class="my-5">
                        <div class="flex items-center gap-x-3">
                            <div class="min-w-[18rem]">
                                <Autocomplete
                                    search-icon
                                    :dark-mode="darkMode"
                                    class="w-full max-w-sm"
                                    v-model="selectedUser"
                                    :options="users"
                                    :placeholder="'Search by User'"
                                    :loading="loadingAutocomplete"
                                    :create-user-input-option="false"
                                    :min-search-input-len="userSearchMinLength"
                                    @search="searchUsers"
                                    @input="handleAutocompleteInput($event, 'user')"
                                />
                            </div>
                            <div class="min-w-[18rem]">
                                <CompanySearchAutocomplete
                                    :dark-mode="darkMode"
                                    v-model="selectedCompany"
                                    :search-icon="true"
                                    @input="handleAutocompleteInput($event, 'company')"
                                    :enable-loading="true"
                                />
                            </div>
                            <Filterable
                                :dark-mode="darkMode"
                                :filters="filters"
                                :model-value="filterInputs"
                                @update:defaults="updateFilterDefaults"
                            />
                            <CustomButton type="submit" :dark-mode="darkMode" @click="submitSearch">
                                Search
                            </CustomButton>
                            <CustomButton :dark-mode="darkMode" color="slate-inverse" type="reset" @click="resetFilters">
                                Reset
                            </CustomButton>
                            <CustomButton color="primary-outline" :disabled="loading" @click="exportToCSV">Export CSV</CustomButton>
                        </div>
                        <div class="flex items-center gap-x-3 mt-3">
                            <FilterableActivePills
                                :active-filters="filterInputs"
                                :dark-mode="darkMode"
                                :filters="filters"
                                @reset-filter="clearFilter"
                            />
                        </div>
                    </div>
                </template>
                <template #row.col.event.total_conference_participants="{item}">
                    <div class="items-center flex gap-1">
                        <simple-icon
                            :icon="item.has_external_participants ? simpleIcon.icons.CHECK : simpleIcon.icons.X_MARK"
                            :color="item.has_external_participants ? simpleIcon.colors.GREEN : simpleIcon.colors.RED"
                            :tooltip="item.has_external_participants ? 'It Does Have External Participants' : 'It Does Not Have External Participants'"
                        />
                        <span>{{item.event.total_conference_participants}}</span>
                    </div>
                </template>
            </SimpleTable>
        </div>
        <AlertsContainer v-if="alertActive" :text="alertText" :type="alertType" />

        <Modal
            v-if="viewDemoDetails"
            :restrict-width="true"
            :hide-confirm="true"
            :dark-mode="darkMode"
            @close="closeDetailsModal()"
        >
            <template v-slot:header>
                Demo Details
            </template>
            <template v-slot:content>
                <div v-if="savingModal" class="mt-12">
                    <LoadingSpinner :dark-mode="darkMode" />
                </div>
                <div v-else class="flex flex-col items-center gap-y-6">
                    <div>
                        <div v-if="viewDemoDetails.company?.id" class="flex items-center gap-x-2 mb-3">
                            <p>Associated Company -</p>
                            <p class="font-semibold">{{ viewDemoDetails.company.name }}</p>
                            <div class="text-primary-500 cursor-pointer"
                                @click="toggleChangeCompany"
                            >
                                <svg class="w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M21.731 2.269a2.625 2.625 0 0 0-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 0 0 0-3.712ZM19.513 8.199l-3.712-3.712-12.15 12.15a5.25 5.25 0 0 0-1.32 2.214l-.8 2.685a.75.75 0 0 0 .933.933l2.685-.8a5.25 5.25 0 0 0 2.214-1.32L19.513 8.2Z" />
                                </svg>
                            </div>
                        </div>
                        <div v-if="!viewDemoDetails.company?.id || changeCompany" class="flex items-center gap-x-3">
                            <p>
                                {{ viewDemoDetails.company?.id ? 'Update' : 'Associate' }} Company:
                            </p>
                            <CompanySearchAutocomplete
                                :dark-mode="darkMode"
                                v-model="companyAutoComplete"
                            />
                            <CustomButton
                                :dark-mode="darkMode"
                                :disabled="!companyAutoComplete"
                                @click="updateAssociatedCompany"
                            >
                                Save
                            </CustomButton>
                        </div>
                    </div>
                    <div class="w-fit">
                        <DemoCard
                            :dark-mode="darkMode"
                            :event="viewDemoDetails.event"
                        />
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import Filterable from "../Shared/components/Filterables/Filterable.vue";
import FilterableActivePills from "../Shared/components/Filterables/FilterableActivePills.vue";
import { nextTick } from "vue";
import Autocomplete from "../Shared/components/Autocomplete.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import Demos from "./modals/Demos.vue";
import SalesOverviewApi from "./services/api.js";
import DemoCard from "../Demo/DemoCard.vue";
import Modal from "../Shared/components/Modal.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import CompanySearchAutocomplete from "../Shared/components/Company/CompanySearchAutocomplete.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";

export default {
    name: "DemosTable",
    components: {
        SimpleIcon,
        CompanySearchAutocomplete,
        LoadingSpinner,
        Modal,
        DemoCard, Demos, AlertsContainer, CustomButton, Autocomplete, FilterableActivePills, Filterable, SimpleTable },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        api: {
            type: SalesOverviewApi,
            required: true,
        },
    },
    mixins: [AlertsMixin],
    data() {
        return {
            demos: [],
            paginationData: {},
            filters: [],
            filterInputs: {},
            filterDefaults: {},
            selectedFilter: null,
            selectionOption: null,
            selectedUser: null,
            selectedCompany: null,
            userSearchMinLength: 3,
            users: [],
            headers: [
                { title: 'Host', field: 'user.name', sortable: false },
                { title: 'Participants', field: 'event.total_conference_participants', sortable: false },
                { title: 'Company', field: 'company.name', sortable: false, clickable: true },
                { title: 'Sales Development Representative', field: 'sdr.name', sortable: false },
                { title: 'Status', field: 'status', sortable: false },
                { title: 'Length', field: 'event.length', sortable: false },
                { title: 'Start Time', field: 'event.start_time', sortable: false, timestamp: true, timezoneField: 'event.timezone' }
            ],
            loading: false,
            initialLoad: true,
            loadingAutocomplete: false,
            viewDemoDetails: null,
            companyAutoComplete: null,
            changeCompany: false,
            savingModal: false,
        }
    },
    mounted() {
        this.loading = true;
        this.getFilterOptions().then(() => this.submitSearch(true));
    },
    computed: {
        simpleIcon() {
            return useSimpleIcon()
        }
    },
    methods: {
        useSimpleIcon,
        submitSearch(ignoreLoading) {
            if (!ignoreLoading && this.loading) return;
            this.loading = true;

            this.api.searchDemos(this.getSearchPayload()).then(resp => {
                if (resp.data?.data?.status) {
                    const paginationData = resp.data.data.demos;
                    this.demos = paginationData.data;
                    this.paginationData = paginationData;
                }
            }).catch(err => {
                console.error(err);
                this.showAlert('error', err.message);
            }).finally(() => {
                this.loading = false;
            });
        },
        exportToCSV() {
            this.loading = true;
            this.api.exportDemos(this.getSearchPayload())
                .then(resp => {
                    const url = window.URL.createObjectURL(new Blob([resp.data]));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', 'demos.csv');

                    document.body.appendChild(link);

                    link.click();
                    link.remove();
                })
                .catch(err => {
                    console.error(err);
                    this.showAlert('error', err.message);
                }).finally(() => this.loading = false);
        },
        getSearchPayload() {
            return {
                filters: this.filterInputs,
                user_id: this.selectedUser,
                per_page: this.paginationData?.per_page ?? 25,
                page: this.paginationData?.page ?? 1,
                company_id: this.selectedCompany,
            }
        },
        handlePerPageChange(perPage) {
            this.paginationData.per_page = perPage ?? 25;
            this.submitSearch();
        },
        handlePageChange(pageData) {
            this.paginationData.page = pageData.newPage ?? 1;
            this.submitSearch();
        },
        selectFilter(filter) {
            this.selectedFilter = filter;
            this.selectedOption = null;
        },
        selectOption(option) {
            this.selectedOption = option;
        },
        clearFilter(filterId) {
            delete this.filterInputs[filterId];
        },
        updateFilterDefaults(filterChange) {
            Object.assign(this.filterDefaults, { ...filterChange });
        },
        async getFilterOptions() {
            return new Promise(res => {
                this.api.getDemoFilterOptions().then(resp => {
                    if (resp.data?.data?.status) {
                        this.filters = resp.data.data.filter_options;
                    }
                }).catch(err => {
                    console.error(err);
                    this.showAlert('error', err.message);
                }).finally(() => {
                    this.initialLoad = false;
                    res(true);
                });
            });
        },
        async resetFilters() {
            await nextTick();
            this.filters.forEach(filter => {
                this.filterInputs[filter.id] = this.filterDefaults?.[filter.id] ?? null;
            });
            this.selectedUser = '';
            this.users = [];
        },
        searchUsers(searchText) {
            if (!searchText) this.selectedUser = null;
            if (searchText?.length < this.userSearchMinLength)
                return;

            this.loadingAutocomplete = true;
            this.api.searchUsers(searchText).then(resp => {
                if (resp.data?.data?.status)
                    this.users = resp.data.data.users;
                else
                    this.users = [];
            }).catch(e => {
                console.error(e);
            }).finally(() => this.loadingAutocomplete = false);
        },
        handleAutocompleteInput({ target }, autocomplete) {
            if (!target.value?.trim()) {
                switch (autocomplete) {
                    case 'user':
                        this.selectedUser = null;
                        break;
                    case 'company':
                        this.selectedCompany = null;
                        break;
                }
            }
        },
        openDetailsModal(demo) {
            this.viewDemoDetails = demo;
        },
        handleCompanyClicked(column, item) {
            if (column === 'company.name' && item.company?.id) {
                window.open(`/companies/${item.company.id}`, '_blank');
            }
        },
        closeDetailsModal() {
            this.viewDemoDetails = null;
            this.companyAutoComplete = null;
            this.changeCompany = false;
        },
        toggleChangeCompany() {
            this.changeCompany = !this.changeCompany;
        },
        updateAssociatedCompany() {
            if (this.savingModal || !this.companyAutoComplete || !this.viewDemoDetails)
                return;

            this.savingModal = true;

            this.api.associateCompanyWithDemo(this.viewDemoDetails.id, this.companyAutoComplete).then(resp => {
                if (resp.data?.data?.status) {
                    this.viewDemoDetails.company = resp.data.data.company;
                    this.closeDetailsModal();
                }
                else
                    this.showAlert('error', 'Error assigning company to demo.');
            }).catch(e => {
                console.error(e);
            }).finally(() => this.savingModal = false);
        },
    },
}
</script>
