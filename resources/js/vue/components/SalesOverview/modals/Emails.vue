<template>
    <Modal :restrict-width="false" :hide-confirm="true" :dark-mode="darkMode" :full-width="false" @close="$emit('close-modal')">
        <template v-slot:header>
            {{ capitalize(direction) }} Emails
        </template>
        <template v-slot:content>
            <LoadingSpinner v-if="loading" :dark-mode="darkMode"/>
            <div v-else>
                <div class="grid grid-cols-5 gap-5 uppercase text-sm font-bold border-b" :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                    <p class="px-3">Date</p>
                    <p class="px-3 col-span-4">Content</p>
                </div>
                <div class="grid grid-cols-5 gap-5 text-sm border-b first:border-none items-center"
                     :class="darkMode ? 'text-slate-100 bg-dark-background hover:bg-dark-module' : 'text-slate-900 bg-light-background hover:bg-light-module'"
                     v-for="email in emails"
                >
                    <p class="p-3">{{ new Date(email.sent_timestamp * 1000).toLocaleString() }}</p>
                    <div class="p-3 col-span-4">
                        <div><span class="font-semibold mr-2">Subject:</span> {{ email.subject }}</div>
                        <div
                            v-for="[type, recipients] in getCleanRecipients(email)"
                            class="flex items-center my-2"
                        >
                            <p class="font-semibold mr-2 capitalize">{{ type }}:</p>
                            <badge v-for="recipientAddress in recipients">{{ recipientAddress }}</badge>
                        </div>
                        <div class="flex items-center my-2">
                            <p class="font-semibold mr-2">Content:</p>
                            <SimpleIcon
                                :icon="viewingEmailId === email.email_id ? simpleIcon.icons.EYE_SLASH : simpleIcon.icons.EYE"
                                :color="simpleIcon.colors.BLUE"
                                @click="viewEmail(email.email_id)"
                            />
                        </div>
                        <iframe
                            :srcdoc="sanitizedContent(email.content)"
                            sandbox="allow-same-origin"
                            class="w-full min-h-96"
                            v-if="email.email_id === viewingEmailId"
                        ></iframe>
                    </div>
                </div>
                <div class="my-3">
                    <Pagination
                        :pagination-data="paginationData"
                        :show-results-per-page="true"
                        @change-page="handlePaginationChange"
                    />
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import {capitalize} from "../../../../composables/stringHelper.js";
import SalesOverviewApi from "./../services/api.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Pagination from "../../Shared/components/Pagination.vue";
import DOMPurify from "dompurify";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import Badge from "../../Shared/components/Badge.vue";

export default {
    name: "Emails",
    components: {Badge, SimpleIcon, Pagination, LoadingSpinner, Modal},
    emits: ['close-modal'],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        filters: {
            type: Object,
            required: true
        },
        direction: {
            type: String,
            required: true
        },
        userId: {
            type: Number,
            required: true
        },
        api: {
            type: SalesOverviewApi,
            required: true
        }
    },
    data() {
        return {
            loading: false,
            emails: [],
            paginationData: {},
            requestFilters: {},
            simpleIcon: useSimpleIcon(),
            viewingEmailId: null
        }
    },
    mounted() {
        this.requestFilters = {...this.filters};
        this.requestFilters['per_page'] = 25;

        this.getEmails();
    },
    methods: {
        getCleanRecipients(email) {
            return Object.entries({
                from: [email?.from?.identifier_value],
                to: email.to?.map(e => e?.identifier_value),
                bcc: email.bcc?.map(e => e?.identifier_value),
                cc: email.cc?.map(e => e?.identifier_value)
            }).filter(([_, value]) =>
                Array.isArray(value) ? value.length > 0 : Boolean(value)
            )
        },
        capitalize,
        async getEmails() {
            this.loading = true;
            await this.api.getEmailsForUser(this.userId, this.direction, this.requestFilters)
                .then(resp => {
                    this.emails = resp.data.data;
                    this.paginationData = resp.data.meta;
                })
                .catch(e => console.error(e));
            this.loading = false;
        },
        handlePaginationChange(pagination) {
            this.requestFilters['page'] = pagination.newPage ?? 1;
            this.getEmails();
        },
        sanitizedContent(content) {
            return DOMPurify.sanitize(content);
        },
        viewEmail(emailId) {
            if (emailId === this.viewingEmailId) {
                this.viewingEmailId = null;
                return;
            }

            this.viewingEmailId = emailId;
        }
    },
}
</script>
