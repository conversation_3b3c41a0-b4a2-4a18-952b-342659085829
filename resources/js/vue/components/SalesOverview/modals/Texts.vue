<template>
    <Modal :restrict-width="false" :hide-confirm="true" :dark-mode="darkMode" :full-width="false" @close="$emit('close-modal')">
        <template v-slot:header>
            {{ capitalize(direction) }} Texts
        </template>
        <template v-slot:content>
            <LoadingSpinner v-if="loading" :dark-mode="darkMode"/>
            <div v-else>
                <div class="grid grid-cols-5 gap-5 uppercase text-sm font-bold border-b" :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                    <p class="px-3">Date</p>
                    <p class="px-3">Phone</p>
                    <p class="px-3 col-span-3">Content</p>
                </div>
                <div class="grid grid-cols-5 gap-5 text-sm border-b first:border-none items-center"
                     :class="darkMode ? 'text-slate-100 bg-dark-background hover:bg-dark-module' : 'text-slate-900 bg-light-background hover:bg-light-module'"
                     v-for="text in texts"
                >
                    <p class="p-3">{{ new Date(text.timestamp * 1000).toLocaleString() }}</p>
                    <p class="p-3"> {{ direction.toLowerCase() === 'inbound' ?  text.from : text.to }} </p>
                    <p class="p-3 col-span-3"> {{ text.content }} </p>
                </div>
                <div class="my-3">
                    <Pagination
                        :pagination-data="paginationData"
                        :show-results-per-page="true"
                        @change-page="handlePaginationChange"
                    />
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import {capitalize} from "../../../../composables/stringHelper.js";
import SalesOverviewApi from "./../services/api.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Pagination from "../../Shared/components/Pagination.vue";

export default {
    name: "Texts",
    components: {Pagination, LoadingSpinner, Modal},
    emits: ['close-modal'],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        filters: {
            type: Object,
            required: true
        },
        direction: {
            type: String,
            required: true
        },
        userId: {
            type: Number,
            required: true
        },
        api: {
            type: SalesOverviewApi,
            required: true
        },
    },
    data() {
        return {
            loading: false,
            texts: [],
            paginationData: {},
            requestFilters: {}
        }
    },
    mounted() {
        this.requestFilters = {...this.filters};
        this.requestFilters['per_page'] = 25;

        this.getTexts();
    },
    methods: {
        capitalize,
        async getTexts () {
            this.loading = true;
            await this.api.getTextsForUser(this.userId, this.direction, this.requestFilters)
                .then(resp => {
                    this.texts = resp.data.data;
                    this.paginationData = resp.data.meta;
                })
                .catch(e => console.error(e))
            this.loading = false;
        },
        handlePaginationChange(pagination) {
            this.requestFilters['page'] = pagination.newPage ?? 1;
            this.getTexts();
        }
    },
}
</script>
