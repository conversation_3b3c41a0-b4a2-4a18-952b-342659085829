<template>
    <Modal :restrict-width="false" :hide-confirm="true" :dark-mode="darkMode" :full-width="false" @close="$emit('close-modal')">
        <template v-slot:header>
            {{ capitalize(direction) }} Calls
        </template>
        <template v-slot:content>
            <LoadingSpinner v-if="loading" :dark-mode="darkMode"/>
            <div v-else>
                <div class="grid grid-cols-5 gap-5 uppercase text-sm font-bold border-b" :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
                    <p class="px-3">Date</p>
                    <p class="px-3">Phone</p>
                    <p class="px-3 col-span-3">Recording</p>
                </div>
                <div class="grid grid-cols-5 gap-5 text-sm border-b first:border-none items-center"
                     :class="darkMode ? 'text-slate-100 bg-dark-background hover:bg-dark-module' : 'text-slate-900 bg-light-background hover:bg-light-module'"
                     v-for="call in calls"
                >
                    <p class="p-3">{{ new Date(call.timestamp * 1000).toLocaleString() }}</p>
                    <p class="p-3">{{ this.direction.toLowerCase() === 'inbound' ? call.from : call.to }}</p>
                    <div class="p-3 col-span-3" v-if="call.recording?.recording_link">
                        <AudioPlayer :audio-file="call.recording?.recording_link" :dark-mode="darkMode" :audio-link="true"/>
                    </div>
                    <p class="p-3 col-span-3" v-else>
                        Unavailable
                    </p>
                </div>
                <div class="my-3">
                    <Pagination
                        :pagination-data="paginationData"
                        :show-results-per-page="true"
                        @change-page="handlePaginationChange"
                    />
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import {capitalize} from "../../../../composables/stringHelper.js";
import SalesOverviewApi from "../services/api.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Pagination from "../../Shared/components/Pagination.vue";
import AudioPlayer from "../../Shared/components/AudioPlayer.vue";

export default {
    name: "Calls",
    components: {AudioPlayer, Pagination, LoadingSpinner, Modal},
    emits: ['close-modal'],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        filters: {
            type: Object,
            required: true
        },
        direction: {
            type: String,
            required: true
        },
        userId: {
            type: Number,
            required: true
        },
        api: {
            type: SalesOverviewApi,
            required: true
        }
    },
    data() {
        return {
            loading: false,
            calls: [],
            paginationData: {},
            requestFilters: {}
        }
    },
    mounted() {
        this.requestFilters = {...this.filters};
        this.requestFilters['per_page'] = 25;

        this.getCalls();
    },
    methods: {
        capitalize,
        async getCalls() {
            this.loading = true;
            await this.api.getCallsForUser(this.userId, this.direction, this.requestFilters)
                .then(resp => {
                    this.calls = resp.data.data;
                    this.paginationData = resp.data.meta;
                })
                .catch(e => console.error(e));
            this.loading = false;
        },
        handlePaginationChange(pagination) {
            this.requestFilters['page'] = pagination.newPage ?? 1;
            this.getCalls();
        }
    },
}
</script>
