<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}"
            >
                <div class="flex flex-col px-5 pt-5 border-b mb-5 gap-4"
                     :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                    <div class="flex items-center justify-between flex-wrap pl-4">
                        <h3 class="text-xl font-medium pb-0 leading-none mr-5"
                            :class="[darkMode ? 'text-primary-500' : '']">Sales Overview</h3>
                    </div>
                    <div class="flex justify-between">
                        <Tab
                            :dark-mode="darkMode"
                            :tabs="tabs"
                            @selected="selectTab"
                            tab-style="fit"
                            background-color="light"
                            :tab-type="'Normal'"
                        />
                    </div>
                </div>
                <div class="mx-10 border rounded-md flex flex-col flex-grow p-3"
                     :class="[darkMode ? 'bg-dark-module border-dark-border text-slate-200' : 'bg-light-module']">
                    <component
                        :is="currentTabComponent"
                        :api="api"
                        :dark-mode="darkMode"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { markRaw } from "vue";
import SalesSummary from "./SalesSummary.vue";
import DemosTable from "./DemosTable.vue";
import Tab from "../Shared/components/Tab.vue";
import SalesOverviewApi from "./services/api.js";

export default {
    name: "SalesOverview",
    components: { Tab, SalesSummary, DemosTable },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            api: SalesOverviewApi.make(),
            tabs: [
                { name: 'Summary', component: markRaw(SalesSummary), current: true },
                { name: 'Demos', component: markRaw(DemosTable), current: false },
            ],
        }
    },
    computed: {
        currentTabComponent() {
            return this.tabs.find(e => e.current)?.component
        },
    },
    methods: {
        changeToSelectedTab() {
            this.tabs.forEach(e => {
                e.current = e.name === this.selectedTab
            })
        },
        setSelectedTab(tab) {
            if (this.tabs.length === 0) return
            this.selectedTab = tab

            if (!this.tabs.find(e => e.name === this.selectedTab)) {
                this.selectedTab = this.tabs[0].name
            }

            this.changeToSelectedTab()
        },
        selectTab(tab) {
            this.setSelectedTab(tab)
        },
    }
}
</script>
