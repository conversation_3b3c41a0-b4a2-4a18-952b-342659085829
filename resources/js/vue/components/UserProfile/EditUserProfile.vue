<template>
    <div>
        <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>
        <div class="border rounded-lg p-4"
             :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
            <div class="">
                <form @submit.prevent="saveProfile">
                    <!-- Name Change -->
                    <div class="mt-4 flex items-center">
                        <label for="name" class="w-1/3 block mb-1 font-semibold text-md" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']">Name</label>
                        <input v-model="user.name" type="text" id="name" name="name" :class="[darkMode ? 'border-dark-border bg-dark-background text-slate-100' : 'border-light-border bg-light-background text-slate-800']" class="w-2/3 rounded-md h-9 py-2 px-3">
                    </div>

                    <!-- Email Address (Read-only) -->
                    <div class="mt-4 flex items-center">
                        <label for="email" class="w-1/3 block mb-1 font-semibold text-md" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']">Email Address</label>
                        <div class="w-2/3">{{ user.email }}</div>
                    </div>

                    <!-- Assigned Phone Numbers (Read-only, can be multiple) -->
                    <div class="mt-4 flex items-center">
                        <label class="w-1/3 block mb-1 font-semibold text-md" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']">Assigned Phone Numbers</label>
                        <div class="w-2/3">
                            <div v-for="(phone, index) in user.phoneNumbers" :key="index" class="mt-1">
                                {{ phone }}
                            </div>
                        </div>
                    </div>

                    <!-- Permissions (Read-only) -->
                    <div class="mt-4 flex">
                        <label class="w-1/3 block mb-1 font-semibold text-md" :class="[darkMode ? 'text-slate-500' : 'text-slate-600']">Permissions</label>
                        <div class="w-2/3">
                            <div v-for="(permissions, role) in userRolesAndPermissions" :key="role" class="mb-4">
                                <h2 class="text-lg font-semibold">{{ role }}</h2>

                                <div class="grid grid-cols-4 gap-4 mt-2">
                                    <div v-for="permission in permissions" :key="permission" class="flex items-center gap-2">
                                        <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
                                        <p class="text-sm">{{ permission }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="flex items-center justify-end mt-4">
                        <button type="submit" class="text-sm bg-primary-500 h-9 px-4 py-2 rounded-md hover:bg-primary-400 transition duration-200 text-white font-medium">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
import {UserSettingsApiFactory} from "../../../services/api/user_settings/factory.js";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import alertsMixin from "../../mixins/alerts-mixin.js";

export default {
    components: {AlertsContainer},
    data() {
        return {
            api: null,
        }
    },
    mixins: [ alertsMixin ],
    props: {
        user: {
            type: Object,
            default: {},
        },
        userRolesAndPermissions: {
            type: Object,
            default: {},
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: 'api'
        },
    },
    created() {
        this.api = UserSettingsApiFactory.makeApiService(this.apiDriver);
    },
    methods: {
        saveProfile() {
            this.api.updateUserProfile(this.user.id, {
                name: this.user.name,
            }).then(resp => {
                    if (!resp.data.data.status) {
                        this.showAlert('error', `An unknown error occured saving user profile.`);
                    }else{
                        this.showAlert('success', `User Profile updated successfully`);
                    }
                })
                .catch(err => {
                    this.showAlert('error', err.response.data?.message || err.message || `An unknown error occurred fetching user data.`);
                })
        },
    },
};
</script>

<style scoped>
/* Add your Tailwind CSS styles here */
</style>
