<template>
    <div class="font-medium flex items-center gap-2" :class="wrapClasses">
        <svg v-if="$slots.default" class="w-5 fill-current" :class="iconClasses" :width="iconSize" :height="iconSize" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <slot></slot>
        </svg>
        <span class="capitalize">
            {{ value }}
        </span>
    </div>
</template>

<script>

const COLORS_SCALE = {
    'blue': {
        border: 550,
        text: 550,
    },
    'orange': {
        border: 700,
        text: 700,
    },
    'yellow': {
        border: 700,
        text: 700,
    },
    'green': {
        border: 500,
        text: 500,
    }
}

export default {
    name: "TableBadge",
    props: {
        color: {
            type: String,
            required: true
        },

        value: {
            type: String,
            required: true
        },

        iconSize: {
            type: [String, Number],
            default: 20
        },

        textSize: {
            type: String,
            default: 'md'
        },

        darkMode: {
            type: <PERSON><PERSON><PERSON>,
            required: true,
        }
    },

    computed: {
        wrapClasses(){
            return this.darkMode
                ? `text-${this.color}-${COLORS_SCALE[this.color]?.text ?? '500'} text-${this.textSize}`
                : `text-${this.color}-500 text-${this.textSize}`
        },

        iconClasses(){
            return `text-${this.color}-500`
        }
    }
}
</script>
