<template>
    <div class="flex col items-center justify-center">
        <audio-player v-if="data.call_recording" :audio-file="data.call_recording.recording_link" :dark-mode="darkMode" />
        <div v-else>
            <p class="mt-6">
                Call recording not found
            </p>
        </div>
    </div>
</template>

<script>
import AudioPlayer from "../../../../Shared/components/AudioPlayer.vue";

export default {
    name: "CallRecording",
    components: { AudioPlayer },
    props: {
        data: {
            type: Object,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: true
        }
    }
}
</script>
