<template>
    <div v-if="data.messages">
        <div class="mt-2 border h-72 overflow-y-auto grid grid-cols-1 gap-3 items-end rounded-t p-4"
             :class="{'bg-light-module border-light-border' : !darkMode, 'bg-dark-background border-dark-border' : darkMode}"
             ref="scrollContainer"
        >
            <div v-for="message in data.messages">
                <outgoing-message v-if="message.direction === 'outbound'" :message="message" :dark-mode="darkMode"/>
                <incoming-message v-else :message="message" :dark-mode="darkMode"/>
            </div>
        </div>
        <small>Total messages: <span class="font-semibold">{{data.messages.length}}</span></small>
    </div>
    <span v-else>
        Texts not found
    </span>
</template>

<script>
import IncomingMessage from "../../../../LeadProcessingNotifications/components/IncomingMessage.vue";
import OutgoingMessage from "../../../../LeadProcessingNotifications/components/OutgoingMessage.vue";
import LoadingSpinner from "../../../../Shared/components/LoadingSpinner.vue";


export default {
    name: "TextMessages",
    components: {LoadingSpinner, OutgoingMessage, IncomingMessage},
    props: {
        data: {
            type: Object,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: true
        }
    },
}
</script>
