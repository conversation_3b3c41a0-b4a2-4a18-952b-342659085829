<template>
    <modal :dark-mode="darkMode"
           full-width
           no-buttons
           @close="handleClose"
    >
        <template v-slot:header>
            <div class="font-medium capitalize">
                Communication Message - {{selectedLog.direction}} {{ selectedLog.communication_type }}
            </div>
        </template>
        <template v-slot:content class="flex items-center justify-center">
            <div v-if="loading" class="h-80 flex items-center justify-center">
                <loading-spinner />
            </div>
            <div v-else>
                <div class="grid grid-cols-5 text-sm font-semibold text-center">
                    <div>Your phone number</div>
                    <div>External phone number</div>
                    <div>Date time</div>
                    <div>Call duration</div>
                    <div>Identity</div>
                </div>
                <div class="grid grid-cols-5 text-center text-sm">
                    <div>{{ selectedLog.phone }}</div>
                    <div>{{ selectedLog.other_number }}</div>
                    <div>{{ $filters.dateFromTimestamp(selectedLog.created_at, 'usWithFullTime') }}</div>
                    <div>{{formatTimeFromSecondsToHumanReadable(selectedLog.call_duration_in_seconds)}}</div>
                    <div v-if="selectedLog.contact" class="flex flex-col">
                        <span>
                            {{ selectedLog.contact?.relationData?.name}}
                        </span>
                        <span>
                            {{selectedLog.contact.contactName}}
                        </span>
                    </div>
                    <div v-else>
                        Unknown
                    </div>
                </div>

                <component
                    :is="component"
                    :darkMode="darkMode"
                    :data="data"
                />
            </div>
        </template>
    </modal>
</template>

<script>
import Modal from "../../../../Shared/components/Modal.vue";
import {CommunicationApiFactory} from "../../../../../../services/api/communication/factory";
import LoadingSpinner from "../../../../Shared/components/LoadingSpinner.vue";

import TextMessages from "./TextMessages.vue";
import CallRecording from "./CallRecording.vue";
import {DateTime} from "luxon";

export default {
    name: "CommunicationLogMessageModal",
    components: { LoadingSpinner, Modal, TextMessages, CallRecording },
    props: {
        darkMode: {
            type: Boolean,
            required: true
        },
        selectedLog: {
            type: Object,
            required: false
        },
        apiDriver: {
            type: String,
            required: true
        }
    },
    emits: ['close'],

    data(){
        return {
            communicationApi: null,
            loading: false,
            data: {}
        }
    },

    mounted() {
        this.registerServices()
        this.getCommunicationLogData()
    },

    computed: {
        component(){
            return this.selectedLog.communication_type === 'Call' ? 'CallRecording' : 'TextMessages'
        }
    },

    methods: {
        handleClose(){
            this.data = {}
            this.loading = false

            this.$emit('close')
        },

        registerServices(){
            this.communicationApi = CommunicationApiFactory.makeApiService(this.apiDriver);
        },

        async getCommunicationLogData(){
            this.loading = true
            const res = await this.communicationApi.getLogFurtherInformation(this.selectedLog.id, this.selectedLog.communication_type)
            this.data = res.data
            this.loading = false
        },

        formatTimeFromSecondsToHumanReadable(seconds){
            return new Date(seconds * 1000).toISOString().slice(11, 19);
        },
    }
}
</script>
