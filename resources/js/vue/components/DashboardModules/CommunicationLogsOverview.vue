<template>
    <div>
        <simple-table
            v-model="tableFilter"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            :pagination-data="paginationData"
            :table-filters="tableFilters"
            title="Communication Logs Overview"
            has-row-click
            @reset="handleReset"
            @search="handleSearch"
            @per-page-change="handlePerPageChange"
            @page-change="handlePageChange"
            @click-row="handleClickRow"
            @export-to-csv="exportToCSV"
        >
            <!-- Cols slots -->
            <template v-slot:row.col.communication_type="{ value, item }">
                <div class="flex flex-col justify-center items-center gap-2">
                    <div>
                        <svg v-if="value === 'Call' && item.direction === 'inbound'" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12.7932 4.5L10.0002 1.707V8.707H17.0002L14.2072 5.914L18.7072 1.414L17.2932 0L12.7932 4.5Z" fill="#0081FF"/>
                            <path d="M13.4223 11.1503C13.2301 10.9756 12.9776 10.8825 12.7181 10.8905C12.4585 10.8985 12.2123 11.0071 12.0313 11.1933L9.63928 13.6543C9.06328 13.5443 7.90528 13.1833 6.71328 11.9943C5.52128 10.8013 5.16028 9.64027 5.05328 9.06827L7.51228 6.67427C7.69869 6.4934 7.80742 6.24709 7.81544 5.98748C7.82347 5.72786 7.73017 5.47531 7.55528 5.28327L3.86028 1.22027C3.68532 1.02763 3.44216 0.910774 3.18243 0.89453C2.92271 0.878285 2.66688 0.963929 2.46928 1.13327L0.299282 2.99427C0.126104 3.16769 0.0225627 3.39861 0.00828196 3.64327C-0.00671804 3.89327 -0.292718 9.81527 4.29928 14.4093C8.30528 18.4143 13.3243 18.7073 14.7053 18.7073C14.9083 18.7073 15.0313 18.7013 15.0643 18.6993C15.3088 18.6858 15.5397 18.5821 15.7123 18.4083L17.5733 16.2373C17.7425 16.0397 17.8282 15.784 17.8121 15.5243C17.7961 15.2647 17.6796 15.0215 17.4873 14.8463L13.4223 11.1503Z" fill="#0081FF"/>
                        </svg>
                        <svg v-if="value === 'Call' && item.direction === 'outbound'" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13.7932 2.793L9.29321 7.293L10.7072 8.707L15.2072 4.207L18.0002 7V0H11.0002L13.7932 2.793Z" fill="#F59E0B"/>
                            <path d="M13.4223 10.4452C13.2301 10.2705 12.9776 10.1774 12.7181 10.1854C12.4585 10.1934 12.2123 10.302 12.0313 10.4882L9.63828 12.9492C9.06228 12.8392 7.90428 12.4782 6.71228 11.2892C5.52028 10.0962 5.15928 8.93519 5.05228 8.36319L7.51128 5.96919C7.69769 5.78832 7.80642 5.54201 7.81444 5.2824C7.82247 5.02278 7.72917 4.77023 7.55428 4.57819L3.85928 0.515192C3.68432 0.322549 3.44116 0.205696 3.18143 0.189452C2.92171 0.173207 2.66588 0.25885 2.46828 0.428192L0.299282 2.28919C0.125936 2.46208 0.0223422 2.69277 0.00828196 2.93719C-0.00671804 3.18719 -0.292718 9.10919 4.29928 13.7032C8.30528 17.7092 13.3233 18.0022 14.7053 18.0022C14.9073 18.0022 15.0313 17.9962 15.0643 17.9942C15.3088 17.9805 15.5396 17.8769 15.7123 17.7032L17.5723 15.5322C17.7417 15.3347 17.8276 15.079 17.8115 14.8192C17.7954 14.5595 17.6788 14.3163 17.4863 14.1412L13.4223 10.4452Z" fill="#F59E0B"/>
                        </svg>
                        <svg v-if="value === 'Text' && item.direction === 'inbound'" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 1.01148e-06H2C0.897 1.01148e-06 0 0.894001 0 1.992V14.008C0 15.106 0.897 16 2 16H5V20L11.351 16H18C19.103 16 20 15.106 20 14.008V1.992C19.9984 1.46279 19.7869 0.95583 19.412 0.582372C19.037 0.208914 18.5292 -0.000531115 18 1.01148e-06Z" fill="#0081FF"/>
                            <path d="M5.793 8.5L3 5.707V12.707H10L7.207 9.914L11.707 5.414L10.293 4L5.793 8.5Z" :fill="[darkMode ? '#0F1A24' : '#FFFFFF']"/>
                        </svg>
                        <svg v-if="value === 'Text' && item.direction === 'outbound'" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 1.01148e-06H2C0.897 1.01148e-06 0 0.894001 0 1.992V14.008C0 15.106 0.897 16 2 16H5V20L11.351 16H18C19.103 16 20 15.106 20 14.008V1.992C19.9984 1.46279 19.7869 0.95583 19.412 0.582372C19.037 0.208914 18.5292 -0.000531115 18 1.01148e-06Z" fill="#F59E0B"/>
                            <path d="M12.5 5.793L8 10.293L9.414 11.707L13.914 7.207L16.707 10V3H9.707L12.5 5.793Z" :fill="[darkMode ? '#0F1A24' : '#FFFFFF']"/>
                        </svg>
                    </div>
                    <div>
                        <table-badge v-if="item.result" :color="getResultBadgeColor(item.result)"
                                     :dark-mode="darkMode"
                                     :value="item.result"
                        >
                        </table-badge>
                    </div>
                </div>
            </template>
            <template v-slot:disclaimer>
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 rounded-full bg-primary-500"></div><p class="text-sm mr-4">Incoming</p>
                <div class="w-2 h-2 rounded-full bg-amber-500"></div><p class="text-sm">Outgoing</p>
              </div>
            </template>
            <template v-slot:row.col.call_duration_in_seconds="{ value, item }">
                <div class="flex items-center justify-center">
                    <p>{{formatTimeFromSecondsToHumanReadable(value)}}</p>
                </div>
            </template>

            <template v-slot:row.col.created_at="{ value, item }">
                <div class="flex items-center justify-center">
                    {{ $filters.dateFromTimestamp(value, 'usWithTime', 'America/Denver') }}
                </div>
            </template>

            <template v-slot:row.col.contact="{ value, item }">
                <div class="flex px-5 gap-2 text-blue-500 items-center text-center justify-center gap-2">
                    <div @click.stop="() => redirectToIdentityPage(value)" v-if="value" class="flex flex-col cursor-pointer">
                        <p>{{ value.relationData.name }}</p>
                        <p class="text-sm">{{ value.contactName }}</p>
                    </div>
                    <p v-else class="cursor-pointer" @click.stop="() => handleEdit(item)">Unknown</p>
                    <svg class="cursor-pointer w-4" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg" @click.stop="() => handleEdit(item)">
                        <path d="M19 15V19C19 19.5304 18.7893 20.0391 18.4142 20.4142C18.0391 20.7893 17.5304 21 17 21H3C2.46957 21 1.96086 20.7893 1.58579 20.4142C1.21071 20.0391 1 19.5304 1 19V5C1 4.46957 1.21071 3.96086 1.58579 3.58579C1.96086 3.21071 2.46957 3 3 3H7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M11.5 14.8L21 5.2L16.8 1L7.3 10.5L7 15L11.5 14.8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </template>
        </simple-table>
        <communication-log-further-information-modal
            v-if="isCommunicationLogFurtherInfoModalVisible"
            :api-driver="apiDriver"
            :dark-mode="darkMode"
            :selected-log="selectedLog"
            @close="handleModalClose"
        />
    </div>
</template>

<script>
import { SimpleTableHiddenFilterTypesEnum } from "../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes";
import { SimpleTableFilterTypesEnum } from "../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum";
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import Dropdown from "../Shared/components/Dropdown.vue";

import CommunicationLogFurtherInformationModal from "./components/CommunicationLogs/CommunicationLogMessageModal/CommunicationLogMessageModal.vue";
import TableBadge from "./components/CommunicationLogs/TableBadge.vue";

import { useContactIdentificationStore } from "../../../stores/communication/contactIdentification";
import { CommunicationApiFactory } from "../../../services/api/communication/factory";

import { mapWritableState } from "pinia";
import axios from 'axios';
import DatePicker from '@vuepic/vue-datepicker';
import {CommunicationRelationTypes} from "../Communications/enums/communication";

export default {
    name: 'CommunicationLogsOverview',
    components: {
        CommunicationLogFurtherInformationModal,
        LoadingSpinner,
        CustomButton,
        SimpleTable,
        TableBadge,
        Dropdown,
        DatePicker,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: 'api'
        },
    },
    data() {
        return {
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'user_phone',
                    title: 'User Phone',
                },
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'external_phone',
                    title: 'External Phone',
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'communication_description',
                    title: 'Communication Description',
                    options: [
                        {
                            id: "outbound",
                            name: "Outbound"
                        },
                        {
                            id: "inbound",
                            name: "Inbound"
                        }
                    ]
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'communication_type',
                    title: 'Communication Type',
                    options: [
                        {
                            id: "call",
                            name: "Call"
                        },
                        {
                            id: "text",
                            name: "Text"
                        }
                    ]
                },
                {
                    type: SimpleTableHiddenFilterTypesEnum.DATE_RANGE,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'date_time',
                    title: 'Date time',
                },
            ],
            headers: [
                { title: 'Type / Direction', field: 'communication_type' },
                { title: 'Your phone number', field: 'phone' },
                {
                    title: 'External Phone Number',
                    field: 'other_number',
                },
                { title: 'Date time (MT)', field: 'created_at' },
                { title: 'Call duration', field: 'call_duration_in_seconds' },
                { title: 'Identity', field: 'contact', cols: 2 }
            ],

            paginationData: {},
            data: [],
            perPage: 100,
            page: 1,
            loading: true,
            tableFilter: {},


            selectedLog: null,
            isCommunicationLogFurtherInfoModalVisible: false,

            callIcon: 'M25.5224 20.3664L19.5896 14.9722C19.3091 14.7174 18.9406 14.5814 18.5618 14.5931C18.183 14.6048 17.8236 14.7633 17.5594 15.035L14.0669 18.6268C13.2262 18.4662 11.5362 17.9394 9.79645 16.204C8.05675 14.4629 7.52988 12.7685 7.37371 11.9336L10.9626 8.43966C11.2346 8.17569 11.3933 7.81621 11.405 7.43731C11.4167 7.05841 11.2806 6.68982 11.0253 6.40954L5.63255 0.478257C5.3772 0.1971 5.02231 0.026557 4.64325 0.00284858C4.26418 -0.0208599 3.8908 0.104134 3.60241 0.351283L0.435337 3.06736C0.183009 3.3206 0.0324027 3.65764 0.0120874 4.01455C-0.00980485 4.37942 -0.427216 13.0224 6.27472 19.7272C12.1214 25.5724 19.4451 26 21.4621 26C21.7569 26 21.9379 25.9912 21.986 25.9883C22.3429 25.9683 22.6798 25.8171 22.9318 25.5636L25.6464 22.3951C25.8945 22.1076 26.0205 21.7346 25.9973 21.3555C25.9741 20.9765 25.8037 20.6215 25.5224 20.3664Z',
            textIcon: 'M21.6 1.21378e-06H2.4C1.0764 1.21378e-06 0 1.0728 0 2.3904V15C0 16.3176 1.0764 17.3904 2.4 17.3904H6V24L13.6212 17.3904H21.6C22.9236 17.3904 24 16.3176 24 15V2.3904C23.9981 1.75535 23.7443 1.147 23.2944 0.698847C22.8444 0.250697 22.2351 -0.000637338 21.6 1.21378e-06Z',
        }
    },

    computed: {
        ...mapWritableState(useContactIdentificationStore, [
            'isContactIdentificationModalVisible',
            'currentCallInfo'
        ]),

    },

    watch: {
        isContactIdentificationModalVisible(val){
            // Get logs on contact modal close
            if (!val) this.getLogs()
        }
    },

    async mounted() {
        this.registerServices()

        await this.getLogs()
    },
    methods: {
        /**
         * Format time to 00:00:00
         * @param {int} seconds
         * @returns {string}
         */
        formatTimeFromSecondsToHumanReadable(seconds){
            return new Date(seconds * 1000).toISOString().slice(11, 19);
        },

        handleSearch(){
            this.getLogs()
        },

        handleReset(){
            this.page = 1
            this.perPage = 100
            this.getLogs()
        },

        handleModalClose(){
            this.isCommunicationLogFurtherInfoModalVisible = !this.isCommunicationLogFurtherInfoModalVisible
            this.selectedLog = null
        },

        handleClickRow(item){
            this.isCommunicationLogFurtherInfoModalVisible = !this.isCommunicationLogFurtherInfoModalVisible
            this.selectedLog = item
        },

        handleEdit(item){
            this.currentCallInfo = {
                logId: item.id,
                otherNumber: item.other_number,
                identified: item.contact,
                callLogType: item.communication_type.toLowerCase()
            }

            this.isContactIdentificationModalVisible = true
        },

        registerServices(){
            this.communicationApi = CommunicationApiFactory.makeApiService(this.apiDriver);
        },

        async getLogs(){
            this.loading = true
            const response = await this.communicationApi.getLogs(this.parseParams())
            const { data, links,  meta } = response.data
            this.data = data
            this.paginationData = { links, ...meta }
            this.loading = false
        },

        /**
         * Remove empty properties from object
         * Empty means null, '' and []
         * @param {Object} obj
         * @returns {Object}
         */
        removeEmptyPropertiesFromObject(obj){
            return window._.transform(window._.omitBy(obj, window._.isNil), function(result, value, key) {
                result[key] = window._.isObject(value) ? window._.omitBy(value, window._.isNil) : value;
            });
        },

        parseParams(){
            const parsedFilters = { ...this.tableFilter }

            // Transform array filter to query to strings
            if(parsedFilters.communication_type) {
                parsedFilters.communication_type.forEach((n, index) => parsedFilters[`communication_type[${index}]`] = n)
                delete parsedFilters.communication_type
            }

            const strippedFilters = this.removeEmptyPropertiesFromObject(parsedFilters)

            return {
                perPage: this.perPage,
                page: this.page,

                ...strippedFilters,
            }
        },

        handlePerPageChange(perPage){
            if (this.perPage === perPage) return
            this.page = 1
            this.perPage = perPage
            this.getLogs()
        },

        handlePageChange({ newPage }){
            if (this.page === newPage) return
            this.page = newPage
            this.getLogs()
        },

        getResultBadgeColor(result){
            const colors = {
                busy: 'amber',
                missed: 'slate',
                answered: 'emerald',
                initial: 'primary',
            }

            return colors[result] ?? 'slate'
        },
        exportToCSV() {

            const appliedFilters = Array.isArray(this.tableFilter) ? this.tableFilter : [this.tableFilter];
            axios.get('/internal-api/v1/export-call-logs', {
              params: {
                filters: appliedFilters,
              },
            }).then((response) => {
                if (response.data.data.success) {
                    const csvFileName = response.data.data.csvFileName;
                    const csvContent = response.data.data.csvContent;

                    const blob = new Blob([csvContent], { type: 'text/csv' });
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', csvFileName);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                  console.log(response.data.data.message);
                }
            }).catch((error) => {
                console.error('Error exporting CSV:', error);
            });
        },

        redirectToIdentityPage(identity){
            let url = '';

            if (CommunicationRelationTypes.isCompanyRelated(identity.relationType))
                url = `/companies/${identity.relationData.id}`

            else if (CommunicationRelationTypes.isLeadRelated(identity.relationType))
                url = `/lead-processing?lead_id=${identity.relationId}`

            if (url) window.open(url, '_blank')
        }
    }
}
</script>
