<script setup>
import BaseTable from "../Shared/components/BaseTable.vue";
import {onMounted, ref} from "vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";

const props = defineProps({
    darkMode: false,
    gridClass: ''
})
const inactivations = ref([])
const loading = ref(false);

onMounted(() => {
    getInactivations()
})

function getInactivations() {
    console.log('here');

    loading.value = true;

    axios.get('/internal-api/v2/campaigns/inactive')
        .then(res => inactivations.value = res.data.data)
        .finally(() => loading.value = false);
}

function getCompanyUrl(id) {
    return `/companies/${id}`
}

</script>

<template>
    <div class="border rounded-lg" :class="[gridClass, darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="p-5">
            <h3 class="font-bold text-sm uppercase text-primary-500">
                Inactive Campaigns
            </h3>
            <p class="text-slate-500 text-sm">Campaigns that have been paused by your assigned companies.</p>
        </div>
        <LoadingSpinner v-if="loading"></LoadingSpinner>
        <div v-else>
            <BaseTable :dark-mode="darkMode" :loading="loading">
                <template #head>
                    <tr>
                        <th>Campaign</th>
                        <th>Type</th>
                        <th>Reason</th>
                        <th>Since</th>
                        <th>Reactivates</th>
                        <th>Spend</th>
                        <th>Company</th>
                    </tr>
                </template>
                <template #body>
                    <tr v-for="inactivation in inactivations" :key="inactivation.id" class="text-sm">
                        <td>{{inactivation.campaign.name}}</td>
                        <td>{{inactivation.type}}</td>
                        <td>{{inactivation.reason}}</td>
                        <td>{{$filters.dateFromTimestamp(inactivation.since)}}</td>
                        <td>{{$filters.dateFromTimestamp(inactivation.reactivates)}}</td>
                        <td>{{$filters.currency(inactivation.campaign.spend)}}</td>
                        <td>
                            <a target="_blank" :href=getCompanyUrl(inactivation.company.id) class="cursor-pointer inline-flex items-center hover:text-primary-500 font-medium truncate">
                                {{inactivation.company.name}}
                                <svg class="w-3.5 ml-2" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                            </a>
                        </td>
                    </tr>
                </template>
            </BaseTable>
        </div>
    </div>
</template>

<style scoped>

</style>
