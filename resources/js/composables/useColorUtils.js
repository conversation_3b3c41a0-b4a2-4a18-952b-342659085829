import {computed} from 'vue';

export function useColorUtils() {
    const COLORS = {
        blue: "rgba(0, 0, 255, 1)",
        green: "rgba(0, 128, 0, 1)",
        red: "rgba(255, 0, 0, 1)",
        yellow: "rgba(255, 255, 0, 1)",
        orange: "rgba(255, 165, 0, 1)"
    };

    const allColors = Object.values(COLORS)

    /**
     * Returns a color from the sequence based on the provided index, looping back to the start if necessary.
     * @param {number} idx
     * @returns {string}
     */
    const getColorInSequence = (idx) => {
        return allColors[idx % allColors.length]
    }

    const adjustBrightness = (color, factor) => {
        const [r, g, b, a] = color
            .match(/\d+(\.\d+)?/g)
            .map((num, index) => (index < 3 ? parseInt(num, 10) : parseFloat(num)));

        const newR = Math.min(255, Math.max(0, r + factor));
        const newG = Math.min(255, Math.max(0, g + factor));
        const newB = Math.min(255, Math.max(0, b + factor));

        return `rgba(${newR}, ${newG}, ${newB}, ${a})`;
    };

    const getLightColor = (color, brightnessFactor = 40) => {
        return computed(() => adjustBrightness(color, brightnessFactor));
    };

    const getDarkColor = (color, brightnessFactor = 40) => {
        return computed(() => adjustBrightness(color, -brightnessFactor));
    };

    return {
        COLORS,
        allColors,
        getColorInSequence,
        getLightColor,
        getDarkColor
    };
}
