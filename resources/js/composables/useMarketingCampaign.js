import {markRaw} from "vue";
import ShortcodeInputCached from "../vue/components/Shared/components/ShortcodeInputCached.vue";
import InternalEmailConfiguration from "../vue/components/MarketingCampaign/InternalEmailConfiguration.vue";
import DripEmailConfiguration from "../vue/components/MarketingCampaign/DripEmailConfiguration.vue";
import SMSConfiguration from "../vue/components/MarketingCampaign/Configuration/SMSConfiguration.vue";
import DripSMSConfiguration from "../vue/components/MarketingCampaign/Configuration/DripSMSConfiguration.vue";

export default function useMarketingCampaign() {
    const statuses = {
        DRAFT: 'draft',
        SENT: 'sent',
        PAUSED: 'paused',
        ACTIVE: 'active',
    }

    const types = {
        INTERNAL_EMAIL: 'internal_email',
        MAILCHIMP_EMAIL: 'mailchimp_email',
        DRIP_EMAIL: 'drip_email',
        SMS: 'sms',
        DRIP_SMS: 'drip_sms',
    }

    const dripSpans = {
        DAY: 'day',
        WEEK: 'week',
        MONTH: 'month',
        YEAR: 'year',
    }

    const dripSpanStyle = {
        [dripSpans.DAY]: {
            suffix: 'days',
        },
        [dripSpans.WEEK]: {
            suffix: 'weeks',
        },
        [dripSpans.MONTH]: {
            suffix: 'months',
        },
        [dripSpans.YEAR]: {
            suffix: 'years',
        },
    }

    const dripSpanRanges = {
        [dripSpans.DAY]: {
            min: 1,
            max: 100
        },
        [dripSpans.WEEK]: {
            min: 1,
            max: 100
        },
        [dripSpans.MONTH]: {
            min: 1,
            max: 100
        },
        [dripSpans.YEAR]: {
            min: 1,
            max: 20,
        },
    }

    const dripSpanOptions = Object.values(dripSpans).map((span) => ({name: span, id: span}))

    const typeConfigurationComponentMap = {
        [types.INTERNAL_EMAIL]: markRaw(InternalEmailConfiguration),
        [types.DRIP_EMAIL]: markRaw(DripEmailConfiguration),
        [types.MAILCHIMP_EMAIL]: null,
        [types.SMS]: markRaw(SMSConfiguration),
        [types.DRIP_SMS]: markRaw(DripSMSConfiguration),
    }

    const typeConfigurationDefaults = {
        [types.INTERNAL_EMAIL]: {},
        [types.DRIP_EMAIL]: {
            send_time: {
                start: {
                    hours: 8,
                    minutes: 30,
                    seconds: 0,
                },
                end: {
                    hours: 16,
                    minutes: 30,
                    seconds: 0,
                }
            },
            sent_at: null,
            span_type: dripSpans.DAY,
            span_value: 7,
            email_template_id: null,
            email_template_name: null,
        },
        [types.MAILCHIMP_EMAIL]: {},
        [types.SMS]: {
            send_time: {
                start: {
                    hours: 8,
                    minutes: 0,
                    seconds: 0,
                },
                end: {
                    hours: 20,
                    minutes: 0,
                    seconds: 0,
                }
            },
            sent_at: null,
        },
        [types.DRIP_SMS]: {
            send_time: {
                start: {
                    hours: 8,
                    minutes: 0,
                    seconds: 0,
                },
                end: {
                    hours: 20,
                    minutes: 0,
                    seconds: 0,
                }
            },
            sent_at: null,
            span_type: dripSpans.DAY,
            span_value: 7,
        },
    }

    const styles = {
        [statuses.DRAFT]: {
            badgeColor: 'grey'
        },
        [statuses.SENT]: {
            badgeColor: 'blue'
        },
        [statuses.PAUSED]: {
            badgeColor: 'orange'
        },
        [statuses.ACTIVE]: {
            badgeColor: 'green'
        },
    }

    const titles = {
        [statuses.DRAFT]: "Draft",
        [statuses.SENT]: "Sent",
        [statuses.PAUSED]: "Paused",
        [statuses.ACTIVE]: "Active",
    }

    const inputTypes = {
        TEXT: 'text',
        TEXTAREA: 'textarea',
    }

    const inputComponentStyle = {
        [inputTypes.TEXT]: '',
        [inputTypes.TEXTAREA]: 'col-span-full'
    }

    const inputComponentMap = {
        [inputTypes.TEXT]: markRaw(ShortcodeInputCached),
        [inputTypes.TEXTAREA]: markRaw(ShortcodeInputCached)
    }

    return {
        statuses,
        styles,
        titles,
        inputComponentMap,
        inputComponentStyle,
        types,
        typeConfigurationComponentMap,
        dripSpans,
        dripSpanRanges,
        dripSpanStyle,
        dripSpanOptions,
        typeConfigurationDefaults,
    }
}
