export default function useLeadRefundsHelper() {
    const LEAD_REFUND_STATUS = {
        PENDING_REVIEW: 'pending_review',
        APPROVED: 'approved',
        REJECTED: 'rejected',
        APPROVED_WITH_REJECTIONS: 'approved_with_rejections',
        MORE_INFORMATION_NEEDED: 'more_information_needed',
        REFUND_ISSUED: 'refund_issued',
        REFUNDED: 'refunded',
    }

    const LEAD_REFUND_STATUS_DATA = {
        [LEAD_REFUND_STATUS.PENDING_REVIEW]: {
            name: 'Pending Review',
            badge_color: 'gray'
        },
        [LEAD_REFUND_STATUS.APPROVED]: {
            name: 'Approved',
            badge_color: 'green'
        },
        [LEAD_REFUND_STATUS.REJECTED]: {
            name: 'Rejected',
            badge_color: 'red'
        },
        [LEAD_REFUND_STATUS.APPROVED_WITH_REJECTIONS]: {
            name: 'Approved With Rejections',
            badge_color: 'green'
        },
        [LEAD_REFUND_STATUS.MORE_INFORMATION_NEEDED]: {
            name: 'More Information Needed',
            badge_color: 'cyan'
        },
        [LEAD_REFUND_STATUS.REFUND_ISSUED]: {
            name: 'Refund Issued',
            badge_color: 'green'
        },
        [LEAD_REFUND_STATUS.REFUNDED]: {
            name: 'Refunded',
            badge_color: 'green'
        },
    }

    const getLeadStatusOptions = () => {
        return Object.values(LEAD_REFUND_STATUS).reduce((prev, curr) => {
            return [...prev, {
                id: curr,
                name: LEAD_REFUND_STATUS_DATA[curr].name
            }]
        }, [])
    }

    const getLeadRefundStatusPresentationData = (leadRefundStatus) => {
        return LEAD_REFUND_STATUS_DATA[leadRefundStatus] ?? {
            name: leadRefundStatus,
            badge_color: 'gray'
        }
    }

    const LEAD_REFUND_ITEM_REFUND_STATUS = {
        STRIPE_ERROR: 'stripe_error',
        REFUNDED: 'refunded',
        REFUND_ISSUED: 'refund_issued',
        PENDING: 'pending_review',
        APPROVED: 'approved',
        MORE_INFORMATION_NEEDED: 'more_information_needed',
        REJECTED: 'rejected',
    }

    const LEAD_REFUND_ITEM_REFUND_STATUS_DATA = {
        [LEAD_REFUND_ITEM_REFUND_STATUS.STRIPE_ERROR]: {
            name: 'Stripe Error',
            badge_color: 'red'
        },
        [LEAD_REFUND_ITEM_REFUND_STATUS.REFUNDED]: {
            name: 'Refunded',
            badge_color: 'green'
        },
        [LEAD_REFUND_ITEM_REFUND_STATUS.REFUND_ISSUED]: {
            name: 'Refund Issued',
            badge_color: 'green'
        },
        [LEAD_REFUND_ITEM_REFUND_STATUS.PENDING]: {
            name: 'Pending Review',
            badge: 'pending_review'
        },
        [LEAD_REFUND_ITEM_REFUND_STATUS.APPROVED]: {
            name: 'Approved',
            badge: 'approved'
        },
        [LEAD_REFUND_ITEM_REFUND_STATUS.MORE_INFORMATION_NEEDED]: {
            name: 'More information needed',
            badge: 'more_information_needed'
        },
        [LEAD_REFUND_ITEM_REFUND_STATUS.REJECTED]: {
            name: 'Rejected',
            badge: 'rejected'
        },
    }

    const getLeadRefundItemRefundStatusPresentationData = (leadRefundItemRefundStatus) => {
        return LEAD_REFUND_ITEM_REFUND_STATUS_DATA[leadRefundItemRefundStatus] ?? {
            name: leadRefundItemRefundStatus,
            badge_color: 'gray'
        }
    }

    return {
        leadRefundStatus: LEAD_REFUND_STATUS,
        getLeadStatusOptions,
        getLeadRefundStatusPresentationData,
        getLeadRefundItemRefundStatusPresentationData
    }
}
