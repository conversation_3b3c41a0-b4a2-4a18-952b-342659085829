import {ref} from 'vue'

export default function useErrorHandler() {
    const message = ref(null)
    const errors = ref({})

    const DEFAULT_ERROR_MESSAGE = 'Server error'
    const ERROR_MESSAGES_JOIN_STRING = '. '

    const resetError = (field) => {
        if (field) {
            errors.value[field] = null
        } else {
            errors.value = {}
        }

        resetMessage()
    }

    const _handleValidationError = (err, defaultErrorMessage = DEFAULT_ERROR_MESSAGE) => {
        errors.value = Object.entries(err.response?.data?.errors).reduce((prev, [field, errorList]) => {
            return Object.assign(prev, {[field]: errorList.join(ERROR_MESSAGES_JOIN_STRING)})
        }, {});

        message.value = err?.response?.data?.message || err?.response?.message || defaultErrorMessage
    }

    const _handleGenericError = (err, defaultErrorMessage = DEFAULT_ERROR_MESSAGE) => {
        errors.value = {}
        message.value = defaultErrorMessage
    }

    const handleError = (err, defaultErrorMessage = DEFAULT_ERROR_MESSAGE) => {
        switch (err?.response?.status) {
            case 422:
                _handleValidationError(err, defaultErrorMessage)
                break;
            default:
                _handleGenericError(err, defaultErrorMessage)
                break;
        }

        console.error(err)
    }

    const resetMessage = () => {
        message.value = null
    }

    return {
        message,
        errors,

        handleError,
        resetError,
        resetMessage,
    }
}
