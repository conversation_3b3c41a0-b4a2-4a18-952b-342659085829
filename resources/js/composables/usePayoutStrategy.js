export default function usePayoutStrategy() {
    const type = {
        REVENUE_PERCENTAGE: 'revenue_percentage',
        RAW_LEAD_FLAT_VALUE: 'raw_lead_flat_value',
        SOLD_LEAD_FLAT_VALUE: 'sold_lead_flat_value',
    };

    const typeStyle = {
        [type.REVENUE_PERCENTAGE]: {
            prefix: '',
            short_title: 'Share',
            suffix: '%',
            value_title: 'Affiliate Portion of Revenue'
        },
        [type.RAW_LEAD_FLAT_VALUE]: {
            prefix: '$',
            short_title: 'per Raw Lead',
            suffix: '',
            value_title: 'Dollars Paid to Affiliate'
        },
        [type.SOLD_LEAD_FLAT_VALUE]: {
            prefix: '$',
            short_title: 'per Sold Lead',
            suffix: '',
            value_title: 'Dollars Paid to Affiliate'
        },
    }

    const getTypeStyle = (incomingType) => {
        return typeStyle[incomingType] ?? typeStyle[type.REVENUE_PERCENTAGE]
    }

    return {
        type,
        typeStyle,
        getTypeStyle
    }
}
