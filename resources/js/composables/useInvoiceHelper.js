import useSimpleIcon from "./useSimpleIcon";

export default function useInvoiceHelper(){
    const simpleIcon = useSimpleIcon()

    const ITEM_TYPES = {
        CREDIT: 'credit',
        MANUAL: 'manual',
        PRODUCT: 'product',
        BUNDLE: 'bundle',
    }

    const ITEM_PRESENTATION = {
        [ITEM_TYPES.CREDIT]: {
            added_title: 'Credit Item Added',
            icon: simpleIcon.icons.CURRENCY_DOLLAR,
        },
        [ITEM_TYPES.MANUAL]: {
            added_title: 'Manual Item Added',
            icon: simpleIcon.icons.PENCIL_SQUARE,
        },
        [ITEM_TYPES.PRODUCT]: {
            added_title: 'Product Added',
            icon: simpleIcon.icons.USER_PLUS,
        },
        [ITEM_TYPES.BUNDLE]: {
            added_title: 'Bundle Added',
            icon: simpleIcon.icons.PLUS_CIRCLE,
        }
    }

    const EVENT_TYPES = {
        REQUEST_INVOICE_TAX: 'request_invoice_tax'
    }

    const EVENT_STYLES = {
        [EVENT_TYPES.REQUEST_INVOICE_TAX]: {
            icon: simpleIcon.icons.RSS,
            color: simpleIcon.colors.BLACK
        }
    }

    const STATUSES =  {
        DRAFT: 'draft',
        ISSUED: 'issued',
        DELETED: 'deleted',
        PAID: 'paid',
        FAILED: 'failed',
        VOIDED: 'voided',
        COLLECTION: 'collection',
        CHARGEBACK: 'chargeback',
        WRITTEN_OFF: 'written_off',
    }

    const AUTHOR_TYPES = {
        COMPANY_USER: 'company_user',
        USER: 'user',
        SYSTEM: 'system'
    }

    const AUTHOR_STYLES = {
        [AUTHOR_TYPES.COMPANY_USER]: {
            color: 'green',
            title: 'Company User',
            icon: simpleIcon.icons.USER_GROUP
        },
        [AUTHOR_TYPES.USER]: {
            color: 'blue',
            title: 'User',
            icon: simpleIcon.icons.USER_CIRCLE,
        },
        [AUTHOR_TYPES.SYSTEM]: {
            color: 'gray',
            title: 'System',
            icon: simpleIcon.icons.BOLT,
        }
    }

    const getStatusStyle = (invoiceStatus) => {
        const statusStyles = {
            [STATUSES.DRAFT]: {
                title: 'Draft',
                color: 'gray',
                icon: null,
                icon_color: null,
            },
            [STATUSES.ISSUED]: {
                title: 'Issued',
                color: 'indigo',
                icon: null,
                icon_color: null,
            },
            [STATUSES.DELETED]: {
                title: 'Deleted',
                color: 'amber',
                icon: simpleIcon.icons.TRASH,
                icon_color: null,
            },
            [STATUSES.PAID]: {
                title: 'Paid',
                color: 'green',
                icon: simpleIcon.icons.CHECK_CIRCLE,
                icon_color: simpleIcon.colors.BLACK,
            },
            [STATUSES.FAILED]: {
                title: 'Failed',
                color: 'red',
                icon: simpleIcon.icons.X_CIRCLE,
                icon_color: simpleIcon.colors.BLACK,
            },
            [STATUSES.VOIDED]: {
                title: 'Voided',
                color: 'orange',
                icon: null,
                icon_color: null,
            },
            [STATUSES.COLLECTION]: {
                title: 'Collection',
                color: 'purple',
                icon: null,
                icon_color: null,
            },
            [STATUSES.REFUNDED]: {
                title: 'Refunded',
                color: 'gray',
                icon: null,
                icon_color: null,
            },
            [STATUSES.CHARGEBACK]: {
                title: 'Chargeback',
                color: 'cyan',
                icon: null,
                icon_color: null,
            },
            [STATUSES.WRITTEN_OFF]: {
                title: 'Written Off',
                color: 'amber',
                icon: null,
                icon_color: null,
            },
        }

        return statusStyles[invoiceStatus] ?? {
            title: invoiceStatus,
            color: 'gray',
        }
    }

    return {
        STATUSES,
        getStatusStyle,
        ITEM_TYPES,
        ITEM_PRESENTATION,
        EVENT_TYPES,
        EVENT_STYLES,
        AUTHOR_TYPES,
        AUTHOR_STYLES
    }
}
