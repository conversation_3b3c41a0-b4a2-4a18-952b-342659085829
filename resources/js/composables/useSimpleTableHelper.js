export default function useSimpleTableHelper() {

    /**
     * Useful for removing filters that are still in the tableFilter object but don't have any value.
     *
     * @param {Object} tableFilter
     * @returns {Object}
     */
    const cleanFilters = (tableFilter) => {
        return Object.fromEntries(Object.entries(tableFilter).filter(([key, value]) => value !== null && value !== ""))
    }

    return {
        cleanFilters,
    }
}