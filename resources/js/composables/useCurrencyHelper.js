export function useCurrencyHelper() {
    const CURRENCIES = {
        USD: 'usd'
    }

    const CURRENCY_DATA = {
        [CURRENCIES.USD]: {
            symbol: '$',
            decimalSeparator: '.',
            thousandSeparator: ','
        }
    }

    const getCurrencyData = (currency = CURRENCIES.USD) => {
        return CURRENCY_DATA[currency] ?? CURRENCY_DATA[CURRENCIES.USD]
    }

    const formatCurrency = (
        amount,
        {
            currency = CURRENCIES.USD,
            decimals = 2,
        } = {}
    ) => {
        if (isNaN(amount)) {
            amount = 0
        }

        const {symbol, decimalSeparator, thousandSeparator} = getCurrencyData(currency)

        const number = parseFloat(amount).toFixed(decimals)

        const [integerPart, decimalPart] = number.split('.')
        const integerWithSeparators = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator)

        const decimalString = decimals > 0 ? `${decimalSeparator}${decimalPart}` : ''

        return `${symbol}${integerWithSeparators}${decimalString}`
    }

    return {
        CURRENCIES,
        formatCurrency,
    }
}
