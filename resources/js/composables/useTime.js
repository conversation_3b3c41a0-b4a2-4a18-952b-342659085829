
export default function useTimeHelper() {

    function formatDuration(seconds) {
        if (isNaN(seconds) || seconds < 0) return "0s";

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        let result = "";
        if (hours > 0) result += `${hours}h`;
        if (minutes > 0) result += `${minutes}m`;
        if (secs > 0 || result === "") result += `${secs}s`;

        return result;
    }

    return {
        formatDuration
    }
}
