import UserContactAdditionalData
    from "../vue/components/Mailbox/components/IdentifiedContact/UserContactAdditionalData.vue";
import ConsumerContactAdditionalData
    from "../vue/components/Mailbox/components/IdentifiedContact/ConsumerContactAdditionalData.vue";
import CompanyUserContactAdditionalData
    from "../vue/components/Mailbox/components/IdentifiedContact/CompanyUserContactAdditionalData.vue";

import {markRaw} from 'vue';

export default function useIdentifiedContact(){
    const identificationStatus = {
        NO_RESULT: 'no_result',
        SINGLE_RESULT: 'single_result',
        MULTIPLE_RESULTS: 'multiple_results',
        IDENTIFYING: 'identifying',
    }

    const contactTypes = {
        CONSUMER         : 'consumer',
        COMPANY_USER     : 'company_user',
        COMPANY_LOCATION : 'company_location',
        USER             : 'user',
    }

    const getIdentificationStatusDescription = (status) => {
        return {
            [identificationStatus.NO_RESULT]: 'Unidentified',
            [identificationStatus.SINGLE_RESULT]: 'Nominated',
            [identificationStatus.MULTIPLE_RESULTS]: 'Multiple matches',
            [identificationStatus.IDENTIFYING]: 'Identifying',
        }[status] ?? status
    }

    const getContactTypeDescription = (type) => {
        return {
            [contactTypes.CONSUMER]: 'Consumer',
            [contactTypes.COMPANY_USER]: 'Company User',
            [contactTypes.COMPANY_LOCATION]: 'Company Location',
            [contactTypes.USER]: 'User',
        }[type] ?? type
    }

    const getAdditionalDataComponent = (identifiedContact) => {
        if (!identifiedContact?.nominated_contact) return null;

        switch (identifiedContact.nominated_contact.model_relation_type) {
            case contactTypes.USER:
                return markRaw(UserContactAdditionalData)
            case contactTypes.COMPANY_USER:
                return markRaw(CompanyUserContactAdditionalData)
            case contactTypes.CONSUMER:
                return markRaw(ConsumerContactAdditionalData)
        }
    }

    const getBadgeData = (identifiedContact) => {
        if (!identifiedContact) return '';

        if (identifiedContact.identification_status === identificationStatus.SINGLE_RESULT && identifiedContact.nominated_contact) {
            // TODO - Return data based on the contact type
            // If contact type is company user name + company name
            // TODO - Return component
            return [
                identifiedContact?.nominated_contact?.contact?.name,
                `(${identifiedContact.identifier_value})`
            ].join(' ')
        }

        return identifiedContact.identifier_value;
    }

    const isSingleResult = (identifiedContact) => {
        return identifiedContact.identification_status === identificationStatus.SINGLE_RESULT;
    }

    const isNoResult = (identifiedContact) => {
        return identifiedContact.identification_status === identificationStatus.NO_RESULT;
    }

    const isMultipleResult = (identifiedContact) => {
        return identifiedContact.identification_status === identificationStatus.MULTIPLE_RESULTS;
    }

    const isIdentifying = (identifiedContact) => {
        return identifiedContact.identification_status === identificationStatus.IDENTIFYING;
    }


    return {
        identificationStatus,
        contactTypes,

        getContactTypeDescription,
        getAdditionalDataComponent,
        getBadgeData,
        getIdentificationStatusDescription,
        isSingleResult,
        isNoResult,
        isMultipleResult,
        isIdentifying
    }
}
