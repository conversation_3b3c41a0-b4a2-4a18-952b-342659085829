import useSimpleIcon from "./useSimpleIcon.js";

export default function useCompanyStatus() {
    const simpleIcon = useSimpleIcon();

    const admin = {
        ADMIN_OVERRIDE: 1,
        ADMIN_APPROVED: 2,
        COLLECTIONS: 3,
        ARCHIVED: 4,
        ADMIN_LOCKED: 5,
        PROFILE_ONLY: 6,
    }

    const adminStyle = {
        [admin.ADMIN_OVERRIDE]: {icon: simpleIcon.icons.ADMIN_OVERRIDE},
        [admin.ADMIN_APPROVED]: {icon: simpleIcon.icons.ADMIN_APPROVED},
        [admin.COLLECTIONS]: {icon: simpleIcon.icons.ADMIN_COLLECTIONS},
        [admin.ARCHIVED]: {icon: simpleIcon.icons.ADMIN_ARCHIVED},
        [admin.ADMIN_LOCKED]: {icon: simpleIcon.icons.ADMIN_LOCKED},
        [admin.PROFILE_ONLY]: {icon: simpleIcon.icons.ADMIN_PROFILE_ONLY},
    }

    const system = {
        ELIGIBLE: 1,
        SUSPENDED_PAYMENT: 2,
        SUSPENDED_PAYMENT_METHOD: 3,
    }

    const systemStyle = {
        [system.ELIGIBLE]: {icon: simpleIcon.icons.SYSTEM_ELIGIBLE},
        [system.SUSPENDED_PAYMENT]: {icon: simpleIcon.icons.SYSTEM_SUSPENDED_PAYMENT},
        [system.SUSPENDED_PAYMENT_METHOD]: {icon: simpleIcon.icons.SYSTEM_SUSPENDED_PAYMENT_METHOD},
    }

    const campaign = {
        NO_CAMPAIGNS: 0,
        CAMPAIGNS_ACTIVE: 1,
        CAMPAIGNS_PAUSED: 2,
        CAMPAIGNS_OFF: 3,
        CAMPAIGNS_OFF_NEVER_PURCHASED: 4,
    }

    const campaignStyle = {
        [campaign.NO_CAMPAIGNS]: {icon: simpleIcon.icons.CAMPAIGN_NO_CAMPAIGNS},
        [campaign.CAMPAIGNS_ACTIVE]: {icon: simpleIcon.icons.CAMPAIGN_CAMPAIGNS_ACTIVE},
        [campaign.CAMPAIGNS_PAUSED]: {icon: simpleIcon.icons.CAMPAIGN_CAMPAIGNS_PAUSED},
        [campaign.CAMPAIGNS_OFF]: {icon: simpleIcon.icons.CAMPAIGN_CAMPAIGNS_OFF},
        [campaign.CAMPAIGNS_OFF_NEVER_PURCHASED]: {icon: simpleIcon.icons.CAMPAIGN_CAMPAIGNS_OFF_NEVER_PURCHASED},
    }

    return {
        admin,
        adminStyle,
        system,
        systemStyle,
        campaign,
        campaignStyle,
    }
}
