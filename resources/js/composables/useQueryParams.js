export default function useQueryParams() {
    const parseQueryParams = (url) => {
        const queryParams = {};
        const params = new URLSearchParams(new URL(url).search);
        params.forEach((value, key) => {
            if (key.endsWith('[]')) {
                const actualKey = key.slice(0, -2);
                if (!queryParams.hasOwnProperty(actualKey)) {
                    queryParams[actualKey] = [];
                }
                queryParams[actualKey].push(value);
            } else {
                queryParams[key] = value;
            }
        });
        return queryParams;
    }

    const mountUrlWithSearchParams = (params, baseUrl = '') => {
        const searchParams = new URLSearchParams();

        Object.entries(params).forEach(([key, value]) => {
            if (Array.isArray(value)) {
                searchParams.delete(key + '[]');
                value.forEach(item => {
                    searchParams.append(key + '[]', item);
                });
            } else {
                searchParams.set(key, value);
            }
        });

        return baseUrl + (searchParams.toString().length > 0 ? '?' + searchParams.toString() : '')
    }

    const setQueryParamsOnCurrentUrl = (params = {}) => {
        const newUrl = getCurrentUrl() + mountUrlWithSearchParams(params);
        window.history.pushState({path: newUrl}, '', newUrl);
    }

    const getCurrentUrl = () => {
        return window.location.protocol + "//" + window.location.host + window.location.pathname
    }

    const getCurrentParams = () => {
        return parseQueryParams(window.location.href)
    }


    return {
        parseQueryParams,
        mountUrlWithSearchParams,
        setQueryParamsOnCurrentUrl,
        getCurrentParams,
        getCurrentUrl
    }
}
