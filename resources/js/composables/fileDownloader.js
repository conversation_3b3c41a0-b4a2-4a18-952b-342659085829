import {slugify} from "../vue/components/Shared/services/strings.js";

export function useFileDownloader() {
    const downloadFile = (content, filename = "export.txt", type = null) => {
        const [name, ext] = filename.split('.')
        filename = `${slugify(name)}.${ext}`

        const blob = new Blob([content], {type});
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return {downloadFile};
}
