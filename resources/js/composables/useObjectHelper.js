export default function useObjectHelper() {
    function removeEmptyProperties(obj) {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }

        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                obj[key] = removeEmptyProperties(obj[key]);

                if (obj[key] === null
                    || obj[key] === undefined
                    || obj[key] === ''
                    || (typeof obj[key] === 'object' && Object.keys(obj[key]).length === 0)
                ) {
                    delete obj[key];
                }
            }
        }

        return obj;
    }

    function getValue(obj, path) {
        try {
            if (!path) return undefined;
            const keys = path.split('.');

            let current = obj;

            for (const key of keys) {
                if (typeof current !== 'object' || current === null || !current.hasOwnProperty(key)) {
                    return undefined;
                }
                current = current[key];
            }

            return current;
        } catch (err) {
            console.error(err)
        }
    }

    return {
        removeEmptyProperties,
        getValue
    }
}
