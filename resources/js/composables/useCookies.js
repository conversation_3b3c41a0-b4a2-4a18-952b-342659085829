export function useCookies() {
    const setCookie = (name, value, expiryInMilli) => {
        let expires = "";
        if (expiryInMilli) {
            const date = new Date();
            date.setTime(date.getTime() + expiryInMilli);
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    };

    const getCookie = (name) => {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    };

    const eraseCookie = (name) => {
        document.cookie = name + '=; Max-Age=-99999999;';
    };

    return {
        setCookie,
        getCookie,
        eraseCookie,
    };
}
