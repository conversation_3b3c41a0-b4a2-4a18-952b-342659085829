export default function useLogLevel() {
    const statuses = {
        EMERGENCY:  'emergency',
        ALERT:      'alert',
        CRITICAL:   'critical',
        ERROR:      'error',
        WARNING:    'warning',
        NOTICE:     'notice',
        INFO:       'info',
        DEBUG:      'debug',
    }

    const styles = {
        [statuses.EMERGENCY]: {
            badgeColor: 'orange'
        },
        [statuses.ALERT]: {
            badgeColor: 'orange'
        },
        [statuses.CRITICAL]: {
            badgeColor: 'red'
        },
        [statuses.ERROR]: {
            badgeColor: 'red'
        },
        [statuses.WARNING]: {
            badgeColor: 'amber'
        },
        [statuses.NOTICE]: {
            badgeColor: 'grey'
        },
        [statuses.INFO]: {
            badgeColor: 'blue'
        },
        [statuses.DEBUG]: {
            badgeColor: 'grey'
        },
    }

    const titles = {
        [statuses.EMERGENCY]: "Emergency",
        [statuses.ALERT]: "Alert",
        [statuses.CRITICAL]: "Critical",
        [statuses.ERROR]: "Error",
        [statuses.WARNING]: "Warning",
        [statuses.NOTICE]: "Notice",
        [statuses.INFO]: "Info",
        [statuses.DEBUG]: "Debug",
    }

    const options = Object.values(statuses).map(function (status) {
        return {id: status, name: titles[status]}
    })

    return {
        statuses,
        styles,
        titles,
        options,
    }
}
