import { computed, ref } from 'vue';

export function usePaginationHelper(dataArray = [], initialPage = 1, perPage = 10) {
    const page = ref(initialPage);
    const perPageRef = ref(perPage);
    const total = ref(dataArray.length);

    const lastPage = computed(() => Math.ceil(total.value / perPageRef.value));
    const from = computed(() => (page.value - 1) * perPageRef.value + 1);
    const to = computed(() => Math.min(page.value * perPageRef.value, total.value));

    const paginatedData = computed(() => {
        const start = (page.value - 1) * perPageRef.value;
        const end = start + perPageRef.value;
        return dataArray.slice(start, end);
    });

    const setPage = (newPage) => {
        if (newPage >= 1 && newPage <= lastPage.value) {
            page.value = newPage;
        }
    };

    const setTotalItems = (newTotal) => {
        total.value = newTotal;
    };

    return {
        page,
        perPage: perPageRef,
        from,
        to,
        total,
        lastPage,
        paginatedData,
        setPage,
        setTotalItems,
    };
}
