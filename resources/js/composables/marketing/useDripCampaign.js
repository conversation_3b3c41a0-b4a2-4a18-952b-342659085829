export default function useDripCampaign() {
    const spans = {
        DAY: 'day',
        WEEK: 'week',
        MONTH: 'month',
        YEAR: 'year',
    }

    const spanStyle = {
        [spans.DAY]: {
            suffix: 'days',
        },
        [spans.WEEK]: {
            suffix: 'weeks',
        },
        [spans.MONTH]: {
            suffix: 'months',
        },
        [spans.YEAR]: {
            suffix: 'years',
        },
    }

    const spanRanges = {
        [spans.DAY]: {
            min: 1,
            max: 100
        },
        [spans.WEEK]: {
            min: 1,
            max: 100
        },
        [spans.MONTH]: {
            min: 1,
            max: 100
        },
        [spans.YEAR]: {
            min: 1,
            max: 20,
        },
    }

    const spanOptions = Object.values(spans).map((span) => ({name: span, id: span}))

    return {
        spanRanges,
        spanOptions,
        spans,
        spanStyle,
    }
}
