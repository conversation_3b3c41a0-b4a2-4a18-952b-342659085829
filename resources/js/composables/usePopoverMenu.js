export default function usePopoverMenu() {
    const SAFE_MARGIN = 10;

    /**
     * Calculates the x and y positions of an element based on the screen size.
     * If the sum of the element's position and its size exceeds the window size, it is repositioned to fit within the window's dimensions.
     *
     * @param {MouseEvent} event
     * @param {HTMLElement} menuElement
     * @param {number} margin
     * @returns {{popoverLeft: number, popoverTop: number}}
     */
    const calculatePosition = (event, menuElement, margin = 0) => {
        const {clientX, clientY, target} = event;
        const elementBounds = target.getBoundingClientRect();
        const menuBounds = menuElement?.getBoundingClientRect();

        const {height: menuHeight, width: menuWidth} = getMenuDimensions(menuBounds, margin);

        const popoverTop = calculateCoordinate(clientY, elementBounds.top, menuHeight, window.innerHeight);
        const popoverLeft = calculateCoordinate(clientX, elementBounds.left, menuWidth, window.innerWidth);

        return {
            popoverLeft,
            popoverTop
        };
    };

    const checkIfElementIsOffScreen = (element, menuOrigin, margin = 0) => {
        if (!element || !menuOrigin) return false;

        const elementBounds = element.getBoundingClientRect();
        const menuBounds = menuOrigin.getBoundingClientRect();

        const {height: menuHeight, width: menuWidth} = getMenuDimensions(menuBounds, margin);

        return isOffScreen(elementBounds.top+ elementBounds.height, menuHeight, window.innerHeight) ||
            isOffScreen(elementBounds.left + elementBounds.width, menuWidth, window.innerWidth);
    };

    const calculateCoordinate = (clientCoordinate, elementStart, elementSize, windowSize) => {
        return (elementStart + elementSize > windowSize)
            ? windowSize - elementSize
            : clientCoordinate + SAFE_MARGIN;
    };

    const isOffScreen = (elementStart, elementSize, windowSize) => {
        return elementStart + elementSize > windowSize;
    };

    const getMenuDimensions = (menuBounds, margin) => {
        return {
            height: (menuBounds?.height ?? 0) + margin + SAFE_MARGIN,
            width: (menuBounds?.width ?? 0) + margin + SAFE_MARGIN
        };
    };

    return {
        checkIfElementIsOffScreen,
        calculatePosition
    };
}
