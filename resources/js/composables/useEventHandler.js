import {ref} from 'vue'

export default function useEventHandler() {
    const listeners = ref({})

    const addListener = (event, listenerId, callback) => {
        if (!listeners.value[event]) {
            listeners.value[event] = {}
        }

        listeners.value[event][listenerId] = callback
    }

    const dispatchEvent = (event) => {
        const listenerCallbacks = listeners.value[event] ?? {}

        Object.values(listenerCallbacks).forEach((callback) => {
            try {
                callback()
            } catch (err) {
                console.error(err)
            }
        })
    }

    return {
        listeners,
        addListener,
        dispatchEvent
    }
}
