import {ref} from 'vue';

export function useCooldown() {
    const lastExecuted = ref(0);
    const onCooldown = ref(false);

    const canExecute = (cooldownInMilliseconds) => {
        const now = Date.now();
        return now - lastExecuted.value >= cooldownInMilliseconds;
    };

    const execute = (cooldownInMilliseconds, action, args = []) => {
        if (canExecute(cooldownInMilliseconds)) {
            action(...args);
            lastExecuted.value = Date.now();
            onCooldown.value = true;
            setTimeout(() => {
                onCooldown.value = false;
            }, cooldownInMilliseconds);
        }
    };

    return {
        execute,
        onCooldown,
    };
}
