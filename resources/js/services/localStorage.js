export class LocalStorageService {
    // Can be replaced by its child
    _namespace = ''
    _separator = '.'


    /**
     * Get item from localstorage
     *
     * @param {String} key
     * @returns {boolean|*}
     */
    getItem(key) {
        const rawItem = localStorage.getItem(this._makeFull<PERSON>ey(key))

        return rawItem ? this._parseStringifiedValue(rawItem) : rawItem
    }

    /**
     * Parse stringified value to it's base form
     *
     * @param value
     * @returns {*}
     * @private
     */
    _parseStringifiedValue(value) {
        if (!value) return

        return JSON.parse(value)
    }


    /**
     * Set value into localstorage
     *
     * @param {string} key
     * @param {*} value
     */
    setItem(key, value) {
        if (!typeof value === 'undefined' || value === null) throw new Error(`value is required`)

        const stringified = typeof value !== "string" ? value.toString() : value

        localStorage.setItem(this._makeFullKey(key), stringified)
    }

    /**
     * Make key full path using namespace
     *
     * @param key
     * @returns {string}
     * @private
     */
    _makeFull<PERSON>ey(key) {
        const splitNamespace = this._splitPath(this._namespace)
        const splitKey = this._splitPath(key)

        return [...splitNamespace, ...splitKey].join(this._separator)
    }

    /**
     * Split and sanitize string
     *
     * @param path
     * @returns {string[]}
     * @private
     */
    _splitPath(path) {
        return path.trim().split(this._separator).filter(s => !!s)
    }
}
