import Pusher from "pusher-js";
import { waitForCallbackToReturnTrue } from "../utilities/loading-utilities";

/**
 * Pusher channel modes
 */
export const PusherChannelModesEnum = {
    PRIVATE: 'private'
}

export class PusherService {
    static CHANNEL_MODES = [PusherChannelModesEnum.PRIVATE]

    /**
     * Pusher Service constructor
     * @param {string} appKey
     * @param {string} appCluster
     * @param {string} authEndpoint
     * @param authParams
     * @param {boolean} logToConsole
     */
    constructor(appKey, appCluster, authEndpoint = '/broadcasting/auth', authParams = {}, logToConsole = false) {
        Pusher.logToConsole = logToConsole

        this._pusher = new Pusher(appKey, {
            cluster: appCluster,
            authEndpoint,
            auth: {
                params: authParams
            },
        });

        this.debugMode = logToConsole

        this.subscriptionSucceeded = false
    }

    /**
     * Format channel name adding private prefix when needed
     *
     * @param {string} channelName
     * @param {string} channelMode
     * @returns
     */
    _formatChannelName(channelName, channelMode) {
        if (channelName.includes(PusherChannelModesEnum[channelMode])) return channelName

        return `${channelMode}-${channelName}`
    }

    /**
     * Format event name adding client when channel is private
     *
     * @param {string} eventName
     * @param {boolean} clientSide
     * @returns
     */
    _formatEventName(eventName, clientSide) {
        if (clientSide && !eventName.includes('client-'))
            return `client-${eventName}`

        return eventName
    }

    /**
     * Subscribe to channel
     *
     * @param {string} channel
     * @param {PusherChannelModesEnum} channelMode
     */
    subscribe(channel, channelMode = PusherChannelModesEnum.PRIVATE) {
        if (!PusherService.CHANNEL_MODES.includes(channelMode))
            throw new Error(`Channel mode should be one of: ${PusherService.CHANNEL_MODES.join()}`)


        const formattedChannelName = this._formatChannelName(channel, channelMode)

        this._channel = this._pusher.subscribe(formattedChannelName)
        this._channel.bind("pusher:subscription_succeeded", () => {
            this.log(`Channel ${formattedChannelName} ready to bind and trigger events`)
            this.subscriptionSucceeded = true
        })
        this._channel.bind("pusher:subscription_error", () => {
            this.log(`Channel ${formattedChannelName} not ready to bind and trigger events`)
            this.subscriptionSucceeded = false
        })
    }

    /**
     * Try to parse event data to json
     *
     * @param {string} data
     * @returns
     */
    _parseTriggeredData(data) {
        try {
            return JSON.parse(data)
        } catch {
            return data
        }
    }

    /**
     * Check if channel is ready to bind or trigger actions
     *
     * @param {string} action
     */
    async _checkChannelSubscriptionSuccess(action = 'bind') {
        if (!this.subscriptionSucceeded) {
            try {
                await waitForCallbackToReturnTrue(() => this.subscriptionSucceeded, 250, 100)
            } catch {
                throw new Error(`Failed to ${action} because channel was not subscribed successfully`)
            }
        }
    }

    /**
     * Bind an event to a callback by its name when channel is ready
     *
     * @param {string} eventName
     * @param {function} callback
     * @param {boolean} clientSide
     */
    bind(eventName, callback, { clientSide = true } = {}) {
        // TODO: - Throw error when binding duplicated event
        if (!this._channel) throw new Error('Subscribed channel not found')
        if (typeof callback !== 'function') throw new Error('Callback should be a function')

        const formattedEventName = this._formatEventName(eventName, clientSide)

        this._checkChannelSubscriptionSuccess().then(() => {
            this._channel.bind(formattedEventName, (data) => {
                callback(this._parseTriggeredData(data))
            })
            this.log(`Success binding event ${formattedEventName}`)
        })
    }

    /**
     * Unbind a bound event
     *
     * @param {string} eventName
     * @param {function} callback
     * @param {boolean} clientSide
     */
    unbind(eventName,  { clientSide = true } = {}) {
        if (!this._channel) throw new Error('Subscribed channel not found')

        const formattedEventName = this._formatEventName(eventName, clientSide)

        this._checkChannelSubscriptionSuccess().then(() => {
            this._channel.unbind(formattedEventName)
            this.log(`Success unbinding event ${formattedEventName}`)
        })
    }

    /**
     * Add triggered_at to event payload
     *
     * @param {object} data
     * @returns
     */
    _addMetaToTriggerPayload(data) {
        return {
            ...data,
            triggered_at: new Date().toISOString()
        }
    }

    /**
     * Trigger a event by its name when channel is ready
     *
     * @param {string} eventName
     * @param {clientSide} boolean
     * @param {object} data
     */
    trigger(eventName, data = {}, { clientSide = true } = {}) {
        if (typeof data !== 'object') throw new Error('Data should be a object')

        const stringfiedData = JSON.stringify(this._addMetaToTriggerPayload(data))

        const formattedEventName = this._formatEventName(eventName, clientSide)

        this._checkChannelSubscriptionSuccess('trigger').then(() => {
            this._channel.trigger(formattedEventName, stringfiedData)
            this.log(`event ${eventName} triggered: ${JSON.stringify(data)}`)
        })
    }

    /**
     * Handle logs
     *
     * @param message
     */
    log(message){
        // TODO - Improve logging
        if (this.debugMode) console.log(`PusherService - ${message}`)
    }
}
