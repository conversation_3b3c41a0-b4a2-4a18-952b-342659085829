import axios from 'axios';
import {BaseApiService} from "./base";

class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getCallForwardingData(userId) {
        return this.axios().get('/' + userId + '/get-call-forwarding-data');
    }

    updateCallForwardingStatus(userId, status) {
        return this.axios().patch('/' + userId + '/update-call-forwarding-status', {
            status: status
        });
    }

    updateCallForwardingNumber(userId, number) {
        return this.axios().patch('/' + userId + '/update-call-forwarding-number', {
            number: number
        });
    }

    getUserTimezone(userId) {
        return this.axios().get(`/${userId}/timezone`);
    }

    updateUserTimezone(userId, timezone) {
        return this.axios().post(`/${userId}/timezone`, {timezone});
    }

    getUserLinks(userId) {
        return this.axios().get(`/${userId}/user-links`);
    }

    updateUserLinks(userId, payload) {
        return this.axios().patch(`/${userId}/update-user-links`, payload);
    }

    // Stores user filter preset
    storeUserFilterPresets(userId, filterPreset){
        return this.axios().post(`/${userId}/filter-presets`, {filterPreset})
    }

    getUserSearchFilters(userId, category) {
        return this.axios().get(`/${userId}/search-filters?category=${category}`)
    }

    storeUserSearchFilters(userId, category, filterPayload) {
        return this.axios().post(`/${userId}/search-filters`, {
            'category' : category,
            'data': filterPayload
        })
    }

    updateUserProfile(userId, payload) {
        return this.axios().patch('/' + userId + '/update-user-profile', payload);
    }
}

export { ApiService };

