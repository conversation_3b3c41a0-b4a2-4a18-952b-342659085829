import {BaseApiService} from "./base";

class DummyApiService extends BaseApiService {
    constructor(delay = 150) {
        super("DummyApiService");

        this.delay = delay;
    }

    _makeResponse(data) {
        return new Promise((resolve, reject) => {
           setTimeout(() => resolve({data: {data}}), this.delay);
        });
    }

    getCallForwardingData() {
        return this._makeResponse([
            { status: 1, forward_to_number: '5555555555' }
        ]);
    }

    updateCallForwardingStatus() {
        return this._makeResponse({
            status: true,
        });
    }

    updateCallForwardingNumber() {
        return this._makeResponse({
            errors: "Dummy API driver only",
        });
    }

    getUserTimezone() {
        return this._makeResponse({
            name: "MOUNTAIN",
            value: "-07:00",
        });
    }
    updateUserTimezone() {
        return this._makeResponse({
            status: true,
        });
    }
    getUserLinks() {
        return this._makeResponse({
            meeting_url: 'www.fakesite.com',
        });
    }

    updateUserLinks(userId) {
        return this._makeResponse({
            status: false,
            message: `Dummy API driver only, ${userId} not updated.`,
        });
    }

        // Stores user filter preset
    storeUserFilterPresets(userId, filterPreset) {
        return this._makeResponse({
            status: false,
            message: `Dummy API driver only, ${userId} not updated.`,
        });
    }

    getUserSearchFilters(userId, category) {
        return this._makeResponse({
            status: false,
            message: `Dummy API driver only, ${category} filters not retrieved.`,
        });
    }

    storeUserSearchFilters(userId, category, filterPayload) {
        return this._makeResponse({
            status: false,
            message: `Dummy API driver only, ${category} filters not updated.`,
        });
    }
}

export { DummyApiService };

