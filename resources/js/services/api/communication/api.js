import axios from 'axios';
import {BaseApiService} from "./base";

class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    createOutboundCall(serviceName, userPhoneNumber, otherNumber, reference, relationType, relationId) {
        return this.axios().post(`/update-outbound-call/`, {
            service_name: serviceName,
            user_phone_number: userPhoneNumber,
            other_phone_number: otherNumber,
            external_reference: reference,
            call_result: 'initial',
            relation_type: relationType,
            relation_id: relationId
        });
    }

    logOutboundCall({ serviceName, userPhoneNumber, otherNumber, reference, relationType, relationId, result }) {
        return this.axios().post(`/update-outbound-call/`, {
            service_name: serviceName,
            user_phone_number: userPhoneNumber,
            other_phone_number: otherNumber,
            external_reference: reference,
            call_result: result,
            relation_type: relationType,
            relation_id: relationId
        });
    }

    logInboundCall({ serviceName, userPhoneNumber, otherNumber, reference, result = 'answered', relationType, relationId }) {
        return this.axios().post(`/update-inbound-call/`, {
            service_name: serviceName,
            user_phone_number: userPhoneNumber,
            other_phone_number: otherNumber,
            external_reference: reference,
            call_result: result,
            relation_type: relationType,
            relation_id: relationId
        });
    }

    updateCallLog({ relationType, relationId, note, id, type }) {
        return this.axios().patch(`/update-relation`, {
            relation_type: relationType,
            relation_id: relationId,
            note,
            type,
            id
        });
    }

    lookupCaller(phoneNumber) {
        return this.axios().get('/lookup-caller', {params: {other: phoneNumber}});
    }

    getVoicemails(phoneNumber) {
        return this.axios().get('/voicemails');
    }

    createOutboundSMS(contactPhone, body, relType, relId) {
        return this.axios().post('/create-outbound-sms', {other_phone_number: contactPhone, sms_body: body, relation_type: relType, relation_id: relId});
    }

    markVoicemailHeard(voicemailId) {
        return this.axios().post('/voicemails/' + voicemailId + '/mark-heard/');
    }

    getCallRecordingsForLeadId(leadId) {
        return this.axios().get('/call_recordings/lead/' + leadId);
    }

    getLogs(params = {}) {
        return this.axios().get('/logs', {
            params
        });
    }

    getLogFurtherInformation(id, type) {
        return this.axios().get('/log-further-data', {
            params: {
                id,
                type
            }
        });
    }

}

export { ApiService };

