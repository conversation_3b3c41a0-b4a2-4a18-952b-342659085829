import {ApiService} from "./api";
import {DummyApiService} from "./dummy";

class CommunicationApiFactory {
    static makeApiService(driver) {
        switch(driver) {
            case 'api':
                return new ApiService('internal-api', 'communication', 1);

            case 'dummy':
            default:
                return new DummyApiService();
        }
    }
}

export { CommunicationApiFactory };
