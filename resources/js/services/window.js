class WindowCustomOptions {
    /**
     * Calculates top and left given width and height
     *
     * @param width
     * @param height
     * @returns {{top: number, left: number}}
     */
    centered({ width, height }) {
        const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : window.screenX;
        const dualScreenTop = window.screenTop !== undefined ? window.screenTop : window.screenY;

        const w = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.w;
        const h = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.h;

        const systemZoom = w / window.screen.availWidth;
        const left = (w - width) / 2 / systemZoom + dualScreenLeft
        const top = (h - height) / 2 / systemZoom + dualScreenTop

        return {
            top,
            left
        }
    }
}

class WindowOptions {
    /**
     * @param {boolean} centered
     * @constructor
     */
    constructor({ centered = true } = {}) {
        this.centered = centered
    }
}

class WindowFeatures {
    /**
     * @param {boolean} popup
     * @param {number} width
     * @param {number} height
     * @constructor
     */
    constructor({
        popup = true,
        width = 400,
        height = 800
    } = {}) {
        this.popup = popup
        this.width = width
        this.height = height
    }
}

export class WindowService {
    constructor({ defaultWindowFeatures = new WindowFeatures(), defaultWindowOptions = new WindowOptions(), windowCustomOptions = new WindowCustomOptions() } = {}) {
        this._defaultWindowFeatures = defaultWindowFeatures
        this._defaultWindowOptions = defaultWindowOptions
        this._windowCustomOptions = windowCustomOptions
    }

    /**
     * Open new window
     *
     * @param {Object} params incoming
     * @param {WindowFeatures} features // default shape of the object
     * @param {WindowOptions} options
     * @returns WindowProxy
     * @public
     */
    openNewWindow({ url, name }, features = this._defaultWindowFeatures, options = this._defaultWindowOptions) {
        const extractedFeatures = this._extractProperties(features, this._defaultWindowFeatures)
        const extractedOptions = this._extractProperties(options, this._defaultWindowOptions)

        const parsedOptionsToFeatures = this._parseCustomOptions(extractedOptions, extractedFeatures)

        const parsedFeatures = this._parseFeatures({ ...extractedFeatures, ...parsedOptionsToFeatures })

        return window.open(url, name, parsedFeatures)
    }


    /**
     * Focus already opened window
     *
     * @param {string} name
     * @returns WindowProxy
     * @public
     */
    focusWindow(name) {
        return window.open('', name)
    }

    /**
     * Call method to remove unknown properties given a list of properties
     *
     * @param {Object} incoming
     * @param {Object} base // default shape of the object
     * @returns string
     * @private
     */
    _extractProperties(incoming = {}, base = {}) {
        const availableFeatures = Object.keys(base)

        return this._stripUnknownPropertiesFromObject(incoming, availableFeatures)
    }

    /**
     * Parse features to window expected pattern
     *
     * @param {Object} features
     * @returns string
     * @private
     */
    _parseFeatures(features = {}) {
        return Object.entries(features).map(([key, val]) => `${key}=${val}`).join()
    }

    /**
     * Remove unknown properties from object
     *
     * @param {Object} obj Incoming object
     * @param {string[]} ref List of properties
     * @returns Object
     * @private
     */
    _stripUnknownPropertiesFromObject(obj = {}, ref = []) {
        return Object.entries(obj)
            .filter(([key]) => ref.includes(key))
            .reduce((prev, [key, val]) => Object.assign(prev, { [key]: val }), {})
    }


    /**
     * Execute custom objects methods and merge them into a object
     *
     @param {Object} customOptions Custom options
     * @param {Object} extractedFeatures Extracted features
     * @returns Object
     * @private
     */
    _parseCustomOptions(customOptions, extractedFeatures) {
        const entries = Object.entries(customOptions)
            .map(([method, val]) => {
                try {
                    return val ? this._windowCustomOptions[method](extractedFeatures) : {}
                } catch (error) {
                    console.error(error);
                    return {}
                }
            })

        return entries.length > 0 ? entries.reduce((prev, curr) => Object.assign({ ...prev, curr }, {})) : {}
    }
}
