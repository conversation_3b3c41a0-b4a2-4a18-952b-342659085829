<?php

namespace Tests\Unit\BillingWorkflows\Activities;

use App\BillingWorkflows\Activities\SendEmailNotification;
use App\DTO\BillingWorkflow\ActivityArgument;
use App\Enums\NotificationMethod;
use App\Enums\CompanyNotificationType;
use App\Enums\Billing\PaymentMethodServices;
use App\Mail\OutreachCadence\ConfigurableEmail;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceTemplate;
use App\Models\CompanyNotificationSetting;
use App\Models\CompanyUserRelationship;
use App\Models\EmailTemplate;
use App\Models\Legacy\EloquentAlert;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Workflow\Models\StoredWorkflow;

class SendEmailNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Config::set('services.google.storage.buckets.email_template_images', 'mocked-bucket');

        Config::set('app.outgoing_communication.test_mode', false);
    }

    /**
     * @throws Exception
     * @note If this does not pass, run `composer update` to ensure your packages are up to date
     */
    #[Test]
    public function it_runs(): void
    {
        Mail::fake();

        $emailTemplate = EmailTemplate::factory()->create();

        $invoice = Invoice::factory()->createQuietly([
            'billing_profile_id' => BillingProfile::factory()->createQuietly([
                'payment_method' => PaymentMethodServices::STRIPE,
            ]),
        ]);

        InvoiceTemplate::factory()->create([
            'model_type' => Invoice::class,
            'model_id' => $invoice->id,
            'is_global' => true,
            'props' => [
                'support_email' => '<EMAIL>',
            ],
        ]);

        $userRelationship = CompanyUserRelationship::factory()->accountManager()->create([
            'company_id' => $invoice->company_id,
        ]);

        $result = app(SendEmailNotification::class, [
            'index' => 1,
            'now' => now()->format('Y-m-d H:i:s'),
            'storedWorkflow' => app(StoredWorkflow::class),
        ])->run(app(ActivityArgument::class, [
            'eventClassTrigger' => 'test',
            'context' => [
                'invoice_uuid' => $invoice->uuid,
            ],
            'actionData' => [
                'target_types' => ['account_manager'],
                'email_template_id' => $emailTemplate->id,
            ],
        ]));

        $this->assertEquals([], $result);

        Mail::assertSent(ConfigurableEmail::class, static function (ConfigurableEmail $email) use ($userRelationship) {
            throw_unless($email->hasTo($userRelationship->user->email), "Expected email to be sent to {$userRelationship->user->email}. Sent instead to ".json_encode($email->to, JSON_THROW_ON_ERROR));

            throw_unless($email->hasFrom('<EMAIL>'), 'Expected from email to be "<EMAIL>". Got "'.$email->fromEmail.'"');

            throw_unless($email->fromName === 'Solarreviews', 'Expected from name to be "Solarreviews". Got "'.$email->fromName.'"');

            return true;
        });

        Mail::assertSentCount(1);
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function it_runs_with_not_sending_email_due_to_no_account_manager(): void
    {
        Mail::fake();

        $emailTemplate = EmailTemplate::factory()->create();

        $invoice = Invoice::factory()->createQuietly();

        InvoiceTemplate::factory()->create([
            'model_type' => Invoice::class,
            'model_id' => $invoice->id,
            'is_global' => true,
            'props' => [
                'support_email' => '<EMAIL>',
            ],
        ]);

        CompanyUserRelationship::factory()->businessDevelopmentManager()->create([
            'company_id' => $invoice->company_id,
        ]);

        $result = app(SendEmailNotification::class, [
            'index' => 1,
            'now' => now()->format('Y-m-d H:i:s'),
            'storedWorkflow' => app(StoredWorkflow::class),
        ])->run(app(ActivityArgument::class, [
            'eventClassTrigger' => 'test',
            'context' => [
                'invoice_uuid' => $invoice->uuid,
            ],
            'actionData' => [
                'target_types' => ['account_manager'],
                'email_template_id' => $emailTemplate->id,
            ],
        ]));

        $this->assertEquals([], $result);

        Mail::assertSentCount(0);
    }

    #[Test]
    public function it_runs_with_empty_target_types(): void
    {
        $this->partialMock(SendEmailNotification::class)->shouldAllowMockingProtectedMethods()->shouldReceive('log');

        Mail::fake();

        $result = app(SendEmailNotification::class, [
            'index' => 1,
            'now' => now()->format('Y-m-d H:i:s'),
            'storedWorkflow' => app(StoredWorkflow::class),
        ])->run(app(ActivityArgument::class, [
            'eventClassTrigger' => 'test',
            'context' => [],
            'actionData' => [
                'target_types' => [],
            ],
        ]));

        $this->assertEquals([], $result);

        Mail::assertSentCount(0);
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function it_runs_with_target_type_is_company_billing_contacts(): void
    {
        Schema::connection('readonly')->disableForeignKeyConstraints();

        EloquentCompany::truncate();

        Schema::connection('readonly')->enableForeignKeyConstraints();

        Mail::fake();

        $emailTemplate = EmailTemplate::factory()->createQuietly();

        $invoice = Invoice::factory()->createQuietly([
            'billing_profile_id' => BillingProfile::factory()->createQuietly([
                'payment_method' => PaymentMethodServices::STRIPE,
            ]),
            'company_id' => Company::factory()->legacyCompany()->createQuietly(),
        ]);

        InvoiceTemplate::factory()->createQuietly([
            'model_type' => Invoice::class,
            'model_id' => $invoice->id,
            'is_global' => true,
            'props' => [
                'support_email' => '<EMAIL>',
            ],
        ]);

        CompanyUserRelationship::factory()->accountManager()->createQuietly([
            'company_id' => $invoice->company_id,
        ]);

        $companyContact = CompanyUser::factory()->createQuietly([
            CompanyUser::FIELD_COMPANY_ID => $invoice->company->id,
            CompanyUser::FIELD_IS_CONTACT => CompanyUser::USER_IS_CONTACT,
        ]);

        CompanyNotificationSetting::query()->createQuietly([
            CompanyNotificationSetting::FIELD_COMPANY_ID => $invoice->company->id,
            CompanyNotificationSetting::FIELD_NOTIFIABLE_TYPE => CompanyUser::class,
            CompanyNotificationSetting::FIELD_NOTIFIABLE_ID => $companyContact->id,
            CompanyNotificationSetting::FIELD_NOTIFICATION_METHOD => NotificationMethod::EMAIL,
            CompanyNotificationSetting::FIELD_NOTIFICATION_TYPE => CompanyNotificationType::INVOICES
        ]);

        $result = app(SendEmailNotification::class, [
            'index' => 1,
            'now' => now()->format('Y-m-d H:i:s'),
            'storedWorkflow' => app(StoredWorkflow::class),
        ])->run(app(ActivityArgument::class, [
            'eventClassTrigger' => 'test',
            'context' => [
                'invoice_uuid' => $invoice->uuid,
            ],
            'actionData' => [
                'target_types' => ['company_billing_contacts'],
                'email_template_id' => $emailTemplate->id,
            ],
        ]));

        $this->assertEquals([], $result);

        Mail::assertSent(ConfigurableEmail::class, static function (ConfigurableEmail $email) use ($companyContact) {
            throw_unless($email->hasTo($companyContact->email), "Expected email to be sent to {$companyContact->email}. Sent instead to ".json_encode($email->to, JSON_THROW_ON_ERROR));

            throw_unless($email->hasFrom('<EMAIL>'), 'Expected from email to be "<EMAIL>". Got "'.$email->fromEmail.'"');

            throw_unless($email->fromName === 'Solarreviews', 'Expected from name to be "Solarreviews". Got "'.$email->fromName.'"');

            return true;
        });

        Mail::assertSentCount(1);
    }

    #[Test]
    public function it_gets_id(): void
    {
        $this->assertEquals('send_email_notification', SendEmailNotification::getId());
    }

    #[Test]
    public function it_gets_name(): void
    {
        $this->assertEquals('Send email notification', SendEmailNotification::getName());
    }

    #[Test]
    public function it_gets_description(): void
    {
        $this->assertEquals('', SendEmailNotification::getDescription());
    }
}
